apply plugin: 'com.android.library'

android {
//    compileSdkVersion 33
//    buildToolsVersion "33.0.0"
    namespace "eddy.android.billing"
    compileSdk = 36

    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 36

    }

    buildTypes {
        release {
            debuggable true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    lint {
        disable 'Instantiatable'
    }

}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    api 'com.android.billingclient:billing:8.0.0'
    implementation 'com.google.android.gms:play-services-analytics-impl:18.2.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
    annotationProcessor "androidx.lifecycle:lifecycle-common-java8:2.9.2"
    //implementation 'com.qmuiteam:qmui:2.0.0-alpha04'
    implementation 'com.google.guava:guava:33.4.8-jre'
    implementation 'com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava'
}
