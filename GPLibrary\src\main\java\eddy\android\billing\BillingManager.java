package eddy.android.billing;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;

import androidx.annotation.NonNull;

import eddy.android.billing.util.Security;

import com.android.billingclient.api.BillingClient;
import com.android.billingclient.api.BillingClientStateListener;
import com.android.billingclient.api.BillingFlowParams;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ConsumeResponseListener;
import com.android.billingclient.api.PendingPurchasesParams;
import com.android.billingclient.api.ProductDetailsResponseListener;
import com.android.billingclient.api.Purchase;
import com.android.billingclient.api.PurchasesResponseListener;
import com.android.billingclient.api.PurchasesUpdatedListener;
import com.android.billingclient.api.QueryProductDetailsParams;
import com.android.billingclient.api.QueryProductDetailsResult;
import com.android.billingclient.api.QueryPurchasesParams;
import com.android.billingclient.api.SkuDetails;
import com.android.billingclient.api.SkuDetailsParams;
import com.android.billingclient.api.SkuDetailsResponseListener;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Date: 2019/10/15
 * Author: anmi
 * Desc:google 支付管理类
 * 处理与Google Play Store的所有交互(通过计费库)，维护与Google Play Store的连接
 * 通过BillingClient缓存临时状态/数据
 */
public class BillingManager implements PurchasesUpdatedListener {

    /**
     * 在BillingManager还没有初始化之前，mBillingClientResponseCode的默认值
     */
    public static final int BILLING_MANAGER_NOT_INITIALIZED = -1;

    private static final String TAG = BillingManager.class.getSimpleName();

   // private final static LoggerUtil mLogger;

    static {
       // mLogger = new LoggerUtil();
       // mLogger.setTag(TAG);
    }

    /**
     * BillingClient 实例对象
     **/
    private BillingClient mBillingClient;

    /**
     * 服务连接状态
     */
    private boolean mIsServiceConnected;

    private final BillingUpdatesListener mBillingUpdatesListener;

    private final Context cc;

    private final List<Purchase> mPurchases = new ArrayList<>();

    private Set<String> mTokensToBeConsumed;

    private int mBillingClientResponseCode = BILLING_MANAGER_NOT_INITIALIZED;


    /* BASE_64_ENCODED_PUBLIC_KEY should be YOUR APPLICATION'S PUBLIC KEY
     * (that you got from the Google Play developer console). This is not your
     * developer public key, it's the *app-specific* public key.
     *
     * Instead of just storing the entire literal string here embedded in the
     * program,  construct the key at runtime from pieces or
     * use bit manipulation (for example, XOR with some other string) to hide
     * the actual key.  The key itself is not secret information, but we don't
     * want to make it easy for an attacker to replace the public key with one
     * of their own and then fake messages from the server.
     */
    //private static final String BASE_64_ENCODED_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAy2VJ+SkdRx25Nw3h0L8Iayt2WLvLI9l3cl8gFdMlBNrnHcJbzS0LS7L+YIpkkbHyi8pNuby7cdVF14gJ1xIpekEDgM+kjbgRRQrTstwDm72l3KQvvjR6sWlQSMAuhKa18bIVm8eh5DE6G/zDOjPMs5WRXp9tfhNqIFbt/GgVrag4SoiuqGFXRj/Ng8+1JZ0kO+HnOVw/LhoxT/PtowvpU5/xvUsDQ4l5NFCx0bgeDDjMf8ItfSqJSIuVEDLDXk8bUllZEgiTF7//0kgOH3us98T++bQfhWWnRmukve62F14YwmcZ8lo0mW9BLpk7jl3hGlaWvalTheHgSmIy0HD8hwIDAQAB";


    /**
     * 用于监听购买列表完成消费的更新
     */
    public interface BillingUpdatesListener {
        void onBillingClientSetupFinished();

        void onConsumeFinished(BillingResult billingResult, String purchaseToken);

        void onPurchasesUpdated(List<Purchase> purchases);

        void onBillingError(@NonNull String error);
    }


    /**
     * 用于监听 Google Play Store客户端的连接状态
     */
    public interface ServiceConnectedListener {
        void onServiceConnected(@BillingClient.BillingResponseCode int resultCode);
    }

    public BillingManager(Context activity, final BillingUpdatesListener updatesListener) {

        //mLogger.printDebugLog("Creating Billing client.");
        this.cc = activity;
        this.mBillingUpdatesListener = updatesListener;

        //构建BillingClient
        mBillingClient = BillingClient.newBuilder(cc)
                .enablePendingPurchases(PendingPurchasesParams.newBuilder().enableOneTimeProducts().build())
                .setListener(this).build();

        //mLogger.printDebugLog("Starting setup.");

        /**
         *异步启动服务连接Google Play Store客户端
         Start setup. This is asynchronous and the specified listener will be called
         once setup completes.
         It also starts to report all the new purchases through onPurchasesUpdated() callback.
         */
        startServiceConnection(new Runnable() {
            @Override
            public void run() {
                //通知购买客户端监听器 通信已就绪
                mBillingUpdatesListener.onBillingClientSetupFinished();

                //IAB已经建立完成，进行查询我们的库存
                //mLogger.printDebugLog("Setup successful. Querying inventory.");

                queryPurchases();

            }
        });

    }




    /**
     * 跨应用查询购买信息，并通过监听器返回结果
     */
    public void queryPurchases() {
        //创建查询任务
        Runnable queryToExecute = new Runnable() {
            @Override
            public void run() {

                QueryPurchasesParams params = QueryPurchasesParams.newBuilder()
                        .setProductType(BillingClient.ProductType.SUBS)
                        .build();
                mBillingClient.queryPurchasesAsync(params, new PurchasesResponseListener() {
                    public void onQueryPurchasesResponse(
                            BillingResult billingResult,
                            List<Purchase> purchases) {
                        // Process the result
                        onPurchasesUpdated(billingResult, purchases);
                    }
                });

//
//
//                //如果支持订阅，将添加订阅处理
//                if (areSubscriptionsSupported()) {
//                    //查询订阅
//                    Purchase.PurchasesResult subscriptionResult
//                            = mBillingClient.queryPurchases(BillingClient.SkuType.SUBS);
//
//                    //mLogger.printDebugLog("Querying purchases and subscriptions elapsed time: %s ms", (System.currentTimeMillis() - time));
//
//                    //mLogger.printDebugLog("Querying subscriptions result code: %s Purchases size: %s", subscriptionResult.getResponseCode(), subscriptionResult.getPurchasesList().size());
//
//                    if (subscriptionResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
//
//                        purchasesResult.getPurchasesList().addAll(subscriptionResult.getPurchasesList());
//
//                    } else {
//                        //mLogger.printErrorLog("Got an error response trying to query subscription purchases");
//                    }
//                } else if (purchasesResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
//                    //跳过订阅购买查询，因为它不受支持
//                    //mLogger.printDebugLog("Skipped subscription purchases query since they are not supported");
//                } else {
//
//                    //mLogger.printDebugLog("queryPurchases() got an error response code: " + purchasesResult.getResponseCode());
//
//                }
//
//                onQueryPurchasesFinished(purchasesResult);


            }
        };
        //执行查询任务的请求
        executeServiceRequest(queryToExecute);
    }


//    /**
//     * 处理查询购买的结果，并通过监听器通知更新后的列表
//     */
//    private void onQueryPurchasesFinished(Purchase.PurchasesResult result) {
//        // Have we been disposed of in the meantime? If so, or bad result code, then quit
//        if (mBillingClient == null || result.getResponseCode() != BillingClient.BillingResponseCode.OK) {
//
//            //mLogger.printErrorLog("Billing client was null or result code (%s)was bad - quitting", result.getResponseCode());
//            return;
//        }
//
//        //mLogger.printDebugLog("Query inventory was successful.");
//
//        // Update the UI and purchases inventory with new list of purchases
//        mPurchases.clear();
//
//        onPurchasesUpdated(result.getBillingResult(), result.getPurchasesList());
//    }


    /**
     * 检查当前客户端是否支持订阅
     * 注意:此方法不会自动重试RESULT_SERVICE_DISCONNECTED。
     * 它只用于queryPurchase执行后， 实现了一个回退机制。
     */
    public boolean areSubscriptionsSupported() {
        BillingResult featureSupported = mBillingClient.isFeatureSupported(BillingClient.FeatureType.SUBSCRIPTIONS);

        if (featureSupported.getResponseCode() != BillingClient.BillingResponseCode.OK) {
            //mLogger.printErrorLog("areSubscriptionsSupported() got an error response: " + featureSupported.getResponseCode());
        }

        return featureSupported.getResponseCode() == BillingClient.BillingResponseCode.OK;
    }

    /**
     * 异步启动服务连接Google Play Store客户端（底层会走AIDL的方式）
     *
     * @param executeOnSuccess
     */
    public void startServiceConnection(final Runnable executeOnSuccess) {
        mBillingClient.startConnection(new BillingClientStateListener() {
            @Override
            public void onBillingSetupFinished(BillingResult billingResult) {
                //mLogger.printDebugLog("Setup finished. Response code: " + billingResult.getResponseCode());

                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    // BillingClient已经准备好了。在这里可以查询购买情况。
                    mIsServiceConnected = true;
                    if (executeOnSuccess != null) {
                        executeOnSuccess.run();
                    }
                }
                mBillingClientResponseCode = billingResult.getResponseCode();

            }

            @Override
            public void onBillingServiceDisconnected() {
                mIsServiceConnected = false;
            }
        });
    }

    /**
     * 处理从帐单库更新购买的回调
     */
    @Override
    public void onPurchasesUpdated(BillingResult billingResult,List<Purchase> purchases) {
        if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
            //购买成功
            for (Purchase purchase : purchases) {
                handlePurchase(purchase);
            }
            mBillingUpdatesListener.onPurchasesUpdated(purchases);
        } else if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.USER_CANCELED) {
            //用户取消了购买流程
            //mLogger.printErrorLog("onPurchasesUpdated() - user cancelled the purchase flow - skipping");
        } else {
            //出现其他错误，具体查询返回的状态码
            //mLogger.printErrorLog("onPurchasesUpdated() got unknown resultCode: " + billingResult.getResponseCode());
            //Eddy 此处需在处理
            mBillingUpdatesListener.onBillingError(String.valueOf(billingResult.getResponseCode()));
        }
    }

    /**
     * 处理采购
     * <p>注意：对于每次购买，都要检查签名在客户端是否有效。
     * 建议将此检查移到后端。
     * 参见{@link Security#verifyPurchase(String, String, String)}
     * </p>
     *
     * @param purchase 待处理的购买信息
     */
    private void handlePurchase(Purchase purchase) {
        if (!verifyValidSignature(purchase.getOriginalJson(), purchase.getSignature())) {
            //mLogger.printErrorLog("Got a purchase: %s : but signature is bad. Skipping...", purchase);
            return;
        }

        //mLogger.printDebugLog("Got a verified purchase: " + purchase);
        mPurchases.clear();
        mPurchases.add(purchase);
    }

    /**
     * 验证应用 的BASE_64_ENCODED_PUBLIC_KEY是否签属了购买
     * <p>
     * 强烈建议以下的判断放至业务后台进行处理判断，因为黑客可以进行反编译提取到BASE_64_ENCODED_PUBLIC_KEY，并重建应用，用“constant true”替换这个方法
     */
    private boolean verifyValidSignature(String signedData, String signature) {
        return true;
        /*
        // Some sanity checks to see if the developer (that's you!) really followed the
        // instructions to run this sample (don't put these checks on your app!)
        if (BASE_64_ENCODED_PUBLIC_KEY.contains("fdfdfdf")) {
            throw new RuntimeException("Please update your app's public key at: "
                    + "BASE_64_ENCODED_PUBLIC_KEY");
        }

        try {
            return Security.verifyPurchase(BASE_64_ENCODED_PUBLIC_KEY, signedData, signature);
        } catch (IOException e) {

            //mLogger.printErrorLog("Got an exception trying to validate a purchase: " + e);

            return false;
        }

         */
    }

    public void initiatePurchaseFlow(final Activity activity,
                                     final SkuDetails skuDetails) {
        if (areSubscriptionsSupported()) {
            Runnable purchaseFlowRequest = new Runnable() {
                @Override
                public void run() {
                    BillingFlowParams purchaseParams = BillingFlowParams.newBuilder()
                            .setSkuDetails(skuDetails)
                            .build();
                    mBillingClient.launchBillingFlow(activity, purchaseParams);
                }
            };
            executeServiceRequest(purchaseFlowRequest);
        }
    }
    /**
     * 启动购买或订阅流程
     */
//    public void initiatePurchaseFlow(final SkuDetails skuDetails, final String developerPayload) {
//        Runnable purchaseFlowRequest = new Runnable() {
//            @Override
//            public void run() {
//                HookUtil.doSetPayloadToBillingClient(mBillingClient,developerPayload);
//
//                BillingFlowParams.Builder builder = BillingFlowParams.newBuilder();
//
//                builder.setSkuDetails(skuDetails);
//
//                mBillingClient.launchBillingFlow((Activity)((ContextWrapper)cc).getBaseContext(), builder.build());
//            }
//        };
//
//        executeServiceRequest(purchaseFlowRequest);
//    }


    /**
     * 异步查询商品信息
     *
     * @param itemType
     * @param skuList
     * @param listener
     */
    public void querySkuDetailsAsync(@BillingClient.SkuType final String itemType, final List<String> skuList,
                                     final SkuDetailsResponseListener listener) {
        // Creating a runnable from the request to use it inside our connection retry policy below
        Runnable queryRequest = new Runnable() {
            @Override
            public void run() {
                // Query the purchase async
                List<QueryProductDetailsParams.Product> productList = new ArrayList<>();
                productList.add(
                    QueryProductDetailsParams.Product.newBuilder()
                        .setProductId("your_product_id")
                        .setProductType(BillingClient.ProductType.INAPP)
                        .build()
                );

                QueryProductDetailsParams params = QueryProductDetailsParams.newBuilder()
                    .setProductList(productList)
                    .build();

                mBillingClient.queryProductDetailsAsync(params, new ProductDetailsResponseListener() {
                    @Override
                    public void onProductDetailsResponse(@NonNull BillingResult billingResult, @NonNull QueryProductDetailsResult queryProductDetailsResult) {

                    }

                });
            }
        };

        executeServiceRequest(queryRequest);
    }


    /**
     * 异步消耗商品
     * 只有消费成功之后，才能真正到账，否则3天之后，会执行退款处理 测试阶段只有5分钟
     *
     * @param purchase
     */
    public void consumeAsync(final Purchase purchase) {
        // If we've already scheduled to consume this token - no action is needed (this could happen
        // if you received the token when querying purchases inside onReceive() and later from
        // onActivityResult()
        if (mTokensToBeConsumed == null) {
            mTokensToBeConsumed = new HashSet<>();
        } else if (mTokensToBeConsumed.contains(purchase.getPurchaseToken())) {

            //mLogger.printErrorLog("Token was already scheduled to be consumed - skipping...");

            return;
        }
        mTokensToBeConsumed.add(purchase.getPurchaseToken());


        // Generating Consume Response listener
        final ConsumeResponseListener onConsumeListener = new ConsumeResponseListener() {

            @Override
            public void onConsumeResponse(BillingResult billingResult, String purchaseToken) {
                //mLogger.printErrorLog("onConsumeResponse code = %s  ,msg = %s ,purchaseToken = %s", billingResult.getResponseCode(), billingResult.getDebugMessage(), purchaseToken);

                if (billingResult.getResponseCode() == BillingClient.BillingResponseCode.OK) {
                    //消耗成功

                } else {
                    // 消费失败,后面查询消费记录后再次消费，否则，就只能等待退款
                }

                mBillingUpdatesListener.onConsumeFinished(billingResult, purchaseToken);
            }
        };

        // Creating a runnable from the request to use it inside our connection retry policy below
       /*
        Runnable consumeRequest = new Runnable() {
            @Override
            public void run() {
                // Consume the purchase async
                ConsumeParams consumeParams = ConsumeParams.newBuilder()
                        .setDeveloperPayload(purchase.getDeveloperPayload())
                        .setPurchaseToken(purchase.getPurchaseToken()).build();
                mBillingClient.consumeAsync(consumeParams, onConsumeListener);
            }
        };

        executeServiceRequest(consumeRequest);
        */
    }


    /**
     * Returns the value Billing client response code or BILLING_MANAGER_NOT_INITIALIZED if the
     * clien connection response was not received yet.
     */
    public int getBillingClientResponseCode() {
        return mBillingClientResponseCode;
    }



    /**
     * 执行任务并判断是否需要重连服务
     * @param runnable
     */
    private void executeServiceRequest(Runnable runnable) {
        if (mIsServiceConnected) {
            runnable.run();
        } else {
            // 如果账单服务被断开，我们尝试重新连接一次.
            // (feel free to introduce your retry policy here).
            startServiceConnection(runnable);
        }
    }

    /**
     * Clear the resources
     * activity -onDestroy调用
     */
    public void destroy() {
        //mLogger.printDebugLog("Destroying the manager.");

        if (mBillingClient != null && mBillingClient.isReady()) {
            mBillingClient.endConnection();
            mBillingClient = null;
        }
    }

}
