package eddy.android.billing;

import android.util.Log;

/**
 * Date: 2019/10/15
 * Author: anmi
 * Desc:
 */
public class LoggerUtil {
    private static final int LOG_D = 0;
    private static final int LOG_E = 1;
    private static final int LOG_W = 2;
    private static final int LOG_I = 3;

    private String tag = LoggerUtil.class.getSimpleName();

    public void setTag(String tag) {
        this.tag = tag;
    }


    private void printLog(String formatMessage, Object... args) {
        printLog(LOG_D, formatMessage, args);
    }

    private void printLog(int logType, String formatMessage, Object... args) {
        String message = getFormatData(formatMessage, args);
        switch (logType) {
            case LOG_E:
                Log.e(tag, message);
                break;
            case LOG_W:
                Log.w(tag, message);
                break;
            case LOG_I:
                Log.i(tag, message);
                break;
            default:
                Log.d(tag, message);
                break;
        }
    }

    public void printInfoLog(String message) {
        printInfoLog(message, "");
    }

    public void printInfoLog(String formatMessage, Object... args) {
        printLog(LOG_I, formatMessage, args);
    }


    public void printWarnLog(String message) {
        printWarnLog(message, "");
    }

    public void printWarnLog(String formatMessage, Object... args) {
        printLog(LOG_W, formatMessage, args);
    }

    public void printDebugLog(String message) {
        printDebugLog(message, "");
    }

    public void printDebugLog(String formatMessage, Object... args) {
        printLog(formatMessage, args);
    }

    public void printErrorLog(String errorMessage) {
        printErrorLog(errorMessage, "");
    }

    public void printErrorLog(String formatErrorMessage, Object... args) {
        printLog(LOG_E, formatErrorMessage, args);
    }

    private String getFormatData(String formatInfo, final Object... args) {
        String data;
        if (args == null || args.length == 0) {
            data = formatInfo;
        } else {
            data = String.format(formatInfo, args);
        }
        return data;
    }
}
