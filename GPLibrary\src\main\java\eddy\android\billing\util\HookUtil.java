package eddy.android.billing.util;

//import com.android.vending.billing.IInAppBillingService;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * Date: 2019/10/16
 * Author: anmi
 * Desc:反射工具类
 */
public class HookUtil {

    /**
     * 反射BillingClient(->BillingClientImpl)类中的 InAppBilling服务的接口，
     * Hook getBuyIntent等API调用，然后填充developerPayload。只要InAppBillingService接口不变动，不需要更新
     * tip:不能混淆IInAppBillingService,BillingClient, BillingClientImpl等类
     * @param billingClientObj BillingClient对象
     * @param developerPayload 附加值
     */
     public static void doSetPayloadToBillingClient(Object billingClientObj, final String developerPayload) {
         /*
        try {
            Class cls = billingClientObj.getClass();
            Field field = cls.getDeclaredField("mService");
            field.setAccessible(true);
            final Object originObj = field.get(billingClientObj);
            //此处用于开关标记，避免拿到旧的代理类，导致developerPayload一直拿到旧的
            final boolean[] toggle = {true};

            IInAppBillingService newService = (IInAppBillingService) Proxy.newProxyInstance(IInAppBillingService.class.getClassLoader(),
                    new Class[]{IInAppBillingService.class}, new InvocationHandler() {
                        @Override
                        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                            if (toggle[0]) {
                                String methodName = method.getName();
                                //根据以下方法对应developerPayload的位置进行填充
                                switch (methodName) {
                                    case "getBuyIntent":
                                        //修改第5个参数
                                        args[4] = developerPayload;
                                        break;
                                    case "getBuyIntentToReplaceSkus":
                                        //修改第6个参数
                                        args[5] = developerPayload;
                                        break;
                                    case "getBuyIntentExtraParams":
                                        //修改第5个参数
                                        args[4] = developerPayload;
                                        break;
                                }

                                toggle[0] = false;
                            }

                            //将mService更改为我们的代理对象
                            return method.invoke(originObj, args);
                        }
                    });

            field.set(billingClientObj, newService);
        } catch (Exception e) {
            e.printStackTrace();
        }
        */
    }
}
