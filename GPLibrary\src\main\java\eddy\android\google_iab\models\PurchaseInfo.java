package eddy.android.google_iab.models;

import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.Purchase;
import eddy.android.google_iab.enums.SkuProductType;


public class PurchaseInfo {

    private String skuId;
    private SkuProductType skuProductType;
    private String producttype;
    private SkuInfo skuInfo;
    private Purchase purchase;
    private ProductDetails productDetails;
    public PurchaseInfo(SkuInfo skuInfo, Purchase purchase) {
        this.skuInfo = skuInfo;
        this.purchase = purchase;
        this.skuId = skuInfo.getSkuId();
        this.skuProductType = skuInfo.getSkuProductType();
    }

    public PurchaseInfo(ProductDetails skuInfo, Purchase purchase) {
        this.productDetails = skuInfo;
        this.purchase = purchase;
        this.skuId = skuInfo.getProductId();
        this.producttype = skuInfo.getProductType();
    }

    public String getSkuId() {
        return skuId;
    }

    public SkuProductType getSkuProductType() {
        return skuProductType;
    }

    public String getProductType() {
        return producttype;
    }

    public SkuInfo getSkuInfo() {
        return skuInfo;
    }

    public Purchase getPurchase() {
        return purchase;
    }
}