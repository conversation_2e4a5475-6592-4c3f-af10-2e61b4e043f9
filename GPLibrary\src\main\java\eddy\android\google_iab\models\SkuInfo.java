package eddy.android.google_iab.models;

import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.SkuDetails;
import eddy.android.google_iab.enums.SkuProductType;


public class SkuInfo {

    private final String skuId;
    private final SkuProductType skuProductType;
    private SkuDetails skuDetails;
    private ProductDetails productDetails;
    public SkuInfo(SkuProductType skuProductType, SkuDetails skuDetails) {
        this.skuProductType = skuProductType;
        this.skuDetails = skuDetails;
        this.skuId = skuDetails.getSku();
    }

    public SkuInfo(SkuProductType skuProductType, ProductDetails productDetails) {
        this.skuProductType = skuProductType;
        this.skuId = productDetails.getProductId();
        this.productDetails = productDetails;
    }

    public String getSkuId() {
        return skuId;
    }

    public SkuProductType getSkuProductType() {
        return skuProductType;
    }

    public SkuDetails getSkuDetails() {
        return skuDetails;
    }

    public ProductDetails getProductDetails() {return productDetails; }
}