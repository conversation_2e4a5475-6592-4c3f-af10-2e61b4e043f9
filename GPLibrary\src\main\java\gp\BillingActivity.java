package gp;

import android.app.ProgressDialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import eddy.android.billing.R;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.SkuDetails;
import eddy.android.google_iab.BillingConnector;
import eddy.android.google_iab.BillingEventListener;
import eddy.android.google_iab.models.BillingResponse;
import eddy.android.google_iab.models.PurchaseInfo;
import eddy.android.google_iab.models.SkuInfo;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;

import org.json.JSONException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class BillingActivity extends AppCompatActivity implements  BillingEventListener {
    private boolean bSubscribed = false;
    private LinearLayout mlayout;
    private RecyclerView main_vp_container;
    private BillingConnector billingConnector;
    private CollapsingToolbarLayout mCollapsingToolbarLayout;
    private LinearLayout head_layout;
    private CoordinatorLayout root_layout;
    private ProgressDialog tipDlog;
    private UserAdapter mAdapter;
    public static BillingViewModel viewModel;
    private final List<PurchaseInfo> purchasedInfoList = new ArrayList<>();
    private List<BillingPurchaseDetails> mPurchases = new ArrayList<>();
    private Handler hudHandler = new Handler(Looper.getMainLooper());
    private Runnable rCallBack = null;
    private Runnable hudRunnable = new Runnable() {
        @Override
        public void run() {
            hudHandler.removeCallbacks(hudRunnable);
            tipDlog.dismiss();
            if (rCallBack != null) {
                rCallBack.run();
            }
        }
    };

    public void showDelayWait(Runnable r, long delayMillis) {
        rCallBack = r;
        hudHandler.postDelayed(hudRunnable, delayMillis);
        tipDlog.show();
    }

    public void DisMissWait() {
        rCallBack = null;
        hudHandler.removeCallbacks(hudRunnable);
        tipDlog.dismiss();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initViews();
    }

    private void initProgressDialog(Context cc) {
        tipDlog = new ProgressDialog(cc);
        tipDlog.setIndeterminate(false);//循环滚动
        tipDlog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
        tipDlog.setMessage("loading...");
        tipDlog.setCancelable(false);//false不能取消显示，true可以取消显示
    }

    private void initViews() {
        //init purchase buttons
        setContentView(R.layout.premium_main);
        initProgressDialog(this);
        AppBarLayout app_bar_layout = findViewById(R.id.app_bar_layout);
        Toolbar mToolbar = findViewById(R.id.toolbar);
        setSupportActionBar(mToolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        mToolbar.setNavigationOnClickListener(v -> {

            onBackPressed();
        });
        head_layout = findViewById(R.id.head_layout);
        root_layout = findViewById(R.id.root_layout);
        //使用CollapsingToolbarLayout必须把title设置到CollapsingToolbarLayout上，设置到Toolbar上则不会显示
        mCollapsingToolbarLayout = findViewById(R.id
                .collapsing_toolbar_layout);

        mlayout = findViewById(R.id.no_ads);
        app_bar_layout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                if (verticalOffset <= -head_layout.getHeight() / 2) {
                    mCollapsingToolbarLayout.setTitle(getString(R.string.str_subscribe));
                } else {
                    mCollapsingToolbarLayout.setTitle(" ");
                }
            }
        });


        // tipDlog.show();
        showDelayWait(new Runnable() {
            @Override
            public void run() {
                //Toast.makeText(BillingActivity.this, "Request Failed!!!!!", Toast.LENGTH_LONG).show();
            }
        }, 5000);

        main_vp_container = findViewById(R.id.main_vp_container);
        main_vp_container.setLayoutManager(new LinearLayoutManager(BillingActivity.this));
      //  main_vp_container.setBackgroundColor(Color.rgb(255, 255, 255));

        if (viewModel != null) {
            viewModel.mProductEvent.observe(this, new Observer<List<BillingPurchaseDetails>>() {
                @Override
                public void onChanged(List<BillingPurchaseDetails> list) {
                    if (list != null) {
                        List<BillingPurchaseDetails> purchases = new ArrayList<>();
                        purchases.addAll(list);
                        mPurchases.clear();
                        mPurchases.addAll(purchases);

                    }
                    DisMissWait();
                    mAdapter.notifyDataSetChanged();
                }
            });
        }

        List<BillingPurchaseDetails> tmp = (List<BillingPurchaseDetails>) viewModel.mProductEvent.getValue();
        if (tmp != null) {
            mPurchases = tmp;
        }

        /*
        测试数据
        BillingPurchaseDetails t = new BillingPurchaseDetails();
        t.setSku(STATIC_WEEK);
        t.setTitle(getString(R.string.str_gp_weekly));
        t.setMessage(getString(R.string.str_gp_weekly_premium));
        t.setPrice(getString(R.string.str_gp_error));
        t.setPurchase(false);
        mPurchases.add(t);
        t = new BillingPurchaseDetails();
        t.setSku(STATIC_MONTH);
        t.setTitle(getString(R.string.str_gp_month));
        t.setMessage(getString(R.string.str_gp_month_premium));
        t.setPrice(getString(R.string.str_gp_error));
        t.setPurchase(false);
        mPurchases.add(t);
        t = new BillingPurchaseDetails();
        t.setSku(STATIC_THREE_MONTH);
        t.setTitle(getString(R.string.str_gp_three_month));
        t.setMessage(getString(R.string.str_gp_three_month_premium));
        t.setPrice(getString(R.string.str_gp_error));
        t.setPurchase(false);
        mPurchases.add(t);
        */
        //数据库读取内容
        mAdapter = new UserAdapter(mPurchases);
        mAdapter.setOnitemClickLintener(position -> {

        });

        main_vp_container.setAdapter(mAdapter);
    }

    private void initializeBillingClient() {

        //create a list with subscription ids
        List<String> subscriptionIds = new ArrayList<>();
        subscriptionIds.add(BillingViewModel.STATIC_WEEK);
        subscriptionIds.add(BillingViewModel.STATIC_MONTH);
        subscriptionIds.add(BillingViewModel.STATIC_THREE_MONTH);

        billingConnector = new  BillingConnector(this,  BillingViewModel.public_key) //"license_key" - public developer key from Play Console
                //   .setConsumableIds(consumableIds) //to set consumable ids - call only for consumable products
                //  .setNonConsumableIds(nonConsumableIds) //to set non-consumable ids - call only for non-consumable products
                .setSubscriptionIds(subscriptionIds) //to set subscription ids - call only for subscription products
                .autoAcknowledge() //legacy option - better call this. Alternatively purchases can be acknowledge via public method "acknowledgePurchase(PurchaseInfo purchaseInfo)"
                .autoConsume() //legacy option - better call this. Alternatively purchases can be consumed via public method consumePurchase(PurchaseInfo purchaseInfo)"
                .enableLogging() //to enable logging for debugging throughout the library - this can be skipped
                .connect(); //to connect billing client with Play Console

        billingConnector.setBillingEventListener(new BillingEventListener() {
            @Override
            public void onProductsFetched(@NonNull List<SkuInfo> skuDetails) {
                try {
                    String sku;
                    SkuDetails detail;
                    for (SkuInfo skuInfo : skuDetails) {
                        sku = skuInfo.getSkuId();
                        detail = skuInfo.getSkuDetails();
                        Iterator<BillingPurchaseDetails> it1 = mPurchases.iterator();
                        while (it1.hasNext()) {
                            BillingPurchaseDetails bp = it1.next();
                            String pur_sku = bp.getSku();
                            if (bp.getPurchase()) {
                                continue;
                            } else {
                                if (sku.equals(pur_sku)) {
                                    bp.setPrice(detail.getPrice());
                                    String title = detail.getTitle();
                                    if (title.contains("(")) {
                                        int v = title.indexOf("(");
                                        title = title.substring(0, v);
                                    }
                                    bp.setTitle(title);
                                    bp.setMessage(detail.getDescription());
                                    bp.setOriginalJson(detail.getOriginalJson());
                                }
                            }
                        }
                    }
                    mAdapter.notifyDataSetChanged();
                    BillingActivity.this.getWindow().getDecorView().postDelayed(() -> tipDlog.dismiss(), 3000);
                } catch (JSONException ex) {
                    //tipDlog.dismiss();
                    DisMissWait();
                } finally {
                    //tipDlog.dismiss();
                    DisMissWait();
                }
            }

            @Override
            public void onPurchasedProductsFetched(@NonNull List<PurchaseInfo> purchases) {
                try {
                    String purchase;
                    SkuDetails detail;
                    for (PurchaseInfo purchaseInfo : purchases) {
                        purchase = purchaseInfo.getSkuId();
                        detail = purchaseInfo.getSkuInfo().getSkuDetails();
                        Iterator<BillingPurchaseDetails> it1 = mPurchases.iterator();
                        while (it1.hasNext()) {
                            BillingPurchaseDetails bp = it1.next();
                            String pur_sku = bp.getSku();
                            if (pur_sku.equals(purchase)) {
                                bSubscribed = true;
                                bp.setPrice(getString(R.string.str_subed));
                                bp.setPurchase(true);
                                String title = detail.getTitle();
                                if (title.contains("(")) {
                                    int v = title.indexOf("(");
                                    title = title.substring(0, v);
                                }
                                bp.setTitle(title);
                                bp.setMessage(detail.getDescription());
                                bp.setOriginalJson(detail.getOriginalJson());
                            }
                        }
                    }
                    mAdapter.notifyDataSetChanged();
                } catch (JSONException ex) {
                    //tipDlog.dismiss();
                    DisMissWait();
                } finally {
                    //tipDlog.dismiss();
                    DisMissWait();
                }
            }

            @Override
            public void onProductsPurchased(@NonNull List<PurchaseInfo> purchases) {
                try {
                    String purchase;
                    SkuDetails detail;
                    for (PurchaseInfo purchaseInfo : purchases) {
                        purchase = purchaseInfo.getSkuId();
                        detail = purchaseInfo.getSkuInfo().getSkuDetails();
                        Iterator<BillingPurchaseDetails> it1 = mPurchases.iterator();
                        while (it1.hasNext()) {
                            BillingPurchaseDetails bp = it1.next();
                            String pur_sku = bp.getSku();
                            if (pur_sku.equals(purchase)) {
                                bSubscribed = true;
                                bp.setPrice(getString(R.string.str_subed));
                                String title = detail.getTitle();
                                if (title.contains("(")) {
                                    int v = title.indexOf("(");
                                    title = title.substring(0, v);
                                }
                                bp.setTitle(title);
                                bp.setMessage(detail.getDescription());
                                bp.setOriginalJson(detail.getOriginalJson());
                            }
                        }
                    }
                    mAdapter.notifyDataSetChanged();
                } catch (JSONException ex) {
                    DisMissWait();
                } finally {
                    DisMissWait();
                }

              //  purchasedInfoList.addAll(purchases); //check "usefulPublicMethods" to see what's going on with this list
            }

            @Override
            public void onPurchaseAcknowledged(@NonNull PurchaseInfo purchase) {
                String acknowledgedSku = purchase.getSkuId();


            }

            @Override
            public void onPurchaseConsumed(@NonNull PurchaseInfo purchase) {
                String consumedSku = purchase.getSkuId();


            }

            @Override
            public void onBillingError(@NonNull BillingConnector billingConnector, @NonNull BillingResponse response) {
                switch (response.getErrorType()) {
                    case CLIENT_NOT_READY:
                        //TODO - client is not ready
                        break;
                    case CLIENT_DISCONNECTED:
                        //TODO - client has disconnected
                        break;
                    case ITEM_NOT_EXIST:
                        //TODO - item doesn't exist
                        break;
                    case ITEM_ALREADY_OWNED:
                        //TODO - item is already owned
                        break;
                    case ACKNOWLEDGE_ERROR:
                        //TODO - error during acknowledgment
                        break;
                    case CONSUME_ERROR:
                        //TODO - error during consumption
                        break;
                    case FETCH_PURCHASED_PRODUCTS_ERROR:
                        //TODO - error occurs while querying purchases
                        break;
                    case BILLING_ERROR:
                        //TODO - error occurs during initialization / querying sku details
                        break;
                }


            }
        });
    }

    @Override
    public void onProductsFetched(@NonNull List<SkuInfo> skuDetails) {

    }

    @Override
    public void onPurchasedProductsFetched(@NonNull List<PurchaseInfo> purchases) {

    }

    @Override
    public void onProductsPurchased(@NonNull List<PurchaseInfo> purchases) {

    }

    @Override
    public void onPurchaseAcknowledged(@NonNull PurchaseInfo purchase) {

    }

    @Override
    public void onPurchaseConsumed(@NonNull PurchaseInfo purchase) {

    }

    @Override
    public void onBillingError(@NonNull BillingConnector billingConnector, @NonNull BillingResponse response) {

    }

    public interface OnitemClick {
        void onItemClick(int position);
    }

    private class UserHolder extends RecyclerView.ViewHolder {
        private BillingPurchaseDetails mPremium;
        private TextView mTextView1;
        private TextView mTextView2;
        private TextView mTextView3;
        //构造方法
        UserHolder(LayoutInflater inflater, ViewGroup parent){
            super(inflater.inflate(R.layout.simple_item_1, parent, false));
            mTextView1= itemView.findViewById(R.id.time_id);
            mTextView2= itemView.findViewById(R.id.price_id);
            mTextView3= itemView.findViewById(R.id.time_id2);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //
                    if (mPremium != null && !mPremium.getPurchase() ) {//&& mPremium.getOriginalJson() != null
                        // SkuDetails skuDetails = new SkuDetails(mPremium.getOriginalJson());
//                            mBillingManager.initiatePurchaseFlow(
//                                    (Activity) v.getContext(),
//                                    skuDetails);
                        //  billingConnector.purchase(BillingActivity.this, skuDetails.getSku());
                        ProductDetails details = mPremium.getProductDetails();
                        viewModel.purchase(BillingActivity.this, details.getProductId());

                    }
                }
            });
        }

        public void bind(BillingPurchaseDetails user){
            mPremium = user;
            mTextView1.setText(mPremium.getTitle());
            mTextView2.setText(mPremium.getPrice());
            mTextView3.setText(mPremium.getMessage());
        }
    }

    //创建Adapter内部类
    private class UserAdapter extends RecyclerView.Adapter<UserHolder>{
        private List<BillingPurchaseDetails> mUsers;
        private OnitemClick onitemClick;

        void setOnitemClickLintener(OnitemClick onitemClick) {
            this.onitemClick = onitemClick;
        }

        UserAdapter(List<BillingPurchaseDetails> users){
            mUsers = users;
        }

        @NonNull
        @Override
        public UserHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            LayoutInflater layoutInflater = LayoutInflater.from(BillingActivity.this);

            return new UserHolder(layoutInflater,parent);
        }

        @Override
        public void onBindViewHolder(UserHolder holder, int position) {
            BillingPurchaseDetails user = mUsers.get(position);
            holder.bind(user);
        }

        @Override
        public int getItemCount() {
            return mUsers.size();
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        /*
        Intent intent = new Intent();
        // intent.putExtra("respond", "2000");
        if (viewModel.isbSubscribed()) {
            intent.putExtra("bill_respond", 1);
            setResult(Activity.RESULT_OK, intent);
        } else {
            intent.putExtra("bill_respond", 0);
            setResult(Activity.RESULT_CANCELED, intent);
        }

         */
    }
}
