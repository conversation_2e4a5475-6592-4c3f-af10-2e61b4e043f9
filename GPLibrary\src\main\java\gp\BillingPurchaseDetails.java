/*
 * Copyright 2020 LiteKite Startup. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package gp;

import com.android.billingclient.api.ProductDetails;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Database Entity, has Schema about Purchase Details.
 *
 * <AUTHOR>
 * @version 1.0, 04/03/2018
 * @since 1.0
 */

public class BillingPurchaseDetails {
	public String mOriginalJson;
	public JSONObject mParsedJson;
	public String mTitle;
	public String mPrice;
	public String mSku;
	public String mMessage;
	private boolean bpurchase;
	private ProductDetails productDetails;
	public BillingPurchaseDetails(String jsonSkuDetails) throws JSONException {
		this.mOriginalJson = jsonSkuDetails;
		this.mParsedJson = new JSONObject(this.mOriginalJson);
	}

	public BillingPurchaseDetails() {
		setPurchase(true);
	}

    public void setOriginalJson(String mOriginalJson) throws JSONException  {
        this.mOriginalJson = mOriginalJson;
        this.mParsedJson = new JSONObject(this.mOriginalJson);
    }

    public void setPurchase(boolean bpurchase) {
		this.bpurchase = bpurchase;
	}

	public boolean getPurchase() {
		return bpurchase;
	}

	public void setTitle(String mTitle) {
		this.mTitle = mTitle;
	}

	public String getTitle() {
		if (mTitle != null) {
			return mTitle;
		} else {
			return this.mParsedJson.optString("title");
		}
	}

	public ProductDetails getProductDetails() {
		return productDetails;
	}

	public void setProductDetails(ProductDetails productDetails) {
		this.productDetails = productDetails;
	}

	public void clearTitle() {
		this.mTitle = null;
	}

	public void setPrice(String mPrice) {
		this.mPrice = mPrice;
	}

	public String getOriginalJson() {
		return this.mOriginalJson;
	}

	public void clearPrice() {
		this.mPrice = null;
	}

	public String getPrice() {
		if (mPrice != null) {
			return mPrice;
		} else {
			if (this.mParsedJson != null)
				return this.mParsedJson.optString("price");
		}
		return null;
	}

	public String getSku() {
		if (mSku != null) {
			return mSku;
		} else {
			if (this.mParsedJson != null)
				return this.mParsedJson.optString("productId");
		}
		return null;
	}

	public void setSku(String mProduct) {
		this.mSku = mProduct;
	}

	public String getMessage() {
		return mMessage;
	}

	public void setMessage(String mMessage) {
		this.mMessage = mMessage;
	}
}



