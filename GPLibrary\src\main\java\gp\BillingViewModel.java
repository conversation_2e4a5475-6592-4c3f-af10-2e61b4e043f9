/*
 * Copyright 2020 LiteKite Startup. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package gp;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.OnLifecycleEvent;

import eddy.android.billing.BillingManager;
import eddy.android.billing.R;
import eddy.android.billing.util.NetworkManager;
import com.android.billingclient.api.BillingResult;
import com.android.billingclient.api.ProductDetails;
import com.android.billingclient.api.Purchase;

import eddy.android.google_iab.BillingConnector;
import eddy.android.google_iab.BillingEventListener;
import eddy.android.google_iab.models.BillingResponse;
import eddy.android.google_iab.models.PurchaseInfo;
import eddy.android.google_iab.models.SkuInfo;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;



/**
 * BillingVM, a view model which provides BillingManager which has BillingClient, a Google Play
 * Billing Library helps to make purchases, gets Sku Details, gets recent purchases list and the
 * list of history of purchases made by the user.
 *
 * <AUTHOR> S
 * @version 1.0, 10/03/2018
 * @since 1.0
 */
public class BillingViewModel<T, O extends Application> extends AndroidViewModel implements
        LifecycleObserver,
		BillingManager.BillingUpdatesListener {

	public static String STATIC_WEEK = "download_week";
	public static String STATIC_MONTH = "download_month";
	public static String STATIC_THREE_MONTH = "download_three";
	public static String STATIC_SIX_MONTH = "fast_vd_six_month";
	public static String STATIC_ONE_YEAR = "download_year";

	public BillingUpdatesListener mBillingListener;
	private static final String TAG = BillingViewModel.class.getName();
	private BillingManager billingManager;
	private BillingConnector billingConnector;
	private static boolean bSubscribed = false;
	private MutableLiveData<T> data;
	public MutableLiveData<String> mHaveSubscribeEvent = new MutableLiveData<>();
	public MutableLiveData<List<BillingPurchaseDetails>> mProductEvent = new MutableLiveData<>();
	public SingleLiveEvent<List<BillingPurchaseDetails>> mPurchasesEvent = new SingleLiveEvent<>();
	private O subApp;
	public static String public_key = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu8UQByVZiim6Pz8ZoA+QGdXhYhnv50+OQmMsjOJtSXM9j4jF+F8kE8J7e+y3Gzk7dHocHPOHS/UHIuLy7ESeSOAJIVsrPmWRkIl2IpmWFAY9U0DVIOFEo1Wnymy51E0Th4D0UkJRkseTlBEm2xa8YkZeeJciVyxirkCDqnqEVo1+2wnjNhZNpIOYDJ9d/5vn+DVzzCD1UWeToAVTSsvyUYAjymNrN0TfugvPAQUFwZI9F4MOZ5KOBSjwgS6UB2WPPFqMkmGSmw73RU7YhDnUz1ekezFZ2kUxsb6uaa0k+rlsuITaeu7s6T6LM/fLCCsDpkT6cDvLJYLEs7FaTOG+MwIDAQAB";
	/**
	 * A Broadcast Receiver which will be called if there was a Network Connectivity Change.
	 * Initiates BillingManager if there is a Network Connection.
	 */
	private BroadcastReceiver networkBrReceiver = new BroadcastReceiver() {
		@Override
		public void onReceive(Context context, Intent intent) {
			if (NetworkManager.isOnline(context)) {
				//BaseActivity.printLog(TAG, "Network Connected");
				initPlayBilling4();
			}
		}
	};

	/**
	 * Initializes BillingManager.
	 *
	 * @param application An Application Instance.
	 */
	public BillingViewModel(@NonNull Application application) {
		super(application);
		subApp = (O) application;
		//setData(data);
	}

	public static boolean isbSubscribed() {
		return bSubscribed;
	}

	public void setUpdateCallback(BillingUpdatesListener listener) {
		mBillingListener = listener;
	}

	public void purchase(Activity activity, String skuId) {
		billingConnector.purchase(activity, skuId);
	}

	/**
	 * Initializes BillingManager.
	 */
	private void initPlayBilling() {
		if (billingManager != null) {
			billingManager.destroy();
			billingManager = null;
		}
		billingManager = new BillingManager(this.getApplication(), this);
	}

	private void initPlayBilling4() {
		List<String> subscriptionIds = new ArrayList<>();
		subscriptionIds.add(STATIC_WEEK);
		subscriptionIds.add(STATIC_MONTH);
		subscriptionIds.add(STATIC_THREE_MONTH);
		subscriptionIds.add(STATIC_ONE_YEAR);

		billingConnector = new  BillingConnector(subApp,  public_key) //"license_key" - public developer key from Play Console
				.setSubscriptionIds(subscriptionIds) //to set subscription ids - call only for subscription products
				.autoAcknowledge() //legacy option - better call this. Alternatively purchases can be acknowledge via public method "acknowledgePurchase(PurchaseInfo purchaseInfo)"
				.autoConsume() //legacy option - better call this. Alternatively purchases can be consumed via public method consumePurchase(PurchaseInfo purchaseInfo)"
				.enableLogging() //to enable logging for debugging throughout the library - this can be skipped
				.connect(); //to connect billing client with Play Console

		billingConnector.setBillingEventListener(new BillingEventListener() {
			@Override
			public void onProductsFetched(@NonNull List<SkuInfo> skuDetails) {
				try {
					String sku;
					//SkuDetails detail;
					ProductDetails productDetails;
					List<BillingPurchaseDetails> purchase = new ArrayList<>();
					for (SkuInfo skuInfo : skuDetails) {
						sku = skuInfo.getSkuId();
						productDetails = skuInfo.getProductDetails();

						BillingPurchaseDetails t = new BillingPurchaseDetails();
						t.setSku(productDetails.getProductId());
						t.setPurchase(false);


						List<ProductDetails.SubscriptionOfferDetails>  listdeatail = productDetails.getSubscriptionOfferDetails();
						//t.setPrice(productDetails.getPrice());
						ProductDetails.PricingPhases price = listdeatail.get(0).getPricingPhases();
						List<ProductDetails.PricingPhase> listPricePhase = price.getPricingPhaseList();
						String str_price = listPricePhase.get(0).getFormattedPrice();
						t.setPrice(str_price);
						String title = productDetails.getTitle();
						if (title.contains("(")) {
							int v = title.indexOf("(");
							title = title.substring(0, v);
						}
						t.setTitle(title);
						t.setMessage(productDetails.getDescription());
						t.setProductDetails(productDetails);
						//t.setOriginalJson(productDetails.zza());
					//	t.setOriginalJson("");
						purchase.add(t);
					}
					mProductEvent.setValue(purchase);
				} finally {
					//tipDlog.dismiss();
				}
			}

			@Override
			public void onPurchasedProductsFetched(@NonNull List<PurchaseInfo> purchases) {
				try {
					String purchase;
					//SkuDetails detail;
					ProductDetails productDetails;
					for (PurchaseInfo purchaseInfo : purchases) {
						purchase = purchaseInfo.getSkuId();
						productDetails = purchaseInfo.getSkuInfo().getProductDetails();
						Iterator<BillingPurchaseDetails> it1 = mProductEvent.getValue().iterator();
						while (it1.hasNext()) {
							BillingPurchaseDetails bp = it1.next();
							String pur_sku = bp.getSku();
							if (pur_sku.equals(purchase)) {
								bSubscribed = true;
								bp.setPrice(subApp.getString(R.string.str_subed));
								bp.setPurchase(true);
								String title = productDetails.getTitle();
								if (title.contains("(")) {
									int v = title.indexOf("(");
									title = title.substring(0, v);
								}
								bp.setTitle(title);
								bp.setMessage(productDetails.getDescription());
							//	bp.setOriginalJson(detail.getOriginalJson());
							}
						}
					}
					mProductEvent.setValue(mProductEvent.getValue());
					if (bSubscribed) {
					//	mHaveSubscribeEvent.call();
						mHaveSubscribeEvent.setValue(null);
					}
				} finally {
					//tipDlog.dismiss();
				}
			}

			@Override
			public void onProductsPurchased(@NonNull List<PurchaseInfo> purchases) {

				try {
					String purchase;
					//SkuDetails detail;
					ProductDetails productDetails;
					for (PurchaseInfo purchaseInfo : purchases) {
						purchase = purchaseInfo.getSkuId();
						productDetails = purchaseInfo.getSkuInfo().getProductDetails();
						Iterator<BillingPurchaseDetails> it1 = mProductEvent.getValue().iterator();
						while (it1.hasNext()) {
							BillingPurchaseDetails bp = it1.next();
							String pur_sku = bp.getSku();
							if (pur_sku.equals(purchase)) {
								bSubscribed = true;
								bp.setPrice(subApp.getString(R.string.str_subed));
								String title = productDetails.getTitle();
								if (title.contains("(")) {
									int v = title.indexOf("(");
									title = title.substring(0, v);
								}
								bp.setTitle(title);
								bp.setMessage(productDetails.getDescription());
								//bp.setOriginalJson(productDetails.getOriginalJson());
							}
						}
					}
					mProductEvent.setValue(mProductEvent.getValue());
					if (bSubscribed) {
					//	mHaveSubscribeEvent.call();
						mHaveSubscribeEvent.setValue(null);
					}
				} finally {
					//tipDlog.dismiss();
				}

			}

			@Override
			public void onPurchaseAcknowledged(@NonNull PurchaseInfo purchase) {
//				String acknowledgedSku = purchase.getSkuId();

//				if (acknowledgedSku.equalsIgnoreCase("non_consumable_id2")) {
//					//TODO - do something
//					Log.d("BillingConnector", "Acknowledge: " + acknowledgedSku);
//					//     Toast.makeText(SampleActivity.this, "Acknowledge: " + acknowledgedSku, Toast.LENGTH_SHORT).show();
//				}

				//TODO - similarly check for other ids
			}

			@Override
			public void onPurchaseConsumed(@NonNull PurchaseInfo purchase) {
				//TODO - similarly check for other ids
			}

			@Override
			public void onBillingError(@NonNull BillingConnector billingConnector, @NonNull BillingResponse response) {
				switch (response.getErrorType()) {
					case CLIENT_NOT_READY:
						//TODO - client is not ready
						break;
					case CLIENT_DISCONNECTED:
						//TODO - client has disconnected
						break;
					case ITEM_NOT_EXIST:
						//TODO - item doesn't exist
						break;
					case ITEM_ALREADY_OWNED:
						//TODO - item is already owned
						break;
					case ACKNOWLEDGE_ERROR:
						//TODO - error during acknowledgment
						break;
					case CONSUME_ERROR:
						//TODO - error during consumption
						break;
					case FETCH_PURCHASED_PRODUCTS_ERROR:
						//TODO - error occurs while querying purchases
						break;
					case BILLING_ERROR:
						//TODO - error occurs during initialization / querying sku details
						break;
				}
			}
		});
	}
	/**
	 * Gives BillingManager Instance.
	 *
	 * @return a BillingManager Instance.
	 */
	@NonNull
	public BillingManager getBillingManager() {
		return billingManager;
	}

	@SuppressWarnings("unused")
	@OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
	void onCreate() {
		NetworkManager.registerNetworkBrReceiver(this.getApplication(), networkBrReceiver);
	}

	@OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
	void onResume() {
		if (billingManager !=null)
			billingManager.queryPurchases();
	}

	@SuppressWarnings("unused")
	@OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
	void onDestroy() {
		if (billingManager !=null)
			billingManager.destroy();
		NetworkManager.unregisterNetworkBrReceiver(this.getApplication(), networkBrReceiver);
	}



	@Override
	public void onBillingClientSetupFinished() {
		if (mBillingListener != null) {
			mBillingListener.onBillingVMClientSetupFinished();
		}
	}

	@Override
	public void onConsumeFinished(BillingResult billingResult, String purchaseToken) {

	}

	@Override
	public void onPurchasesUpdated(List<Purchase> purchases) {
		if (purchases.size() > 0) {
		//	Settings.SetSubscriptioned(true);
		} else {
		//	Settings.SetSubscriptioned(false);
		}
	}

	@Override
	public void onBillingError(@NonNull String error) {
		//BaseActivity.showToast(this.getApplication(), error);
	}

	public interface BillingUpdatesListener {
		void onBillingVMClientSetupFinished();
	}
}