<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="3dp"
                android:left="3dp"
                android:right="3dp"
                android:top="3dp" />
            <solid android:color="#0DCCCCCC" />
            <corners android:radius="60dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="3dp"
                android:left="3dp"
                android:right="3dp"
                android:top="3dp" />
            <solid android:color="#10CCCCCC" />
            <corners android:radius="60dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="3dp"
                android:left="3dp"
                android:right="3dp"
                android:top="3dp" />
            <solid android:color="#15CCCCCC" />
            <corners android:radius="60dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="3dp"
                android:left="3dp"
                android:right="3dp"
                android:top="3dp" />
            <solid android:color="#20CCCCCC" />
            <corners android:radius="60dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="3dp"
                android:left="3dp"
                android:right="3dp"
                android:top="3dp" />
            <solid android:color="#30CCCCCC" />
            <corners android:radius="60dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="#FFEC8B" />
            <corners android:radius="60dp" />
        </shape>
    </item>

    <item>
        <selector>
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <padding
                        android:bottom="3dp"
                        android:left="3dp"
                        android:right="3dp"
                        android:top="3dp" />
                    <solid android:color="#ff00ffff" />
                    <corners android:radius="60dp" />
                </shape>
            </item>
        </selector>
    </item>
</layer-list>