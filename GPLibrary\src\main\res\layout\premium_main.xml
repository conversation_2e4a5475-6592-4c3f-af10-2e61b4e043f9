<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="gp.BillingActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:background="#191919"
        android:theme="@style/toolbar_yellow_theme"
        >

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#191919"
            app:collapsedTitleTextAppearance="@style/ToolBarTitleText"
            app:statusBarScrim="@android:color/transparent"
            app:contentScrim="#191919"
            app:expandedTitleMarginEnd="48dp"
            app:expandedTitleMarginStart="48dp"
            app:expandedTitleTextAppearance="@style/transparentText"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            >


            <LinearLayout
                android:id="@+id/head_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#191919"
                android:orientation="vertical"
                app:layout_collapseMode="pin"
                app:layout_collapseParallaxMultiplier="0.7">


                <RelativeLayout
                    android:id="@+id/toolbar_parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="50dp"
                    android:padding="20dp">

                    <ImageView
                        android:id="@+id/head_premium"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerHorizontal="true"
                        android:scaleType="centerCrop"
                        android:src="@mipmap/iconfont_premium"
                        app:tint="#FFEC8B"/>

                    <LinearLayout
                        android:id="@+id/no_ads"
                        android:layout_below="@+id/head_premium"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/remove_ad"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/str_remove_ad"
                            android:textColor="#CFCFCF"
                            android:gravity="center"
                            android:layout_weight="1"
                            android:textSize="24sp"
                            android:textStyle="bold"/>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/str_more_function"
                            android:gravity="center"
                            android:layout_weight="1"
                            android:textColor="#CFCFCF"
                            android:textSize="24sp"
                            android:textStyle="bold"/>
                    </LinearLayout>
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/no_ads"
                        android:layout_marginTop="8dp"
                        android:text="@string/str_google_store_cancel_sub"
                        android:gravity="center"
                        android:textColor="#CFCFCF"
                        android:textSize="18sp"
                        android:textStyle="normal"/>
                </RelativeLayout>
            </LinearLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                android:background="#191919">

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>


        <androidx.core.widget.NestedScrollView
            android:id="@+id/nsv"

            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:ignore="MissingConstraints">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#191919"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                >
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/main_vp_container"
                    android:background="#191919"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    />

                <TextView
                    android:id="@+id/tip_error"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:text="@string/str_gp_premium_tip"
                    android:textAlignment="textStart"
                    android:textColor="#CFCFCF"
                    android:visibility="visible"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/main_vp_container"
                    tools:ignore="RtlCompat" />
                </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>



</androidx.coordinatorlayout.widget.CoordinatorLayout>