<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/gradual"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="10dp"
    android:layout_marginEnd="10dp"
    android:clickable="true"
    tools:ignore="MissingDefaultResource">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1 Month"
        android:textColor="@android:color/black"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="4dp"
        android:paddingEnd="4dp"
        android:textSize="16sp"
        android:textStyle="bold|italic"
        android:id="@+id/time_id"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Content"
        android:textColor="@android:color/black"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="10dp"
        android:textSize="12sp"
        android:textStyle="normal"
        android:layout_below="@+id/time_id"
        android:id="@+id/time_id2"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:text="US$100.0/Month"
        android:textColor="@android:color/black"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_centerInParent="true"
        android:id="@+id/price_id"/>



</RelativeLayout>