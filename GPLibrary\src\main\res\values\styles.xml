<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppLightTheme" parent="@style/Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="colorPrimary">#ffffff</item>
        <item name="colorPrimaryDark">#ffffff</item>
        <item name="colorAccent">#ffff00</item>
        <item name="colorControlNormal">#ffffff</item>
        <item name="colorControlActivated">#ffffff</item>
        <item name="colorControlHighlight">#ffffff</item>
        <item name="colorButtonNormal">#ffffff</item>
    </style>

    <style name="transparentText" parent="TextAppearance.AppCompat.Small">
        <item name="android:textColor" >#00000000</item>
    </style>

    <style name="ToolBarTitleText" parent="TextAppearance.AppCompat.Medium">
        <item name="android:textColor">#FFEC8B</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>



    <style name="toolbar_yellow_theme" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">#FFEC8B</item>
        <item name="android:actionBarSize">46dp</item>
    </style>


    <style name="AppDarkTheme" parent="@style/Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="colorPrimary">#191919</item>
        <item name="colorPrimaryDark">#191919</item>
        <item name="colorAccent">#ffff00</item>
        <item name="colorControlNormal">#191919</item>
        <item name="colorControlActivated">#191919</item>
        <item name="colorControlHighlight">#191919</item>
        <item name="colorButtonNormal">#191919</item>
    </style>
</resources>