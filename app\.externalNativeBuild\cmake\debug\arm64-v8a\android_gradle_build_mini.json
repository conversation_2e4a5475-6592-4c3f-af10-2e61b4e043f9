{"buildFiles": ["/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt"], "cleanCommands": ["/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/arm64-v8a --target clean"], "libraries": {"JniNdk-Debug-arm64-v8a": {"artifactName": "JniNdk", "buildCommand": "/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/arm64-v8a --target JniNdk", "abi": "arm64-v8a", "output": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/arm64-v8a/libJniNdk.so"}}}