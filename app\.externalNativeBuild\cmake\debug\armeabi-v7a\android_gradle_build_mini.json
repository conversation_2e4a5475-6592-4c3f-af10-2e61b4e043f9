{"buildFiles": ["/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt"], "cleanCommands": ["/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/armeabi-v7a --target clean"], "libraries": {"JniNdk-Debug-armeabi-v7a": {"artifactName": "JniNdk", "buildCommand": "/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/armeabi-v7a --target JniNdk", "abi": "armeabi-v7a", "output": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/armeabi-v7a/libJniNdk.so"}}}