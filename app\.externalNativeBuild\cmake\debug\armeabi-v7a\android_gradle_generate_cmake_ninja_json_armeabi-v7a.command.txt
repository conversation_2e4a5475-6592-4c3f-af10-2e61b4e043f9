Executable : /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake
arguments : 
-H/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni
-B/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/armeabi-v7a
-DANDROID_ABI=armeabi-v7a
-DANDROID_PLATFORM=android-19
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/build/intermediates/cmake/debug/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-DANDROID_NDK=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/android.toolchain.cmake
-DCMA<PERSON>_MAKE_PROGRAM=/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/ninja
-GAndroid Gradle - Ninja
jvmArgs : 

