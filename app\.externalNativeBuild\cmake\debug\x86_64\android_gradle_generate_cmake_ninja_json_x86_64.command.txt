Executable : /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake
arguments : 
-H/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni
-B/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/x86_64
-DANDROID_ABI=x86_64
-DANDROID_PLATFORM=android-19
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/build/intermediates/cmake/debug/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-DANDROID_NDK=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/ninja
-GAndroid Gradle - Ninja
jvmArgs : 

