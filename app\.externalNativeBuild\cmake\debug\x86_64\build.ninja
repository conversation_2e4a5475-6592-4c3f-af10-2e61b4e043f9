# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.6

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: JniNdk
# Configuration: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/x86_64 && /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1
build edit_cache: phony CMakeFiles/edit_cache.util

#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/x86_64 && /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake -H/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni -B/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/debug/x86_64
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1
build rebuild_cache: phony CMakeFiles/rebuild_cache.util
# =============================================================================
# Object build statements for SHARED_LIBRARY target JniNdk

build CMakeFiles/JniNdk.dir/lion_ndk_tools.cpp.o: CXX_COMPILER__JniNdk /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/lion_ndk_tools.cpp
  DEFINES = -DJniNdk_EXPORTS
  DEP_FILE = CMakeFiles/JniNdk.dir/lion_ndk_tools.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security   -O0 -fno-limit-debug-info  -fPIC   -std=gnu++11
  IN_ABS = /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/lion_ndk_tools.cpp
  OBJECT_DIR = CMakeFiles/JniNdk.dir
  OBJECT_FILE_DIR = CMakeFiles/JniNdk.dir

# =============================================================================
# Link build statements for SHARED_LIBRARY target JniNdk


#############################################
# Link the shared library /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so

build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so: CXX_SHARED_LIBRARY_LINKER__JniNdk CMakeFiles/JniNdk.dir/lion_ndk_tools.cpp.o
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security   -O0 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--exclude-libs,libgcc.a -Wl,--exclude-libs,libatomic.a -static-libstdc++ -Wl,--build-id -Wl,--warn-shared-textrel -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -Wl,-z,noexecstack
  LINK_LIBRARIES = -latomic -lm
  OBJECT_DIR = CMakeFiles/JniNdk.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libJniNdk.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_FILE = /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so
  TARGET_PDB = JniNdk.so.dbg
# =============================================================================
# Target aliases.

build JniNdk: phony /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so
build libJniNdk.so: phony /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so
# =============================================================================
# Folder targets.

# =============================================================================
# =============================================================================
# Built-in targets


#############################################
# The main all target.

build all: phony /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so

#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeCInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeCXXInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeGenericSystem.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/Clang-C.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/Clang.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/GNU.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Platform/Android.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Platform/Linux.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Platform/UnixPaths.cmake /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt /Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/android.toolchain.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.6.0-rc2/CMakeCCompiler.cmake CMakeFiles/3.6.0-rc2/CMakeCXXCompiler.cmake CMakeFiles/3.6.0-rc2/CMakeSystem.cmake
  pool = console

#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeCInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeCXXInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeGenericSystem.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/Clang-C.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/Clang.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Compiler/GNU.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Platform/Android.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Platform/Linux.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/share/cmake-3.6/Modules/Platform/UnixPaths.cmake /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt /Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/android.toolchain.cmake /Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.6.0-rc2/CMakeCCompiler.cmake CMakeFiles/3.6.0-rc2/CMakeCXXCompiler.cmake CMakeFiles/3.6.0-rc2/CMakeSystem.cmake: phony

#############################################
# Clean all the built files.

build clean: CLEAN

#############################################
# Print all primary targets available.

build help: HELP
