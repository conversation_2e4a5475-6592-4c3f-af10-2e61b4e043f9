Executable : /Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake
arguments : 
-H/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni
-B/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/arm64-v8a
-DANDROID_ABI=arm64-v8a
-DANDROID_PLATFORM=android-19
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/build/intermediates/cmake/release/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Release
-DANDROID_NDK=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/ninja
-GAndroid Gradle - Ninja
jvmArgs : 

