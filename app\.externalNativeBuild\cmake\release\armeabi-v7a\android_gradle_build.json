{"buildFiles": ["/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt"], "cleanCommands": ["/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/armeabi-v7a --target clean"], "cppFileExtensions": ["cpp"], "libraries": {"JniNdk-Release-armeabi-v7a": {"abi": "armeabi-v7a", "artifactName": "JniNdk", "buildCommand": "/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/armeabi-v7a --target JniNdk", "buildType": "release", "files": [{"flags": "  --target=armv7-none-linux-androideabi19 --gcc-toolchain=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/sysroot  -DJniNdk_EXPORTS  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -fno-addrsig -march=armv7-a -mthumb -Wa,--noexecstack -Wformat -Werror=format-security   -Oz -DNDEBUG  -fPIC   -std=gnu++11  -c ", "src": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/lion_ndk_tools.cpp", "workingDirectory": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/armeabi-v7a"}], "output": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/armeabi-v7a/libJniNdk.so", "toolchain": "10043455936188339771"}}, "toolchains": {"10043455936188339771": {"cCompilerExecutable": "/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang", "cppCompilerExecutable": "/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++"}}}