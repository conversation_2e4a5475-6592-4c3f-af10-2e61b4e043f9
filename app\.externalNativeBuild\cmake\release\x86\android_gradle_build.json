{"buildFiles": ["/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt"], "cleanCommands": ["/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/x86 --target clean"], "cppFileExtensions": ["cpp"], "libraries": {"JniNdk-Release-x86": {"abi": "x86", "artifactName": "JniNdk", "buildCommand": "/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/x86 --target JniNdk", "buildType": "release", "files": [{"flags": "  --target=i686-none-linux-android19 --gcc-toolchain=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64 --sysroot=/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/sysroot  -DJniNdk_EXPORTS  -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -fno-addrsig -Wa,--noexecstack -Wformat -Werror=format-security   -O2 -DNDEBUG  -fPIC   -std=gnu++11  -c ", "src": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/lion_ndk_tools.cpp", "workingDirectory": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/x86"}], "output": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86/libJniNdk.so", "toolchain": "10043455936188339771"}}, "toolchains": {"10043455936188339771": {"cCompilerExecutable": "/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang", "cppCompilerExecutable": "/Users/<USER>/Works/Developer/Tools/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++"}}}