{"buildFiles": ["/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jni/CMakeLists.txt"], "cleanCommands": ["/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/x86_64 --target clean"], "libraries": {"JniNdk-Release-x86_64": {"artifactName": "JniNdk", "buildCommand": "/Users/<USER>/Works/Developer/Tools/Android/sdk/cmake/3.6.4111459/bin/cmake --build /Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/.externalNativeBuild/cmake/release/x86_64 --target JniNdk", "abi": "x86_64", "output": "/Users/<USER>/Works/Developer/Workspace/Android/FD_CN/app/src/main/jniLibs/x86_64/libJniNdk.so"}}}