apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
android {
    namespace "amazon.browser.video.downloader"
    compileSdk = 36
    defaultConfig {
        applicationId "amazon.browser.video.downloader"
        targetSdkVersion 36
        minSdkVersion 23

        versionCode 22 //21,1.88
        versionName "1.0.22"

        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true


        ndk {
            // 设置支持的SO库架构
            abiFilters 'arm64-v8a', 'armeabi-v7a' //, 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            //jniLibs.srcDir "src/main/libs"
        }
    }
    signingConfigs {
//        debug {
//            storeFile file('../lionKey.jks')
//            storePassword 'lionkeyok'
//            keyAlias 'lionkey'
//            keyPassword 'lionkeyok'
//        }

        release {
            storeFile file('../eddykey.jks')
            storePassword 'eddykeyok'
            keyAlias 'eddykey'
            keyPassword 'eddykeyok'
        }
    }

    buildTypes {
        release {
            zipAlignEnabled true
            minifyEnabled true
            shrinkResources true
            debuggable false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            ndk {
                debugSymbolLevel = 'FULL'//或者 'SYMBOL_TABLE'
            }
        }

//        debug {
//            signingConfig signingConfigs.release
//         //   minifyEnabled true
//            debuggable true
//          //  zipAlignEnabled true
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }
    lint {
        abortOnError false
        checkReleaseBuilds false
        disable 'MissingTranslation'
    }
}

repositories {
    mavenCentral()
    jcenter()
    maven { url 'https://cboost.jfrog.io/artifactory/chartboost-ads/' }
}

configurations.all {
    resolutionStrategy.eachDependency {  details ->
        def requested = details.requested
        if (requested.group == 'com.android.support') {
            if (!requested.name.startsWith("multidex")) {
                details.useVersion '25.3.1'
            }
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation 'junit:junit:4.13.2'
    implementation project(':GPLibrary')
    //api project(':ffmpeg')
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.multidex:multidex:2.0.1'


    implementation files('libs/nineoldandroids-2.4.0.jar')

    implementation 'com.google.android.gms:play-services-ads:24.5.0'
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'com.google.android.ads:mediation-test-suite:3.0.0'

    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'com.google.android.gms:play-services-basement:18.7.1'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'

    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    implementation 'org.jsoup:jsoup:1.21.1'
  //  implementation 'com.coder.command:ffmpeg:1.0.9'
    def lifecycle_version = "2.2.0"
    implementation "androidx.lifecycle:lifecycle-extensions:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.9.2"
   // annotationProcessor "androidx.lifecycle:lifecycle-compiler:$lifecycle_version"
    annotationProcessor "androidx.lifecycle:lifecycle-common-java8:2.9.2"
   // implementation 'com.android.billingclient:billing:3.0.0'

    implementation "androidx.documentfile:documentfile:1.1.0"
    implementation 'com.google.ads.mediation:adcolony:4.8.0.2'
    implementation 'com.android.support:multidex:1.0.3'

    //Chartboost
    implementation 'com.google.ads.mediation:chartboost:9.9.0.0'


    implementation 'com.github.AnJoiner:FFmpegCommand:1.3.2'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
    def dialogx_version = "0.0.49.beta18"
    api "com.github.kongzue.DialogX:DialogX:${dialogx_version}"

    implementation 'androidx.core:core-ktx:1.16.0'
    implementation platform('com.google.firebase:firebase-bom:34.0.0')
    implementation 'com.google.firebase:firebase-analytics'

    implementation 'com.tencent.bugly:crashreport:latest.release'
}
