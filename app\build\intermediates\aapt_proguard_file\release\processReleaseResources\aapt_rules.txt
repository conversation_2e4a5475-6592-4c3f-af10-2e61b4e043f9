-keep class amazon.browser.lionpro.primary.AcyEvaluate { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyIntent { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyMain { <init>(); }
-keep class amazon.browser.lionpro.primary.AcySetting { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyStorage { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyWifiShare { <init>(); }
-keep class amazon.browser.lionpro.primary.LookPicture { <init>(); }
-keep class amazon.browser.lionpro.primary.LookVideo { <init>(); }
-keep class amazon.browser.lionpro.primary.M3u8MergeServer { <init>(); }
-keep class amazon.browser.lionpro.primary.Mainly { <init>(); }
-keep class amazon.browser.lionpro.primary.MoreApps { <init>(); }
-keep class amazon.browser.lionpro.primary.SerMain { <init>(); }
-keep class amazon.browser.lionpro.primary.SerWifiShare { <init>(); }
-keep class amazon.browser.lionpro.primary.UpdateActivity { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.content.FileProvider { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.adcolony.sdk.AdColonyAdViewActivity { <init>(); }
-keep class com.adcolony.sdk.AdColonyInterstitialActivity { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivity { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivityV2 { <init>(); }
-keep class com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity { <init>(); }
-keep class com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService { <init>(); }
-keep class com.chartboost.sdk.view.CBImpressionActivity { <init>(); }
-keep class com.chartboost.sdk.view.FullscreenAdActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.ConfigurationItemDetailActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.ConfigurationItemsSearchActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.HomeActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.NetworkDetailActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.ads.AdActivity { <init>(); }
-keep class com.google.android.gms.ads.AdService { <init>(); }
-keep class com.google.android.gms.ads.MobileAdsInitProvider { <init>(); }
-keep class com.google.android.gms.ads.NotificationHandlerActivity { <init>(); }
-keep class com.google.android.gms.ads.OutOfContextTestingActivity { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.squareup.picasso.PicassoProvider { <init>(); }
-keep class gp.BillingActivity { <init>(); }
-keep class lion.PlayActivity { <init>(); }
-keep class amazon.browser.lionpro.toys.CommonBackButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class amazon.browser.lionpro.views.HorizontalProgressBarWithNumber { <init>(android.content.Context, android.util.AttributeSet); }

-keep class amazon.browser.lionpro.views.KProgressHUD.BackgroundLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class amazon.browser.lionpro.views.ProgressBar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AppCompatTextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.cardview.widget.CardView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.helper.widget.Flow { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.ConstraintLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.fragment.app.FragmentContainerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.AspectRatioFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.SubtitleView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.TrackSelectionView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.viewpager.widget.ViewPager { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.viewpager2.widget.ViewPager2 { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.flexbox.FlexboxLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.gms.ads.nativead.MediaView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.gms.ads.nativead.NativeAdView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.AppBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.CollapsingToolbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButtonToggleGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.ClippableRoundedCornerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.TouchObserverFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.tabs.TabLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ChipTextInputComboView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockFaceView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockHandView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.TimePickerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.ActivityScreenShotImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.BottomDialogScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.DialogXBaseRelativeLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.MaxLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.MaxRelativeLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.PopMenuListView { <init>(android.content.Context, android.util.AttributeSet); }

-keepclassmembers class * { *** showP(android.view.View); }

