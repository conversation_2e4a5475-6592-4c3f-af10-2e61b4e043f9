1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="amazon.browser.video.downloader"
4    android:versionCode="22"
5    android:versionName="1.0.22" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="36" />
10    <!-- package="amazon.browser.lionpro" -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:6:5-67
11-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:7:5-80
12-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:7:22-77
13    <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> &lt;!&ndash; <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/> &ndash;&gt; -->
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:9:5-101
14-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:9:22-78
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:10:5-79
15-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:10:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:11:5-76
16-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:11:22-73
17    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 定位 adfaico -->
17-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:12:5-68
17-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:12:22-65
18    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> -->
19    <!--
20  <uses-permission android:name="android.permission.WRITE_CALENDAR"/>
21      <uses-permission android:name="android.permission.READ_CALENDAR"/>
22    -->
23    <!-- 查看设备信息 -->
24    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 可选，在咨询清单中制定versionName属性，藉此在版本名称下方报告资料 -->
24-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:19:5-75
24-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:19:22-72
25    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> &lt;!&ndash; <uses-permission android:name="android.permission.READ_LOGS" />&lt;!&ndash; 获取日志&ndash;&gt; &ndash;&gt; -->
26    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
26-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:21:5-78
26-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:21:22-75
27    <uses-permission android:name="com.android.vending.BILLING" />
27-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:22:5-67
27-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:22:22-64
28    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> -->
29    <!-- <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> -->
30    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
30-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:25:5-117
30-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:25:22-114
31    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
31-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:26:5-78
31-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:26:22-76
32
33    <queries>
33-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:17:5-23:15
34        <intent>
34-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:18:9-22:18
35            <action android:name="android.intent.action.ACTION_VIEW" />
35-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:19:13-72
35-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:19:21-69
36
37            <data android:scheme="https" />
37-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
37-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:23-44
38        </intent>
39        <intent>
39-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:13:9-15:18
40            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
40-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:14:13-91
40-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:14:21-88
41        </intent>
42        <intent>
42-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:16:9-18:18
43            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
43-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:17:13-116
43-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:17:21-113
44        </intent> <!-- For browser content -->
45        <intent>
45-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:38:9-44:18
46            <action android:name="android.intent.action.VIEW" />
46-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:17-69
46-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:25-66
47
48            <category android:name="android.intent.category.BROWSABLE" />
48-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:124:17-78
48-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:124:27-75
49
50            <data android:scheme="https" />
50-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
50-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:23-44
51        </intent> <!-- End of browser content -->
52        <!-- For CustomTabsService -->
53        <intent>
53-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:47:9-49:18
54            <action android:name="android.support.customtabs.action.CustomTabsService" />
54-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:48:13-90
54-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:48:21-87
55        </intent> <!-- End of CustomTabsService -->
56        <!-- For MRAID capabilities -->
57        <intent>
57-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:52:9-56:18
58            <action android:name="android.intent.action.INSERT" />
58-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:53:13-67
58-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:53:21-64
59
60            <data android:mimeType="vnd.android.cursor.dir/event" />
60-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
61        </intent>
62        <intent>
62-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:57:9-61:18
63            <action android:name="android.intent.action.VIEW" />
63-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:17-69
63-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:25-66
64
65            <data android:scheme="sms" />
65-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
65-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:23-44
66        </intent>
67        <intent>
67-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:62:9-66:18
68            <action android:name="android.intent.action.DIAL" />
68-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:63:13-65
68-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:63:21-62
69
70            <data android:path="tel:" />
70-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
71        </intent>
72
73        <package android:name="com.android.vending" />
73-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:16:9-55
73-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:16:18-52
74        <package android:name="com.amazon.venezia" />
74-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:17:9-54
74-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:17:18-51
75        <package android:name="com.sec.android.app.samsungapps" />
75-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:18:9-67
75-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:18:18-64
76        <package android:name="com.huawei.appmarket" />
76-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:19:9-56
76-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:19:18-53
77    </queries>
78
79    <uses-permission android:name="amazon.browser.video.downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
79-->[com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:28:5-97
79-->[com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:28:22-94
80    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
80-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:26:5-110
80-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:26:22-107
81    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
81-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:26:5-88
81-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:26:22-85
82    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
82-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:27:5-82
82-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:27:22-79
83    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
83-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:29:5-83
83-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:29:22-80
84    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
84-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
84-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
85
86    <permission
86-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
87        android:name="amazon.browser.video.downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
87-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
88        android:protectionLevel="signature" /> <!-- android:requestLegacyExternalStorage="true" -->
88-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
89    <!-- android:debuggable="true" -->
90    <!-- tools:ignore="HardcodedDebugMode" -->
91    <application
91-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:31:5-242:19
92        android:name="amazon.browser.lionpro.primary.Mainly"
92-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:32:9-61
93        android:allowBackup="true"
93-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:33:9-35
94        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
94-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
95        android:extractNativeLibs="false"
96        android:hardwareAccelerated="true"
96-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:34:9-43
97        android:icon="@mipmap/icon_app"
97-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:35:9-40
98        android:label="@string/app_name"
98-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:36:9-41
99        android:networkSecurityConfig="@xml/network_security_config"
99-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:41:9-69
100        android:supportsRtl="true"
100-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:37:9-35
101        android:theme="@style/AppTheme" >
101-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:39:9-40
102
103        <!-- <property -->
104        <!-- android:name="android.adservices.AD_SERVICES_CONFIG" -->
105        <!-- android:value="@xml/gma_ad_services_config" -->
106        <!-- tools:replace="android:resource" /> -->
107        <activity
107-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:47:9-60:20
108            android:name="amazon.browser.lionpro.primary.AcyMain"
108-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:48:13-66
109            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|uiMode|screenLayout|smallestScreenSize"
109-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:49:13-122
110            android:exported="true"
110-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:53:13-36
111            android:screenOrientation="user"
111-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:50:13-45
112            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
112-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:51:13-69
113            android:windowSoftInputMode="adjustPan" >
113-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:52:13-52
114            <intent-filter>
114-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:55:13-59:29
115                <action android:name="android.intent.action.MAIN" />
115-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:56:17-69
115-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:56:25-66
116
117                <category android:name="android.intent.category.LAUNCHER" />
117-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:58:17-77
117-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:58:27-74
118            </intent-filter>
119        </activity>
120
121        <service android:name="amazon.browser.lionpro.primary.SerMain" />
121-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:62:9-74
121-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:62:18-71
122        <service android:name="amazon.browser.lionpro.primary.M3u8MergeServer" />
122-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:63:9-82
122-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:63:18-79
123
124        <meta-data
124-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:64:9-66:70
125            android:name="com.google.android.gms.ads.APPLICATION_ID"
125-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:65:13-69
126            android:value="ca-app-pub-8603317425868964~6066157158" />
126-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:66:13-67
127        <!-- <meta-data -->
128        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
129        <!-- android:value="ca-app-pub-3940256099942544~3347511713" /> -->
130
131
132        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
133        <!-- android:value="ca-app-pub-3940256099942544~3347511713"/> -->
134        <!-- <meta-data -->
135        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
136        <!-- android:value="ca-app-pub-3064461767247622~9204542388"/> -->
137        <activity
137-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:77:9-83:45
138            android:name="amazon.browser.lionpro.primary.AcyStorage"
138-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:78:13-69
139            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
139-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:79:13-83
140            android:screenOrientation="user"
140-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:80:13-45
141            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
141-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:81:13-69
142            android:windowSoftInputMode="adjustPan" />
142-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:82:13-52
143        <activity
143-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:84:9-89:55
144            android:name="amazon.browser.lionpro.primary.LookPicture"
144-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:85:13-70
145            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
145-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:86:13-83
146            android:screenOrientation="sensor"
146-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:87:13-47
147            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
147-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:88:13-69
148            android:windowSoftInputMode="adjustPan" />
148-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:89:13-52
149        <activity
149-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:90:9-95:55
150            android:name="amazon.browser.lionpro.primary.LookVideo"
150-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:91:13-68
151            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
151-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:92:13-83
152            android:screenOrientation="sensor"
152-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:93:13-47
153            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
153-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:94:13-69
154            android:windowSoftInputMode="adjustPan" />
154-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:95:13-52
155        <activity
155-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:96:9-101:55
156            android:name="amazon.browser.lionpro.primary.AcySetting"
156-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:97:13-69
157            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
157-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:98:13-83
158            android:screenOrientation="user"
158-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:99:13-45
159            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
159-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:100:13-69
160            android:windowSoftInputMode="adjustPan" />
160-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:101:13-52
161        <!-- <activity -->
162        <!-- android:name=".primary.AcyRemoveAd" -->
163        <!-- android:configChanges="orientation|keyboard|keyboardHidden|screenSize" -->
164        <!-- android:screenOrientation="user" -->
165        <!-- android:theme="@style/Theme.AppCompat.Light.NoActionBar" -->
166        <!-- android:windowSoftInputMode="adjustPan" /> -->
167
168
169        <!-- <activity -->
170        <!-- android:name=".primary.AcyMain" -->
171        <!-- android:configChanges="orientation|keyboard|keyboardHidden|screenSize" -->
172        <!-- android:screenOrientation="user" -->
173        <!-- android:theme="@style/Theme.AppCompat.Light.NoActionBar" -->
174        <!-- android:windowSoftInputMode="adjustPan" /> -->
175
176        <activity
176-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:116:9-129:20
177            android:name="amazon.browser.lionpro.primary.AcyIntent"
177-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:117:13-68
178            android:exported="true"
178-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:119:13-36
179            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" >
179-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:118:13-83
180            <intent-filter>
180-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:120:13-128:29
181                <action android:name="android.intent.action.VIEW" />
181-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:17-69
181-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:25-66
182
183                <category android:name="android.intent.category.DEFAULT" />
183-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:123:17-76
183-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:123:27-73
184                <category android:name="android.intent.category.BROWSABLE" />
184-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:124:17-78
184-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:124:27-75
185
186                <data android:scheme="http" />
186-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
186-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:23-44
187                <data android:scheme="https" />
187-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
187-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:23-44
188            </intent-filter>
189        </activity>
190        <activity
190-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:130:9-136:45
191            android:name="amazon.browser.lionpro.primary.AcyWifiShare"
191-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:131:13-71
192            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
192-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:132:13-83
193            android:screenOrientation="portrait"
193-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:133:13-49
194            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
194-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:134:13-69
195            android:windowSoftInputMode="adjustPan" />
195-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:135:13-52
196
197        <!-- <activity android:name="gp.PremiumActivity" -->
198        <!-- android:theme="@style/AppLightTheme"> -->
199        <!-- </activity> -->
200
201        <activity
201-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:142:9-144:20
202            android:name="gp.BillingActivity"
202-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:142:19-52
203            android:theme="@style/AppLightTheme" >
203-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:143:13-49
204        </activity>
205
206        <service android:name="amazon.browser.lionpro.primary.SerWifiShare" />
206-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:146:9-79
206-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:146:18-76
207
208        <activity
208-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:148:9-154:55
209            android:name="amazon.browser.lionpro.primary.AcyEvaluate"
209-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:149:13-70
210            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
210-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:150:13-83
211            android:excludeFromRecents="false"
211-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:151:13-47
212            android:screenOrientation="user"
212-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:152:13-45
213            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
213-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:153:13-83
214            android:windowSoftInputMode="adjustPan" />
214-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:154:13-52
215        <activity
215-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:155:9-161:55
216            android:name="amazon.browser.lionpro.primary.UpdateActivity"
216-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:156:13-73
217            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
217-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:157:13-83
218            android:excludeFromRecents="false"
218-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:158:13-47
219            android:screenOrientation="user"
219-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:159:13-45
220            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
220-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:160:13-69
221            android:windowSoftInputMode="adjustPan" /> <!-- <meta-data android:name="com.google.android.gms.version" /> -->
221-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:161:13-52
222        <!-- <meta-data android:value="57ae9e28e0f55a9c20000cc7" android:name="UMENG_APPKEY" /> -->
223        <provider
223-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:163:9-173:20
224            android:name="androidx.core.content.FileProvider"
224-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:164:13-62
225            android:authorities="amazon.browser.video.downloader.fileprovider"
225-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:165:13-79
226            android:exported="false"
226-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:166:13-37
227            android:grantUriPermissions="true" >
227-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:167:13-47
228
229            <!-- 元数据 -->
230            <meta-data
230-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:170:13-172:54
231                android:name="android.support.FILE_PROVIDER_PATHS"
231-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:171:17-67
232                android:resource="@xml/file_paths" />
232-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:172:17-51
233        </provider>
234
235        <activity
235-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:175:9-178:16
236            android:name="lion.PlayActivity"
236-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:176:13-45
237            android:configChanges="orientation|keyboard|keyboardHidden|navigation" /> <!-- <activity android:name="com.adcolony.sdk.AdColonyInterstitialActivity" -->
237-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:177:13-83
238        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
239        <!-- android:hardwareAccelerated="true"/> -->
240        <!-- <activity android:name="com.adcolony.sdk.AdColonyAdViewActivity" -->
241        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
242        <!-- android:hardwareAccelerated="true"/> -->
243        <!-- Vungle Activities -->
244        <!-- <activity -->
245        <!-- android:name="com.vungle.warren.ui.VungleActivity" -->
246        <!-- android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize" -->
247        <!-- android:launchMode="singleTop" -->
248        <!-- android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> -->
249        <!-- <activity -->
250        <!-- android:name="com.vungle.warren.ui.VungleFlexViewActivity" -->
251        <!-- android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize" -->
252        <!-- android:hardwareAccelerated="true" -->
253        <!-- android:launchMode="singleTop" -->
254        <!-- android:theme="@android:style/Theme.Translucent.NoTitleBar" /> &lt;!&ndash; tapjoy &ndash;&gt; -->
255
256
257        <!-- <activity -->
258        <!-- android:name="com.tapjoy.TJAdUnitActivity" -->
259        <!-- android:configChanges="orientation|keyboardHidden|screenSize" -->
260        <!-- android:hardwareAccelerated="true" -->
261        <!-- android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" /> -->
262        <!-- <activity -->
263        <!-- android:name="com.tapjoy.TJContentActivity" -->
264        <!-- android:configChanges="orientation|keyboardHidden|screenSize" -->
265        <!-- android:theme="@android:style/Theme.Translucent.NoTitleBar" /> -->
266        <!-- applovin -->
267        <meta-data
267-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:207:9-209:118
268            android:name="applovin.sdk.key"
268-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:208:13-44
269            android:value="v6AAJu5yUShTMlS5p4B6bxtcdf6RKVWEchOwZTt3xTXHI-M63TDeT4G5lNX4t2ITlhOJ9GM5puM6tiDwFQZkVr" /> <!-- InMobi -->
269-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:209:13-115
270
271        <activity
271-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:211:9-217:45
272            android:name="amazon.browser.lionpro.primary.MoreApps"
272-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:212:13-67
273            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
273-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:213:13-83
274            android:screenOrientation="user"
274-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:214:13-45
275            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
275-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:215:13-69
276            android:windowSoftInputMode="adjustPan" />
276-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:216:13-52
277
278        <!-- <activity -->
279        <!-- android:name="com.inmobi.rendering.InMobiAdActivity" -->
280        <!-- android:configChanges="keyboardHidden|orientation|keyboard|smallestScreenSize|screenSize|screenLayout" -->
281        <!-- android:hardwareAccelerated="true" -->
282        <!-- android:resizeableActivity="false" -->
283        <!-- android:theme="@android:style/Theme.NoTitleBar" -->
284        <!-- tools:ignore="UnusedAttribute" /> -->
285        <!-- du ads -->
286        <meta-data
286-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:227:9-229:64
287            android:name="app_license"
287-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:228:13-39
288            android:value="d9fca5ef10d49e53f001b3443ff09f39" />
288-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:229:13-61
289
290        <activity
290-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:231:9-234:64
291            android:name="com.google.android.gms.ads.AdActivity"
291-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:232:13-65
292            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
292-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:233:13-122
293            android:exported="false"
293-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:76:13-37
294            android:theme="@android:style/Theme.Translucent" />
294-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:234:13-61
295
296        <meta-data
296-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:236:9-238:69
297            android:name="com.google.android.gms.version"
297-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:237:13-58
298            android:value="@integer/google_play_services_version" />
298-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:238:13-66
299        <meta-data
299-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:239:9-241:36
300            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
300-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:240:13-69
301            android:value="true" />
301-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:241:13-33
302
303        <activity
303-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:10:9-15:70
304            android:name="com.google.android.ads.mediationtestsuite.activities.HomeActivity"
304-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:11:13-93
305            android:exported="false"
305-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:12:13-37
306            android:label="Mediation Test Suite"
306-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:13:13-49
307            android:screenOrientation="portrait"
307-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:14:13-49
308            android:theme="@style/gmts_TestSuiteTheme.NoActionBar" />
308-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:15:13-67
309        <activity
309-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:16:9-21:63
310            android:name="com.google.android.ads.mediationtestsuite.activities.NetworkDetailActivity"
310-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:17:13-102
311            android:exported="false"
311-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:18:13-37
312            android:screenOrientation="portrait"
312-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:19:13-49
313            android:theme="@style/gmts_AppSecondaryTheme"
313-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:20:13-58
314            android:windowSoftInputMode="stateAlwaysHidden" />
314-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:21:13-60
315        <activity
315-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:22:9-27:73
316            android:name="com.google.android.ads.mediationtestsuite.activities.ConfigurationItemDetailActivity"
316-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:23:13-112
317            android:exported="false"
317-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:24:13-37
318            android:label="@string/gmts_ad_unit_details_title"
318-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:25:13-63
319            android:screenOrientation="portrait"
319-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:26:13-49
320            android:theme="@style/gmts_AppSecondaryTheme.NoActionBar" />
320-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:27:13-70
321        <activity
321-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:28:9-32:73
322            android:name="com.google.android.ads.mediationtestsuite.activities.ConfigurationItemsSearchActivity"
322-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:29:13-113
323            android:exported="false"
323-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:30:13-37
324            android:screenOrientation="portrait"
324-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:31:13-49
325            android:theme="@style/gmts_AppSecondaryTheme.NoActionBar" /> <!-- <meta-data -->
325-->[com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:32:13-70
326        <!-- android:name="com.google.android.play.billingclient.version" -->
327        <!-- android:value="4.0.0" /> -->
328        <activity
328-->[:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:9-20:75
329            android:name="com.android.billingclient.api.ProxyBillingActivity"
329-->[:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-78
330            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
330-->[:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-96
331            android:exported="false"
331-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:29:13-37
332            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
332-->[:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-72
333        <activity
333-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:26:9-33:80
334            android:name="com.chartboost.sdk.view.CBImpressionActivity"
334-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:27:13-72
335            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
335-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:28:13-106
336            android:enableOnBackInvokedCallback="true"
336-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:29:13-55
337            android:excludeFromRecents="true"
337-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:30:13-46
338            android:exported="false"
338-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:31:13-37
339            android:hardwareAccelerated="true"
339-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:32:13-47
340            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
340-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:33:13-77
341        <activity
341-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:34:9-40:86
342            android:name="com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity"
342-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:35:13-92
343            android:configChanges="keyboardHidden|orientation|screenSize"
343-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:36:13-74
344            android:excludeFromRecents="true"
344-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:37:13-46
345            android:exported="false"
345-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:38:13-37
346            android:hardwareAccelerated="true"
346-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:39:13-47
347            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
347-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:40:13-83
348        <activity
348-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:41:9-48:72
349            android:name="com.chartboost.sdk.view.FullscreenAdActivity"
349-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:42:13-72
350            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
350-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:43:13-106
351            android:enableOnBackInvokedCallback="true"
351-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:44:13-55
352            android:excludeFromRecents="true"
352-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:45:13-46
353            android:exported="false"
353-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:46:13-37
354            android:hardwareAccelerated="true"
354-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:47:13-47
355            android:theme="@style/Theme.AppCompat.Light.NoActionBar" /> <!-- ExoPlayer DownloadService -->
355-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:48:13-69
356        <service
356-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:51:9-61:19
357            android:name="com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService"
357-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:52:13-113
358            android:exported="false" >
358-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:53:13-37
359
360            <!-- This is needed for Scheduler -->
361            <intent-filter>
361-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:56:13-60:29
362                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />
362-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:57:17-102
362-->[com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:57:25-99
363
364                <category android:name="android.intent.category.DEFAULT" />
364-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:123:17-76
364-->E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:123:27-73
365            </intent-filter>
366        </service>
367
368        <meta-data
368-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:22:9-24:37
369            android:name="com.google.android.play.billingclient.version"
369-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:23:13-73
370            android:value="8.0.0" />
370-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:24:13-34
371
372        <activity
372-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:31:9-35:75
373            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
373-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:32:13-80
374            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
374-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:33:13-96
375            android:exported="false"
375-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:34:13-37
376            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
376-->[com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:35:13-72
377        <activity
377-->[com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:10:9-14:50
378            android:name="com.adcolony.sdk.AdColonyInterstitialActivity"
378-->[com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:11:13-73
379            android:configChanges="keyboardHidden|orientation|screenSize"
379-->[com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:12:13-74
380            android:exported="false"
380-->[com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:13:13-37
381            android:hardwareAccelerated="true" />
381-->[com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:14:13-47
382
383        <receiver
383-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:29:9-33:20
384            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
384-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:30:13-85
385            android:enabled="true"
385-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:31:13-35
386            android:exported="false" >
386-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:32:13-37
387        </receiver>
388
389        <service
389-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:35:9-38:40
390            android:name="com.google.android.gms.measurement.AppMeasurementService"
390-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:36:13-84
391            android:enabled="true"
391-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:37:13-35
392            android:exported="false" />
392-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:38:13-37
393        <service
393-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:39:9-43:72
394            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
394-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:40:13-87
395            android:enabled="true"
395-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:41:13-35
396            android:exported="false"
396-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:42:13-37
397            android:permission="android.permission.BIND_JOB_SERVICE" />
397-->[com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:43:13-69
398        <service
398-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:30:9-36:19
399            android:name="com.google.firebase.components.ComponentDiscoveryService"
399-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:31:13-84
400            android:directBootAware="true"
400-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:32:13-43
401            android:exported="false" >
401-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:32:13-37
402            <meta-data
402-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:33:13-35:85
403                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
403-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:34:17-139
404                android:value="com.google.firebase.components.ComponentRegistrar" />
404-->[com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:35:17-82
405            <meta-data
405-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:15:13-17:85
406                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
406-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:16:17-130
407                android:value="com.google.firebase.components.ComponentRegistrar" />
407-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:17:17-82
408            <meta-data
408-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:18:13-20:85
409                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
409-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:19:17-127
410                android:value="com.google.firebase.components.ComponentRegistrar" />
410-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:20:17-82
411            <meta-data
411-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:35:13-37:85
412                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
412-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:36:17-109
413                android:value="com.google.firebase.components.ComponentRegistrar" />
413-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:37:17-82
414        </service>
415
416        <provider
416-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:80:9-85:43
417            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
417-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:81:13-76
418            android:authorities="amazon.browser.video.downloader.mobileadsinitprovider"
418-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:82:13-73
419            android:exported="false"
419-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:83:13-37
420            android:initOrder="100" />
420-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:84:13-36
421
422        <service
422-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:87:9-91:43
423            android:name="com.google.android.gms.ads.AdService"
423-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:88:13-64
424            android:enabled="true"
424-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:89:13-35
425            android:exported="false" />
425-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:90:13-37
426
427        <activity
427-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:93:9-97:43
428            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
428-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:94:13-82
429            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
429-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:95:13-122
430            android:exported="false" />
430-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:96:13-37
431        <activity
431-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:98:9-105:43
432            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
432-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:99:13-82
433            android:excludeFromRecents="true"
433-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:100:13-46
434            android:exported="false"
434-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:101:13-37
435            android:launchMode="singleTask"
435-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:102:13-44
436            android:taskAffinity=""
436-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:103:13-36
437            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
437-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:104:13-72
438
439        <meta-data
439-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:107:9-109:36
440            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
440-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:108:13-79
441            android:value="true" />
441-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:109:13-33
442        <meta-data
442-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:110:9-112:36
443            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
443-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:111:13-83
444            android:value="true" />
444-->[com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:112:13-33
445
446        <activity
446-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:27:9-30:50
447            android:name="com.adcolony.sdk.AdColonyAdViewActivity"
447-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:28:13-67
448            android:configChanges="keyboardHidden|orientation|screenSize"
448-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:29:13-74
449            android:hardwareAccelerated="true" />
449-->[com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:30:13-47
450        <activity
450-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
451            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
451-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
452            android:excludeFromRecents="true"
452-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
453            android:exported="false"
453-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
454            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
454-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
455        <!--
456            Service handling Google Sign-In user revocation. For apps that do not integrate with
457            Google Sign-In, this service will never be started.
458        -->
459        <service
459-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
460            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
460-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
461            android:exported="true"
461-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
462            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
462-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
463            android:visibleToInstantApps="true" />
463-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
464
465        <activity
465-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
466            android:name="com.google.android.gms.common.api.GoogleApiActivity"
466-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
467            android:exported="false"
467-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
468            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
468-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
469
470        <provider
470-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:23:9-28:39
471            android:name="com.google.firebase.provider.FirebaseInitProvider"
471-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:24:13-77
472            android:authorities="amazon.browser.video.downloader.firebaseinitprovider"
472-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:25:13-72
473            android:directBootAware="true"
473-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:26:13-43
474            android:exported="false"
474-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:27:13-37
475            android:initOrder="100" />
475-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:28:13-36
476        <provider
476-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
477            android:name="androidx.startup.InitializationProvider"
477-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
478            android:authorities="amazon.browser.video.downloader.androidx-startup"
478-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
479            android:exported="false" >
479-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
480            <meta-data
480-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
481                android:name="androidx.work.WorkManagerInitializer"
481-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
482                android:value="androidx.startup" />
482-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
483            <meta-data
483-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
484                android:name="androidx.emoji2.text.EmojiCompatInitializer"
484-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
485                android:value="androidx.startup" />
485-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
486            <meta-data
486-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
487                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
487-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
488                android:value="androidx.startup" />
488-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
489            <meta-data
489-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
490                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
490-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
491                android:value="androidx.startup" />
491-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
492        </provider>
493
494        <service
494-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
495            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
495-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
496            android:directBootAware="false"
496-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
497            android:enabled="@bool/enable_system_alarm_service_default"
497-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
498            android:exported="false" />
498-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
499        <service
499-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
500            android:name="androidx.work.impl.background.systemjob.SystemJobService"
500-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
501            android:directBootAware="false"
501-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
502            android:enabled="@bool/enable_system_job_service_default"
502-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
503            android:exported="true"
503-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
504            android:permission="android.permission.BIND_JOB_SERVICE" />
504-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
505        <service
505-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
506            android:name="androidx.work.impl.foreground.SystemForegroundService"
506-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
507            android:directBootAware="false"
507-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
508            android:enabled="@bool/enable_system_foreground_service_default"
508-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
509            android:exported="false" />
509-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
510
511        <receiver
511-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
512            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
512-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
513            android:directBootAware="false"
513-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
514            android:enabled="true"
514-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
515            android:exported="false" />
515-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
516        <receiver
516-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
517            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
517-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
518            android:directBootAware="false"
518-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
519            android:enabled="false"
519-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
520            android:exported="false" >
520-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
521            <intent-filter>
521-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
522                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
522-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
522-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
523                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
523-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
523-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
524            </intent-filter>
525        </receiver>
526        <receiver
526-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
527            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
527-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
528            android:directBootAware="false"
528-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
529            android:enabled="false"
529-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
530            android:exported="false" >
530-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
531            <intent-filter>
531-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
532                <action android:name="android.intent.action.BATTERY_OKAY" />
532-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
532-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
533                <action android:name="android.intent.action.BATTERY_LOW" />
533-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
533-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
534            </intent-filter>
535        </receiver>
536        <receiver
536-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
537            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
537-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
538            android:directBootAware="false"
538-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
539            android:enabled="false"
539-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
540            android:exported="false" >
540-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
541            <intent-filter>
541-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
542                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
542-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
542-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
543                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
543-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
543-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
544            </intent-filter>
545        </receiver>
546        <receiver
546-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
547            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
547-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
548            android:directBootAware="false"
548-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
549            android:enabled="false"
549-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
550            android:exported="false" >
550-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
551            <intent-filter>
551-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
552                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
552-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
552-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
553            </intent-filter>
554        </receiver>
555        <receiver
555-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
556            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
556-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
557            android:directBootAware="false"
557-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
558            android:enabled="false"
558-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
559            android:exported="false" >
559-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
560            <intent-filter>
560-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
561                <action android:name="android.intent.action.BOOT_COMPLETED" />
561-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
561-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
562                <action android:name="android.intent.action.TIME_SET" />
562-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
562-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
563                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
563-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
563-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
564            </intent-filter>
565        </receiver>
566        <receiver
566-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
567            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
567-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
568            android:directBootAware="false"
568-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
569            android:enabled="@bool/enable_system_alarm_service_default"
569-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
570            android:exported="false" >
570-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
571            <intent-filter>
571-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
572                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
572-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
572-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
573            </intent-filter>
574        </receiver>
575        <receiver
575-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
576            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
576-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
577            android:directBootAware="false"
577-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
578            android:enabled="true"
578-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
579            android:exported="true"
579-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
580            android:permission="android.permission.DUMP" >
580-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
581            <intent-filter>
581-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
582                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
582-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
582-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
583            </intent-filter>
584        </receiver>
585
586        <uses-library
586-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
587            android:name="android.ext.adservices"
587-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
588            android:required="false" />
588-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
589
590        <provider
590-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:8:9-11:40
591            android:name="com.squareup.picasso.PicassoProvider"
591-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:9:13-64
592            android:authorities="amazon.browser.video.downloader.com.squareup.picasso"
592-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:10:13-72
593            android:exported="false" />
593-->[com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:11:13-37
594
595        <service
595-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
596            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
596-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
597            android:exported="false" >
597-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
598            <meta-data
598-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
599                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
599-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
600                android:value="cct" />
600-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
601        </service>
602        <service
602-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
603            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
603-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
604            android:exported="false"
604-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
605            android:permission="android.permission.BIND_JOB_SERVICE" >
605-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
606        </service>
607
608        <receiver
608-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
609            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
609-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
610            android:exported="false" />
610-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
611        <receiver
611-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
612            android:name="androidx.profileinstaller.ProfileInstallReceiver"
612-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
613            android:directBootAware="false"
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
614            android:enabled="true"
614-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
615            android:exported="true"
615-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
616            android:permission="android.permission.DUMP" >
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
617            <intent-filter>
617-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
618                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
618-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
618-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
619            </intent-filter>
620            <intent-filter>
620-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
621                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
621-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
621-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
622            </intent-filter>
623            <intent-filter>
623-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
624                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
624-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
624-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
625            </intent-filter>
626            <intent-filter>
626-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
627                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
627-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
627-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
628            </intent-filter>
629        </receiver>
630
631        <service
631-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
632            android:name="androidx.room.MultiInstanceInvalidationService"
632-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
633            android:directBootAware="true"
633-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
634            android:exported="false" />
634-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
635    </application>
636
637</manifest>
