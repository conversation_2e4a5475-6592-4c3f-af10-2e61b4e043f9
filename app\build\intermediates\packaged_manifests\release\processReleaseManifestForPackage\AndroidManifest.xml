<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="amazon.browser.video.downloader"
    android:versionCode="22"
    android:versionName="1.0.22" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="36" />
    <!-- package="amazon.browser.lionpro" -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!-- <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> &lt;!&ndash; <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/> &ndash;&gt; -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 定位 adfaico -->
    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> -->
    <!--
  <uses-permission android:name="android.permission.WRITE_CALENDAR"/>
      <uses-permission android:name="android.permission.READ_CALENDAR"/>
    -->
    <!-- 查看设备信息 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 可选，在咨询清单中制定versionName属性，藉此在版本名称下方报告资料 -->
    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> &lt;!&ndash; <uses-permission android:name="android.permission.READ_LOGS" />&lt;!&ndash; 获取日志&ndash;&gt; &ndash;&gt; -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> -->
    <!-- <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> -->
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <queries>
        <intent>
            <action android:name="android.intent.action.ACTION_VIEW" />

            <data android:scheme="https" />
        </intent>
        <intent>
            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
        </intent>
        <intent>
            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
        </intent> <!-- For browser content -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent> <!-- End of browser content -->
        <!-- For CustomTabsService -->
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent> <!-- End of CustomTabsService -->
        <!-- For MRAID capabilities -->
        <intent>
            <action android:name="android.intent.action.INSERT" />

            <data android:mimeType="vnd.android.cursor.dir/event" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="sms" />
        </intent>
        <intent>
            <action android:name="android.intent.action.DIAL" />

            <data android:path="tel:" />
        </intent>

        <package android:name="com.android.vending" />
        <package android:name="com.amazon.venezia" />
        <package android:name="com.sec.android.app.samsungapps" />
        <package android:name="com.huawei.appmarket" />
    </queries>

    <uses-permission android:name="amazon.browser.video.downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <permission
        android:name="amazon.browser.video.downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" /> <!-- android:requestLegacyExternalStorage="true" -->
    <!-- android:debuggable="true" -->
    <!-- tools:ignore="HardcodedDebugMode" -->
    <application
        android:name="amazon.browser.lionpro.primary.Mainly"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:extractNativeLibs="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/icon_app"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >

        <!-- <property -->
        <!-- android:name="android.adservices.AD_SERVICES_CONFIG" -->
        <!-- android:value="@xml/gma_ad_services_config" -->
        <!-- tools:replace="android:resource" /> -->
        <activity
            android:name="amazon.browser.lionpro.primary.AcyMain"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|uiMode|screenLayout|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service android:name="amazon.browser.lionpro.primary.SerMain" />
        <service android:name="amazon.browser.lionpro.primary.M3u8MergeServer" />

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-8603317425868964~6066157158" />
        <!-- <meta-data -->
        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
        <!-- android:value="ca-app-pub-3940256099942544~3347511713" /> -->


        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
        <!-- android:value="ca-app-pub-3940256099942544~3347511713"/> -->
        <!-- <meta-data -->
        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
        <!-- android:value="ca-app-pub-3064461767247622~9204542388"/> -->
        <activity
            android:name="amazon.browser.lionpro.primary.AcyStorage"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.LookPicture"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="sensor"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.LookVideo"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="sensor"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.AcySetting"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <!-- <activity -->
        <!-- android:name=".primary.AcyRemoveAd" -->
        <!-- android:configChanges="orientation|keyboard|keyboardHidden|screenSize" -->
        <!-- android:screenOrientation="user" -->
        <!-- android:theme="@style/Theme.AppCompat.Light.NoActionBar" -->
        <!-- android:windowSoftInputMode="adjustPan" /> -->


        <!-- <activity -->
        <!-- android:name=".primary.AcyMain" -->
        <!-- android:configChanges="orientation|keyboard|keyboardHidden|screenSize" -->
        <!-- android:screenOrientation="user" -->
        <!-- android:theme="@style/Theme.AppCompat.Light.NoActionBar" -->
        <!-- android:windowSoftInputMode="adjustPan" /> -->

        <activity
            android:name="amazon.browser.lionpro.primary.AcyIntent"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name="amazon.browser.lionpro.primary.AcyWifiShare"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />

        <!-- <activity android:name="gp.PremiumActivity" -->
        <!-- android:theme="@style/AppLightTheme"> -->
        <!-- </activity> -->

        <activity
            android:name="gp.BillingActivity"
            android:theme="@style/AppLightTheme" >
        </activity>

        <service android:name="amazon.browser.lionpro.primary.SerWifiShare" />

        <activity
            android:name="amazon.browser.lionpro.primary.AcyEvaluate"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:excludeFromRecents="false"
            android:screenOrientation="user"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.UpdateActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:excludeFromRecents="false"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" /> <!-- <meta-data android:name="com.google.android.gms.version" /> -->
        <!-- <meta-data android:value="57ae9e28e0f55a9c20000cc7" android:name="UMENG_APPKEY" /> -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="amazon.browser.video.downloader.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >

            <!-- 元数据 -->
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name="lion.PlayActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|navigation" /> <!-- <activity android:name="com.adcolony.sdk.AdColonyInterstitialActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:hardwareAccelerated="true"/> -->
        <!-- <activity android:name="com.adcolony.sdk.AdColonyAdViewActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:hardwareAccelerated="true"/> -->
        <!-- Vungle Activities -->
        <!-- <activity -->
        <!-- android:name="com.vungle.warren.ui.VungleActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize" -->
        <!-- android:launchMode="singleTop" -->
        <!-- android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> -->
        <!-- <activity -->
        <!-- android:name="com.vungle.warren.ui.VungleFlexViewActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize" -->
        <!-- android:hardwareAccelerated="true" -->
        <!-- android:launchMode="singleTop" -->
        <!-- android:theme="@android:style/Theme.Translucent.NoTitleBar" /> &lt;!&ndash; tapjoy &ndash;&gt; -->


        <!-- <activity -->
        <!-- android:name="com.tapjoy.TJAdUnitActivity" -->
        <!-- android:configChanges="orientation|keyboardHidden|screenSize" -->
        <!-- android:hardwareAccelerated="true" -->
        <!-- android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" /> -->
        <!-- <activity -->
        <!-- android:name="com.tapjoy.TJContentActivity" -->
        <!-- android:configChanges="orientation|keyboardHidden|screenSize" -->
        <!-- android:theme="@android:style/Theme.Translucent.NoTitleBar" /> -->
        <!-- applovin -->
        <meta-data
            android:name="applovin.sdk.key"
            android:value="v6AAJu5yUShTMlS5p4B6bxtcdf6RKVWEchOwZTt3xTXHI-M63TDeT4G5lNX4t2ITlhOJ9GM5puM6tiDwFQZkVr" /> <!-- InMobi -->

        <activity
            android:name="amazon.browser.lionpro.primary.MoreApps"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />

        <!-- <activity -->
        <!-- android:name="com.inmobi.rendering.InMobiAdActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|keyboard|smallestScreenSize|screenSize|screenLayout" -->
        <!-- android:hardwareAccelerated="true" -->
        <!-- android:resizeableActivity="false" -->
        <!-- android:theme="@android:style/Theme.NoTitleBar" -->
        <!-- tools:ignore="UnusedAttribute" /> -->
        <!-- du ads -->
        <meta-data
            android:name="app_license"
            android:value="d9fca5ef10d49e53f001b3443ff09f39" />

        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
            android:value="true" />

        <activity
            android:name="com.google.android.ads.mediationtestsuite.activities.HomeActivity"
            android:exported="false"
            android:label="Mediation Test Suite"
            android:screenOrientation="portrait"
            android:theme="@style/gmts_TestSuiteTheme.NoActionBar" />
        <activity
            android:name="com.google.android.ads.mediationtestsuite.activities.NetworkDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/gmts_AppSecondaryTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name="com.google.android.ads.mediationtestsuite.activities.ConfigurationItemDetailActivity"
            android:exported="false"
            android:label="@string/gmts_ad_unit_details_title"
            android:screenOrientation="portrait"
            android:theme="@style/gmts_AppSecondaryTheme.NoActionBar" />
        <activity
            android:name="com.google.android.ads.mediationtestsuite.activities.ConfigurationItemsSearchActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/gmts_AppSecondaryTheme.NoActionBar" /> <!-- <meta-data -->
        <!-- android:name="com.google.android.play.billingclient.version" -->
        <!-- android:value="4.0.0" /> -->
        <activity
            android:name="com.android.billingclient.api.ProxyBillingActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.chartboost.sdk.view.CBImpressionActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:enableOnBackInvokedCallback="true"
            android:excludeFromRecents="true"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:excludeFromRecents="true"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />
        <activity
            android:name="com.chartboost.sdk.view.FullscreenAdActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"
            android:enableOnBackInvokedCallback="true"
            android:excludeFromRecents="true"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" /> <!-- ExoPlayer DownloadService -->
        <service
            android:name="com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService"
            android:exported="false" >

            <!-- This is needed for Scheduler -->
            <intent-filter>
                <action android:name="com.google.android.exoplayer.downloadService.action.RESTART" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.google.android.play.billingclient.version"
            android:value="8.0.0" />

        <activity
            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.adcolony.sdk.AdColonyInterstitialActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="false"
            android:hardwareAccelerated="true" />

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <provider
            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
            android:authorities="amazon.browser.video.downloader.mobileadsinitprovider"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="com.google.android.gms.ads.AdService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false" />
        <activity
            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true" />

        <activity
            android:name="com.adcolony.sdk.AdColonyAdViewActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:hardwareAccelerated="true" />
        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="amazon.browser.video.downloader.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="amazon.browser.video.downloader.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <provider
            android:name="com.squareup.picasso.PicassoProvider"
            android:authorities="amazon.browser.video.downloader.com.squareup.picasso"
            android:exported="false" />

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
    </application>

</manifest>