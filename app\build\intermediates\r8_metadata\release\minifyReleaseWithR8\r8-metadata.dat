{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": true, "isRuntimeInvisibleParameterAnnotationsKept": true, "isRuntimeInvisibleTypeAnnotationsKept": true, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": true, "isStackMapTableKept": false}, "isAccessModificationEnabled": false, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": false, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "libraryDesugaring": {"identifier": "com.tools.android:desugar_jdk_libs_configuration:2.1.5"}, "minApiLevel": "23", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 37747150400, "numberOfThreads": 20}, "dexFiles": [{"checksum": "53b298e80652246425e1869a9a933a3d54a9bc86eeeacf02a0b73f0737e24de5", "startup": false}, {"checksum": "803a39b958380c1588756154e57728d42b4e45c7f5f93e43ed4c49316ed6b55f", "startup": false}], "stats": {"noObfuscationPercentage": 6.19, "noOptimizationPercentage": 100.0, "noShrinkingPercentage": 6.31}, "featureSplits": {"featureSplits": [{"dexFiles": []}], "isolatedSplits": false}, "resourceOptimization": {"isOptimizedShrinkingEnabled": false}, "version": "8.11.18"}