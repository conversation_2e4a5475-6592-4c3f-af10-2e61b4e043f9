-- Merging decision tree log ---
manifest
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:2:1-244:12
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:2:1-244:12
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:2:1-244:12
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:2:1-244:12
MERGED from [com.github.kongzue.DialogX:DialogX:0.0.49.beta18] C:\Users\<USER>\.gradle\caches\8.13\transforms\daa6ace68e4e58e72b5da1cae7619629\transformed\jetified-DialogX-0.0.49.beta18\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:2:1-35:12
MERGED from [com.github.AnJoiner:FFmpegCommand:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\01418f0bb56234be9291285c890b2581\transformed\jetified-FFmpegCommand-1.3.2\AndroidManifest.xml:2:1-9:12
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:2:1-64:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80cfb6b34c8ac097447bd526a2ee2d1\transformed\constraintlayout-2.0.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\507c6593d6c43b85e298dcd43fe83de2\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e1206851174c2a71fce0be4f3db2dd0\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:2:1-38:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8edbe318b842172fd0a7e7882c49e2b\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfad3ea2cf17a86249622988bff2a0b\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4800a7c071882b7f0431d2ffa07290b0\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fba1957e041ce4d777b5f45031e116f5\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b31b837fcd90eb33f1b29127e8d2c017\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4223b5d7b14b01e84213bc6d2e4657c7\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b7d5a25474507ad9f3f70f3975fc475\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\557da46f2cf0492a19c1a7f84c73b5fc\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4781b906e97d6a95ac2d27eac821fe0a\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c6a0ca5e0c81aa3750605acc4baf5a3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.android.gms:play-services-ads:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed672f713736551646e02791405a5923\transformed\jetified-play-services-ads-24.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\998f9146e2959cb7f7aea37ddc046745\transformed\jetified-firebase-analytics-23.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:2:1-33:12
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\98a1ef1f556e290f1657c52a2d405724\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b70d8e2946340751f926927bcc46355f\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e398d89bb150e4cfe04fce26b1b61714\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ce5481b1044f45c28a3f96b2e70e67a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8a98d538755981fae0f4832024731a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c342dd76e3e52d0d02b470fd6b07cc52\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2e93d1baa0d7927e093ac4f3df9ccd\transformed\jetified-play-services-stats-17.1.0\AndroidManifest.xml:2:1-8:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625eab3cbcafe99b259d12a26dc7a53c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72796bda93b4de1092bf69336eec070c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd5f47b64eeba6d1d8b726f352f968c1\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\913ab01fdb14e2976f972a536b9e95cb\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0865b0d75bc612ae78a97d6ddf50fd7f\transformed\documentfile-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\582e2ad63f16a4421886162528ce5d87\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\862a73af1f03ec6c8fb6641d4e1d39fa\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc683ef3f367957144af2e8853c70fb5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d965e70b4d45c18dc52895768f044c4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33367b0e0354e6e37c04c83d715464b3\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b265b12e5ec5acbaeef44103a7c75d2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\468e889c42d5a4e5fd3d58e62972ce3f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\978503c6ac2139b9688421187d87bddb\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b521110901b4ae7de4256039262ed8ae\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83833e738d244b35a93c28ec7e9bf42d\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e838f687d051d86718ba5702c772c5\transformed\jetified-lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aac4ef6378b1db6fe83156e67ef8c76f\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e68fa80193914af8fba0b7ad4934f34\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6fd51372c611a2567cd88e5c8985e18\transformed\jetified-lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f1ac8d5ca3b3419df6433a690e989d\transformed\jetified-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e136b98f43154d24eff7db764b8dee5a\transformed\lifecycle-runtime-2.9.2\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24eca39a1384e357709d100b7cf3d5e\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3000334ecfada1bb421546f8a4c74d\transformed\jetified-lifecycle-service-2.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a08bef2f621cd7aabf1e94037b974fe3\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c1c88644ecadc661acde7f5441b4be6\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\84683e4c011fea4c373f1ea8f17eaacf\transformed\jetified-lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d954572012be292b49e6401b9753243\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d2a2c826fb42e12e8b8ca37ee28485f\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\784034ef828ed0e9f7e862d0f2163680\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\194a2bee434b719060020e11cb51daad\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6619715ad669283d4bf5f8d7bf43fd5\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d70c2b4c64835a1332ec4b411c509383\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aefa536aed4635c8c80b7fc559dc303c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a831ad12897d5ff6823c40f2b91eb4\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b01bac89cef837b016a37a243dc8ab97\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc1adff3b0b30d29c96d43ef3e07cb5d\transformed\jetified-flexbox-3.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb2175d190bb14a01613832621ca821\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\469e23a07d0e29759904aa58f72fc1a2\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c59885c9ad6cc2e227a8c18a7c243154\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dab60b6ec7b87ea8d13fc68233f87729\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3a881a6e2f556f31196d1e94b9b28c\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d98e0cc5d2de794b22df65bc82f67cf\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd98646fc2cb58b7d5d7e9fbe4955ed4\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fdea3ea95bfda561b4265c5be2dedb6a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc07655ba36acd544196330469808a5f\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\709d49b5a98f52acdc2ab493a38b5974\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\384da646fb352a19406fee696724f0d9\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2db25255f39a4b8148d9823a7d3e85e4\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e66c8a9d4313077a2f0d0043f5391159\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc38323765f52088f2be80f3730d00b\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\493390f283f7d835e771e48835a9ed0f\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd70b21072f4226bfc46625a814f65ce\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e032c5a6b90c4b4b993235e24bde1e72\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9587b8c6beee5f14f7ef29f6db7f9966\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\854dd7ced34e61634eb6685e542c79a3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b6e838136e2ef6f996eb2b96881c66a\transformed\jetified-firebase-components-19.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5c5d2a36df431e38409576891336cb0\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.tencent.bugly:crashreport:4.1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece97ebe988a73d09b112128f40951e\transformed\jetified-crashreport-4.1.9.3\AndroidManifest.xml:2:1-11:12
MERGED from [com.android.volley:volley:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\374355ca82ecf06e73f6cc08b444889c\transformed\jetified-volley-1.2.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:24:5-67
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:11:5-67
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:9:5-101
	tools:node
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:9:79-99
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:10:5-79
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-79
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:9:5-79
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:9:5-79
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:14:5-79
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:14:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:12:5-79
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:12:5-79
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:24:5-79
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:11:5-76
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:11:22-73
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:12:5-68
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:27:5-68
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:27:5-68
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:12:22-65
uses-permission#android.permission.READ_PHONE_STATE
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:21:5-78
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:21:22-75
uses-permission#com.android.vending.BILLING
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:22:5-67
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:10:5-67
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:10:5-67
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:22:22-64
uses-permission#com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:25:5-117
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:25:22-114
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:26:5-78
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:15:5-79
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:15:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:13:5-79
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:13:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:26:22-76
application
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:31:5-242:19
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:31:5-242:19
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:9:5-33:19
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:9:5-33:19
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-21:19
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-21:19
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:25:5-62:19
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:25:5-62:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80cfb6b34c8ac097447bd526a2ee2d1\transformed\constraintlayout-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80cfb6b34c8ac097447bd526a2ee2d1\transformed\constraintlayout-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:21:5-36:19
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:21:5-36:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8edbe318b842172fd0a7e7882c49e2b\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8edbe318b842172fd0a7e7882c49e2b\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:9:5-15:19
MERGED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:9:5-15:19
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:30:5-20
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:30:5-20
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\998f9146e2959cb7f7aea37ddc046745\transformed\jetified-firebase-analytics-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\998f9146e2959cb7f7aea37ddc046745\transformed\jetified-firebase-analytics-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:22:5-31:19
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:22:5-31:19
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\98a1ef1f556e290f1657c52a2d405724\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\98a1ef1f556e290f1657c52a2d405724\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ce5481b1044f45c28a3f96b2e70e67a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ce5481b1044f45c28a3f96b2e70e67a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [com.google.android.gms:play-services-stats:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2e93d1baa0d7927e093ac4f3df9ccd\transformed\jetified-play-services-stats-17.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-stats:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2e93d1baa0d7927e093ac4f3df9ccd\transformed\jetified-play-services-stats-17.1.0\AndroidManifest.xml:6:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aefa536aed4635c8c80b7fc559dc303c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aefa536aed4635c8c80b7fc559dc303c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:7:5-12:19
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:37:9-35
	android:label
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:36:9-41
	android:hardwareAccelerated
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:34:9-43
	android:icon
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:35:9-40
	android:allowBackup
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:33:9-35
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:39:9-40
	android:networkSecurityConfig
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:41:9-69
	tools:replace
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:38:9-37
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:32:9-61
activity#amazon.browser.lionpro.primary.AcyMain
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:47:9-60:20
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:50:13-45
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:52:13-52
	android:exported
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:53:13-36
	tools:ignore
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:54:13-42
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:49:13-122
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:51:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:48:13-66
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.MAIN
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:58:17-77
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:58:27-74
service#amazon.browser.lionpro.primary.SerMain
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:62:9-74
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:62:18-71
service#amazon.browser.lionpro.primary.M3u8MergeServer
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:63:9-82
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:63:18-79
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:64:9-66:70
	android:value
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:66:13-67
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:65:13-69
activity#amazon.browser.lionpro.primary.AcyStorage
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:77:9-83:45
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:80:13-45
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:82:13-52
	tools:ignore
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:83:13-42
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:79:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:81:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:78:13-69
activity#amazon.browser.lionpro.primary.LookPicture
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:84:9-89:55
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:87:13-47
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:89:13-52
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:86:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:88:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:85:13-70
activity#amazon.browser.lionpro.primary.LookVideo
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:90:9-95:55
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:93:13-47
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:95:13-52
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:92:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:94:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:91:13-68
activity#amazon.browser.lionpro.primary.AcySetting
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:96:9-101:55
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:99:13-45
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:101:13-52
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:98:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:100:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:97:13-69
activity#amazon.browser.lionpro.primary.AcyIntent
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:116:9-129:20
	android:exported
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:119:13-36
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:118:13-83
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:117:13-68
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:http+data:scheme:https
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:120:13-128:29
action#android.intent.action.VIEW
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:17-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:121:25-66
category#android.intent.category.DEFAULT
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:123:17-76
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:123:27-73
category#android.intent.category.BROWSABLE
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:124:17-78
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:124:27-75
data
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:17-47
	android:scheme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:126:23-44
activity#amazon.browser.lionpro.primary.AcyWifiShare
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:130:9-136:45
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:133:13-49
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:135:13-52
	tools:ignore
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:136:13-42
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:132:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:134:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:131:13-71
activity#gp.BillingActivity
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:142:9-144:20
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:143:13-49
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:142:19-52
service#amazon.browser.lionpro.primary.SerWifiShare
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:146:9-79
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:146:18-76
activity#amazon.browser.lionpro.primary.AcyEvaluate
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:148:9-154:55
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:152:13-45
	android:excludeFromRecents
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:151:13-47
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:154:13-52
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:150:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:153:13-83
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:149:13-70
activity#amazon.browser.lionpro.primary.UpdateActivity
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:155:9-161:55
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:159:13-45
	android:excludeFromRecents
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:158:13-47
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:161:13-52
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:157:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:160:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:156:13-73
provider#androidx.core.content.FileProvider
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:163:9-173:20
	android:grantUriPermissions
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:167:13-47
	android:authorities
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:165:13-79
	android:exported
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:166:13-37
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:164:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:170:13-172:54
	android:resource
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:172:17-51
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:171:17-67
activity#lion.PlayActivity
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:175:9-178:16
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:177:13-83
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:176:13-45
meta-data#applovin.sdk.key
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:207:9-209:118
	android:value
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:209:13-115
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:208:13-44
activity#amazon.browser.lionpro.primary.MoreApps
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:211:9-217:45
	android:screenOrientation
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:214:13-45
	android:windowSoftInputMode
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:216:13-52
	tools:ignore
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:217:13-42
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:213:13-83
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:215:13-69
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:212:13-67
meta-data#app_license
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:227:9-229:64
	android:value
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:229:13-61
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:228:13-39
activity#com.google.android.gms.ads.AdActivity
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:231:9-234:64
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:73:9-78:43
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:233:13-122
	android:theme
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:234:13-61
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:232:13-65
meta-data#com.google.android.gms.version
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:236:9-238:69
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:238:13-66
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:237:13-58
meta-data#com.google.android.gms.ads.AD_MANAGER_APP
ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:239:9-241:36
	android:value
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:241:13-33
	android:name
		ADDED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml:240:13-69
uses-sdk
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
MERGED from [com.github.kongzue.DialogX:DialogX:0.0.49.beta18] C:\Users\<USER>\.gradle\caches\8.13\transforms\daa6ace68e4e58e72b5da1cae7619629\transformed\jetified-DialogX-0.0.49.beta18\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.kongzue.DialogX:DialogX:0.0.49.beta18] C:\Users\<USER>\.gradle\caches\8.13\transforms\daa6ace68e4e58e72b5da1cae7619629\transformed\jetified-DialogX-0.0.49.beta18\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.AnJoiner:FFmpegCommand:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\01418f0bb56234be9291285c890b2581\transformed\jetified-FFmpegCommand-1.3.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.AnJoiner:FFmpegCommand:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\01418f0bb56234be9291285c890b2581\transformed\jetified-FFmpegCommand-1.3.2\AndroidManifest.xml:5:5-7:41
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.ads.mediation:chartboost:9.9.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\67bda058a929f84b58f3d98bc7399de6\transformed\jetified-chartboost-9.9.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80cfb6b34c8ac097447bd526a2ee2d1\transformed\constraintlayout-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80cfb6b34c8ac097447bd526a2ee2d1\transformed\constraintlayout-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\507c6593d6c43b85e298dcd43fe83de2\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\507c6593d6c43b85e298dcd43fe83de2\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e1206851174c2a71fce0be4f3db2dd0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e1206851174c2a71fce0be4f3db2dd0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8edbe318b842172fd0a7e7882c49e2b\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8edbe318b842172fd0a7e7882c49e2b\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfad3ea2cf17a86249622988bff2a0b\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\edfad3ea2cf17a86249622988bff2a0b\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4800a7c071882b7f0431d2ffa07290b0\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4800a7c071882b7f0431d2ffa07290b0\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fba1957e041ce4d777b5f45031e116f5\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\fba1957e041ce4d777b5f45031e116f5\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b31b837fcd90eb33f1b29127e8d2c017\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b31b837fcd90eb33f1b29127e8d2c017\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4223b5d7b14b01e84213bc6d2e4657c7\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4223b5d7b14b01e84213bc6d2e4657c7\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b7d5a25474507ad9f3f70f3975fc475\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b7d5a25474507ad9f3f70f3975fc475\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\557da46f2cf0492a19c1a7f84c73b5fc\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\557da46f2cf0492a19c1a7f84c73b5fc\transformed\jetified-media3-ui-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4781b906e97d6a95ac2d27eac821fe0a\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4781b906e97d6a95ac2d27eac821fe0a\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c6a0ca5e0c81aa3750605acc4baf5a3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c6a0ca5e0c81aa3750605acc4baf5a3\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-ads:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed672f713736551646e02791405a5923\transformed\jetified-play-services-ads-24.5.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed672f713736551646e02791405a5923\transformed\jetified-play-services-ads-24.5.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\998f9146e2959cb7f7aea37ddc046745\transformed\jetified-firebase-analytics-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\998f9146e2959cb7f7aea37ddc046745\transformed\jetified-firebase-analytics-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eae8d09ad32130b1b9e142f717133a09\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:21:5-44
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:6:5-9:1108
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:6:5-9:1108
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\98a1ef1f556e290f1657c52a2d405724\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\98a1ef1f556e290f1657c52a2d405724\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b70d8e2946340751f926927bcc46355f\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b70d8e2946340751f926927bcc46355f\transformed\jetified-play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e398d89bb150e4cfe04fce26b1b61714\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\e398d89bb150e4cfe04fce26b1b61714\transformed\jetified-play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ce5481b1044f45c28a3f96b2e70e67a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ce5481b1044f45c28a3f96b2e70e67a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8a98d538755981fae0f4832024731a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8a98d538755981fae0f4832024731a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c342dd76e3e52d0d02b470fd6b07cc52\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c342dd76e3e52d0d02b470fd6b07cc52\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2e93d1baa0d7927e093ac4f3df9ccd\transformed\jetified-play-services-stats-17.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-stats:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2e93d1baa0d7927e093ac4f3df9ccd\transformed\jetified-play-services-stats-17.1.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625eab3cbcafe99b259d12a26dc7a53c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\625eab3cbcafe99b259d12a26dc7a53c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72796bda93b4de1092bf69336eec070c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72796bda93b4de1092bf69336eec070c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd5f47b64eeba6d1d8b726f352f968c1\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd5f47b64eeba6d1d8b726f352f968c1\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\913ab01fdb14e2976f972a536b9e95cb\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\913ab01fdb14e2976f972a536b9e95cb\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0865b0d75bc612ae78a97d6ddf50fd7f\transformed\documentfile-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0865b0d75bc612ae78a97d6ddf50fd7f\transformed\documentfile-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\582e2ad63f16a4421886162528ce5d87\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\582e2ad63f16a4421886162528ce5d87\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\862a73af1f03ec6c8fb6641d4e1d39fa\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\862a73af1f03ec6c8fb6641d4e1d39fa\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc683ef3f367957144af2e8853c70fb5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc683ef3f367957144af2e8853c70fb5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d965e70b4d45c18dc52895768f044c4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d965e70b4d45c18dc52895768f044c4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33367b0e0354e6e37c04c83d715464b3\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33367b0e0354e6e37c04c83d715464b3\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b265b12e5ec5acbaeef44103a7c75d2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b265b12e5ec5acbaeef44103a7c75d2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\468e889c42d5a4e5fd3d58e62972ce3f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\468e889c42d5a4e5fd3d58e62972ce3f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\978503c6ac2139b9688421187d87bddb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\978503c6ac2139b9688421187d87bddb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b521110901b4ae7de4256039262ed8ae\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b521110901b4ae7de4256039262ed8ae\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83833e738d244b35a93c28ec7e9bf42d\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83833e738d244b35a93c28ec7e9bf42d\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e838f687d051d86718ba5702c772c5\transformed\jetified-lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e838f687d051d86718ba5702c772c5\transformed\jetified-lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aac4ef6378b1db6fe83156e67ef8c76f\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\aac4ef6378b1db6fe83156e67ef8c76f\transformed\lifecycle-viewmodel-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e68fa80193914af8fba0b7ad4934f34\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e68fa80193914af8fba0b7ad4934f34\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6fd51372c611a2567cd88e5c8985e18\transformed\jetified-lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6fd51372c611a2567cd88e5c8985e18\transformed\jetified-lifecycle-livedata-core-ktx-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f1ac8d5ca3b3419df6433a690e989d\transformed\jetified-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f1ac8d5ca3b3419df6433a690e989d\transformed\jetified-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e136b98f43154d24eff7db764b8dee5a\transformed\lifecycle-runtime-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e136b98f43154d24eff7db764b8dee5a\transformed\lifecycle-runtime-2.9.2\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24eca39a1384e357709d100b7cf3d5e\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b24eca39a1384e357709d100b7cf3d5e\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3000334ecfada1bb421546f8a4c74d\transformed\jetified-lifecycle-service-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3000334ecfada1bb421546f8a4c74d\transformed\jetified-lifecycle-service-2.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a08bef2f621cd7aabf1e94037b974fe3\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a08bef2f621cd7aabf1e94037b974fe3\transformed\lifecycle-livedata-core-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c1c88644ecadc661acde7f5441b4be6\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c1c88644ecadc661acde7f5441b4be6\transformed\lifecycle-livedata-2.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\84683e4c011fea4c373f1ea8f17eaacf\transformed\jetified-lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\84683e4c011fea4c373f1ea8f17eaacf\transformed\jetified-lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d954572012be292b49e6401b9753243\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d954572012be292b49e6401b9753243\transformed\jetified-ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d2a2c826fb42e12e8b8ca37ee28485f\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d2a2c826fb42e12e8b8ca37ee28485f\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\784034ef828ed0e9f7e862d0f2163680\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\784034ef828ed0e9f7e862d0f2163680\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\194a2bee434b719060020e11cb51daad\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\194a2bee434b719060020e11cb51daad\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6619715ad669283d4bf5f8d7bf43fd5\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6619715ad669283d4bf5f8d7bf43fd5\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d70c2b4c64835a1332ec4b411c509383\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d70c2b4c64835a1332ec4b411c509383\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aefa536aed4635c8c80b7fc559dc303c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aefa536aed4635c8c80b7fc559dc303c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a831ad12897d5ff6823c40f2b91eb4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a831ad12897d5ff6823c40f2b91eb4\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b01bac89cef837b016a37a243dc8ab97\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b01bac89cef837b016a37a243dc8ab97\transformed\jetified-core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc1adff3b0b30d29c96d43ef3e07cb5d\transformed\jetified-flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc1adff3b0b30d29c96d43ef3e07cb5d\transformed\jetified-flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb2175d190bb14a01613832621ca821\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceb2175d190bb14a01613832621ca821\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\469e23a07d0e29759904aa58f72fc1a2\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\469e23a07d0e29759904aa58f72fc1a2\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c59885c9ad6cc2e227a8c18a7c243154\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c59885c9ad6cc2e227a8c18a7c243154\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dab60b6ec7b87ea8d13fc68233f87729\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dab60b6ec7b87ea8d13fc68233f87729\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3a881a6e2f556f31196d1e94b9b28c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3a881a6e2f556f31196d1e94b9b28c\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d98e0cc5d2de794b22df65bc82f67cf\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d98e0cc5d2de794b22df65bc82f67cf\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd98646fc2cb58b7d5d7e9fbe4955ed4\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd98646fc2cb58b7d5d7e9fbe4955ed4\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fdea3ea95bfda561b4265c5be2dedb6a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fdea3ea95bfda561b4265c5be2dedb6a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc07655ba36acd544196330469808a5f\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dc07655ba36acd544196330469808a5f\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\709d49b5a98f52acdc2ab493a38b5974\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\709d49b5a98f52acdc2ab493a38b5974\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\384da646fb352a19406fee696724f0d9\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\384da646fb352a19406fee696724f0d9\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2db25255f39a4b8148d9823a7d3e85e4\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2db25255f39a4b8148d9823a7d3e85e4\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e66c8a9d4313077a2f0d0043f5391159\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e66c8a9d4313077a2f0d0043f5391159\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc38323765f52088f2be80f3730d00b\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc38323765f52088f2be80f3730d00b\transformed\jetified-exoplayer-datasource-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\493390f283f7d835e771e48835a9ed0f\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\493390f283f7d835e771e48835a9ed0f\transformed\jetified-exoplayer-extractor-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd70b21072f4226bfc46625a814f65ce\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd70b21072f4226bfc46625a814f65ce\transformed\jetified-exoplayer-decoder-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e032c5a6b90c4b4b993235e24bde1e72\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e032c5a6b90c4b4b993235e24bde1e72\transformed\jetified-exoplayer-database-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9587b8c6beee5f14f7ef29f6db7f9966\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9587b8c6beee5f14f7ef29f6db7f9966\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\854dd7ced34e61634eb6685e542c79a3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\854dd7ced34e61634eb6685e542c79a3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b6e838136e2ef6f996eb2b96881c66a\transformed\jetified-firebase-components-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b6e838136e2ef6f996eb2b96881c66a\transformed\jetified-firebase-components-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5c5d2a36df431e38409576891336cb0\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5c5d2a36df431e38409576891336cb0\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.tencent.bugly:crashreport:4.1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece97ebe988a73d09b112128f40951e\transformed\jetified-crashreport-4.1.9.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.tencent.bugly:crashreport:4.1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece97ebe988a73d09b112128f40951e\transformed\jetified-crashreport-4.1.9.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.volley:volley:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\374355ca82ecf06e73f6cc08b444889c\transformed\jetified-volley-1.2.0\AndroidManifest.xml:5:5-43
MERGED from [com.android.volley:volley:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\374355ca82ecf06e73f6cc08b444889c\transformed\jetified-volley-1.2.0\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed672f713736551646e02791405a5923\transformed\jetified-play-services-ads-24.5.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\src\main\AndroidManifest.xml
activity#com.google.android.ads.mediationtestsuite.activities.HomeActivity
ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:10:9-15:70
	android:screenOrientation
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:14:13-49
	android:label
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:13:13-49
	android:exported
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:12:13-37
	android:theme
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:15:13-67
	android:name
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:11:13-93
activity#com.google.android.ads.mediationtestsuite.activities.NetworkDetailActivity
ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:16:9-21:63
	android:screenOrientation
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:19:13-49
	android:windowSoftInputMode
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:21:13-60
	android:exported
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:20:13-58
	android:name
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:17:13-102
activity#com.google.android.ads.mediationtestsuite.activities.ConfigurationItemDetailActivity
ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:22:9-27:73
	android:screenOrientation
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:26:13-49
	android:label
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:25:13-63
	android:exported
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:24:13-37
	android:theme
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:27:13-70
	android:name
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:23:13-112
activity#com.google.android.ads.mediationtestsuite.activities.ConfigurationItemsSearchActivity
ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:28:9-32:73
	android:screenOrientation
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:31:13-49
	android:exported
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:32:13-70
	android:name
		ADDED from [com.google.android.ads:mediation-test-suite:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\AndroidManifest.xml:29:13-113
activity#com.android.billingclient.api.ProxyBillingActivity
ADDED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:9-20:75
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:26:9-30:75
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:26:9-30:75
	android:exported
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:29:13-37
	android:configChanges
		ADDED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-96
	android:theme
		ADDED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-72
	android:name
		ADDED from [:GPLibrary] E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\GPLibrary\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-78
queries
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:17:5-23:15
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:12:5-19:15
MERGED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:12:5-19:15
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:15:5-20:15
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:15:5-20:15
intent#action:name:android.intent.action.ACTION_VIEW+data:scheme:https
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:18:9-22:18
action#android.intent.action.ACTION_VIEW
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:19:13-72
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:19:21-69
activity#com.chartboost.sdk.view.CBImpressionActivity
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:26:9-33:80
	android:excludeFromRecents
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:30:13-46
	android:hardwareAccelerated
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:32:13-47
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:31:13-37
	android:configChanges
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:28:13-106
	android:theme
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:33:13-77
	android:enableOnBackInvokedCallback
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:29:13-55
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:27:13-72
activity#com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:34:9-40:86
	android:excludeFromRecents
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:37:13-46
	android:hardwareAccelerated
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:39:13-47
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:36:13-74
	android:theme
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:40:13-83
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:35:13-92
activity#com.chartboost.sdk.view.FullscreenAdActivity
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:41:9-48:72
	android:excludeFromRecents
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:45:13-46
	android:hardwareAccelerated
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:47:13-47
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:46:13-37
	android:configChanges
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:43:13-106
	android:theme
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:48:13-69
	android:enableOnBackInvokedCallback
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:44:13-55
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:42:13-72
service#com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:51:9-61:19
	android:exported
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:53:13-37
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:52:13-113
intent-filter#action:name:com.google.android.exoplayer.downloadService.action.RESTART+category:name:android.intent.category.DEFAULT
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:56:13-60:29
action#com.google.android.exoplayer.downloadService.action.RESTART
ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:57:17-102
	android:name
		ADDED from [com.chartboost:chartboost-sdk:9.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965418c81afb566ff94177c831282f71\transformed\jetified-chartboost-sdk-9.9.0\AndroidManifest.xml:57:25-99
intent#action:name:com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:13:9-15:18
action#com.android.vending.billing.InAppBillingService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:14:13-91
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:14:21-88
intent#action:name:com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:16:9-18:18
action#com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:17:13-116
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:17:21-113
meta-data#com.google.android.play.billingclient.version
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:22:9-24:37
	android:value
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:24:13-34
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:23:13-73
activity#com.android.billingclient.api.ProxyBillingActivityV2
ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:31:9-35:75
	android:exported
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:33:13-96
	android:theme
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:35:13-72
	android:name
		ADDED from [com.android.billingclient:billing:8.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\AndroidManifest.xml:32:13-80
activity#com.adcolony.sdk.AdColonyInterstitialActivity
ADDED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:10:9-14:50
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:23:9-26:50
MERGED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:23:9-26:50
	android:hardwareAccelerated
		ADDED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:14:13-47
	android:exported
		ADDED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:13:13-37
	android:configChanges
		ADDED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:12:13-74
	android:name
		ADDED from [com.google.ads.mediation:adcolony:4.8.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\890eb394df5932414a60b08b2d676356\transformed\jetified-adcolony-4.8.0.2\AndroidManifest.xml:11:13-73
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:28:5-97
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:28:22-94
uses-permission#amazon.browser.video.downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:28:5-97
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [com.google.android.gms:play-services-analytics-impl:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\AndroidManifest.xml:28:22-94
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\AndroidManifest.xml:40:13-87
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:27:22-79
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:31:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\AndroidManifest.xml:34:17-139
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:38:9-44:18
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:63:21-62
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\AndroidManifest.xml:111:13-83
package#com.android.vending
ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:16:9-55
	android:name
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:16:18-52
package#com.amazon.venezia
ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:17:9-54
	android:name
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:17:18-51
package#com.sec.android.app.samsungapps
ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:18:9-67
	android:name
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:18:18-64
package#com.huawei.appmarket
ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:19:9-56
	android:name
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:19:18-53
activity#com.adcolony.sdk.AdColonyAdViewActivity
ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:27:9-30:50
	android:hardwareAccelerated
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:30:13-47
	android:configChanges
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:29:13-74
	android:name
		ADDED from [com.adcolony:sdk:4.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\AndroidManifest.xml:28:13-67
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273eb1077d0c9744d7418b6f87d6f925\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07b91b8d43ba5e38477f6b51b29ec0b9\transformed\jetified-firebase-installations-19.0.0\AndroidManifest.xml:19:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\AndroidManifest.xml:36:17-109
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a6b131a8bf533c615f808cf4d0393ac\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#amazon.browser.video.downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca0ea377a588f12229556ebd85b7720a\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
provider#com.squareup.picasso.PicassoProvider
ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:8:9-11:40
	android:authorities
		ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [com.squareup.picasso:picasso:2.71828] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\AndroidManifest.xml:9:13-64
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\939626039f77264d23690def85bc4203\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a9fdcb3a49a1932ffb67e10277d48f4\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
