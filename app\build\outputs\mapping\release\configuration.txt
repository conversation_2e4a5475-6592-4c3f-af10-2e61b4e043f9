# The proguard configuration file for the following section is E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class amazon.browser.lionpro.primary.AcyEvaluate { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyIntent { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyMain { <init>(); }
-keep class amazon.browser.lionpro.primary.AcySetting { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyStorage { <init>(); }
-keep class amazon.browser.lionpro.primary.AcyWifiShare { <init>(); }
-keep class amazon.browser.lionpro.primary.LookPicture { <init>(); }
-keep class amazon.browser.lionpro.primary.LookVideo { <init>(); }
-keep class amazon.browser.lionpro.primary.M3u8MergeServer { <init>(); }
-keep class amazon.browser.lionpro.primary.Mainly { <init>(); }
-keep class amazon.browser.lionpro.primary.MoreApps { <init>(); }
-keep class amazon.browser.lionpro.primary.SerMain { <init>(); }
-keep class amazon.browser.lionpro.primary.SerWifiShare { <init>(); }
-keep class amazon.browser.lionpro.primary.UpdateActivity { <init>(); }
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.core.content.FileProvider { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.adcolony.sdk.AdColonyAdViewActivity { <init>(); }
-keep class com.adcolony.sdk.AdColonyInterstitialActivity { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivity { <init>(); }
-keep class com.android.billingclient.api.ProxyBillingActivityV2 { <init>(); }
-keep class com.chartboost.sdk.internal.clickthrough.EmbeddedBrowserActivity { <init>(); }
-keep class com.chartboost.sdk.internal.video.repository.exoplayer.VideoRepositoryDownloadService { <init>(); }
-keep class com.chartboost.sdk.view.CBImpressionActivity { <init>(); }
-keep class com.chartboost.sdk.view.FullscreenAdActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.ConfigurationItemDetailActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.ConfigurationItemsSearchActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.HomeActivity { <init>(); }
-keep class com.google.android.ads.mediationtestsuite.activities.NetworkDetailActivity { <init>(); }
-keep class com.google.android.datatransport.runtime.backends.TransportBackendDiscovery { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver { <init>(); }
-keep class com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService { <init>(); }
-keep class com.google.android.gms.ads.AdActivity { <init>(); }
-keep class com.google.android.gms.ads.AdService { <init>(); }
-keep class com.google.android.gms.ads.MobileAdsInitProvider { <init>(); }
-keep class com.google.android.gms.ads.NotificationHandlerActivity { <init>(); }
-keep class com.google.android.gms.ads.OutOfContextTestingActivity { <init>(); }
-keep class com.google.android.gms.auth.api.signin.RevocationBoundService { <init>(); }
-keep class com.google.android.gms.auth.api.signin.internal.SignInHubActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementJobService { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementReceiver { <init>(); }
-keep class com.google.android.gms.measurement.AppMeasurementService { <init>(); }
-keep class com.google.firebase.components.ComponentDiscoveryService { <init>(); }
-keep class com.google.firebase.provider.FirebaseInitProvider { <init>(); }
-keep class com.squareup.picasso.PicassoProvider { <init>(); }
-keep class gp.BillingActivity { <init>(); }
-keep class lion.PlayActivity { <init>(); }
-keep class amazon.browser.lionpro.toys.CommonBackButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class amazon.browser.lionpro.views.HorizontalProgressBarWithNumber { <init>(android.content.Context, android.util.AttributeSet); }

-keep class amazon.browser.lionpro.views.KProgressHUD.BackgroundLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class amazon.browser.lionpro.views.ProgressBar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class android.widget.Space { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.app.AlertController$RecycleListView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ActionMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ExpandedMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.view.menu.ListMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContainer { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarContextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionBarOverlayLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActionMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ActivityChooserView$InnerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AlertDialogLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.AppCompatTextView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ButtonBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ContentFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.DialogTitle { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.FitWindowsLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.SearchView$SearchAutoComplete { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.Toolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.appcompat.widget.ViewStubCompat { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.cardview.widget.CardView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.helper.widget.Flow { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.constraintlayout.widget.ConstraintLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.coordinatorlayout.widget.CoordinatorLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.core.widget.NestedScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.fragment.app.FragmentContainerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.AspectRatioFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.SubtitleView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.media3.ui.TrackSelectionView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.recyclerview.widget.RecyclerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.viewpager.widget.ViewPager { <init>(android.content.Context, android.util.AttributeSet); }

-keep class androidx.viewpager2.widget.ViewPager2 { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.flexbox.FlexboxLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.gms.ads.nativead.MediaView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.gms.ads.nativead.NativeAdView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.AppBarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.CollapsingToolbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.appbar.MaterialToolbar { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.button.MaterialButtonToggleGroup { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.chip.Chip { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.datepicker.MaterialCalendarGridView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.BaselineLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.CheckableImageButton { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.ClippableRoundedCornerLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuItemView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.NavigationMenuView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.internal.TouchObserverFrameLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.Snackbar$SnackbarLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.snackbar.SnackbarContentLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.tabs.TabLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputEditText { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.textfield.TextInputLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ChipTextInputComboView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockFaceView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.ClockHandView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.google.android.material.timepicker.TimePickerView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.ActivityScreenShotImageView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.BottomDialogScrollView { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.DialogXBaseRelativeLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.MaxLinearLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.MaxRelativeLayout { <init>(android.content.Context, android.util.AttributeSet); }

-keep class com.kongzue.dialogx.util.views.PopMenuListView { <init>(android.content.Context, android.util.AttributeSet); }

-keepclassmembers class * { *** showP(android.view.View); }


# End of content from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\build\intermediates\default_proguard_files\global\proguard-android.txt-8.11.1
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize steps (and performs some
# of these optimizations on its own).
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.
-dontoptimize

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\build\intermediates\default_proguard_files\global\proguard-android.txt-8.11.1
# The proguard configuration file for the following section is E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\proguard-rules.pro
# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in E:\android-studio\SDK/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-optimizationpasses 5          # 指定代码的压缩级别
-dontusemixedcaseclassnames   # 是否使用大小写混合
-dontpreverify           # 混淆时是否做预校验
-verbose                # 混淆时是否记录日志

-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*  # 混淆时所采用的算法

-keep public class * extends android.app.Activity      # 保持哪些类不被混淆
-keep public class * extends android.app.Application   # 保持哪些类不被混淆
-keep public class * extends android.app.Service       # 保持哪些类不被混淆
-keep public class * extends android.content.BroadcastReceiver  # 保持哪些类不被混淆
-keep public class * extends android.content.ContentProvider    # 保持哪些类不被混淆
-keep public class * extends android.app.backup.BackupAgentHelper # 保持哪些类不被混淆
-keep public class * extends android.preference.Preference        # 保持哪些类不被混淆
#-keep public class com.android.vending.licensing.ILicensingService    # 保持哪些类不被混淆
#-keep public class java.net { *; }

#-keep public class io.vov.vitamio.MediaPlayer { *; }
#-keep public class io.vov.vitamio.IMediaScannerService { *; }
#-keep public class io.vov.vitamio.MediaScanner { *; }
#-keep public class io.vov.vitamio.MediaScannerClient { *; }
#-keep public class io.vov.vitamio.VitamioLicense { *; }
#-keep public class io.vov.vitamio.Vitamio { *; }
#-keep public class io.vov.vitamio.MediaMetadataRetriever { *; }

#-keep class io.vov.vitamio.activity.** { *; }
#-keep class io.vov.vitamio.utils.** { *; }
#-keep class io.vov.vitamio.widget.** { *; }
#-keep class io.vov.vitamio.** { *; }
#-keep class org.apache.** {*;}
#-keep class com.netease.nis.bugrpt.** {*;}
-dontwarn com.sun.crypto.provider.**
#-keep class com.sun.crypto.provider.** { *;}

-printmapping mapping.txt
-keepattributes Annotation
-keepattributes JavascriptInterface
-keep class android.webkit.JavascriptInterface {*;}

-keep public class lion.widget$JSVideoTag

-keep class lion.widget$JsInteration {
    *;
}
-keepclassmembers class lion.widget$JSVideoTag { *; }
-keepclassmembers class * implements lion.widget.JSVideoTag {
    *;
}

-keep public class * implements lion.widget.JSVideoTag{*;}
-keepclassmembers class * implements lion.widget.JSVideoTag {
    *;
}


#-keep class com.aladdin.video.widgets.NBrowserTaber{*;}
#-keepclassmembers class com.aladdin.video.widgets.NBrowserTaber {
#    *;
#}
#-keep class com.aladdin.video.widgets.NBrowserTaber$JSVideoTag{*;}
#-keepclassmembers class com.aladdin.video.widgets.NBrowserTaber$JSVideoTag {
#    public *;
#}


-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

-keep public class lionpro.R$*{
public static final int *;
}
-keep class com.android.vending.billing.*
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#-dontwarn com.flurry.sdk.**
#-keep class com.flurry.sdk.* { *;}

#-keep class com.noqoush.adfalcon.android.sdk.** {*;}
#-keep class com.google.ads.mediation.adfalcon.** {*;}
-keep public class com.google.android.gms.ads.* {
 public *;
}
-keep public class com.google.ads.* {
 public *;
}


#-keepattributes SourceFile,LineNumberTable
#-keep class com.inmobi.** { *; }
#-dontwarn com.inmobi.**
#-keep public class com.google.android.gms.**
#-dontwarn com.google.android.gms.**
#-dontwarn com.squareup.picasso.**
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient{
#     public *;
#}
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info{
#     public *;
#}
# skip the Picasso library classes
-keep class com.squareup.picasso.* {*;}
-dontwarn com.squareup.picasso.**
-dontwarn com.squareup.okhttp.**
# skip Moat classes
-keep class com.moat.* {*;}
-dontwarn com.moat.**
# skip AVID classes
-keep class com.integralads.avid.library.* {*;}

# For communication with AdColony's WebView
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep ADCNative class members unobfuscated
#-keepclassmembers class com.adcolony.sdk.ADCNative** {
#    *;
# }


-keep class com.samsung.* {*;}
-dontwarn com.samsung.**
-printmapping mapping.txt

-keepattributes SourceFile,LineNumberTable
-keep class com.inmobi.* { *; }
-dontwarn com.inmobi.**
-keep public class com.google.android.gms.*
-dontwarn com.google.android.gms.**
-dontwarn com.squareup.picasso.**
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient{public *;}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient.*{public *;}
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info{public *;}
#skip the Picasso library classes
-keep class com.squareup.picasso.* {*;}
-dontwarn com.squareup.picasso.**
-dontwarn com.squareup.okhttp.**
#skip Moat classes
-keep class com.moat.* {*;}
-dontwarn com.moat.**
#skip AVID classes
-keep class com.integralads.avid.library.* {*;}

-keep public class com.google.android.gms.ads.* {public *;}
-keep class com.facebook.ads.NativeAd


-keep class com.duapps.ad.*{*;}
-dontwarn com.duapps.ad.**
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
        @com.google.android.gms.common.annotation.KeepName *;}
-keep class com.google.android.gms.common.GooglePlayServicesUtil {
      public <methods>;}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient* {
      public <methods>;}
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
#      public <methods>;}

-keep public class * extends androidx.versionedparcelable.VersionedParcelable {
<init>();
}

-keep class com.coder.ffmpeg.** {*;}
-dontwarn  com.coder.ffmpeg.**
-dontwarn org.jspecify.nullness.Nullable

-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}


-dontwarn com.google.protobuf.java_com_google_android_gmscore_sdk_target_granule__proguard_group_gtm_N1281923064GeneratedExtensionRegistryLite$Loader
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.OpenSSLProvider

# 保护 registerReceiver 方法不被混淆，避免 API 兼容性问题
-keep class android.content.Context {
    public android.content.Intent registerReceiver(android.content.BroadcastReceiver, android.content.IntentFilter);
    public android.content.Intent registerReceiver(android.content.BroadcastReceiver, android.content.IntentFilter, int);
}

# 保护 AcyMain 中的关键方法
-keep class amazon.browser.lionpro.primary.AcyMain {
    public void onCreate(android.os.Bundle);
}
# End of content from E:\works\Windows\SoftWare\Developer\WorkSpace\Android\FD_CN_PLAY_X_MediaStore\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\daa6ace68e4e58e72b5da1cae7619629\transformed\jetified-DialogX-0.0.49.beta18\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\daa6ace68e4e58e72b5da1cae7619629\transformed\jetified-DialogX-0.0.49.beta18\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\proguard.txt
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Keep public classes
-keep,includedescriptorclasses class com.google.android.ads.mediationtestsuite.MediationTestSuite { *; }
-keep,includedescriptorclasses class com.google.android.ads.mediationtestsuite.MediationTestSuiteListener { *; }
-keep,includedescriptorclasses class com.google.android.gms.ads.MobileAds  { *; }

-dontwarn org.xmlpull.v1.**
-dontnote org.xmlpull.v1.**
-keep class org.xmlpull.** { *; }

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**

# Application classes that will be serialized/deserialized over Gson
-keep,includedescriptorclasses class com.google.android.ads.mediationtestsuite.dataobjects.** { <fields>; }
-keep enum com.google.android.ads.mediationtestsuite.dataobjects.** { *; }

# Prevent proguard from stripping interface information from TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

##---------------End: proguard configuration for Gson  ----------


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\15cbbb23a6fcfc3123b16868a197155d\transformed\jetified-mediation-test-suite-3.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\01418f0bb56234be9291285c890b2581\transformed\jetified-FFmpegCommand-1.3.2\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\01418f0bb56234be9291285c890b2581\transformed\jetified-FFmpegCommand-1.3.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior
-keepattributes RuntimeVisible*Annotation*

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AppCompatViewInflater reads the viewInflaterClass theme attribute which then
# reflectively instantiates MaterialComponentsViewInflater using the no-argument
# constructor. We only need to keep this constructor and the class name if
# AppCompatViewInflater is also being kept.
-if class androidx.appcompat.app.AppCompatViewInflater
-keep class com.google.android.material.theme.MaterialComponentsViewInflater {
    <init>();
}


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e73db39cd306072120cdbfd44884a470\transformed\material-1.12.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0e1206851174c2a71fce0be4f3db2dd0\transformed\appcompat-1.7.1\proguard.txt
# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl* {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0e1206851174c2a71fce0be4f3db2dd0\transformed\appcompat-1.7.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\proguard.txt
# Keep the AIDL interface
-keep class com.android.vending.billing.** { *; }
-keep class com.google.android.apps.play.billingtestcompanion.aidl.** { *; }

-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.android.apps.common.proguard.UsedByReflection

-keepnames class com.android.billingclient.api.ProxyBillingActivity
-keepnames class com.android.billingclient.api.ProxyBillingActivityV2

# Avoids Proguard warning at build time due to Protobuf use of sun.misc.Unsafe
# and libcore.io.Memory which are available at runtime.
-dontwarn libcore.io.Memory
-dontwarn sun.misc.Unsafe


# For Phenotype
# An unused P/H transitive dependency: com.google.android.libraries.phenotype.registration.PhenotypeResourceReader is stripped out from all Granular normal deps and "can't find reference..." DepsVersionCompat test warning
# is suppressed by ProGuard -dontwarn config.
-dontwarn com.google.android.libraries.phenotype.registration.PhenotypeResourceReader
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Uses reflection to determine if these classes are present and has a graceful
# fallback if they aren't. The test failure it fixes appears to be caused by flogger.
-dontwarn dalvik.system.VMStack
-dontwarn com.google.common.flogger.backend.google.GooglePlatform
-dontwarn com.google.common.flogger.backend.system.DefaultPlatform
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.play_billing.zzfi {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\859174f3c5605121f1670cd82f1e1056\transformed\jetified-billing-8.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\proguard.txt
# Proguard rules specific to the core module.

# Constructors accessed via reflection in DefaultRenderersFactory
-dontnote androidx.media3.decoder.vp9.LibvpxVideoRenderer
-keepclassmembers class androidx.media3.decoder.vp9.LibvpxVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.av1.Libgav1VideoRenderer
-keepclassmembers class androidx.media3.decoder.av1.Libgav1VideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.ExperimentalFfmpegVideoRenderer {
  <init>(long, android.os.Handler, androidx.media3.exoplayer.video.VideoRendererEventListener, int);
}
-dontnote androidx.media3.decoder.opus.LibopusAudioRenderer
-keepclassmembers class androidx.media3.decoder.opus.LibopusAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.flac.LibflacAudioRenderer
-keepclassmembers class androidx.media3.decoder.flac.LibflacAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer
-keepclassmembers class androidx.media3.decoder.ffmpeg.FfmpegAudioRenderer {
  <init>(android.os.Handler, androidx.media3.exoplayer.audio.AudioRendererEventListener, androidx.media3.exoplayer.audio.AudioSink);
}
-dontnote androidx.media3.decoder.midi.MidiRenderer
-keepclassmembers class androidx.media3.decoder.midi.MidiRenderer {
  <init>(android.content.Context);
}

# Constructors accessed via reflection in DefaultDownloaderFactory
-dontnote androidx.media3.exoplayer.dash.offline.DashDownloader
-keepclassmembers class androidx.media3.exoplayer.dash.offline.DashDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.hls.offline.HlsDownloader
-keepclassmembers class androidx.media3.exoplayer.hls.offline.HlsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader
-keepclassmembers class androidx.media3.exoplayer.smoothstreaming.offline.SsDownloader {
  <init>(androidx.media3.common.MediaItem, androidx.media3.datasource.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}

# Constructors accessed via reflection in DefaultMediaSourceFactory
-dontnote androidx.media3.exoplayer.dash.DashMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.dash.DashMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.hls.HlsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.hls.HlsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.smoothstreaming.SsMediaSource$Factory {
  <init>(androidx.media3.datasource.DataSource$Factory);
}
-dontnote androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory
-keepclasseswithmembers class androidx.media3.exoplayer.rtsp.RtspMediaSource$Factory {
  <init>();
}

# Constructors and methods accessed via reflection in CompositingVideoSinkProvider
-dontnote androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory
-keepclasseswithmembers class androidx.media3.effect.PreviewingSingleInputVideoGraph$Factory {
  <init>(androidx.media3.common.VideoFrameProcessor$Factory);
}
-dontnote androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder
-keepclasseswithmembers class androidx.media3.effect.DefaultVideoFrameProcessor$Factory$Builder {
  androidx.media3.effect.DefaultVideoFrameProcessor$Factory build();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\78e269c0a898a6a4ecb8e2ff14202efd\transformed\jetified-media3-exoplayer-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4800a7c071882b7f0431d2ffa07290b0\transformed\jetified-media3-extractor-1.4.1\proguard.txt
# Proguard rules specific to the extractor module.

# Methods accessed via reflection in DefaultExtractorsFactory
-dontnote androidx.media3.decoder.flac.FlacExtractor
-keepclassmembers class androidx.media3.decoder.flac.FlacExtractor {
  <init>(int);
}
-dontnote androidx.media3.decoder.flac.FlacLibrary
-keepclassmembers class androidx.media3.decoder.flac.FlacLibrary {
  public static boolean isAvailable();
}
-dontnote androidx.media3.decoder.midi.MidiExtractor
-keepclassmembers class androidx.media3.decoder.midi.MidiExtractor {
  <init>();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4800a7c071882b7f0431d2ffa07290b0\transformed\jetified-media3-extractor-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b31b837fcd90eb33f1b29127e8d2c017\transformed\jetified-media3-datasource-1.4.1\proguard.txt
# Proguard rules specific to the DataSource module.

# Constant folding for resource integers may mean that a resource passed to this method appears to be unused. Keep the method to prevent this from happening.
-keepclassmembers class androidx.media3.datasource.RawResourceDataSource {
  public static android.net.Uri buildRawResourceUri(int);
}

# Constructors accessed via reflection in DefaultDataSource
-dontnote androidx.media3.datasource.rtmp.RtmpDataSource
-keepclassmembers class androidx.media3.datasource.rtmp.RtmpDataSource {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b31b837fcd90eb33f1b29127e8d2c017\transformed\jetified-media3-datasource-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\proguard.txt
# Proguard rules specific to the common module.

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# From https://github.com/google/guava/wiki/UsingProGuardWithGuava
-dontwarn java.lang.ClassValue
-dontwarn java.lang.SafeVarargs
-dontwarn javax.lang.model.element.Modifier
-dontwarn sun.misc.Unsafe

# Don't warn about Guava's compile-only dependencies.
# These lines are needed for ProGuard but not R8.
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.j2objc.annotations.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Workaround for https://issuetracker.google.com/issues/112297269
# This is needed for ProGuard but not R8.
-keepclassmembernames class com.google.common.base.Function { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\40d3f1c77669f17642539f808b9322a4\transformed\jetified-media3-common-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\557da46f2cf0492a19c1a7f84c73b5fc\transformed\jetified-media3-ui-1.4.1\proguard.txt
# Proguard rules specific to the UI module.

# Constructor method and classes accessed via reflection in PlayerView
-dontnote androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView
-keepclassmembers class androidx.media3.exoplayer.video.spherical.SphericalGLSurfaceView {
  <init>(android.content.Context);
}
-dontnote androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView
-keepclassmembers class androidx.media3.exoplayer.video.VideoDecoderGLSurfaceView {
  <init>(android.content.Context);
}
-keepnames class androidx.media3.exoplayer.ExoPlayer {}
-keepclassmembers class androidx.media3.exoplayer.ExoPlayer {
  void setImageOutput(androidx.media3.exoplayer.image.ImageOutput);
}
-keepclasseswithmembers class androidx.media3.exoplayer.image.ImageOutput {
  void onImageAvailable(long, android.graphics.Bitmap);
}

# Constructor method accessed via reflection in TrackSelectionDialogBuilder
-dontnote androidx.appcompat.app.AlertDialog.Builder
-keepclassmembers class androidx.appcompat.app.AlertDialog$Builder {
  <init>(android.content.Context, int);
  public android.content.Context getContext();
  public androidx.appcompat.app.AlertDialog$Builder setTitle(java.lang.CharSequence);
  public androidx.appcompat.app.AlertDialog$Builder setView(android.view.View);
  public androidx.appcompat.app.AlertDialog$Builder setPositiveButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog$Builder setNegativeButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog create();
}
# Equivalent methods needed when the library is de-jetified.
-dontnote androidx.appcompat.app.AlertDialog.Builder
-keepclassmembers class androidx.appcompat.app.AlertDialog$Builder {
  <init>(android.content.Context, int);
  public android.content.Context getContext();
  public androidx.appcompat.app.AlertDialog$Builder setTitle(java.lang.CharSequence);
  public androidx.appcompat.app.AlertDialog$Builder setView(android.view.View);
  public androidx.appcompat.app.AlertDialog$Builder setPositiveButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog$Builder setNegativeButton(int, android.content.DialogInterface$OnClickListener);
  public androidx.appcompat.app.AlertDialog create();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\557da46f2cf0492a19c1a7f84c73b5fc\transformed\jetified-media3-ui-1.4.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4781b906e97d6a95ac2d27eac821fe0a\transformed\recyclerview-1.4.0\proguard.txt
# Copyright (C) 2015 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# When layoutManager xml attribute is used, RecyclerView inflates
#LayoutManagers' constructors using reflection.
-keep public class * extends androidx.recyclerview.widget.RecyclerView$LayoutManager {
    public <init>(android.content.Context, android.util.AttributeSet, int, int);
    public <init>();
}

-keepclassmembers class androidx.recyclerview.widget.RecyclerView {
    public void suppressLayout(boolean);
    public boolean isLayoutSuppressed();
}
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4781b906e97d6a95ac2d27eac821fe0a\transformed\recyclerview-1.4.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\ed672f713736551646e02791405a5923\transformed\jetified-play-services-ads-24.5.0\proguard.txt
-keep public class com.google.android.gms.ads.internal.ClientApi {
  <init>();
}

# When built for Android API level < 30, Proguard warns that it can't find
# android.telephony.TelephonyDisplayInfo (since it was added only in API level
# 30). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.telephony.TelephonyDisplayInfo

# When built for Android API level < 30, Proguard warns that it can't find
# android.view.Surface#setFrameRate(float, int) (since it was added only in API
# level 30). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.view.Surface

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.ApplicationMediaCapabilities (since it was added only in API
# level 31). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.ApplicationMediaCapabilities

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.MediaFeature (since it was added only in API level 31). But,
# all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.MediaFeature

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.ApplicationMediaCapabilities$Builder (since it was added only in
# API level 31). But, all its usages are guarded by runtime checks of the API
# level. Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.ApplicationMediaCapabilities$Builder

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.MediaFeature$HdrType (since it was added only in API level 31).
# But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.MediaFeature$HdrType

# When built for Android API level < 32, Proguard warns that it can't find
# android.media.AudioAttributes$Builder (since it was added only in API level
# 32). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.AudioAttributes$Builder

# When built for Android API level < 33, Proguard warns that it can't find
# android.adservices.measurement.MeasurementManager (since it was added only
# in API level 33). But, all its usages are guarded by runtime checks of the
# API level. Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.adservices.measurement.MeasurementManager

# When built for Android API level < 33, Proguard warns that it can't find
# javax.lang.model.element.Modifier (since it was added only in API level 33).
# But, all its usages are guarded by runtime checks of the API level. Hence, it
# is safe to suppress Proguard's warnings.
-dontwarn javax.lang.model.element.Modifier

# These are checked at runtime for whether they exist, so it is fine if the API level doesn't include these.
-dontwarn android.content.pm.ApkChecksum
-dontwarn android.content.pm.PackageManager$OnChecksumsReadyListener
# Only for the requestChecksums method, but sadly -dontwarn can't take just a single method.
-dontwarn android.content.pm.PackageManager

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.ads.zzgzh {
  <fields>;
}

# Auto-generated proguard rule(s) with obfuscated symbol
-dontwarn com.google.android.gms.ads.internal.util.zzx


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\ed672f713736551646e02791405a5923\transformed\jetified-play-services-ads-24.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.gtm.zzacf {
  <fields>;
}

-dontwarn dalvik.system.VMStack
-dontwarn com.google.common.flogger.backend.google.GooglePlatform
-dontwarn com.google.common.flogger.backend.system.DefaultPlatform
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\19f0bc2e70a6c825005c4c13694ee0ac\transformed\jetified-play-services-analytics-impl-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmf {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\965a982e0045a0a03473145ff3b81dbc\transformed\jetified-play-services-measurement-23.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\proguard.txt
# Can be removed once we pull in a dependency on firebase-common that includes
# https://github.com/firebase/firebase-android-sdk/pull/1472/commits/856f1ca1151cdd88679bbc778892f23dfa34fc06#diff-a2ed34b5a38b4c6c686b09e54865eb48
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmf {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\bd2602adb8d7fdaae1370f49b9169a1b\transformed\jetified-play-services-measurement-api-23.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmf {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5154df2ba166f918cd2a11b5d9ec6b\transformed\jetified-play-services-measurement-sdk-23.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmf {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4be3a0315bf8b7e1c0fcbfb095f7f443\transformed\jetified-play-services-measurement-impl-23.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\proguard.txt
# Keep implementations of the AdMob mediation adapter interfaces. Adapters for
# third party ad networks implement these interfaces and are invoked by the
# AdMob SDK via reflection.

-keep class * implements com.google.android.gms.ads.mediation.MediationAdapter {
  public *;
}
-keep class * implements com.google.ads.mediation.MediationAdapter {
  public *;
}
-keep class * implements com.google.android.gms.ads.mediation.customevent.CustomEvent {
  public *;
}
-keep class * implements com.google.ads.mediation.customevent.CustomEvent {
  public *;
}
-keep class * extends com.google.android.gms.ads.mediation.MediationAdNetworkAdapter {
  public *;
}
-keep class * extends com.google.android.gms.ads.mediation.Adapter {
  public *;
}

# Keep classes used for offline ads created by reflection. WorkManagerUtil is
# created reflectively by callers within GMSCore and OfflineNotificationPoster
# is created reflectively by WorkManager.
-keep class com.google.android.gms.ads.internal.util.WorkManagerUtil {
  public *;
}
-keep class com.google.android.gms.ads.internal.offline.buffering.OfflineNotificationPoster {
  public *;
}
-keep class com.google.android.gms.ads.internal.offline.buffering.OfflinePingSender {
  public *;
}

# Keeps the entry for full SDK to access via reflection.
-keep class com.google.android.gms.ads.internal.client.LiteSdkInfo {
  public *;
}

# Keeps the entry for first party plugins to access via reflection.
-keep class com.google.android.gms.ads.MobileAds {
  private void setPlugin(java.lang.String);
}

# Keep recordEvent API for Immersive SDK to access via reflection.
-keep class com.google.android.gms.ads.nativead.NativeAd {
  protected abstract void recordEvent(android.os.Bundle);
}

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.ads.zzgzh {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\904936832aa181f4d19f0014d9ae7021\transformed\jetified-play-services-ads-api-24.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\proguard.txt
# For communication with AdColony's WebView
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\9b55abd956269bc900c079ce38d796e0\transformed\jetified-sdk-4.8.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e398d89bb150e4cfe04fce26b1b61714\transformed\jetified-play-services-auth-base-18.0.4\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.auth.zzeu {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e398d89bb150e4cfe04fce26b1b61714\transformed\jetified-play-services-auth-base-18.0.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\proguard.txt
# Methods enable and disable in this class are complained as unresolved
# references, but they are system APIs and are not used by Fido client apps.
-dontwarn android.nfc.NfcAdapter

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7867ab37732dec912dac9b2eff3a4c\transformed\jetified-play-services-fido-20.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\1be32509351a279272995f6537746fe8\transformed\jetified-play-services-base-18.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\proguard.txt
-dontwarn com.google.firebase.platforminfo.KotlinDetector
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\a561256fa35705ed298ded8513a351ba\transformed\jetified-firebase-common-22.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker (also marked with @Keep)
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\2d99b3e34ce7735a108df02a183b32b7\transformed\work-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8a98d538755981fae0f4832024731a\transformed\transition-1.5.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Keep a field in transition that is used to keep a reference to weakly-referenced object
-keepclassmembers class androidx.transition.ChangeBounds$* extends android.animation.AnimatorListenerAdapter {
  androidx.transition.ChangeBounds$ViewBounds mViewBounds;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\6b8a98d538755981fae0f4832024731a\transformed\transition-1.5.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0865b0d75bc612ae78a97d6ddf50fd7f\transformed\documentfile-1.1.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0865b0d75bc612ae78a97d6ddf50fd7f\transformed\documentfile-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\862a73af1f03ec6c8fb6641d4e1d39fa\transformed\webkit-1.11.0-alpha02\proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\862a73af1f03ec6c8fb6641d4e1d39fa\transformed\webkit-1.11.0-alpha02\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4d965e70b4d45c18dc52895768f044c4\transformed\coordinatorlayout-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4d965e70b4d45c18dc52895768f044c4\transformed\coordinatorlayout-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\33367b0e0354e6e37c04c83d715464b3\transformed\jetified-glide-4.16.0\proguard.txt
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

# Uncomment for DexGuard only
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\33367b0e0354e6e37c04c83d715464b3\transformed\jetified-glide-4.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\3b265b12e5ec5acbaeef44103a7c75d2\transformed\vectordrawable-animated-1.1.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# keep setters in VectorDrawables so that animations can still work.
-keepclassmembers class androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$* {
   void set*(***);
   *** get*();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\3b265b12e5ec5acbaeef44103a7c75d2\transformed\vectordrawable-animated-1.1.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\proguard.txt
# Proguard rules specific to the core module.

# Constructors accessed via reflection in DefaultRenderersFactory
-dontnote com.google.android.exoplayer2.ext.vp9.LibvpxVideoRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.vp9.LibvpxVideoRenderer {
  <init>(long, android.os.Handler, com.google.android.exoplayer2.video.VideoRendererEventListener, int);
}
-dontnote com.google.android.exoplayer2.ext.av1.Libgav1VideoRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.av1.Libgav1VideoRenderer {
  <init>(long, android.os.Handler, com.google.android.exoplayer2.video.VideoRendererEventListener, int);
}
-dontnote com.google.android.exoplayer2.ext.opus.LibopusAudioRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.opus.LibopusAudioRenderer {
  <init>(android.os.Handler, com.google.android.exoplayer2.audio.AudioRendererEventListener, com.google.android.exoplayer2.audio.AudioSink);
}
-dontnote com.google.android.exoplayer2.ext.flac.LibflacAudioRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.flac.LibflacAudioRenderer {
  <init>(android.os.Handler, com.google.android.exoplayer2.audio.AudioRendererEventListener, com.google.android.exoplayer2.audio.AudioSink);
}
-dontnote com.google.android.exoplayer2.ext.ffmpeg.FfmpegAudioRenderer
-keepclassmembers class com.google.android.exoplayer2.ext.ffmpeg.FfmpegAudioRenderer {
  <init>(android.os.Handler, com.google.android.exoplayer2.audio.AudioRendererEventListener, com.google.android.exoplayer2.audio.AudioSink);
}

# Constructors accessed via reflection in DefaultDownloaderFactory
-dontnote com.google.android.exoplayer2.source.dash.offline.DashDownloader
-keepclassmembers class com.google.android.exoplayer2.source.dash.offline.DashDownloader {
  <init>(com.google.android.exoplayer2.MediaItem, com.google.android.exoplayer2.upstream.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote com.google.android.exoplayer2.source.hls.offline.HlsDownloader
-keepclassmembers class com.google.android.exoplayer2.source.hls.offline.HlsDownloader {
  <init>(com.google.android.exoplayer2.MediaItem, com.google.android.exoplayer2.upstream.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}
-dontnote com.google.android.exoplayer2.source.smoothstreaming.offline.SsDownloader
-keepclassmembers class com.google.android.exoplayer2.source.smoothstreaming.offline.SsDownloader {
  <init>(com.google.android.exoplayer2.MediaItem, com.google.android.exoplayer2.upstream.cache.CacheDataSource$Factory, java.util.concurrent.Executor);
}

# Constructors accessed via reflection in DefaultMediaSourceFactory
-dontnote com.google.android.exoplayer2.source.dash.DashMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.dash.DashMediaSource$Factory {
  <init>(com.google.android.exoplayer2.upstream.DataSource$Factory);
}
-dontnote com.google.android.exoplayer2.source.hls.HlsMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.hls.HlsMediaSource$Factory {
  <init>(com.google.android.exoplayer2.upstream.DataSource$Factory);
}
-dontnote com.google.android.exoplayer2.source.smoothstreaming.SsMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.smoothstreaming.SsMediaSource$Factory {
  <init>(com.google.android.exoplayer2.upstream.DataSource$Factory);
}
-dontnote com.google.android.exoplayer2.source.rtsp.RtspMediaSource$Factory
-keepclasseswithmembers class com.google.android.exoplayer2.source.rtsp.RtspMediaSource$Factory {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c5abe1355f9d2cb76e0e536988b95446\transformed\jetified-exoplayer-core-2.18.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\83833e738d244b35a93c28ec7e9bf42d\transformed\media-1.7.0\proguard.txt
# Copyright (C) 2017 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent Parcelable objects from being removed or renamed.
-keep class android.support.v4.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Prevent Parcelable objects from being removed or renamed.
-keep class androidx.media.** implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\83833e738d244b35a93c28ec7e9bf42d\transformed\media-1.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\dd16d40402a318e32993919126063beb\transformed\core-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e838f687d051d86718ba5702c772c5\transformed\jetified-lifecycle-viewmodel-savedstate-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c1e838f687d051d86718ba5702c772c5\transformed\jetified-lifecycle-viewmodel-savedstate-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4e68fa80193914af8fba0b7ad4934f34\transformed\jetified-lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4e68fa80193914af8fba0b7ad4934f34\transformed\jetified-lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\f6fd51372c611a2567cd88e5c8985e18\transformed\jetified-lifecycle-livedata-core-ktx-2.9.2\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\f6fd51372c611a2567cd88e5c8985e18\transformed\jetified-lifecycle-livedata-core-ktx-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f1ac8d5ca3b3419df6433a690e989d\transformed\jetified-savedstate-release\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f1ac8d5ca3b3419df6433a690e989d\transformed\jetified-savedstate-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b24eca39a1384e357709d100b7cf3d5e\transformed\jetified-lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b24eca39a1384e357709d100b7cf3d5e\transformed\jetified-lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3000334ecfada1bb421546f8a4c74d\transformed\jetified-lifecycle-service-2.9.2\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\8b3000334ecfada1bb421546f8a4c74d\transformed\jetified-lifecycle-service-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\611cda7b11745a0eadb0454d7e763f91\transformed\jetified-lifecycle-process-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\a08bef2f621cd7aabf1e94037b974fe3\transformed\lifecycle-livedata-core-2.9.2\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\a08bef2f621cd7aabf1e94037b974fe3\transformed\lifecycle-livedata-core-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4c1c88644ecadc661acde7f5441b4be6\transformed\lifecycle-livedata-2.9.2\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4c1c88644ecadc661acde7f5441b4be6\transformed\lifecycle-livedata-2.9.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\f3f34b0b41f94b6cd3c8bc07c2a4898e\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\f3f34b0b41f94b6cd3c8bc07c2a4898e\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e7faefe48ae1e76dcdb0d4cf5e7dd872\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e7faefe48ae1e76dcdb0d4cf5e7dd872\transformed\rules\lib\META-INF\proguard\androidx.datastore_datastore-preferences-core.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\e773b6351cd2e43ea8caff4684cec063\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\e773b6351cd2e43ea8caff4684cec063\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\401090466158eb8f45d16e7aa1099a8b\transformed\jetified-play-services-tasks-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmf {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c0dd4a766d3799b94bc565db295605\transformed\jetified-play-services-measurement-sdk-api-23.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzmf {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\434eb06e298adbedc6716b5ef94f7c3c\transformed\jetified-play-services-measurement-base-23.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.**

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.annotations.NullMarked

# Annotations no longer exist. Suppression prevents ProGuard failures in
# SDKs which depend on earlier versions of play-services-basement.
-dontwarn com.google.android.gms.common.util.VisibleForTesting

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\c7c627f874de1b4c40ed8426fd444748\transformed\jetified-play-services-basement-18.7.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a831ad12897d5ff6823c40f2b91eb4\transformed\fragment-1.5.4\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The default FragmentFactory creates Fragment instances using reflection
-if public class ** extends androidx.fragment.app.Fragment
-keepclasseswithmembers,allowobfuscation public class <1> {
    public <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b0a831ad12897d5ff6823c40f2b91eb4\transformed\fragment-1.5.4\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\b01bac89cef837b016a37a243dc8ab97\transformed\jetified-core-ktx-1.16.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\b01bac89cef837b016a37a243dc8ab97\transformed\jetified-core-ktx-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\cc1adff3b0b30d29c96d43ef3e07cb5d\transformed\jetified-flexbox-3.0.0\proguard.txt
#
# Copyright 2016 Google Inc. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# The FlexboxLayoutManager may be set from a layout xml, in that situation the RecyclerView
# tries to instantiate the layout manager using reflection.
# This is to prevent the layout manager from being obfuscated.
-keepnames public class com.google.android.flexbox.FlexboxLayoutManager
# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\cc1adff3b0b30d29c96d43ef3e07cb5d\transformed\jetified-flexbox-3.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea406a55c6ff9bbd0003151150abcf\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\469e23a07d0e29759904aa58f72fc1a2\transformed\jetified-core-viewtree-1.0.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\469e23a07d0e29759904aa58f72fc1a2\transformed\jetified-core-viewtree-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\proguard.txt
### OKHTTP

# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote okhttp3.internal.Platform


### OKIO

# java.nio.file.* usage which cannot be used at runtime. Animal sniffer annotation.
-dontwarn okio.Okio
# JDK 7-only method which is @hide on Android. Animal sniffer annotation.
-dontwarn okio.DeflaterSink

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4153e0e8ff2862b9ff6b59fdbd2c35\transformed\jetified-picasso-2.71828\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\990924aaf14b4a1bace18820bce4f026\transformed\jetified-transport-backend-cct-3.1.8\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\dd98646fc2cb58b7d5d7e9fbe4955ed4\transformed\jetified-transport-api-3.0.0\proguard.txt
-dontwarn com.google.auto.value.AutoValue
-dontwarn com.google.auto.value.AutoValue$Builder

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\dd98646fc2cb58b7d5d7e9fbe4955ed4\transformed\jetified-transport-api-3.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\0540a3969ecaa041b9df3304b3bc4f31\transformed\jetified-startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\709d49b5a98f52acdc2ab493a38b5974\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\709d49b5a98f52acdc2ab493a38b5974\transformed\jetified-firebase-encoders-json-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\a68ec3a3ddfec9508c87b1dea8221f31\transformed\room-runtime-2.2.5\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc38323765f52088f2be80f3730d00b\transformed\jetified-exoplayer-datasource-2.18.7\proguard.txt
# Proguard rules specific to the DataSource module.

# Constant folding for resource integers may mean that a resource passed to this method appears to be unused. Keep the method to prevent this from happening.
-keepclassmembers class com.google.android.exoplayer2.upstream.RawResourceDataSource {
  public static android.net.Uri buildRawResourceUri(int);
}

# Constructors accessed via reflection in DefaultDataSource
-dontnote com.google.android.exoplayer2.ext.rtmp.RtmpDataSource
-keepclassmembers class com.google.android.exoplayer2.ext.rtmp.RtmpDataSource {
  <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\4dc38323765f52088f2be80f3730d00b\transformed\jetified-exoplayer-datasource-2.18.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\493390f283f7d835e771e48835a9ed0f\transformed\jetified-exoplayer-extractor-2.18.7\proguard.txt
# Proguard rules specific to the extractor module.

# Methods accessed via reflection in DefaultExtractorsFactory
-dontnote com.google.android.exoplayer2.ext.flac.FlacExtractor
-keepclassmembers class com.google.android.exoplayer2.ext.flac.FlacExtractor {
  <init>(int);
}
-dontnote com.google.android.exoplayer2.ext.flac.FlacLibrary
-keepclassmembers class com.google.android.exoplayer2.ext.flac.FlacLibrary {
  public static boolean isAvailable();
}

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\493390f283f7d835e771e48835a9ed0f\transformed\jetified-exoplayer-extractor-2.18.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\proguard.txt
# Proguard rules specific to the common module.

# Don't warn about checkerframework and Kotlin annotations
-dontwarn org.checkerframework.**
-dontwarn kotlin.annotations.jvm.**
-dontwarn javax.annotation.**

# From https://github.com/google/guava/wiki/UsingProGuardWithGuava
-dontwarn java.lang.ClassValue
-dontwarn java.lang.SafeVarargs
-dontwarn javax.lang.model.element.Modifier
-dontwarn sun.misc.Unsafe

# Don't warn about Guava's compile-only dependencies.
# These lines are needed for ProGuard but not R8.
-dontwarn com.google.errorprone.annotations.**
-dontwarn com.google.j2objc.annotations.**
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Workaround for https://issuetracker.google.com/issues/112297269
# This is needed for ProGuard but not R8.
-keepclassmembernames class com.google.common.base.Function { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\1466665b1d0b3524c2f0037048497d08\transformed\jetified-exoplayer-common-2.18.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-firebase-components-19.0.0\proguard.txt
-dontwarn com.google.firebase.components.Component$Instantiation
-dontwarn com.google.firebase.components.Component$ComponentType

-keep class * implements com.google.firebase.components.ComponentRegistrar { void <init>(); }
-keep,allowshrinking interface com.google.firebase.components.ComponentRegistrar

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\********************************\transformed\jetified-firebase-components-19.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\921bcb44a817599d1333342898988260\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\921bcb44a817599d1333342898988260\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\93ad3f1d867a6c2b5ddc30273002fbda\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**

# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*

# OkHttp platform used only on JVM and when Conscrypt and other security providers are available.
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\93ad3f1d867a6c2b5ddc30273002fbda\transformed\rules\lib\META-INF\proguard\okhttp3.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\347c482c5687f49a2c0c2c7528a3218a\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# Keep `Companion` object fields of serializable classes.
# This avoids serializer lookup through `getDeclaredClasses` as done for named companion objects.
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

# Don't print notes about potential mistakes or omissions in the configuration for kotlinx-serialization classes
# See also https://github.com/Kotlin/kotlinx.serialization/issues/1900
-dontnote kotlinx.serialization.**

# Serialization core uses `java.lang.ClassValue` for caching inside these specified classes.
# If there is no `java.lang.ClassValue` (for example, in Android), then R8/ProGuard will print a warning.
# However, since in this case they will not be used, we can disable these warnings
-dontwarn kotlinx.serialization.internal.ClassValueReferences

# disable optimisation for descriptor field because in some versions of ProGuard, optimization generates incorrect bytecode that causes a verification error
# see https://github.com/Kotlin/kotlinx.serialization/issues/2719
-keepclassmembers public class **$$serializer {
    private ** descriptor;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\347c482c5687f49a2c0c2c7528a3218a\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-common.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\347c482c5687f49a2c0c2c7528a3218a\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# Rule to save runtime annotations on serializable class.
# If the R8 full mode is used, annotations are removed from classes-files.
#
# For the annotation serializer, it is necessary to read the `Serializable` annotation inside the serializer<T>() function - if it is present,
# then `SealedClassSerializer` is used, if absent, then `PolymorphicSerializer'.
#
# When using R8 full mode, all interfaces will be serialized using `PolymorphicSerializer`.
#
# see https://github.com/Kotlin/kotlinx.serialization/issues/2050

 -if @kotlinx.serialization.Serializable class **
 -keep, allowshrinking, allowoptimization, allowobfuscation, allowaccessmodification class <1>

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\347c482c5687f49a2c0c2c7528a3218a\transformed\rules\lib\META-INF\com.android.tools\r8\kotlinx-serialization-r8.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece97ebe988a73d09b112128f40951e\transformed\jetified-crashreport-4.1.9.3\proguard.txt

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\6ece97ebe988a73d09b112128f40951e\transformed\jetified-crashreport-4.1.9.3\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.13\transforms\374355ca82ecf06e73f6cc08b444889c\transformed\jetified-volley-1.2.0\proguard.txt
# Prevent Proguard from inlining methods that are intentionally extracted to ensure locals have a
# constrained liveness scope by the GC. This is needed to avoid keeping previous request references
# alive for an indeterminate amount of time. See also https://github.com/google/volley/issues/114
-keepclassmembers,allowshrinking,allowobfuscation class com.android.volley.NetworkDispatcher {
    void processRequest();
}
-keepclassmembers,allowshrinking,allowobfuscation class com.android.volley.CacheDispatcher {
    void processRequest();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.13\transforms\374355ca82ecf06e73f6cc08b444889c\transformed\jetified-volley-1.2.0\proguard.txt
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>