# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in E:\android-studio\SDK/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-optimizationpasses 5          # 指定代码的压缩级别
-dontusemixedcaseclassnames   # 是否使用大小写混合
-dontpreverify           # 混淆时是否做预校验
-verbose                # 混淆时是否记录日志

-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*  # 混淆时所采用的算法

-keep public class * extends android.app.Activity      # 保持哪些类不被混淆
-keep public class * extends android.app.Application   # 保持哪些类不被混淆
-keep public class * extends android.app.Service       # 保持哪些类不被混淆
-keep public class * extends android.content.BroadcastReceiver  # 保持哪些类不被混淆
-keep public class * extends android.content.ContentProvider    # 保持哪些类不被混淆
-keep public class * extends android.app.backup.BackupAgentHelper # 保持哪些类不被混淆
-keep public class * extends android.preference.Preference        # 保持哪些类不被混淆
#-keep public class com.android.vending.licensing.ILicensingService    # 保持哪些类不被混淆
#-keep public class java.net { *; }

#-keep public class io.vov.vitamio.MediaPlayer { *; }
#-keep public class io.vov.vitamio.IMediaScannerService { *; }
#-keep public class io.vov.vitamio.MediaScanner { *; }
#-keep public class io.vov.vitamio.MediaScannerClient { *; }
#-keep public class io.vov.vitamio.VitamioLicense { *; }
#-keep public class io.vov.vitamio.Vitamio { *; }
#-keep public class io.vov.vitamio.MediaMetadataRetriever { *; }

#-keep class io.vov.vitamio.activity.** { *; }
#-keep class io.vov.vitamio.utils.** { *; }
#-keep class io.vov.vitamio.widget.** { *; }
#-keep class io.vov.vitamio.** { *; }
#-keep class org.apache.** {*;}
#-keep class com.netease.nis.bugrpt.** {*;}
-dontwarn com.sun.crypto.provider.**
#-keep class com.sun.crypto.provider.** { *;}

-printmapping mapping.txt
-keepattributes Annotation
-keepattributes JavascriptInterface
-keep class android.webkit.JavascriptInterface {*;}

-keep public class lion.widget$JSVideoTag

-keep class lion.widget$JsInteration {
    *;
}
-keepclassmembers class lion.widget$JSVideoTag { *; }
-keepclassmembers class * implements lion.widget.JSVideoTag {
    *;
}

-keep public class * implements lion.widget.JSVideoTag{*;}
-keepclassmembers class * implements lion.widget.JSVideoTag {
    *;
}


#-keep class com.aladdin.video.widgets.NBrowserTaber{*;}
#-keepclassmembers class com.aladdin.video.widgets.NBrowserTaber {
#    *;
#}
#-keep class com.aladdin.video.widgets.NBrowserTaber$JSVideoTag{*;}
#-keepclassmembers class com.aladdin.video.widgets.NBrowserTaber$JSVideoTag {
#    public *;
#}


-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

-keep public class lionpro.R$*{
public static final int *;
}
-keep class com.android.vending.billing.*
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#-dontwarn com.flurry.sdk.**
#-keep class com.flurry.sdk.* { *;}

#-keep class com.noqoush.adfalcon.android.sdk.** {*;}
#-keep class com.google.ads.mediation.adfalcon.** {*;}
-keep public class com.google.android.gms.ads.* {
 public *;
}
-keep public class com.google.ads.* {
 public *;
}


#-keepattributes SourceFile,LineNumberTable
#-keep class com.inmobi.** { *; }
#-dontwarn com.inmobi.**
#-keep public class com.google.android.gms.**
#-dontwarn com.google.android.gms.**
#-dontwarn com.squareup.picasso.**
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient{
#     public *;
#}
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info{
#     public *;
#}
# skip the Picasso library classes
-keep class com.squareup.picasso.* {*;}
-dontwarn com.squareup.picasso.**
-dontwarn com.squareup.okhttp.**
# skip Moat classes
-keep class com.moat.* {*;}
-dontwarn com.moat.**
# skip AVID classes
-keep class com.integralads.avid.library.* {*;}

# For communication with AdColony's WebView
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep ADCNative class members unobfuscated
#-keepclassmembers class com.adcolony.sdk.ADCNative** {
#    *;
# }


-keep class com.samsung.* {*;}
-dontwarn com.samsung.**
-printmapping mapping.txt

-keepattributes SourceFile,LineNumberTable
-keep class com.inmobi.* { *; }
-dontwarn com.inmobi.**
-keep public class com.google.android.gms.*
-dontwarn com.google.android.gms.**
-dontwarn com.squareup.picasso.**
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient{public *;}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient.*{public *;}
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info{public *;}
#skip the Picasso library classes
-keep class com.squareup.picasso.* {*;}
-dontwarn com.squareup.picasso.**
-dontwarn com.squareup.okhttp.**
#skip Moat classes
-keep class com.moat.* {*;}
-dontwarn com.moat.**
#skip AVID classes
-keep class com.integralads.avid.library.* {*;}

-keep public class com.google.android.gms.ads.* {public *;}
-keep class com.facebook.ads.NativeAd


-keep class com.duapps.ad.*{*;}
-dontwarn com.duapps.ad.**
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
        @com.google.android.gms.common.annotation.KeepName *;}
-keep class com.google.android.gms.common.GooglePlayServicesUtil {
      public <methods>;}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient* {
      public <methods>;}
#-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
#      public <methods>;}

-keep public class * extends androidx.versionedparcelable.VersionedParcelable {
<init>();
}

-keep class com.coder.ffmpeg.** {*;}
-dontwarn  com.coder.ffmpeg.**
-dontwarn org.jspecify.nullness.Nullable

-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}


-dontwarn com.google.protobuf.java_com_google_android_gmscore_sdk_target_granule__proguard_group_gtm_N1281923064GeneratedExtensionRegistryLite$Loader
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.OpenSSLProvider

# 保护 registerReceiver 方法不被混淆，避免 API 兼容性问题
-keep class android.content.Context {
    public android.content.Intent registerReceiver(android.content.BroadcastReceiver, android.content.IntentFilter);
    public android.content.Intent registerReceiver(android.content.BroadcastReceiver, android.content.IntentFilter, int);
}

# 保护 AcyMain 中的关键方法
-keep class amazon.browser.lionpro.primary.AcyMain {
    public void onCreate(android.os.Bundle);
}