<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    >
<!--    package="amazon.browser.lionpro"-->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<!--    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> &lt;!&ndash; <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/> &ndash;&gt;-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" tools:node="replace"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 定位 adfaico -->
<!--    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />-->
    <!--
  <uses-permission android:name="android.permission.WRITE_CALENDAR"/>
      <uses-permission android:name="android.permission.READ_CALENDAR"/>
    -->
    <!-- 查看设备信息 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 可选，在咨询清单中制定versionName属性，藉此在版本名称下方报告资料 -->
<!--    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> &lt;!&ndash; <uses-permission android:name="android.permission.READ_LOGS" />&lt;!&ndash; 获取日志&ndash;&gt; &ndash;&gt;-->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="com.android.vending.BILLING" />
<!--    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />-->
<!--    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />-->
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
<!--    android:requestLegacyExternalStorage="true"-->
<!--    android:debuggable="true"-->
<!--    tools:ignore="HardcodedDebugMode"-->

    <application
        android:name="amazon.browser.lionpro.primary.Mainly"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/icon_app"
        android:label="@string/app_name"
        android:supportsRtl="true"
        tools:replace="android:name"
        android:theme="@style/AppTheme"

        android:networkSecurityConfig="@xml/network_security_config"
        >
<!--        <property-->
<!--            android:name="android.adservices.AD_SERVICES_CONFIG"-->
<!--            android:value="@xml/gma_ad_services_config"-->
<!--            tools:replace="android:resource" />-->
        <activity
            android:name="amazon.browser.lionpro.primary.AcyMain"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|uiMode|screenLayout|smallestScreenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan"
            android:exported="true"
            tools:ignore="Instantiatable">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service android:name="amazon.browser.lionpro.primary.SerMain" />
        <service android:name="amazon.browser.lionpro.primary.M3u8MergeServer" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-8603317425868964~6066157158" />
<!--        <meta-data-->
<!--            android:name="com.google.android.gms.ads.APPLICATION_ID"-->
<!--            android:value="ca-app-pub-3940256099942544~3347511713" />-->


        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
        <!-- android:value="ca-app-pub-3940256099942544~3347511713"/> -->
        <!-- <meta-data -->
        <!-- android:name="com.google.android.gms.ads.APPLICATION_ID" -->
        <!-- android:value="ca-app-pub-3064461767247622~9204542388"/> -->
        <activity
            android:name="amazon.browser.lionpro.primary.AcyStorage"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="Instantiatable" />
        <activity
            android:name="amazon.browser.lionpro.primary.LookPicture"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="sensor"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.LookVideo"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="sensor"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.AcySetting"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" />
<!--        <activity-->
<!--            android:name=".primary.AcyRemoveAd"-->
<!--            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"-->
<!--            android:screenOrientation="user"-->
<!--            android:theme="@style/Theme.AppCompat.Light.NoActionBar"-->
<!--            android:windowSoftInputMode="adjustPan" />-->

<!--        <activity-->
<!--            android:name=".primary.AcyMain"-->
<!--            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"-->
<!--            android:screenOrientation="user"-->
<!--            android:theme="@style/Theme.AppCompat.Light.NoActionBar"-->
<!--            android:windowSoftInputMode="adjustPan" />-->

        <activity
            android:name="amazon.browser.lionpro.primary.AcyIntent"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
            </intent-filter>
        </activity>
        <activity
            android:name="amazon.browser.lionpro.primary.AcyWifiShare"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="Instantiatable" />

<!--        <activity android:name="gp.PremiumActivity"-->
<!--            android:theme="@style/AppLightTheme">-->
<!--        </activity>-->

        <activity android:name="gp.BillingActivity"
            android:theme="@style/AppLightTheme">
        </activity>

        <service android:name="amazon.browser.lionpro.primary.SerWifiShare" />

        <activity
            android:name="amazon.browser.lionpro.primary.AcyEvaluate"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:excludeFromRecents="false"
            android:screenOrientation="user"
            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="amazon.browser.lionpro.primary.UpdateActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:excludeFromRecents="false"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan" /> <!-- <meta-data android:name="com.google.android.gms.version" /> -->
        <!-- <meta-data android:value="57ae9e28e0f55a9c20000cc7" android:name="UMENG_APPKEY" /> -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="amazon.browser.video.downloader.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">

            <!-- 元数据 -->
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name="lion.PlayActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|navigation"
             /> <!-- <activity android:name="com.adcolony.sdk.AdColonyInterstitialActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:hardwareAccelerated="true"/> -->
        <!-- <activity android:name="com.adcolony.sdk.AdColonyAdViewActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|screenSize" -->
        <!-- android:hardwareAccelerated="true"/> -->
        <!-- Vungle Activities -->
<!--        <activity-->
<!--            android:name="com.vungle.warren.ui.VungleActivity"-->
<!--            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"-->
<!--            android:launchMode="singleTop"-->
<!--            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />-->
<!--        <activity-->
<!--            android:name="com.vungle.warren.ui.VungleFlexViewActivity"-->
<!--            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize"-->
<!--            android:hardwareAccelerated="true"-->
<!--            android:launchMode="singleTop"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> &lt;!&ndash; tapjoy &ndash;&gt;-->

<!--        <activity-->
<!--            android:name="com.tapjoy.TJAdUnitActivity"-->
<!--            android:configChanges="orientation|keyboardHidden|screenSize"-->
<!--            android:hardwareAccelerated="true"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen" />-->
<!--        <activity-->
<!--            android:name="com.tapjoy.TJContentActivity"-->
<!--            android:configChanges="orientation|keyboardHidden|screenSize"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> -->
        <!-- applovin -->
        <meta-data
            android:name="applovin.sdk.key"
            android:value="v6AAJu5yUShTMlS5p4B6bxtcdf6RKVWEchOwZTt3xTXHI-M63TDeT4G5lNX4t2ITlhOJ9GM5puM6tiDwFQZkVr" /> <!-- InMobi -->

        <activity
            android:name="amazon.browser.lionpro.primary.MoreApps"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize"
            android:screenOrientation="user"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="Instantiatable" />

        <!-- <activity -->
        <!-- android:name="com.inmobi.rendering.InMobiAdActivity" -->
        <!-- android:configChanges="keyboardHidden|orientation|keyboard|smallestScreenSize|screenSize|screenLayout" -->
        <!-- android:hardwareAccelerated="true" -->
        <!-- android:resizeableActivity="false" -->
        <!-- android:theme="@android:style/Theme.NoTitleBar" -->
        <!-- tools:ignore="UnusedAttribute" /> -->
        <!-- du ads -->
        <meta-data
            android:name="app_license"
            android:value="d9fca5ef10d49e53f001b3443ff09f39" />

        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:theme="@android:style/Theme.Translucent" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
            android:value="true" />
    </application>

</manifest>