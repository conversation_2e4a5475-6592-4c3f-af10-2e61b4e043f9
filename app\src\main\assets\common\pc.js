/**
 * Created by leron on 2016/8/25.
 */


var div_video,div_music,div_picture,div_gif,div_doc,div_apk,div_other



function on_init() {

    var _token=Cookies.get("token")
    get_need_login(_token,function (d) {
        if(d.need_login){
            show_login_dialog(d)
        }else{
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"login",
                    "token":Cookies.get("token")
                },
                success:function (data) {
                    var _d=$.parseJSON(data)
                    if(_d.status){
                        Cookies.set("token",_d.token)
                        show_content(_d)
                    }else{
                        window.location="/"
                    }
                },
                error:function () {
                    alert("login fail!")
                }
            })
        }
    })
}

function verification_login(){
    $.ajax({
        type:"POST",
        url:"/ajax",
        data:{
            "command":"verification",
            "token":Cookies.get("token")
        },
        success:function(data){
            var _d=$.parseJSON(data)
            if(_d.need_login)window.location="/"
        },
        error:function(){
        }
    })
}


function get_need_login(token,cber) {
    $.ajax({
        type:"POST",
        url:"/ajax",
        data:{
            "command":"need_login",
            "token":token
        },
        success:function (data) {
            var _jd=$.parseJSON(data)
            cber(_jd)
        },
        error:function () {
            alert("Network error,please refresh!")
        }
    })
}

function show_login_dialog(dm) {
    if(div_video)div_video.empty()
    if(div_music)div_music.empty()
    if(div_picture)div_picture.empty()
    if(div_gif)div_gif.empty()
    if(div_doc)div_doc.empty()
    if(div_apk)div_apk.empty()
    if(div_other)div_other.empty()
    div_video=null
    div_music=null
    div_picture=null
    div_gif=null
    div_doc=null
    div_apk=null
    div_other=null

    $("body").empty()

    $("<div />",{
        id:"div_dialog_login"
    }).appendTo($("body"))
    $("#div_dialog_login").css("margin-left",($(document).width()-$("#div_dialog_login").width())/2)
    $("#div_dialog_login").css("margin-top",($(document).height()-$("#div_dialog_login").height())/2-$("#div_dialog_login").height()/3)

    $("<h2 />",{
        style:"color: black;padding: 22px"
    }).text(dm.tip_title).appendTo($("#div_dialog_login"))
    $("<input />",{
        id:"login_input_pwd",
        style:"margin-top:16px;padding:10px;width:350px;text-align:center",
        type:"password"
    }).appendTo($("#div_dialog_login"))

    $("<button />",{
        id:"login_btn",
        autofocus:true,
        type:"button",
        style:"padding:10px 20px 10px 20px;margin-top:40px"
    }).text(dm.tip_ok).appendTo($("#div_dialog_login"))
    $("#login_btn").click(function () {
        var _pwd=$("#login_input_pwd").val()
        if(_pwd == null || _pwd==undefined || _pwd==''){
            return
        }
        $(this).attr("disabled","true")
        $.ajax({
            type:"POST",
            url:"/ajax",
            data:{
                "command":"login",
                "password":$("#login_input_pwd").val()
            },
            success:function (data) {
                $("#login_btn").attr("disabled",false)
                var _d=$.parseJSON(data)
                if(_d.status){
                    Cookies.set("token",_d.token)
                    show_content(_d)
                }else{
                    $("#login_input_pwd").val('')
                    alert("Incorrect password!")
                }
            },
            error:function () {
                $("#login_btn").attr("disabled",false)
                alert("login fail!")
            }
        })
    })

    $(window).resize(function () {
        $("#div_dialog_login").css("margin-left",($(document).width()-$("#div_dialog_login").width())/2)
        $("#div_dialog_login").css("margin-top",($(document).height()-$("#div_dialog_login").height())/2-$("#div_dialog_login").height()/3)
    })
}


function show_content(str_map) {

    $("body").empty()

    $("<div />",{
        id:"div_content_menu"
    }).appendTo($("body"))

    $("<div />",{
        id:"div_menu_video",
        class:"div_menu_item"
    }).text(str_map.tip_video).appendTo($("#div_content_menu"))
    $("<div />",{
        id:"div_menu_music",
        class:"div_menu_item"
    }).text(str_map.tip_music).appendTo($("#div_content_menu"))
    $("<div />",{
        id:"div_menu_picture",
        class:"div_menu_item"
    }).text(str_map.tip_picture).appendTo($("#div_content_menu"))
    $("<div />",{
        id:"div_menu_gif",
        class:"div_menu_item"
    }).text(str_map.tip_gif).appendTo($("#div_content_menu"))
    $("<div />",{
        id:"div_menu_doc",
        class:"div_menu_item"
    }).text(str_map.tip_doc).appendTo($("#div_content_menu"))
    $("<div />",{
        id:"div_menu_apk",
        class:"div_menu_item"
    }).text(str_map.tip_apk).appendTo($("#div_content_menu"))
    $("<div />",{
        id:"div_menu_other",
        class:"div_menu_item"
    }).text(str_map.tip_other).appendTo($("#div_content_menu"))

    $("<div />",{
        id:"div_content_list"
    }).appendTo($("body"))


    $("div.div_menu_item").click(function () {
        if($(this).css("background-color") === "rgb(0, 136, 136)")return
        $("div.div_menu_item").css("background-color","rgb(88, 88, 88)")
        $(this).css("background-color","rgb(0, 136, 136)")
        load_resource($(this).attr("id"))
    })
	
	$("#div_menu_video").click()

	setInterval(verification_login,3000)
}

function load_resource(id_name) {
    if(!id_name)return
    $("#div_content_list").empty()
    if(id_name === "div_menu_video"){
        if(!div_video){
            div_video=$("<div />",{
                id:"div_list_video",
                class:"div_video"
            })
        }
        if(div_video && !div_video.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "token":Cookies.get("token"),
                    "command":"data",
                    "category":"video"
                },
                success:function (d) {
                    div_video.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_video.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_video"
                        })
                        _tmp.attr("src_path",div_video.ctm_data.datas[i].src_path)
                        _tmp.attr("src_name",div_video.ctm_data.datas[i].name)
                        _tmp.attr("thumb_path",div_video.ctm_data.datas[i].thumb)
                        _tmp.appendTo(div_video)
                        $("<img />",{
                            width:"100%",
                            height:"100%",
                            src:div_video.ctm_data.datas[i].thumb
                        }).appendTo(_tmp)
                        $("<p />",{
                            style:"position:absolute;left:0px;top:0px;margin:0px;padding-top:4px;padding-bottom:4px;background-color:rgba(0,0,0,0.2)",
                            width:"100%",
                            text:div_video.ctm_data.datas[i].name
                        }).appendTo(_tmp)
                        $("<img />",{
                            class:"video_item_download",
                            width:"100%",
                            height:"100%",
                        }).attr("download_url",div_video.ctm_data.datas[i].download_url).appendTo(_tmp)
                    }
					div_video.appendTo($("#div_content_list"))
                    $(".div_list_video").click(function () {
                        var _ppp=$(this).attr("src_path")
                        var _dialog=$("<div />",{
                            id:"video_dialog",
                            style:"position: absolute;top: 0;right: 0;bottom: 0;left: 0;background-color:rgba(0,0,0,0.9);overflow:hidden;"
                        })
                        _dialog.appendTo($("body"))
                        var _vv=$("<video />",{
                            id:"video_player",
                            autoplay:"autoplay",
                            controls:"controls"
                        })
                        _vv.appendTo(_dialog)
                        $("<source />",{
                            src:_ppp,
                            type:"video/mp4"
                        }).appendTo(_vv)
                        $("<div />",{
                            class:"viewer-button viewer-close"
                        }).click(function () {
                            _dialog.empty()
                            _dialog.remove()
                        }).appendTo(_dialog)

                    })
                    $(".video_item_download").click(function (e) {
                        var el = document.createElement("a");
                        document.body.appendChild(el);
                        el.href = $(this).attr("download_url");
                        el.click();
                        document.body.removeChild(el);
                        e.stopPropagation()
                    })
                },
                error:function () {
                    alert("service fail!")
                }
            })
        }else{
			div_video.appendTo($("#div_content_list"))
			$(".div_list_video").click(function () {
				var _ppp=$(this).attr("src_path")
				var _dialog=$("<div />",{
					id:"video_dialog",
					style:"position: absolute;top: 0;right: 0;bottom: 0;left: 0;background-color:rgba(0,0,0,0.9);overflow:hidden;"
				})
				_dialog.appendTo($("body"))
				var _vv=$("<video />",{
					id:"video_player",
					autoplay:"autoplay",
					controls:"controls"
				})
				_vv.appendTo(_dialog)
				$("<source />",{
					src:_ppp,
					type:"video/mp4"
				}).appendTo(_vv)
				$("<div />",{
					class:"viewer-button viewer-close"
				}).click(function () {
					_dialog.empty()
					_dialog.remove()
				}).appendTo(_dialog)
			})
			$(".video_item_download").click(function (e) {
				var el = document.createElement("a");
				document.body.appendChild(el);
				el.href = $(this).attr("download_url");
				el.click();
				document.body.removeChild(el);
				e.stopPropagation()
			})
		}
    }else if(id_name === "div_menu_music"){
		if(!div_music){
            div_music=$("<div />",{
                id:"div_list_music",
                class:"div_music"
            })
            div_music.appendTo($("#div_content_list"))
        }
        if(div_music && !div_music.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"data",
                    "category":"music"
                },
                success:function (d) {
                    div_music.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_music.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_music"
                        })
                        _tmp.attr("src_path",div_music.ctm_data.datas[i].src_path)
                        _tmp.attr("src_name",div_music.ctm_data.datas[i].name)

                        _tmp.appendTo(div_music)
                        $("<p />",{
                            style:"position:absolute;left:0px;top:0px;margin:0px;padding-top:4px;padding-bottom:4px;background-color:rgba(0,0,0,0.2)",
                            width:"100%",
                            text:div_music.ctm_data.datas[i].name
                        }).appendTo(_tmp)
                        $("<img />",{
                            class:"video_item_download",
                            width:"100%",
                            height:"100%",
                        }).attr("download_url",div_music.ctm_data.datas[i].download_url).appendTo(_tmp)
                    }
                    $(".div_list_music").click(function () {
                        var _ppp=$(this).attr("src_path")
                        var _dialog=$("<div />",{
                            id:"music_dialog",
                            style:"position: absolute;top: 0;right: 0;bottom: 0;left: 0;background-color:rgba(0,0,0,0.9);overflow:hidden;"
                        })
                        _dialog.appendTo($("body"))
                        var _vv=$("<audio />",{
                            id:"music_player",
                            autoplay:"autoplay",
                            controls:"controls"
                        })
                        _vv.appendTo(_dialog)
                        $("<source />",{
                            src:_ppp,
                            type:"audio/mp3"
                        }).appendTo(_vv)
                        $("<div />",{
                            class:"viewer-button viewer-close"
                        }).click(function () {
                            _dialog.empty()
                            _dialog.remove()
                        }).appendTo(_dialog)

                    })
                    $(".video_item_download").click(function (e) {
                        var el = document.createElement("a");
                        document.body.appendChild(el);
                        el.href = $(this).attr("download_url");
                        el.click();
                        document.body.removeChild(el);
                        e.stopPropagation()
                    })
                },
                error:function () {
                    alert("service fail!")
                }
            })
        }else{
            div_music.appendTo($("#div_content_list"))
            $(".div_list_music").click(function () {
                var _ppp=$(this).attr("src_path")
                var _dialog=$("<div />",{
                    id:"music_dialog",
                    style:"position: absolute;top: 0;right: 0;bottom: 0;left: 0;background-color:rgba(0,0,0,0.9);overflow:hidden;"
                })
                _dialog.appendTo($("body"))
                var _vv=$("<audio />",{
                    id:"music_player",
                    autoplay:"autoplay",
                    controls:"controls"
                })
                _vv.appendTo(_dialog)
                $("<source />",{
                    src:_ppp,
                    type:"audio/mp3"
                }).appendTo(_vv)
                $("<div />",{
                    class:"viewer-button viewer-close"
                }).click(function () {
                    _dialog.empty()
                    _dialog.remove()
                }).appendTo(_dialog)

            })
            $(".video_item_download").click(function (e) {
                var el = document.createElement("a");
                document.body.appendChild(el);
                el.href = $(this).attr("download_url");
                el.click();
                document.body.removeChild(el);
                e.stopPropagation()
            })
        }
    }else if(id_name === "div_menu_picture"){
        if(!div_picture){
            div_picture=$("<div />",{
                id:"div_list_picture",
                class:"div_picture"
            })
        }
        if(div_picture && !div_picture.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"data",
                    "category":"picture"
                },
                success:function (d) {
                    div_picture.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_picture.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_picture"
                        })
                        _tmp.appendTo(div_picture)
                        $("<img />",{
                            width:"100%",
                            height:"100%",
                            src:div_picture.ctm_data.datas[i].src_path
                        }).appendTo(_tmp)
                    }

                    div_picture.appendTo($("#div_content_list"))
                    $('.div_picture').viewer()
                },
                error:function () {
                    alert("service fail!")
                }
            })
        }else{
            div_picture.appendTo($("#div_content_list"))
            $('.div_picture').viewer()
        }
    }else if(id_name === "div_menu_gif"){
		if(!div_gif){
            div_gif=$("<div />",{
                id:"div_list_picture",
                class:"div_gif"
            })
        }
        if(div_gif && !div_gif.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"data",
                    "category":"gif"
                },
                success:function (d) {
                    div_gif.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_gif.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_picture"
                        })
                        _tmp.appendTo(div_gif)
                        $("<img />",{
                            width:"100%",
                            height:"100%",
                            src:div_gif.ctm_data.datas[i].src_path
                        }).appendTo(_tmp)
                    }

                    div_gif.appendTo($("#div_content_list"))
                    $('.div_gif').viewer()
                },
                error:function () {
                    alert("service fail!")
                }
            })
        }else{
            div_gif.appendTo($("#div_content_list"))
            $('.div_gif').viewer()
        }
    }else if(id_name === "div_menu_doc"){
		if(!div_doc){
            div_doc=$("<div />",{
                id:"div_list_doc",
                class:"div_doc"
            })
        }
        if(div_doc && !div_doc.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"data",
                    "category":"doc"
                },
                success:function (d) {
                    div_doc.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_doc.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_doc"
                        })
                        _tmp.appendTo(div_doc)
                        $("<p />",{
                            style:"position:absolute;left:0px;top:0px;margin:0px;padding-top:4px;padding-bottom:4px;background-color:rgba(0,0,0,0.2)",
                            width:"100%",
                            text:div_doc.ctm_data.datas[i].name
                        }).appendTo(_tmp)
                        $("<img />",{
                            class:"video_item_download",
                            width:"100%",
                            height:"100%",
                        }).attr("download_url",div_doc.ctm_data.datas[i].download_url).appendTo(_tmp)
                        div_doc.appendTo($("#div_content_list"))
                        $(".video_item_download").click(function (e) {
                            var el = document.createElement("a");
                            document.body.appendChild(el);
                            el.href = $(this).attr("download_url");
                            el.click();
                            document.body.removeChild(el);
                            e.stopPropagation()
                        })
                    }
                },
                error:function () {
                    alert("service fail!")
                }
            })
		}else{
            div_doc.appendTo($("#div_content_list"))
            $(".video_item_download").click(function (e) {
                var el = document.createElement("a");
                document.body.appendChild(el);
                el.href = $(this).attr("download_url");
                el.click();
                document.body.removeChild(el);
                e.stopPropagation()
            })
        }
    }else if(id_name === "div_menu_apk"){
		if(!div_apk){
            div_apk=$("<div />",{
                id:"div_list_apk",
                class:"div_apk"
            })
        }
        if(div_apk && !div_apk.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"data",
                    "category":"apk"
                },
                success:function (d) {
                    div_apk.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_apk.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_apk"
                        })
                        _tmp.appendTo(div_apk)
                        $("<img />",{
                            width:"100%",
                            height:"100%",
                            src:div_apk.ctm_data.datas[i].thumb
                        }).appendTo(_tmp)
                        $("<p />",{
                            style:"position:absolute;left:0px;top:0px;margin:0px;padding-top:4px;padding-bottom:4px;background-color:rgba(0,0,0,0.2)",
                            width:"100%",
                            text:div_apk.ctm_data.datas[i].name
                        }).appendTo(_tmp)
                        $("<img />",{
                            class:"video_item_download",
                            width:"100%",
                            height:"100%",
                        }).attr("download_url",div_apk.ctm_data.datas[i].download_url).appendTo(_tmp)
                        div_apk.appendTo($("#div_content_list"))
                        $(".video_item_download").click(function (e) {
                            var el = document.createElement("a");
                            document.body.appendChild(el);
                            el.href = $(this).attr("download_url");
                            el.click();
                            document.body.removeChild(el);
                            e.stopPropagation()
                        })
                    }
                },
                error:function () {
                    alert("service fail!")
                }
            })
        }else{
            div_apk.appendTo($("#div_content_list"))
            $(".video_item_download").click(function (e) {
                var el = document.createElement("a");
                document.body.appendChild(el);
                el.href = $(this).attr("download_url");
                el.click();
                document.body.removeChild(el);
                e.stopPropagation()
            })
        }
    }else if(id_name === "div_menu_other"){
        if(!div_other){
            div_other=$("<div />",{
                id:"div_list_other",
                class:"div_other"
            })
        }
        if(div_other && !div_other.ctm_data){
            $.ajax({
                type:"POST",
                url:"/ajax",
                data:{
                    "command":"data",
                    "category":"other"
                },
                success:function (d) {
                    div_other.ctm_data=$.parseJSON(d)
                    for(var i=0;i<div_other.ctm_data.datas.length;++i){
                        var _tmp=$("<div />",{
                            class:"div_list_other"
                        })
                        _tmp.appendTo(div_other)
                        $("<p />",{
                            style:"position:absolute;left:0px;top:0px;margin:0px;padding-top:4px;padding-bottom:4px;background-color:rgba(0,0,0,0.2)",
                            width:"100%",
                            text:div_other.ctm_data.datas[i].name
                        }).appendTo(_tmp)
                        $("<img />",{
                            class:"video_item_download",
                            width:"100%",
                            height:"100%",
                        }).attr("download_url",div_other.ctm_data.datas[i].download_url).appendTo(_tmp)
                        div_other.appendTo($("#div_content_list"))
                        $(".video_item_download").click(function (e) {
                            var el = document.createElement("a");
                            document.body.appendChild(el);
                            el.href = $(this).attr("download_url");
                            el.click();
                            document.body.removeChild(el);
                            e.stopPropagation()
                        })
                    }
                },
                error:function () {
                    alert("service fail!")
                }
            })
        }else{
            div_other.appendTo($("#div_content_list"))
            $(".video_item_download").click(function (e) {
                var el = document.createElement("a");
                document.body.appendChild(el);
                el.href = $(this).attr("download_url");
                el.click();
                document.body.removeChild(el);
                e.stopPropagation()
            })
        }
    }
}