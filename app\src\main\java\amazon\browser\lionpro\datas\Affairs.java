package amazon.browser.lionpro.datas;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import amazon.browser.lionpro.primary.Global;

import java.util.ArrayList;

/**
 * Created by leron on 2016/4/12.
 */
public class Affairs {


    //历史记录相关
    public static class TypeHistory{
        private static final String SQL_get_history_by_day="SELECT * FROM freevideo WHERE type=1 AND day=? ORDER BY timestamp DESC";
        private static final String SQL_check_today_exists="SELECT * FROM freevideo WHERE type=1 AND day=date('now','localtime') AND url=? LIMIT 1";
        private static final String SQL_insert_history="INSERT INTO freevideo (type,title,url,thumbnail,day,timestamp) " +
                "VALUES (1,?,?,?,date('now','localtime'),datetime('now','localtime'))";
        private static final String SQL_update_history="UPDATE freevideo SET timestamp=datetime('now','localtime') WHERE _ID=";
        private static final String SQL_get_history_group_by_day="SELECT _ID,day,count(*) as size FROM freevideo WHERE type=1 GROUP BY day ORDER BY day DESC";
        private static final String SQL_delete_history_by_id ="DELETE FROM freevideo WHERE _ID=?";
        private static final String SQL_delete_history_by_day="DELETE FROM freevideo WHERE type=1 AND day=?";
        private static final String SQL_delete_history_all="DELETE FROM freevideo WHERE type=1";

        public void add_history(Context cc,final String title,final String url,final String thum_path){

            new Thread() {
                @Override
                public void run() {
                    super.run();
                    DB.Share_Instance(cc).db_execute(new DB.Caller() {
                        @Override
                        public Object on_execute(SQLiteDatabase db) {
                            Cursor _cursor=db.rawQuery(SQL_check_today_exists, new String[]{url});
                            if(_cursor.moveToFirst()){//update
                                int _ID=_cursor.getInt(_cursor.getColumnIndex("_ID"));
                                _cursor.close();
                                db.execSQL(SQL_update_history+_ID);
                            }
                            else{//insert
                                _cursor.close();
                                db.execSQL(SQL_insert_history,new String[]{title,url,thum_path});
                            }
                            return null;
                        }
                    });
                }
            }.start();
        }

        public ArrayList<Struct.StructHistoryGroup> get_history_group(Context cc){
            final ArrayList<Struct.StructHistoryGroup> _datas=new ArrayList<>();
            DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    Cursor _cursor=db.rawQuery(SQL_get_history_group_by_day, null);
                    for(_cursor.moveToFirst();!_cursor.isAfterLast();_cursor.moveToNext()) {
                        Struct.StructHistoryGroup _item=new Struct.StructHistoryGroup();
                        _item.ID = _cursor.getInt(_cursor.getColumnIndex("_ID"));
                        _item.day= _cursor.getString(_cursor.getColumnIndex("day"));
                        _item.history_size=_cursor.getInt(_cursor.getColumnIndex("size"));
                        _datas.add(_item);
                    }
                    _cursor.close();
                    for(int i=0;i< _datas.size();++i){
                        Struct.StructHistoryGroup _temp=_datas.get(i);
                        _temp.datas=new ArrayList<>();
                        Cursor _cursor2=db.rawQuery(SQL_get_history_by_day,new String[]{_temp.day});
                        for(_cursor2.moveToFirst();!_cursor2.isAfterLast();_cursor2.moveToNext()){
                            Struct.StructWebsite _tmp=new Struct.StructWebsite();
                            _tmp.ID=_cursor2.getInt(_cursor2.getColumnIndex("_ID"));
                            _tmp.type=_cursor2.getInt(_cursor2.getColumnIndex("type"));
                            _tmp.pid=_cursor2.getInt(_cursor2.getColumnIndex("PID"));
                            _tmp.dir=_cursor2.getString(_cursor2.getColumnIndex("dir"));
                            _tmp.title=_cursor2.getString(_cursor2.getColumnIndex("title"));
                            _tmp.url=_cursor2.getString(_cursor2.getColumnIndex("url"));
                            _tmp.icon_file_name =_cursor2.getString(_cursor2.getColumnIndex("thumbnail"));
                            _tmp.day=_cursor2.getString(_cursor2.getColumnIndex("day"));
                            _tmp.timestamp =_cursor2.getString(_cursor2.getColumnIndex("timestamp"));
                            _temp.datas.add(_tmp);
                        }
                        _cursor2.close();
                    }
                    return true;
                }
            });
            return _datas;
        }

        public ArrayList<Struct.StructWebsite> get_history_by_day(Context cc,String day){
            ArrayList<Struct.StructWebsite> _datas = DB.Share_Instance(cc).db_select_webstie(SQL_get_history_by_day, new String[]{day});
            return _datas;
        }

        public boolean delete_history_by_ID(Context cc,final int id){
            Object _o=DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_delete_history_by_id, new String[]{String.valueOf(id)});
                    return true;
                }
            });
            if(_o!=null)return true;
            else return false;
        }
        public boolean delete_history_by_IDs(Context cc,final ArrayList<Integer> ids){
            if(ids==null)return false;
            Object _o=DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    for(int i=0;i<ids.size();++i) {
                        db.execSQL(SQL_delete_history_by_id, new String[]{String.valueOf(ids.get(i))});
                    }
                    return true;
                }
            });
            if(_o!=null)return true;
            else return false;
        }

        public boolean delete_history_by_day(Context cc,final String day){
            Object _o=DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_delete_history_by_day, new String[]{day});
                    return true;
                }
            });
            if(_o!=null)return true;
            else return false;
        }
        public boolean clear_history(Context cc){
            Object _o=DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_delete_history_all);
                    return true;
                }
            });
            if(_o!=null)return true;
            else return false;
        }

    }

    public static class TypeFavorite {
        //收藏相关
        private static final String SQL_insert_favorite = "INSERT INTO freevideo (type,title,url,thumbnail,day,timestamp,PID,dir) " +
                "VALUES (2,?,?,?,date('now','localtime'),datetime('now','localtime'),?,?)";
        private static final String SQL_update_favorite = "UPDATE freevideo SET title=? , url=? , thumbnail=? WHERE _ID=?";
        private static final String SQL_update_favorite_up="UPDATE freevideo SET day=date('now','localtime'),timestamp=datetime('now','localtime') WHERE _ID=?";
        private static final String SQL_delete_favorite = "DELETE FROM freevideo WHERE _ID=?";
        private static final String SQL_update_favorite_dir = "UPDATE freevideo SET dir=? WHERE _ID=?";
        private static final String SQL_check_same_dir_on_same_layer = "SELECT * FROM freevideo WHERE type=2 AND PID=? AND dir=? LIMIT 1";
        private static final String SQL_check_same_favorite="SELECT * FROM freevideo WHERE type=2 AND title=? AND url=?";
        private static final String SQL_select_by_pid = "SELECT * FROM freevideo WHERE type=2 AND PID=? ORDER BY timestamp DESC";
        private static final String SQL_delete_favorite_dir_1 = "SELECT * FROM freevideo WHERE type=2 AND dir<>NULL AND PID=?";
        private static final String SQL_delete_favorite_dir_2 = "DELETE FROM freevideo WHERE type=2 AND (PID=? OR _ID=?)";
        private static final String SQL_select_favorite_star = "SELECT * FROM freevideo WHERE type=2 AND url=? LIMIT 1";
        private static final String SQL_select_favorite= "SELECT * FROM freevideo WHERE type=2 AND title=? AND url=? LIMIT 1";

        public boolean has_same_dir_on_same_layer(Context cc, final int pid, final String dir) {
            if (dir == null || dir.length() == 0) return true;
            Boolean _v = (Boolean) DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    Cursor _cursor = db.rawQuery(SQL_check_same_dir_on_same_layer, new String[]{String.valueOf(pid), dir});
                    if (_cursor != null && _cursor.moveToFirst()) {
                        int _ID = _cursor.getInt(_cursor.getColumnIndex("_ID"));
                        _cursor.close();
                        return Boolean.TRUE;
                    } else {
                        _cursor.close();
                        return Boolean.FALSE;
                    }
                }
            });
            return _v.booleanValue();
        }

        public boolean add_favorite_dir(Context cc, final int pid, final String dir_name) {
            if (dir_name == null || dir_name.length() == 0) return false;
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_insert_favorite, new String[]{null, null, null, String.valueOf(pid), dir_name});
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }

        public boolean add_favorite(Context cc, final String title, final String url, final String thum_path, final int pid) {
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_insert_favorite, new String[]{title, url, thum_path, String.valueOf(pid), null});
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }

        public boolean update_favorite(Context cc, final int id, final String title, final String url, final String thum_path) {
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_update_favorite, new String[]{title, url, thum_path, String.valueOf(id)});
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }

        public boolean delete_favorite(Context cc, final int id) {
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_delete_favorite, new String[]{String.valueOf(id)});
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }
        public boolean delete_favorites(Context cc,final ArrayList<Integer> ids){
            if(ids==null)return false;
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    for(int i=0;i<ids.size();++i) {
                        db.execSQL(SQL_delete_favorite, new String[]{String.valueOf(ids.get(i))});
                    }
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }

        public ArrayList<Struct.StructWebsite> get_favorite_dir(Context cc, int pid) {
            ArrayList<Struct.StructWebsite> _data = DB.Share_Instance(cc).db_select_webstie(SQL_select_by_pid, new String[]{String.valueOf(pid)});
            for (int i = 0; i < _data.size(); ++i) {
                Struct.StructWebsite _tmp = _data.get(i);
                if (_tmp.dir != null && !_tmp.is_dir) {
                    _tmp.is_dir = true;
                    _data.add(_data.remove(i));
                    --i;
                }
            }
            return _data;
        }

        public boolean update_favorite_dir(Context cc, final int id, final String dir) {
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    db.execSQL(SQL_update_favorite_dir, new String[]{dir, String.valueOf(id)});
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }

        public boolean delete_favorite_dir(Context cc, final int id) {
            Object _o = DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Object on_execute(SQLiteDatabase db) {
                    delete_favorite_folder(id, db);
                    return true;
                }
            });
            if (_o != null) return true;
            else return false;
        }

        private void delete_favorite_folder(int id, SQLiteDatabase db) {
            ArrayList<Integer> _subs = new ArrayList<Integer>();
            Cursor _cursor = db.rawQuery(SQL_delete_favorite_dir_1, new String[]{String.valueOf(id)});
            for (_cursor.moveToFirst(); !_cursor.isAfterLast(); _cursor.moveToNext()) {
                int _sub_dir_id = _cursor.getInt(_cursor.getColumnIndex("_ID"));
                _subs.add(_sub_dir_id);
            }
            _cursor.close();
            for (int i = 0; i < _subs.size(); ++i) {
                delete_favorite_folder(_subs.get(i), db);
            }
            db.execSQL(SQL_delete_favorite_dir_2, new String[]{String.valueOf(id), String.valueOf(id)});
        }

        public Struct.StructWebsite get_favorite_star(Context cc, final String url) {
            return (Struct.StructWebsite) DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Struct.StructWebsite on_execute(SQLiteDatabase db) {
                    Cursor _cursor = db.rawQuery(SQL_select_favorite_star, new String[]{url});
                    if (_cursor == null || !_cursor.moveToFirst() || _cursor.isAfterLast())
                        return null;
                    Struct.StructWebsite _tmp = new Struct.StructWebsite();
                    _tmp.ID = _cursor.getInt(_cursor.getColumnIndex("_ID"));
                    _tmp.type = _cursor.getInt(_cursor.getColumnIndex("type"));
                    _tmp.pid = _cursor.getInt(_cursor.getColumnIndex("PID"));
                    _tmp.dir = _cursor.getString(_cursor.getColumnIndex("dir"));
                    _tmp.title = _cursor.getString(_cursor.getColumnIndex("title"));
                    _tmp.url = _cursor.getString(_cursor.getColumnIndex("url"));

                    if (_cursor.getString(_cursor.getColumnIndex("thumbnail")) == null) {
                        _tmp.icon_file_name = Global.path_website;
                    } else {
                        _tmp.icon_file_name = _cursor.getString(_cursor.getColumnIndex("thumbnail"));
                    }
                    _tmp.day = _cursor.getString(_cursor.getColumnIndex("day"));
                    _tmp.timestamp = _cursor.getString(_cursor.getColumnIndex("timestamp"));
                    _cursor.close();
                    return _tmp;
                }
            });
        }

        public Struct.StructWebsite add_favorite(Context cc, final String title, final String url, final String thum_path) {
            return (Struct.StructWebsite) DB.Share_Instance(cc).db_execute(new DB.Caller() {
                @Override
                public Struct.StructWebsite on_execute(SQLiteDatabase db) {

                    int _ID=-1;
                    Cursor _cursor = db.rawQuery(SQL_check_same_favorite, new String[]{title, url});
                    if (_cursor != null && _cursor.moveToFirst()) {
                        _ID = _cursor.getInt(_cursor.getColumnIndex("_ID"));
                    }
                    _cursor.close();
                    if(_ID==-1){//新增
                        db.execSQL(SQL_insert_favorite, new String[]{title, url, thum_path, "0", null});
                    }else{//提上
                        db.execSQL(SQL_update_favorite_up,new String[]{Integer.toString(_ID)});
                    }
                    _cursor = db.rawQuery(SQL_select_favorite, new String[]{title,url});
                    if (_cursor == null || !_cursor.moveToFirst() || _cursor.isAfterLast())return null;
                    Struct.StructWebsite _tmp = new Struct.StructWebsite();
                    _tmp.ID = _cursor.getInt(_cursor.getColumnIndex("_ID"));
                    _tmp.type = _cursor.getInt(_cursor.getColumnIndex("type"));
                    _tmp.pid = _cursor.getInt(_cursor.getColumnIndex("PID"));
                    _tmp.dir = _cursor.getString(_cursor.getColumnIndex("dir"));
                    _tmp.title = _cursor.getString(_cursor.getColumnIndex("title"));
                    _tmp.url = _cursor.getString(_cursor.getColumnIndex("url"));
                    _tmp.icon_file_name = _cursor.getString(_cursor.getColumnIndex("thumbnail"));
                    _tmp.day = _cursor.getString(_cursor.getColumnIndex("day"));
                    _tmp.timestamp = _cursor.getString(_cursor.getColumnIndex("timestamp"));
                    _cursor.close();
                    return _tmp;
                }
            });
        }

        public ArrayList<Struct.StructWebsite> get_favorites(Context cc) {
            return get_favorite_dir(cc,0);
        }
    }




    public static class TypeAutomatically{
        private static final String SQL_select_by_like="SELECT * FROM freevideo WHERE title like ? OR url like ? ORDER BY timestamp DESC LIMIT 8";

        public ArrayList<Struct.StructWebsite> get_by_like(Context cc, String str){
            return DB.Share_Instance(cc).db_select_webstie(SQL_select_by_like,new String[]{"%"+str+"%","%"+str+"%"});
        }
    }
}
