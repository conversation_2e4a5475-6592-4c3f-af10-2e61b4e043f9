package amazon.browser.lionpro.datas;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import java.util.ArrayList;
import lion.CL;

/**
 * Created by leron on 2015/6/25.
 */
public class DB {

    public interface Caller{
        Object on_execute(SQLiteDatabase db);
    }



    private final int DB_VERSION=1;
    private static DB database;
    private CTMDbHelp db_helper;
    private SQLiteDatabase db;
    private DB(Context cc){
        db_helper=new CTMDbHelp(cc,"freevideo",null,DB_VERSION);
    }
    public static DB Share_Instance(Context cc){
        if(database==null){
            database=new DB(cc);
        }
        return database;
    }

    public synchronized ArrayList<Struct.StructWebsite> db_select_webstie(String sql, String[] args){
        ArrayList<Struct.StructWebsite> _datas=new ArrayList<>();
        try{
            db=db_helper.getReadableDatabase();
            Cursor _cursor=db.rawQuery(sql,args);
            for(_cursor.moveToFirst();!_cursor.isAfterLast();_cursor.moveToNext()){
                Struct.StructWebsite _tmp=new Struct.StructWebsite();
                _tmp.ID=_cursor.getInt(_cursor.getColumnIndex("_ID"));
                _tmp.type=_cursor.getInt(_cursor.getColumnIndex("type"));
                _tmp.pid=_cursor.getInt(_cursor.getColumnIndex("PID"));
                _tmp.dir=_cursor.getString(_cursor.getColumnIndex("dir"));
                _tmp.title=_cursor.getString(_cursor.getColumnIndex("title"));
                _tmp.url=_cursor.getString(_cursor.getColumnIndex("url"));
                _tmp.icon_file_name =_cursor.getString(_cursor.getColumnIndex("thumbnail"));
                _tmp.day=_cursor.getString(_cursor.getColumnIndex("day"));
                _tmp.timestamp =_cursor.getString(_cursor.getColumnIndex("timestamp"));
                _datas.add(_tmp);
            }
            _cursor.close();
        }catch(Exception e){
            CL.CLOGE("database select error",e);
        }finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return _datas;
    }

    public synchronized Object db_execute(Caller caller){
        if(caller==null)return null;
        Object _result=null;
        try{
            db=db_helper.getWritableDatabase();
            _result=caller.on_execute(db);
        }catch (Exception e){
            CL.CLOGE("database execute caller error",e);
        }finally {
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return _result;
    }

    public synchronized void db_clear(){
        try{
            db=db_helper.getWritableDatabase();
            db.execSQL("DELETE FROM crazy");
        }catch (Exception e){
            CL.CLOGE("database clear error",e);
        }
        finally {
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }


//    public void db_insert(String sql,Object[] args){
//        try{
//            db=db_helper.getWritableDatabase();
//            if(args == null)db.execSQL(sql);
//            else db.execSQL(sql,args);
//        }catch (Exception e){
//            CLT.CLOGE("database insert error",e);
//        }
//        finally {
//            if(db!=null && db.isOpen()){
//                db.close();
//                db=null;
//            }
//        }
//    }
//
//    public void db_delete(String sql){
//        try{
//            db=db_helper.getWritableDatabase();
//            db.execSQL(sql);
//        }catch (Exception e){
//            CLT.CLOGE("database delete error",e);
//        }
//        finally {
//            if(db!=null && db.isOpen()){
//                db.close();
//                db=null;
//            }
//        }
//    }


    private class CTMDbHelp extends SQLiteOpenHelper {

        public CTMDbHelp(Context context, String name, SQLiteDatabase.CursorFactory factory, int version) {
            super(context, name, factory, version);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            //历史记录和收藏
            StringBuffer _sql=new StringBuffer();
            _sql.append("create table if not exists ");
            _sql.append("freevideo (");
            _sql.append("_ID INTEGER PRIMARY KEY, ");
            _sql.append("type INTEGER, ");
            _sql.append("PID INTEGER, ");
            _sql.append("dir TEXT(200), ");
            _sql.append("title TEXT(200), ");
            _sql.append("url TEXT(200), ");
            _sql.append("thumbnail TEXT(200), ");
            _sql.append("day TEXT, ");
            _sql.append("timestamp TEXT");
            _sql.append(")");
            db.execSQL(_sql.toString());

        }
        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {

        }
    }

}
