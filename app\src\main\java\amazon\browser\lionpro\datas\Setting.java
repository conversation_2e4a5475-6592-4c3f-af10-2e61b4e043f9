package amazon.browser.lionpro.datas;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Environment;

import amazon.browser.lionpro.primary.Global;

import org.json.JSONArray;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;

/**
 * Created by leron on 2016/8/2.
 */
public class Setting {

    private static Setting setting;
    public static void Init(Context context){
        if(setting==null)setting=new Setting(context);
//        setting.set_outline_switch_ad(true);
//        Global.Switch_AD = true;
    }
    public static Setting Share_Setting(){
        return setting;
    }


    private Context cc;
    private Setting(Context context){
        this.cc=context;
    }
    private SharedPreferences get_sp(){
        return this.cc.getSharedPreferences("clsetting",Context.MODE_PRIVATE);
    }


    public String get_main_page(){
        SharedPreferences _sp=get_sp();

        if (Global.country == 1)
            Global.homepage="https://www.baidu.com";
        else
            Global.homepage="https://www.google.com";
        return _sp.getString("main_page", Global.homepage);
    }
    public void set_main_page(String v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("main_page",v==null?"":v).apply();
    }

    public int get_searcher(){
        SharedPreferences _sp=get_sp();
        int value = 101;

        if (Global.country == 1)
            value = 102;
        else
            value = 101;
        return _sp.getInt("searcher", value);
    }
    public void set_searcher(int code){
        if(code!=101 && code!=102)return;
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("searcher",code).apply();
    }

    public int get_UA(){
        SharedPreferences _sp=get_sp();
        return _sp.getInt("user_agent", 101);
    }
    public void set_UA(int code){
        if(code!=101 && code!=102 && code!=103)return;
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("user_agent",code).apply();
    }

    public boolean get_horizontal_full_screen(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("horizontal", false);
    }
    public void set_horizontal_full_screen(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("horizontal",v).apply();
    }

    public boolean get_only_wifi(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("only_wifi", false);
    }
    public void set_only_wifi(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("only_wifi",v).apply();
    }

    public boolean get_night_mode() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("night_mode", false);
    }

    public void set_night_mode(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("night_mode",v).apply();
    }

    public boolean get_fullscreen_mode() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("fullscreen", false);
    }

    public void set_fullscreen_mode(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("fullscreen",v).apply();
    }

    public String get_export_dir(){
        SharedPreferences _sp=get_sp();
        return _sp.getString("export_dir", new File(Environment.getExternalStorageDirectory(),Global.downloadPathName).getAbsolutePath());
    }
    public void set_export_dir(String dir){
        if(dir==null || dir.isEmpty())return;
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("export_dir",dir).apply();
    }

    public String get_new_export_dir(){
        SharedPreferences _sp=get_sp();
        return _sp.getString("new_export_dir", new File(Environment.getExternalStorageDirectory(),Global.downloadPathName).getAbsolutePath());
    }
    public void set_new_export_dir(String dir){
        if(dir==null || dir.isEmpty())return;
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("new_export_dir",dir).apply();
    }

    public boolean get_download_path_mode(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("download_path_mode", true);
    }
    public void set_download_path_mode(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("download_path_mode",v).apply();
    }

    public boolean get_download_path_mode_tip(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("download_path_mode_tip", false);
    }
    public void set_download_path_mode_tip(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("download_path_mode_tip",v).apply();
    }

    public boolean get_m3u8_merge_mode_tip(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("m3u8_merge_mode_tip", false);
    }
    public void set_m3u8_merge_mode_tip(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("m3u8_merge_mode_tip",v).apply();
    }

    public boolean get_m3u8_merge_mode(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("m3u8_merge_mod", false);
    }
    public void set_m3u8_merge_mode(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("m3u8_merge_mod",v).apply();
    }

    public boolean get_filter_switch(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("filter_switch", false);
    }

    public void set_filter_switch(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("filter_switch",v).apply();
    }

    public int get_thread_number(){
        SharedPreferences _sp=get_sp();
        return _sp.getInt("thread_number", 4);
    }
    public void set_thread_number(int code){
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("thread_number",code).apply();
    }

    public int get_free_number(){
        SharedPreferences _sp=get_sp();
        return _sp.getInt("free_number", 3);
    }
    public void set_free_number(int code){
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("free_number",code).apply();
    }

    public boolean get_free_complete() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("free_complete", false);
    }
    public void set_free_complete(boolean code) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("free_complete",code).apply();
    }

    public static final String Type_fav="type_fav";
    public static final String Type_fav_history="type_fav_history";
    public static final String Type_play_list="type_play_list";
    public static final String Type_item="type_item";
    public static final String Type_video="type_video";
    public void set_tip(String type,boolean v){
        if(type==null || type.isEmpty())return;
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean(type,v).apply();
    }
    public boolean get_tip(String type){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean(type, false);
    }


    public int get_AD_times(){
        SharedPreferences _sp=get_sp();
        return _sp.getInt("show_download_ad",0);
    }
    public void set_AD_times(int times){
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("show_download_ad",times).apply();
    }

    public void set_outline_switch_ad(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("outline_switch_ad",v).apply();
    }
    public boolean get_outline_switch_ad(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("outline_switch_ad", true);
    }

    public void set_frequency_show(int v){
        SharedPreferences _sp=get_sp();
       // if(v<5)v=10;
        _sp.edit().putInt("frequency_show",v).apply();
    }
    public int get_frequency_show(){
        SharedPreferences _sp=get_sp();
        return _sp.getInt("frequency_show", 28);
    }

    public void set_evaluate(boolean v){
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("evaluate",v).apply();
    }
    public boolean get_evaluate(){
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("evaluate", false);
    }

    public void set_complete_first_download(boolean b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("first_download",b).apply();
    }

    public boolean get_complete_first_download() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("first_download", false);
    }

    public void set_subscription_flag(boolean b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("subscription",b).apply();
    }

    public boolean get_subscription_flag() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("subscription", false);
    }

    public void set_ads_pos(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("ads_pos",b).apply();
    }

    public int get_ads_pos() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("ads_pos", 1);

    }

    public void set_video_ads_pos(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("video_ads_pos",b).apply();
    }

    public int get_video_ads_pos() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("video_ads_pos", 0);

    }

    public void set_nine_ads_pos(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("nine_ads_pos",b).apply();
    }

    public int get_nine_ads_pos() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("nine_ads_pos", 0);

    }

    public void set_music_ads_pos(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("music_ads_pos",b).apply();
    }

    public int get_music_ads_pos() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("music_ads_pos", 1);
    }

    public void set_update(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("update",b).apply();
    }

    public int get_update() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("update", 0);
    }

    public void set_tip_update(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("tip_update",b).apply();
    }

    public int get_tip_update() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("tip_update", 0);
    }

    public void set_redpoint(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("readpoint",b).apply();
    }

    public int get_redpoint() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("readpoint", 0);
    }

    public void set_update_packagename(String name) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("update_packagename",name).apply();
    }

    public String get_update_packagename() {
        SharedPreferences _sp=get_sp();
        return _sp.getString("update_packagename", "");
    }

    public void set_update_count(int name) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("update_count",name).apply();
    }

    public int get_update_count() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("update_count", 50);
    }

    public void set_app_run_count(int name) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("run_count",name).apply();
    }

    public int get_app_run_count() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("run_count", 0);
       // return _sp.getInt("run_count", 945439394);
        //Eddy
    }

    public void set_app_run_time(long name) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putLong("run_time",name).apply();
    }

    public long get_app_run_time() {
        SharedPreferences _sp=get_sp();
        return _sp.getLong("run_time", 0);
    }

    public void set_server_config_run_time(long name) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putLong("server_run_time",name).apply();
    }

    public long get_server_config_run_time() {
        SharedPreferences _sp=get_sp();
        return _sp.getLong("server_run_time", 1000);
    }

    public void set_banner_di(String v) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("banner_id",v).apply();
    }

    public String get_banner_id() {
        SharedPreferences _sp=get_sp();
        //return _sp.getString("banner_id", "ca-app-pub-3940256099942544/6300978111"/*"ca-app-pub-3064461767247622/4417189525"*/);
        return _sp.getString("banner_id", "ca-app-pub-8603317425868964/9448936767");
    }

    public void set_ainterstitial_id(String v) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("interstitial_id",v).apply();
    }

    public String get_ainterstitial_id() {
        SharedPreferences _sp=get_sp();
       // return _sp.getString("interstitial_id", "ca-app-pub-3064461767247622/5911152877");
        return _sp.getString("init", "ca-app-pub-3064461767247622/6573537436");
    }

    public void set_admob_app_id(String v) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putString("admob_app_id",v).apply();
    }

    public String get_admob_app_id() {
        SharedPreferences _sp=get_sp();
        return _sp.getString("admob_app_id", "ca-app-pub-3064461767247622~8952288761");
    }

    public void set_video_banner_type(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("video_banner_type",b).apply();
    }

    public int get_video_banner_type() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("video_banner_type", 0);
    }

    public void set_nine_banner_type(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("nice_banner_type",b).apply();
    }

    public int get_nice_banner_type() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("nice_banner_type", 0);
    }

    public void set_banner_type(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("banner_type",b).apply();
    }

    public int get_banner_type() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("banner_type", 7);
    }

    public void set_banner2_type(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("banner_type2",b).apply();
    }

    public int get_banner2_type() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("banner_type2", 1);
    }

    public void set_pos_web_state(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("pos_web_state",b).apply();
    }

    public int get_pos_web_state() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("pos_web_state", 0);

    }

    public void set_pos_music_state(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("pos_music_state",b).apply();
    }

    public int get_pos_music_state() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("pos_music_state", 0);
    }

    public void set_pos_pic_state(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("pos_pic_state",b).apply();
    }

    public int get_pos_pic_state() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("pos_pic_state", 0);
    }

    public void set_pos_video_state(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("pos_video_state",b).apply();
    }

    public int get_pos_video_state() {

        SharedPreferences _sp=get_sp();
        return _sp.getInt("pos_video_state", 0);
    }

    public void set_pos_storage_state(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("pos_storage_state",b).apply();
    }

    public int get_pos_storage_state() {

        SharedPreferences _sp=get_sp();
        return _sp.getInt("pos_storage_state", 0);

    }

    public void set_pos_settings_state(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("pos_settings_state",b).apply();
    }

    public int get_pos_settings_state() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("pos_settings_state", 0);
    }

    public void set_download_enable(boolean b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("download_enable",b).apply();
    }

    public boolean get_download_enable() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("download_enable", true);
    }

    public void set_youtube_play_close(boolean b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("youtube_play_close",b).apply();
    }

    public boolean get_youtube_play_close() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("youtube_play_close", false);
    }

    public void set_youtube_play_in_count(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("youtube_play_in_count",b).apply();
    }

    public int get_youtube_play_in_count() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("youtube_play_in_count", 100);
    }

    public void set_open_ad_count(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("open_ad_count",b).apply();
    }

    public int get_open_ad_count() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("open_ad_count", 100);

    }

    public void set_close_other_ads_count(int b) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("close_other_ads_count",b).apply();
    }

    public int get_close_other_ads_count() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("close_other_ads_count", 100);

    }

    public void set_click_redpoint(int value) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("click_redpoint",value).apply();
    }

    public int get_click_redpoint() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("click_redpoint", 0);
    }

    public void set_tip_count(int value) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putInt("tip_count",value).apply();
    }

    public int get_tip_count() {
        SharedPreferences _sp=get_sp();
        return _sp.getInt("tip_count", 0);
    }

    public void set_click_update(boolean value) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putBoolean("click_update",value).apply();
    }

    public boolean get_click_update() {
        SharedPreferences _sp=get_sp();
        return _sp.getBoolean("click_update", false);
    }

    public void set_filter_mini_size(long value) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putLong("filter_mini_size",value).apply();
    }

    public long get_filter_mini_size() {
        SharedPreferences _sp=get_sp();
        return _sp.getLong("filter_mini_size", 0);
    }

    public void set_filter_max_size(long value) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putLong("filter_max_size",value).apply();
    }

    public long get_filter_max_size() {
        SharedPreferences _sp=get_sp();
        return _sp.getLong("filter_max_size", -1);
    }

    public void set_export_tip(long value) {
        SharedPreferences _sp=get_sp();
        _sp.edit().putLong("export_tip",value).apply();
    }

    public long get_export_tip() {
        SharedPreferences _sp=get_sp();
        return _sp.getLong("export_tip", 0);
    }

    public static class ApkInfo implements Serializable {
        String pack_name;
        String title;
        String image;

        public ApkInfo(String pack_name, String title, String image) {
            this.pack_name = pack_name;
            this.title = title;
            this.image = image;
        }

        public String getPack_name() {
            return pack_name;
        }

        public String getTitle() {
            return title;
        }

        public String getImage() {
            return image;
        }
    }

    public static JSONArray download_apk_name_array;
    public static ArrayList<ApkInfo> apks_list = new ArrayList<ApkInfo>();;
    public ArrayList<ApkInfo> getApks() { return apks_list; }
    public static void setApkInfo(String pack, String title, String image) {
        ApkInfo apk = new ApkInfo(pack, title, image);
        apks_list.add(apk);
    }

}
