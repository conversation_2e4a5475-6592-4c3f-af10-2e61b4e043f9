package amazon.browser.lionpro.datas;

import android.graphics.Bitmap;

import amazon.browser.lionpro.downloader.Data;

import java.io.File;
import java.util.ArrayList;

/**
 * Created by leron on 2016/4/12.
 */
public class Struct {

    public static class StructWebsite{
        public int ID;
        public int type;      // 1:history 2:favorite
        public int pid;
        public String dir;
        public String title;
        public String url;
        public String icon_file_name;
        public String day;
        public String timestamp;


        //UI操作相关
        public Bitmap bitmap_icon;
        public boolean is_dir=false;
        public boolean selected=false;
    }

    public static class StructHistoryGroup{
        public int ID;
        public String day;
        public int history_size;
        public ArrayList<StructWebsite> datas;
        public boolean selected;
    }


    public static class StoreDir{

        public int type;    //1-video 2-music 3-image 4-GIF 5-doc 6-apk 7-other 100-custom
        public String name;
        public int files_size;
        public long length;

        public ArrayList<File> files;
        public ArrayList<Data.StructDLItem> dls;
    }

}
