package amazon.browser.lionpro.downloader;


import android.os.Build;

import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.util.HttpsSslCertificate;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.concurrent.locks.ReentrantLock;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;

import lion.CL;

/**
 * Created by leron on 2016/7/8.
 */
public class CommonDownloader{

    public interface Eventer{
        int State_Start=5001;
        int State_Stop=5002;
        int State_Complete=5003;
        int State_packet=5004;

        int Error_No_Path=10;
        int Error_Create_File =12;
        int Error_Network=13;
        int Error_File_RW=14;

        void on_state_change(Data.StructDLItem item,int state);
        void on_load_ratio(Data.StructDLItem item,long dl_size,float ratio);
        void on_error(Data.StructDLItem item,int code);
        void on_delete(Data.StructDLItem item,boolean success);
    }



    protected Data.StructDLItem data;
    protected Eventer listener;


    public CommonDownloader(Data.StructDLItem dl_data){
        lock_wirter=new ReentrantLock();
        this.data=dl_data;
    }

    public void set_listener(Eventer listen){
        this.listener=listen;
    }

    public Eventer get_listener(){
        return this.listener;
    }


    public void go() {
        if(delete)return;
        if(!is_start) {
            is_start=true;
            run=true;
            if (data == null) return;
            if (data.path == null) {
                if (listener != null) listener.on_error(data,Eventer.Error_No_Path);
                is_start=false;
                return;
            }
            not_run_once=false;
            crt_status=Eventer.State_Start;
            if(listener!=null)listener.on_state_change(data,crt_status);
            delete=false;
            download_size=0;
            speed=0;
            start_time=System.currentTimeMillis();
            last_update_time=start_time;
            new Worker().start();
        }
    }

    public void stop() {
        if(delete)return;
        run=false;
        if (blocks_complete != null) {
            synchronized (blocks_complete) {
                blocks_complete.notifyAll();
            }
        }
    }

    public void delete(){
        run=false;
        delete=true;
        int status = get_status();
        if(status==Eventer.State_Complete || status==Eventer.State_Stop){
            File _f=new File(this.data.path);
            if(_f.exists()){
                if(_f.delete()) {
                    if (this.listener != null) {
                        this.listener.on_delete(this.data, true);
                    }
                }
            } else {
                if(this.listener!=null)this.listener.on_delete(this.data,true);
            }
        }else{
            synchronized (blocks_complete){
                blocks_complete.notifyAll();
            }
        }
    }

    public long get_total_size(){
        if(not_run_once){
            if(this.data==null || this.data.path==null)return 0;
            File _f=new File(this.data.path);
            if(_f.exists())return _f.length();
            else return 0;
        }
        return file_size;
    }
    public int get_speed(){
        if(crt_status==Eventer.State_Start){
            long _crt_time=System.currentTimeMillis();
            long _time=_crt_time-start_time;
            return (int)(download_size/(_time/1000.0f));
        }
        return speed;
    }
    public int get_status(){
        return crt_status;
    }

    /*
    error code:
        10:没有指定存储位置
        11:磁盘空间不够
        12:创建文件失败

     */


    private class DLBlock{
        private long start,end;
        private int index;
        private int length;
        private int crt_point=0;
        private ByteBuffer buffer;
    }


    private final int Max_Thread_Number=1;
    private final int Max_Unit=300*1024;
    private final int Max_Cache_Number=16;
    private boolean not_run_once=true;
    private volatile boolean run=true;
    private boolean delete=false;
    private boolean is_start=false;
    private File dl_file;
    private FileOutputStream dl_fos;
    private ArrayList<DLBlock> blocks;
    private ArrayList<DLBlock> blocks_complete;
    private int crt_block_index;
    private ReentrantLock lock_wirter;
    private long file_size;
    private long download_size=0;
    private long last_update_time=0;
    private int speed;
    private long start_time=0;
    protected int crt_status=Eventer.State_Stop;
    private int threader_count=0;

    public int getCrt_status() {
        return crt_status;
    }

    private synchronized void add_threader_count(){
        ++threader_count;
        CL.CLOGI("start thread count:"+threader_count);
    }
    private synchronized void sub_threader_count(){
        --threader_count;
        CL.CLOGI("end thread count:"+threader_count);
    }

    private synchronized DLBlock get_block(){
        if(blocks.size()>0)return blocks.remove(0);
        return null;
    }


    private class Worker extends Thread{

        private int exception_times=0;
        private final int REPLAY_NUM = 20;
        @Override
        public void run() {

            add_threader_count();

            //初始分配 多线程启动
            if(blocks==null){
                dl_file=new File(data.path);
                try {
                    if(!dl_file.exists()) {
                        dl_file.createNewFile();
                    }
                    dl_fos=new FileOutputStream(dl_file,true);
                }catch (Exception ex){
                    if(listener!=null){
                        listener.on_error(data,Eventer.Error_Create_File);
                        crt_status=Eventer.State_Stop;
                        listener.on_state_change(data,crt_status);
                    }
                    is_start=false;
                    run=false;
                    return;
                }
                file_size=dl_file.length();
                if(listener!=null)listener.on_load_ratio(data,file_size,speed);
                blocks=new ArrayList<>();
                blocks_complete=new ArrayList<>();
                crt_block_index =0;
                long _need_dl_length=data.length-file_size;
                //可能已经下载完成了,或者是多下载了
                if(_need_dl_length<=0){
                    //下载完成了
                    blocks=null;
                    blocks_complete=null;
                    is_start=false;
                    crt_status=Eventer.State_Complete;
                    if(listener!=null)listener.on_state_change(data,crt_status);
                    return;
                }
                //计算分多少个块
                if(_need_dl_length<=Max_Unit){
                    DLBlock _one=new DLBlock();
                    _one.index=0;
                    _one.start=file_size;
                    _one.end=data.length-1;
                    _one.length=(int)(_one.end-_one.start+1);
                    blocks.add(_one);
                }else{
                    int _bs=(int)(_need_dl_length/Max_Unit)+(_need_dl_length%Max_Unit>0?1:0);
                    for(int i=0;i<_bs;++i){
                        DLBlock _tmp=new DLBlock();
                        _tmp.index=i;
                        _tmp.start=file_size+i*Max_Unit;
                        long _end=file_size+(i+1)*Max_Unit-1;
                        if(_end<data.length)_tmp.end=_end;
                        else _tmp.end=data.length-1;
                        _tmp.length=(int)(_tmp.end-_tmp.start+1);
                        blocks.add(_tmp);
                    }
                }
                CL.CLOGI("init clocks size:"+blocks.size());
//                for(int i=0;i<blocks.size();++i){
//                    CL.CLOGI("index:"+i+"  "+blocks.get(i).start+" / "+blocks.get(i).end);
//                }
                //开启多线程
                if(blocks.size()>1){
                    for(int i=1;i<Max_Thread_Number;++i){
                        new Worker().start();
                    }
                }
            }

            //下载 处理文件写入
            while (run){
                DLBlock _d=get_block();
                if(_d==null)break;
                //下载
                while (run){
                    try{
                        HttpURLConnection _conn=null;
                        if(data.url.startsWith("http://")){
                            _conn=(HttpURLConnection)new URL(data.url).openConnection();
                        }else if(data.url.startsWith("https://")){
                            _conn=(HttpsURLConnection)new URL(data.url).openConnection();
                        }

                        if (_conn instanceof HttpsURLConnection) {
                            SSLContext sc = SSLContext.getInstance("SSL");
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager2()}, new SecureRandom());
                            } else {
                                sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager()}, new java.security.SecureRandom());
                            }
                            ((HttpsURLConnection) _conn).setSSLSocketFactory(sc.getSocketFactory());
                            ((HttpsURLConnection) _conn).setHostnameVerifier(new HttpsSslCertificate.TrustAnyHostnameVerifier());
                        }


                        _conn.setConnectTimeout(2000);
                        _conn.setReadTimeout(3000);
                        _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                        _conn.setRequestProperty("Range", "bytes=" + _d.start + "-"+_d.end);
                        _conn.connect();
                        _d.crt_point=0;

//                        CL.CLOGI("-------------------------------------------------");
//                        for (Map.Entry<String,List<String>> item:_conn.getHeaderFields().entrySet()){
//                            CL.CLOGI(item.getKey()+":");
//                            for(int i=0;i<item.getValue().size();++i){
//                                CL.CLOGI("       "+item.getValue().get(i));
//                            }
//                        }
//                        CL.CLOGI("-------------------------------------------------");
//                        Thread.sleep(5000);

                        ByteBuffer _buff=ByteBuffer.allocate(Max_Unit);
                        InputStream _is=_conn.getInputStream();
                        byte[] _bb=new byte[8192];
                        int _rc=-1;
                        while (run && (_rc=_is.read(_bb))!=-1){
                            if(_buff.position()==_d.length){
                                break;
                            }
                            exception_times = 0;
                            if(_rc+_d.crt_point>_d.length){
                                _rc=_d.length-_d.crt_point;
                            }
                            _d.crt_point+=_rc;
                            _buff.put(_bb,0,_rc);
                        }
                        _conn.disconnect();
                        _buff.flip();
                        _d.buffer=_buff;
                        if(_d.buffer.limit()!=_d.end-_d.start+1){
                            CL.CLOGI("下载数据不全:"+(_d.end- _d.start+1)+" / "+_d.buffer);
                            throw new RuntimeException("data not enough");
                        }
                        exception_times=0;
                        //加入待写队列
                        synchronized (blocks_complete){
                            while (_d.index!=crt_block_index && blocks_complete.size()>=Max_Cache_Number){
                                blocks_complete.wait();
                                if(!run)break;
                            }
                            blocks_complete.add(_d);
                            CL.CLOGI("cache block size:"+blocks_complete.size());
                        }

                        //写文件
                        if(lock_wirter.tryLock()) {
                            try {
                                while (true) {
                                    DLBlock _block = null;
                                    synchronized (blocks_complete) {
                                        for (int i = 0; i < blocks_complete.size(); ++i) {
                                            DLBlock _tmp = blocks_complete.get(i);
                                            if (_tmp.index == crt_block_index) {
                                                _block = blocks_complete.remove(i);
                                                ++crt_block_index;
                                                break;
                                            }
                                        }
                                        blocks_complete.notifyAll();
                                    }
                                    if (_block != null) {
                                        dl_fos.write(_block.buffer.array(),0,_block.buffer.limit());
                                        int _limit=_block.buffer.limit();
                                        download_size+=_limit;
                                        file_size+=_limit;
                                        long _crt_time=System.currentTimeMillis();
                                        long _time=_crt_time-start_time;
                                        speed=(int)(download_size/(_time/1000.0f));
                                        if(_crt_time-last_update_time>=1000){
                                            last_update_time=_crt_time;
                                            data.pos = file_size;
                                            data.max = data.length;
                                            if(listener!=null)listener.on_load_ratio(data,file_size,speed);
                                        }
//                                        CL.CLOGI("write:"+_block.index+"  "+_block.start+"/"+_block.end+"  dl_size:"+file_size+" ratio:"+speed);
                                    } else
                                        break;
                                }
                            }catch (Exception ex){
                                CL.CLOGE("write download file error:"+ex.toString(),ex);
                                if(listener!=null)listener.on_error(data,Eventer.Error_File_RW);
                                run=false;
                            }finally {
                                lock_wirter.unlock();
                            }
                        }
                        //跳出异常循环
                        break;

                    }catch (Exception ex){
                        CL.CLOGI("download error:"+ex.toString());
                        ++exception_times;
                        if(!delete && exception_times>=REPLAY_NUM){
                            exception_times=0;
                            run=false;
                            synchronized (blocks_complete){
                                blocks_complete.notifyAll();
                            }
                            if(exception_times>=REPLAY_NUM) {
                                CL.CLOGI("downloader error exit!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!********************");
                            }
                            if(listener!=null)listener.on_error(data,Eventer.Error_Network);
                        }
                    }
                }
            }

            //over
            sub_threader_count();
            CL.CLOGI("worker is finish!");
            if(threader_count==0){
                //检查是否还有最后一个没有被写入
                try {
                    if(blocks_complete.size()>0 && run) {
                        while (true) {
                            DLBlock _block = null;
                            for (int i = 0; i < blocks_complete.size(); ++i) {
                                DLBlock _tmp = blocks_complete.get(i);
                                if (_tmp.index == crt_block_index) {
                                    _block = blocks_complete.remove(i);
                                    break;
                                }
                            }
                            if (_block != null) {
                                dl_fos.write(_block.buffer.array(),0,_block.buffer.limit());
                                int _limit=_block.buffer.limit();
                                download_size+=_limit;
                                file_size+=_limit;
                                long _time=System.currentTimeMillis()-start_time;
                                speed=(int)(download_size/(_time/1000.0f));
                                ++crt_block_index;
                                if(listener!=null)listener.on_load_ratio(data,file_size,speed);
                                CL.CLOGI("last write:"+_block.index+"  "+_block.start+"/"+_block.end+"  dl_size:"+file_size+" ratio:"+speed);
                            }else break;
                        }
                    }
                    dl_fos.close();
                }catch (Exception ex){
                    CL.CLOGE("the last write error:"+ex.toString(),ex);
                }

                CL.CLOGI("downloader was stop!!!");
                long _crt_time=System.currentTimeMillis();
                long _cha=_crt_time-start_time;
                CL.CLOGI("download time:"+_cha);
                blocks.clear();
                blocks=null;
                blocks_complete.clear();
                blocks_complete=null;
                is_start=false;
                if(!delete) {
                    if (file_size == data.length) {
                        crt_status = Eventer.State_Complete;
                        if (listener != null) listener.on_state_change(data, crt_status);
                    } else {
                        crt_status = Eventer.State_Stop;
                        if (listener != null) listener.on_state_change(data, crt_status);
                    }
                }else{
                    crt_status=Eventer.State_Stop;
                    if(dl_file!=null && dl_file.exists()){
                        if(dl_file.delete()){
                            if(listener!=null)listener.on_delete(data,true);
                            download_size=0;
                            file_size=0;
                        }
                    }
                    if(listener!=null)listener.on_delete(data,false);
                }
            }
        }
    }
}
