package amazon.browser.lionpro.downloader;

import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ViewHolder;
import amazon.browser.lionpro.screen.DownloadingDLDelagate;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

/**
 * Created by leron on 2016/7/1.
 */
public class Data {

    public static final int Type_Picture =1000;
    public static final int Type_Image_PNG=1001;
    public static final int Type_Image_JPG=1002;
    public static final int Type_Image_BMP=1003;
    public static final int Type_Image_WEBP=1004;
    public static final int Type_Image_GIF=1005;

    public static final int Type_Video=2000;
    public static final int Type_Video_MP4=2001;
    public static final int Type_Video_3GP=2002;
//    public static final int Type_Video_M3U8=2003;
    public static final int Type_Video_MKV=2004;
    public static final int Type_Video_WEBM=2005;
    public static final int Type_Video_MERGE=2006;

    public static final int Type_Music=3000;
    public static final int Type_Music_MP3=3001;
    public static final int Type_Music_M4A=3002;
    public static final int Type_Music_OGG=3003;
    public static final int Type_Music_WAV=3004;
    public static final int Type_Music_FLAC=3005;

    public static final int Type_Doc=4000;

    public static final int Type_APK=5000;
    public static final int Type_Other=6000;

    public static final int Type_M3U8=8000;



//    public static int Get_Type_Major(int type){
//        if(type== Type_Picture)return Type_Picture;
//        else if(type==Type_Video)return Type_Video;
//        else if(type==Type_Music)return Type_Music;
//        else if(type==Type_Doc)return Type_Doc;
//        else if(type==Type_APK)return Type_APK;
//        else if(type==Type_Other)return Type_Other;
//        return 0;
//    }
    public static int Get_Type_minor(String suffix){
        if(suffix==null || suffix.isEmpty())return 0;
        String _suffix=suffix.toLowerCase();
        if(_suffix.equals("mp4") || _suffix.equals("mpg4"))return Type_Video_MP4;
        else if(_suffix.equals("3gpp"))return Type_Video_3GP;
        else if(_suffix.equals("3gp"))return Type_Video_3GP;
        else if(_suffix.equals("mkv"))return Type_Video_MKV;
//        else if(_suffix.equals("m3u8"))return Type_Video_M3U8;
        else if(_suffix.equals("png"))return Type_Image_PNG;
        else if(_suffix.equals("jpg") || _suffix.equals("jpeg"))return Type_Image_JPG;
        else if(_suffix.equals("bmp"))return Type_Image_BMP;
        else if(_suffix.equals("webp"))return Type_Image_WEBP;
        else if(_suffix.equals("gif"))return Type_Image_GIF;
        else if(_suffix.equals("mp3"))return Type_Music_MP3;
        else if(_suffix.equals("m4a"))return Type_Music_M4A;
        else if(_suffix.equals("ogg"))return Type_Music_OGG;
        else if(_suffix.equals("wav"))return Type_Music_WAV;
        else if(_suffix.equals("flac"))return Type_Music_FLAC;
        else if(_suffix.equals("webm"))return Type_Video_WEBM;
        return 0;
    }
    public static String Get_Type_Suffix(int suffix){
        if(suffix==Type_Video_MP4)return "mp4";
        else if(suffix==Type_Video_3GP)return "3gp";
        else if(suffix==Type_Video_MKV)return "mkv";
//        else if(suffix==Type_Video_M3U8)return "m3u8";
        else if(suffix==Type_Image_PNG)return "png";
        else if(suffix==Type_Image_JPG)return "jpg";
        else if(suffix==Type_Image_BMP)return "bmp";
        else if(suffix==Type_Image_WEBP)return "webp";
        else if(suffix==Type_Image_GIF)return "gif";
        else if(suffix==Type_Music_MP3)return "mp3";
        else if(suffix==Type_Music_M4A)return "m4a";
        else if(suffix==Type_Music_OGG)return "ogg";
        else if(suffix==Type_Music_WAV)return "wav";
        else if(suffix==Type_Music_FLAC)return "flac";
        else if(suffix==Type_APK)return "apk";
        else if(suffix==Type_Video_WEBM)return "webm";
        return null;
    }
//    public static String Get_Suffix_Encrypt(int suffix){
//        if(suffix==Type_Video_MP4)return "cl129";
//        else if(suffix==Type_Video_3GP)return "cl201";
//        else if(suffix==Type_Video_MKV)return "cl45c34";
//        else if(suffix==Type_Video_M3U8)return "cl330";
//        else if(suffix==Type_Image_PNG)return "cl838";
//        else if(suffix==Type_Image_JPG)return "cl2834";
//        else if(suffix==Type_Image_BMP)return "cl67df";
//        else if(suffix==Type_Image_WEBP)return "cl89sd";
//        else if(suffix==Type_Image_GIF)return "cl6476";
//        else if(suffix==Type_Music_MP3)return "cl99a";
//        else if(suffix==Type_Music_M4A)return "cl900";
//        else if(suffix==Type_Music_OGG)return "cl8363";
//        else if(suffix==Type_Music_WAV)return "cl0s02";
//        else if(suffix==Type_Music_FLAC)return "clodd02";
//        return null;
//    }
//    public static int Get_Suffix_Decrypt(String str){
//        if(str==null || str.isEmpty())return 0;
//        String _suffix=str.toLowerCase();
//        if(_suffix.equals("cl129"))return Type_Video_MP4;
//        else if(_suffix.equals("cl201"))return Type_Video_3GP;
//        else if(_suffix.equals("cl45c34"))return Type_Video_MKV;
//        else if(_suffix.equals("cl330"))return Type_Video_M3U8;
//        else if(_suffix.equals("cl838"))return Type_Image_PNG;
//        else if(_suffix.equals("cl2834"))return Type_Image_JPG;
//        else if(_suffix.equals("cl67df"))return Type_Image_BMP;
//        else if(_suffix.equals("cl89sd"))return Type_Image_WEBP;
//        else if(_suffix.equals("cl6476"))return Type_Image_GIF;
//        else if(_suffix.equals("cl99a"))return Type_Music_MP3;
//        else if(_suffix.equals("cl900"))return Type_Music_M4A;
//        else if(_suffix.equals("cl8363"))return Type_Music_OGG;
//        else if(_suffix.equals("cl0s02"))return Type_Music_WAV;
//        else if(_suffix.equals("clodd02"))return Type_Music_FLAC;
//        return 0;
//    }


    public static class StructDLItem {

        protected int ID;
        public String ident;
        public String ident_md5;
        public String name;
        public String title,url, url_thumb;
        public String headerParams;
        public int type_major,type_minor;
        public String suffix;
        public long length;
        public String path;
        public String path_thumb;
        public String duration;
        public String provenance;
        public byte[] data_binary;
        public boolean downloaded;
        public String quality;
        public Boolean hasAd;
        public int thread_num;
       // public String listHeaderParams;
        public long pos, max;
        public int show_type=0; //1--wait_confirm 2--div 3--dl_item
        public CommonDownloader dler;

        public int index;

        //UI
        public boolean selected=false;
        protected ViewHolder mHolder;
        public void setHolder(ViewHolder holder) {
            mHolder = holder;
        }

        public ViewHolder getHolder() {
            return mHolder;
        }

        public int getID() {
            return ID;
        }

        private DownloadingDLDelagate dlg;
        public void setDownloadingDLDelagate(DownloadingDLDelagate delagate) {
            dlg = delagate;
        }

        public DownloadingDLDelagate getDelagate() {
            return dlg;
        }

        protected StructDLItem(){}
        protected static StructDLItem Create_New(String ident,String title,String url,int major,int minor,long length, String params){
            Data.StructDLItem _tmp=new Data.StructDLItem();
            _tmp.ident=ident;
            _tmp.type_major=major;
            _tmp.type_minor=minor;
            _tmp.title=title;
            _tmp.url=url;
            _tmp.length=length;
            _tmp.show_type=1;
            _tmp.dler=null;
            _tmp.headerParams = params;
            return _tmp;
        }
        public static StructDLItem Create_New(String ident,String title,String url,int major,String minor,long length, String params){
            Data.StructDLItem _tmp=new Data.StructDLItem();
            _tmp.ident=ident;
            _tmp.type_major=major;
            _tmp.type_minor=major;
            _tmp.suffix=minor;
            _tmp.title=title;
            _tmp.url=url;
            _tmp.length=length;
            _tmp.show_type=1;
            _tmp.dler=null;
            _tmp.headerParams = params;
            return _tmp;
        }

        public static String Get_Json(StructDLItem item){
            try {
                JSONObject _root = new JSONObject();
                _root.put("ID", item.ID);
                _root.put("ident_md5",item.ident_md5);
                _root.put("title",item.title);
                _root.put("name",item.name);
                return _root.toString();
            }catch (Exception ex){}
            return null;
        }
        public static StructDLItem Parse_Single(String json){
            StructDLItem _tmp=new StructDLItem();
            try {
                JSONObject _root = new JSONObject(json);
                if(_root.has("ID"))_tmp.ID=_root.getInt("ID");
                if(_root.has("ident_md5"))_tmp.ident_md5=_root.getString("ident_md5");
                if(_root.has("title"))_tmp.ident_md5=_root.getString("title");
                if(_root.has("name"))_tmp.ident_md5=_root.getString("name");
            }catch (Exception ex){}
            return _tmp;
        }
        public static String Get_Json(ArrayList<StructDLItem> items){
            try{
                JSONArray _root=new JSONArray();
                for(int i=0;i<items.size();++i) {
                    StructDLItem _tmp=items.get(i);
                    JSONObject _item = new JSONObject();
                    _item.put("ID", _tmp.ID);
                    _item.put("ident_md5",_tmp.ident_md5);
                    _item.put("title",_tmp.title);
                    _item.put("name",_tmp.name);
                    _root.put(_item);
                }
                return _root.toString();
            }catch (Exception ex){}
            return null;
        }
        public static ArrayList<StructDLItem> Parse_many(String json){
            ArrayList<StructDLItem> _datas=new ArrayList<>();
            try {
                JSONArray _root=new JSONArray(json);
                for(int i=0;i<_root.length();++i){
                    StructDLItem _tmp=new StructDLItem();
                    JSONObject _item = _root.getJSONObject(i);
                    if(_item.has("ID"))_tmp.ID=_item.getInt("ID");
                    if(_item.has("ident_md5"))_tmp.ident_md5=_item.getString("ident_md5");
                    if(_item.has("title"))_tmp.ident_md5=_item.getString("title");
                    if(_item.has("name"))_tmp.ident_md5=_item.getString("name");
                    _datas.add(_tmp);
                }
            }catch (Exception ex){}
            return _datas;
        }
/*
        @Override
        public boolean on_callback_success(String msg) {
            if (msg.compareTo("ad_loaded") == 0) {
                adapter.notifyDataSetChanged();
            }
            return true;
        }

        @Override
        public void on_callback_fail(int code, String msg) {
            if (bEarned) {
                clickDownloadBtn();
            }
            if (!bEarned && rewardedAd != null && code == 100) {
                CL.CLOGI("Eddy onCB1");
                // showWait();
                //rewardedAd = null;
                //LoadReward(cc, DList.this);

            }
        }
*/

    }



}
