package amazon.browser.lionpro.downloader;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

import lion.CL;


/**
 * Created by leron on 2016/2/28.
 */
class DataBase {

    private final int DB_VERSION=4;
    private static DataBase database;
    private CTMDbHelp db_helper;
    private SQLiteDatabase db;
    private DataBase(Context cc){
        db_helper=new CTMDbHelp(cc,"cldldb",null,DB_VERSION);
    }
    protected static void Init(Context cc){
        if(database==null){
            database=new DataBase(cc);
        }
    }
    protected static DataBase Share_Instance(){
        return database;
    }

    public synchronized ArrayList<Data.StructDLItem> get_all_download(){
        ArrayList<Data.StructDLItem> _datas=new ArrayList<Data.StructDLItem>();
        try{
            db=db_helper.getReadableDatabase();
            Cursor _cursor=db.rawQuery("SELECT * FROM cldl ORDER BY timestamp DESC", null);
            for(_cursor.moveToFirst();!_cursor.isAfterLast();_cursor.moveToNext()){
                Data.StructDLItem _tmp=new Data.StructDLItem();
                _tmp.ID=_cursor.getInt(_cursor.getColumnIndex("_ID"));
                _tmp.ident=_cursor.getString(_cursor.getColumnIndex("ident"));
                _tmp.ident_md5=_cursor.getString(_cursor.getColumnIndex("ident_md5"));
                _tmp.name=_cursor.getString(_cursor.getColumnIndex("name"));
                _tmp.title=_cursor.getString(_cursor.getColumnIndex("title"));
                _tmp.url=_cursor.getString(_cursor.getColumnIndex("url"));
                _tmp.url_thumb =_cursor.getString(_cursor.getColumnIndex("url_thumb"));
                _tmp.type_major=_cursor.getInt(_cursor.getColumnIndex("type_major"));
                _tmp.type_minor=_cursor.getInt(_cursor.getColumnIndex("type_minor"));
                _tmp.suffix=_cursor.getString(_cursor.getColumnIndex("suffix"));
                _tmp.length=_cursor.getLong(_cursor.getColumnIndex("length"));
                _tmp.path=_cursor.getString(_cursor.getColumnIndex("path"));
                _tmp.duration=_cursor.getString(_cursor.getColumnIndex("duration"));
                _tmp.provenance =_cursor.getString(_cursor.getColumnIndex("provenance"));
                _tmp.data_binary=_cursor.getBlob(_cursor.getColumnIndex("data_binary"));
                _tmp.headerParams = _cursor.getString(_cursor.getColumnIndex("list_header_params"));
                _tmp.thread_num = _cursor.getInt(_cursor.getColumnIndex("thread_num"));
                int _ds=_cursor.getInt(_cursor.getColumnIndex("downloaded"));
                _tmp.downloaded=_ds==0?false:true;
                _datas.add(_tmp);
            }
            _cursor.close();

        }catch (Exception ex) {
            CL.CLOGE("dl get_all_download error:" + ex.toString(), ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return _datas;
    }

    public synchronized ArrayList<Data.StructDLItem> get_downloaded(int type){
        ArrayList<Data.StructDLItem> _datas=new ArrayList<Data.StructDLItem>();
        try{
            db=db_helper.getReadableDatabase();
            Cursor _cursor=db.rawQuery("SELECT * FROM cldl WHERE type_major=? AND downloaded=1 ORDER BY timestamp DESC",
                    new String[]{String.valueOf(type)});
            for(_cursor.moveToFirst();!_cursor.isAfterLast();_cursor.moveToNext()){
                Data.StructDLItem _tmp=new Data.StructDLItem();
                _tmp.ID=_cursor.getInt(_cursor.getColumnIndex("_ID"));
                _tmp.ident=_cursor.getString(_cursor.getColumnIndex("ident"));
                _tmp.ident_md5=_cursor.getString(_cursor.getColumnIndex("ident_md5"));
                _tmp.name=_cursor.getString(_cursor.getColumnIndex("name"));
                _tmp.title=_cursor.getString(_cursor.getColumnIndex("title"));
                _tmp.url=_cursor.getString(_cursor.getColumnIndex("url"));
                _tmp.url_thumb =_cursor.getString(_cursor.getColumnIndex("url_thumb"));
                _tmp.type_major=_cursor.getInt(_cursor.getColumnIndex("type_major"));
                _tmp.type_minor=_cursor.getInt(_cursor.getColumnIndex("type_minor"));
                _tmp.suffix=_cursor.getString(_cursor.getColumnIndex("suffix"));
                _tmp.length=_cursor.getLong(_cursor.getColumnIndex("length"));
                _tmp.path=_cursor.getString(_cursor.getColumnIndex("path"));
                _tmp.duration=_cursor.getString(_cursor.getColumnIndex("duration"));
                _tmp.provenance =_cursor.getString(_cursor.getColumnIndex("provenance"));
                _tmp.data_binary=_cursor.getBlob(_cursor.getColumnIndex("data_binary"));
                _tmp.headerParams = _cursor.getString(_cursor.getColumnIndex("list_header_params"));
                _tmp.thread_num = _cursor.getInt(_cursor.getColumnIndex("thread_num"));
                int _ds=_cursor.getInt(_cursor.getColumnIndex("downloaded"));
                _tmp.downloaded=_ds==0?false:true;
                _datas.add(_tmp);
            }
            _cursor.close();

        }catch (Exception ex) {
            CL.CLOGE("dl get_all_download error:" + ex.toString(), ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return _datas;
    }

    public synchronized ArrayList<Data.StructDLItem> get_show_download(){
        ArrayList<Data.StructDLItem> _datas=new ArrayList<Data.StructDLItem>();
        try{
            db=db_helper.getReadableDatabase();
            Cursor _cursor=db.rawQuery("SELECT * FROM cldl WHERE dont_show=0 ORDER BY timestamp DESC", null);
            for(_cursor.moveToFirst();!_cursor.isAfterLast();_cursor.moveToNext()){
                Data.StructDLItem _tmp=new Data.StructDLItem();
                _tmp.ID=_cursor.getInt(_cursor.getColumnIndex("_ID"));
                _tmp.ident=_cursor.getString(_cursor.getColumnIndex("ident"));
                _tmp.ident_md5=_cursor.getString(_cursor.getColumnIndex("ident_md5"));
                _tmp.name=_cursor.getString(_cursor.getColumnIndex("name"));
                _tmp.title=_cursor.getString(_cursor.getColumnIndex("title"));
                _tmp.url=_cursor.getString(_cursor.getColumnIndex("url"));
                _tmp.url_thumb =_cursor.getString(_cursor.getColumnIndex("url_thumb"));
                _tmp.type_major=_cursor.getInt(_cursor.getColumnIndex("type_major"));
                _tmp.type_minor=_cursor.getInt(_cursor.getColumnIndex("type_minor"));
                _tmp.suffix=_cursor.getString(_cursor.getColumnIndex("suffix"));
                _tmp.length=_cursor.getLong(_cursor.getColumnIndex("length"));
                _tmp.path=_cursor.getString(_cursor.getColumnIndex("path"));
                _tmp.duration=_cursor.getString(_cursor.getColumnIndex("duration"));
                _tmp.provenance =_cursor.getString(_cursor.getColumnIndex("provenance"));
                _tmp.data_binary=_cursor.getBlob(_cursor.getColumnIndex("data_binary"));
                _tmp.headerParams = _cursor.getString(_cursor.getColumnIndex("list_header_params"));
                _tmp.thread_num = _cursor.getInt(_cursor.getColumnIndex("thread_num"));
                int _ds=_cursor.getInt(_cursor.getColumnIndex("downloaded"));
                _tmp.downloaded=_ds==0?false:true;
                _datas.add(_tmp);
            }
            _cursor.close();

        }catch (Exception ex) {
            CL.CLOGE("dl get_show_download error:" + ex.toString(), ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return _datas;
    }

    public synchronized boolean check_file_repeat(String md5){
        try{
            db=db_helper.getReadableDatabase();
            Cursor _cursor=db.rawQuery("SELECT count(*) AS size FROM cldl WHERE ident_md5=?", new String[]{md5});
            _cursor.moveToFirst();
            int _size=_cursor.getInt(_cursor.getColumnIndex("size"));
            _cursor.close();
            if(_size>0)return true;
        }catch (Exception ex) {
            CL.CLOGE("dl update download item path error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return false;
    }

    public synchronized int insert_download_item(Data.StructDLItem item){
        if(item == null)return -1;
        try{
            Date date = new Date();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timestamp = sf.format(date);


            db=db_helper.getReadableDatabase();
                    //datetime('now','localtime')
            db.execSQL("INSERT INTO cldl (ident,ident_md5,title,name,url,url_thumb,type_major,type_minor,suffix,length,path,duration,provenance,data_binary,timestamp,list_header_params, thread_num)" +
                    "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                    new Object[]{item.ident,item.ident_md5,item.title,item.name,item.url,item.url_thumb,item.type_major,item.type_minor,item.suffix,
                            item.length,item.path,item.duration,item.provenance,item.data_binary, timestamp, item.headerParams, item.thread_num});
            Cursor _cursor=db.rawQuery("SELECT MAX(_ID) AS CID FROM cldl",null);
            if(_cursor==null || !_cursor.moveToFirst() || _cursor.isAfterLast())return -1;
            int _id=_cursor.getInt(_cursor.getColumnIndex("CID"));
            _cursor.close();
            return _id;
        }catch (Exception ex) {
            CL.CLOGE("dl insert download item error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return -1;
    }

    public synchronized void update_status(int ID, String path, String duration,long length, boolean downloaded){
        if(ID<=0)return;
        try{
            db=db_helper.getReadableDatabase();
            db.execSQL("UPDATE cldl SET path=?,duration=?,downloaded=?,length=? WHERE _ID=?", new Object[]{path, duration,downloaded,length, ID});
        }catch (Exception ex) {
            CL.CLOGE("dl update download item path error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }
    
    public synchronized void update_all_status(Data.StructDLItem item){
        if(item.ID<=0)return;
        try{

            Date date = new Date();
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timestatimestampmp = sf.format(date);
            db=db_helper.getReadableDatabase();
            db.execSQL("UPDATE cldl SET ident=?,ident_md5=?,title=?,name=?,url=?,url_thumb=?,type_major=?,type_minor=?,suffix=?,length=?,path=?,duration=?,provenance=?,data_binary=?,list_header_params=? WHERE _ID=?",
             new Object[]{item.ident, item.ident_md5,item.title,item.name, item.url, item.url_thumb, item.type_major, item.type_minor, item.suffix, item.length, item.path, item.duration, item.provenance,item.data_binary, item.headerParams, item.ID});
        }catch (Exception ex) {
            CL.CLOGE("dl update download item path error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }

//    public synchronized void dont_show_item(int ID){
//        if(ID<=0)return;
//        try{
//            db=db_helper.getReadableDatabase();
//            db.execSQL("UPDATE cldl SET dont_show=1 WHERE _ID=?", new Object[]{ID});
//        }catch (Exception ex) {
//            CL.CLOGE("dl dont show download item error:"+ex.toString(),ex);
//        }
//        finally{
//            if(db!=null && db.isOpen()){
//                db.close();
//                db=null;
//            }
//        }
//    }

    public synchronized void clear_completed(){
        try{
            db=db_helper.getReadableDatabase();
            db.execSQL("UPDATE cldl SET dont_show=1 WHERE downloaded>0");
        }catch (Exception ex) {
            CL.CLOGE("dl dont show download item error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }


    public synchronized void delete_item(int ID){
        if(ID<=0)return;
        try{
            db=db_helper.getReadableDatabase();
            db.execSQL("DELETE FROM cldl WHERE _ID=?", new Object[]{ID});
        }catch (Exception ex) {
            CL.CLOGE("dl delete download item error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }

    public synchronized void update_rename(int ID,String name){
        if(ID<=0)return;
        try{
            db=db_helper.getReadableDatabase();
            db.execSQL("UPDATE cldl SET name=? WHERE _ID=?", new Object[]{name,ID});
        }catch (Exception ex) {
            CL.CLOGE("dl update download item name error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }

    //play list

    public synchronized ArrayList<Data.StructDLItem> get_music_play_list(){
        ArrayList<Data.StructDLItem> _datas=new ArrayList<Data.StructDLItem>();
        try{
            db=db_helper.getReadableDatabase();
            Cursor _cursor=db.rawQuery("SELECT * FROM cldl WHERE music_pl=1 ORDER BY timestamp DESC", null);
            for(_cursor.moveToFirst();!_cursor.isAfterLast();_cursor.moveToNext()){
                Data.StructDLItem _tmp=new Data.StructDLItem();
                _tmp.ID=_cursor.getInt(_cursor.getColumnIndex("_ID"));
                _tmp.ident=_cursor.getString(_cursor.getColumnIndex("ident"));
                _tmp.ident_md5=_cursor.getString(_cursor.getColumnIndex("ident_md5"));
                _tmp.name=_cursor.getString(_cursor.getColumnIndex("name"));
                _tmp.title=_cursor.getString(_cursor.getColumnIndex("title"));
                _tmp.url=_cursor.getString(_cursor.getColumnIndex("url"));
                _tmp.url_thumb =_cursor.getString(_cursor.getColumnIndex("url_thumb"));
                _tmp.type_major=_cursor.getInt(_cursor.getColumnIndex("type_major"));
                _tmp.type_minor=_cursor.getInt(_cursor.getColumnIndex("type_minor"));
                _tmp.suffix=_cursor.getString(_cursor.getColumnIndex("suffix"));
                _tmp.length=_cursor.getLong(_cursor.getColumnIndex("length"));
                _tmp.path=_cursor.getString(_cursor.getColumnIndex("path"));
                _tmp.duration=_cursor.getString(_cursor.getColumnIndex("duration"));
                _tmp.provenance =_cursor.getString(_cursor.getColumnIndex("provenance"));
                _tmp.data_binary=_cursor.getBlob(_cursor.getColumnIndex("data_binary"));
                _tmp.headerParams = _cursor.getString(_cursor.getColumnIndex("list_header_params"));
                _tmp.thread_num = _cursor.getInt(_cursor.getColumnIndex("thread_num"));
                int _ds=_cursor.getInt(_cursor.getColumnIndex("downloaded"));
                _tmp.downloaded=_ds==0?false:true;
                _datas.add(_tmp);
            }
            _cursor.close();

        }catch (Exception ex) {
            CL.CLOGE("dl get_all_download error:" + ex.toString(), ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
        return _datas;
    }

    public synchronized void set_music_show(int ID,boolean show){
        if(ID<=0)return;
        try{
            db=db_helper.getReadableDatabase();
            db.execSQL("UPDATE cldl SET music_pl=? WHERE _ID=?", new Object[]{show?1:0,ID});
        }catch (Exception ex) {
            CL.CLOGE("dl set music show error:"+ex.toString(),ex);
        }
        finally{
            if(db!=null && db.isOpen()){
                db.close();
                db=null;
            }
        }
    }



    private class CTMDbHelp extends SQLiteOpenHelper {

        public CTMDbHelp(Context context, String name, SQLiteDatabase.CursorFactory factory, int version) {
            super(context, name, factory, version);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            //下载记录
            StringBuffer _sql=new StringBuffer();
            _sql.append("create table if not exists ");
            _sql.append("cldl (");
            _sql.append("_ID INTEGER PRIMARY KEY, ");
            _sql.append("ident TEXT, ");
            _sql.append("ident_md5 TEXT, ");
            _sql.append("title TEXT, ");
            _sql.append("name TEXT, ");
            _sql.append("url TEXT, ");
            _sql.append("url_thumb TEXT, ");
            _sql.append("type_major INTEGER, ");
            _sql.append("type_minor INTEGER, ");
            _sql.append("suffix TEXT, ");
            _sql.append("length INTEGER, ");
            _sql.append("path TEXT, ");
            _sql.append("duration TEXT, ");
            _sql.append("provenance TEXT, ");
            _sql.append("data_binary, BLOB, ");
            _sql.append("downloaded INTEGER DEFAULT 0, ");
            _sql.append("dont_show INTEGER DEFAULT 0, ");
            _sql.append("music_pl INTEGER DEFAULT 0, ");
            _sql.append("timestamp TEXT, ");
            _sql.append("list_header_params TEXT, ");
            _sql.append("thread_num INTEGER DEFAULT 0");
            _sql.append(")");
            db.execSQL(_sql.toString());

        }
        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            if (newVersion == 3) {
                db.execSQL("alter table cldl add column list_header_params TEXT");
            } else if (newVersion == 4) {
                db.execSQL("alter table cldl add column thread_num INTEGER DEFAULT 0");
            }
        }
    }
}
