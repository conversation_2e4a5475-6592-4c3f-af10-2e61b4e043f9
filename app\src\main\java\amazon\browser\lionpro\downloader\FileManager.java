package amazon.browser.lionpro.downloader;

import android.content.Context;


import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.primary.Global;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;

import lion.CL;
import lion.CLCallback;
import lion.CLFileSystem;
import lion.CLTools;

/**
 * Created by leron on 2016/7/7.
 */
public class  FileManager {

    public interface Eventer{
        void on_data_update();
        void on_error(int code);
    }


    private Context cc;
    private static Eventer listener;

    private File dir_user_1,dir_user_2;
    private boolean scanning=false;
    private ArrayList<Struct.StoreDir> datas;

    protected FileManager(Context context){
        this.cc=context;
        init_data();
    }

    public static void Set_Listener(Eventer listen){
        listener=listen;
    }

    public ArrayList<Struct.StoreDir> get_datas(){
        if(scanning)return null;
        build_baisc_folder();
        if(dir_user_1==null || !dir_user_1.exists()){
            if(listener!=null)listener.on_error(-100);
            return null;
        }
        return this.datas;
    }
    public File get_major_dir(int type){
        build_baisc_folder();
        if(dir_user_1==null || !dir_user_1.exists())return null;
        if(type==Data.Type_Picture){
            return new File(dir_user_1,"picture");
        }else if(type==Data.Type_Image_GIF){
            return new File(dir_user_1,"gif");
        }else if(type==Data.Type_Video){
            return new File(dir_user_1,"video");
        }
        return null;
    }
    public File get_minor_dir(int type){
        build_baisc_folder();
        if(dir_user_2==null || !dir_user_2.exists())return null;
        if(type==Data.Type_Picture){
            return new File(dir_user_2,"picture");
        }else if(type==Data.Type_Image_GIF){
            return new File(dir_user_2,"gif");
        }
        return null;
    }

    private void build_baisc_folder(){
        //if (Global.IsAndroid10()) {
        if (true) {
            if (Global.Dir_Root_1 != null) {
                //            CL.CLOGI("root1:"+Dir_Root_1.getAbsolutePath());
                if(!Global.Dir_Root_1.exists()) Global.Dir_Root_1.mkdirs();
                dir_user_1=new File(Global.Dir_Root_1,"/user");
                if(!dir_user_1.exists())dir_user_1.mkdirs();
                if(dir_user_1.exists())build_folder_ext(dir_user_1);
            }
        } else {
            /*
            File[] _dirs= CLFileSystem.Get_App_Store(cc);
            if(_dirs==null){
                return;
            }
            File Dir_Root_1=_dirs[0];
            if(Dir_Root_1!=null && Dir_Root_1.exists()){
                CL.CLOGI("root1:"+Dir_Root_1.getAbsolutePath());
                if(!Dir_Root_1.exists()) Dir_Root_1.mkdirs();
                dir_user_1=new File(Dir_Root_1,"/" + Global.downloadPathName + "/user");
                if(!dir_user_1.exists())dir_user_1.mkdirs();
                if(dir_user_1.exists())build_folder_ext(dir_user_1);
            }
            if(_dirs.length>1) {
                File Dir_Root_2 = _dirs[1];
                if (Dir_Root_2 != null && Dir_Root_2.exists()) {
                    CL.CLOGI("root2:" + Dir_Root_2.getAbsolutePath());
                    //Dir_Root_2 = new File(Dir_Root_2, ".freedownloader");
                    Dir_Root_2 = new File(Dir_Root_2, "."+Global.downloadPathName);
                    if (!Dir_Root_2.exists()) Dir_Root_2.mkdirs();
                    dir_user_2 = new File(Dir_Root_2, "user");
                    if (!dir_user_2.exists()) dir_user_2.mkdirs();
                    if(dir_user_2.exists())build_folder_ext(dir_user_2);
                }
            }

            if(dir_user_1!=null)CL.CLOGI("build basic folder:"+dir_user_1.getAbsolutePath());
            if(dir_user_2!=null)CL.CLOGI("build basic folder:"+dir_user_2.getAbsolutePath());

             */
        }
    }

    private void build_folder_ext(File dir){
        File _dir=new File(dir,"video");
        if(!_dir.exists())_dir.mkdir();
        _dir=new File(dir,"music");
        if(!_dir.exists())_dir.mkdir();
        _dir=new File(dir,"picture");
        if(!_dir.exists())_dir.mkdir();
        _dir=new File(dir,"gif");
        if(!_dir.exists())_dir.mkdir();
        _dir=new File(dir,"doc");
        if(!_dir.exists())_dir.mkdir();
        _dir=new File(dir,"apk");
        if(!_dir.exists())_dir.mkdir();
        _dir=new File(dir,"other");
        if(!_dir.exists())_dir.mkdir();
    }

    private void init_data(){
        if(!scanning)scanning=true;
        else return;

        datas=new ArrayList<>();
        //初始化SD卡存储
        if(dir_user_1==null){
            build_baisc_folder();
        }
        if(dir_user_1==null || !dir_user_1.exists()){
            //提示无SD卡
            //CL.CLOGI("!!!TIP no sdcard");
            scanning = false;
            if(listener!=null)listener.on_error(-100);
            return;
        }
        
        //初始化默认统计按钮
        for(int i=1;i<8;++i){
            Struct.StoreDir _item=new Struct.StoreDir();
            switch (i){
                case 1:
                    _item.name=cc.getResources().getString(R.string.store_video);
                    _item.type=1;
                    break;
                case 2:
                    _item.name=cc.getResources().getString(R.string.store_music);
                    _item.type=2;
                    break;
                case 3:
                    _item.name=cc.getResources().getString(R.string.store_image);
                    _item.type=3;
                    break;
                case 4:
                    _item.name=cc.getResources().getString(R.string.store_gif);
                    _item.type=4;
                    break;
                case 5:
                    _item.name=cc.getResources().getString(R.string.store_doc);
                    _item.type=5;
                    break;
                case 6:
                    _item.name=cc.getResources().getString(R.string.store_apk);
                    _item.type=6;
                    break;
                case 7:
                    _item.name=cc.getResources().getString(R.string.store_other);
                    _item.type=7;
                    break;
            }
            datas.add(_item);
        }
        //其他自定义文件夹
//        if(mounted){
//            ArrayList<VirtualCatalog.VCFile> _dirs=VirtualCatalog.Share_Instance(cc).list_folder(0);
//            for(int i=0;i<_dirs.size();++i){
//                VirtualCatalog.VCFile _item=_dirs.get(i);
//                Struct.StructLocal _tmp=new Struct.StructLocal();
//                _tmp.type=0;
//                _tmp.vc=_item;
//                datas.add(_tmp);
//            }
//        }
        //扫描
        if(CLFileSystem.Whether_Availability())new Scaner().start();
    }

    //扫描线程
    private class Scaner extends Thread{

        @Override
        public void run() {
            CL.CLOGI("start scan...");
            try{
                for(int i=0;i<datas.size();++i){
                    Struct.StoreDir _d=datas.get(i);
                    if(_d.type==1){//video
                        _d.dls=DataBase.Share_Instance().get_downloaded(Data.Type_Video);
                        _d.dls.addAll(DataBase.Share_Instance().get_downloaded(Data.Type_M3U8));
                        count_download_dir(_d);
                    }else if(_d.type==2){//music
                        _d.dls=DataBase.Share_Instance().get_downloaded(Data.Type_Music);
                        count_download_dir(_d);
                    }else if(_d.type==3 || _d.type==4){
                            scan_dir(_d);
                    }else if(_d.type==5){//doc
                        _d.dls=DataBase.Share_Instance().get_downloaded(Data.Type_Doc);
                        count_download_dir(_d);
                    }else if(_d.type==6){//apk
                        _d.dls=DataBase.Share_Instance().get_downloaded(Data.Type_APK);
                        count_download_dir(_d);
                    }else if(_d.type==7){//other
                        _d.dls=DataBase.Share_Instance().get_downloaded(Data.Type_Other);
                        count_download_dir(_d);
                    }
                }
                CL.CLOGI("end scan!");
            }catch (Exception ex){
                CL.CLOGE("scaner error:"+ex.toString(),ex);
            }finally {
                scanning=false;
                if(listener!=null)listener.on_data_update();
            }
        }

        private void count_download_dir(Struct.StoreDir dir){
            if(dir.dls==null)return;
            for(int i=0;i<dir.dls.size();++i){
                Data.StructDLItem _item=dir.dls.get(i);
                ++dir.files_size;
                dir.length+=_item.length;
            }
        }

        private void scan_dir(Struct.StoreDir dir){
            File _dir_1=null,_dir_2=null;

            if (dir_user_1 != null) {
                CLTools.isFolderExists(dir_user_1.getAbsolutePath());
            }

            if (dir_user_2 != null) {
                CLTools.isFolderExists(dir_user_2.getAbsolutePath());
            }

            if(dir.type==3) {
                if (dir_user_1 != null && dir_user_1.exists())_dir_1 = new File(dir_user_1, "picture");
                if (dir_user_2 != null && dir_user_2.exists())_dir_2 = new File(dir_user_2, "picture");
            }else if(dir.type==4){
                if(dir_user_1!=null && dir_user_1.exists())_dir_1 = new File(dir_user_1, "gif");
                if(dir_user_2!=null && dir_user_2.exists())_dir_2=new File(dir_user_2,"gif");
            }

            if(_dir_1!=null && !_dir_1.exists())_dir_1.mkdir();
            if(_dir_2!=null && !_dir_2.exists())_dir_2.mkdir();

            dir.files=new ArrayList<>();
            if(_dir_1!=null){
                File[] _f1=_dir_1.listFiles();
                if(_f1!=null && _f1.length>0)
                    for(int n=0;n<_f1.length;++n){
                        dir.files.add(_f1[n]);
                        dir.length+=_f1[n].length();
                    }
            }
            if(_dir_2!=null){
                File[] _f2=_dir_2.listFiles();
                if(_f2!=null && _f2.length>0)
                    for(int n=0;n<_f2.length;++n){
                        dir.files.add(_f2[n]);
                        dir.length+=_f2[n].length();
                    }
            }
            dir.files_size=dir.files.size();
        }
    }


    protected void store_picture(final int type, final String md5, final File file, final CLCallback.CB_TF cber){
        if(type ==0 || md5==null || file==null || cber==null)return;
        new Thread(){
            public void run(){
                try{
                    int _type_major=0;
                    if(type==Data.Type_Image_JPG || type==Data.Type_Image_PNG || type==Data.Type_Image_BMP || type==Data.Type_Image_WEBP){
                        _type_major=3;
                        if(dir_user_1==null)build_baisc_folder();
                        boolean _was_exist=false;
                        if(dir_user_1!=null){
                            File _dir_picture1=new File(dir_user_1,"picture");
                            if(_dir_picture1.exists()){
                                File _fff=new File(_dir_picture1,md5+type);
                                if(_fff.exists())_was_exist=true;
                            }
                        }
                        if(!_was_exist && dir_user_2!=null){
                            File _dir_picture2=new File(dir_user_2,"picture");
                            if(_dir_picture2.exists()){
                                File _fff=new File(_dir_picture2,md5+type);
                                if(_fff.exists())_was_exist=true;
                            }
                        }
                        if(_was_exist){
                            cber.on_callback_success();
                            return;
                        }
                    }else if(type==Data.Type_Image_GIF){
                        _type_major=4;
                        if(dir_user_1==null)build_baisc_folder();
                        boolean _was_exist=false;
                        if(dir_user_1!=null){
                            File _dir_picture1=new File(dir_user_1,"gif");
                            if(_dir_picture1.exists()){
                                File _fff=new File(_dir_picture1,md5+type);
                                if(_fff.exists())_was_exist=true;
                            }
                        }
                        if(!_was_exist && dir_user_2!=null){
                            File _dir_picture2=new File(dir_user_2,"gif");
                            if(_dir_picture2.exists()){
                                File _fff=new File(_dir_picture2,md5+type);
                                if(_fff.exists())_was_exist=true;
                            }
                        }
                        if(_was_exist){
                            cber.on_callback_success();
                            return;
                        }
                    }else{
                        cber.on_callback_fail(-10,"not support picture format");
                        return;
                    }
                    String pic_type = "unknown";
                    File _out=null;
                    if(type==Data.Type_Image_JPG || type==Data.Type_Image_PNG || type==Data.Type_Image_BMP || type==Data.Type_Image_WEBP){
                      //Global.Flurry_Send_Event("dl_picture");

                        if (type == Data.Type_Image_JPG) {
                            pic_type = "jpg";
                        } else if (type == Data.Type_Image_PNG) {
                            pic_type = "png";
                        } else if (type == Data.Type_Image_BMP) {
                            pic_type = "bmp";
                        } else if (type == Data.Type_Image_WEBP) {
                            pic_type = "webp";
                        }

                        if(dir_user_1!=null && dir_user_1.getUsableSpace()>file.length()){

                            _out=new File(dir_user_1,"picture/"+md5+"."+pic_type);
                        }else if(dir_user_2!=null && dir_user_2.getUsableSpace()>file.length()){
                            _out=new File(dir_user_2,"picture/"+md5+"."+pic_type);
                        }else{
                            cber.on_callback_fail(-11,"no space to storage!");
                            return;
                        }
                    }else if(type==Data.Type_Image_GIF){
                        if (type == Data.Type_Image_JPG) {
                            pic_type = "gif";
                        }

                       //Global.Flurry_Send_Event("dl_gif");
                        if(dir_user_1!=null && dir_user_1.getUsableSpace()>file.length()){
                            _out=new File(dir_user_1,"gif/"+md5+"."+pic_type);
                        }else if(dir_user_2!=null && dir_user_2.getUsableSpace()>file.length()){
                            _out=new File(dir_user_2,"gif/"+md5+"."+pic_type);
                        }else{
                            cber.on_callback_fail(-11,"no space to storage!");
                            return;
                        }
                    }

                    FileInputStream _fis=new FileInputStream(file);
                    FileOutputStream _fos=new FileOutputStream(_out);
                    byte[] _buff=new byte[8192];
                    int _count=-1;
                    while ((_count=_fis.read(_buff))!=-1){
                        _fos.write(_buff,0,_count);
                    }
                    _fis.close();
                    _fos.flush();
                    _fos.close();

                    cber.on_callback_success();
                    if(!scanning && datas!=null){
                        for(int i=0;i<datas.size();++i){
                            Struct.StoreDir _d=datas.get(i);
                            if(_d.type==_type_major){
                                _d.files.add(_out);
                                ++_d.files_size;
                                _d.length+=file.length();
                            }
                        }
                    }

                }catch (Exception ex){
                    cber.on_callback_fail(-1,ex.toString());
                }
            }
        }.start();
    }

    public void notify_download_complete(Data.StructDLItem item){
        if(item==null)return;
        if(item.ID<=0)return;
        if(datas==null || datas.size() == 0)return;
        int _index=-1;
        if(item.type_major==Data.Type_Video || item.type_major==Data.Type_M3U8)_index=0;
        else if(item.type_major==Data.Type_Music)_index=1;
        else if(item.type_major==Data.Type_Doc)_index=4;
        else if(item.type_major==Data.Type_APK)_index=5;
        else if(item.type_major==Data.Type_Other)_index=6;
        if(_index==-1|| datas.size() == 0)return;
        Struct.StoreDir _dir=datas.get(_index);
        for(int i=0;i<_dir.dls.size();++i){
            if(item.ID==_dir.dls.get(i).ID)return;
        }
        _dir.dls.add(0,item);
        ++_dir.files_size;
        _dir.length+=item.length;
        if(listener!=null)listener.on_data_update();
    }

    protected File get_video_directory(){
        try {
            if (dir_user_1 == null) build_baisc_folder();
            if (dir_user_1 == null) return null;
            if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath())) {
                dir_user_1.mkdirs();
                //return null;
            }
        } catch (Exception ex) {
            return null;
        }
        return new File(dir_user_1,"video");
    }
    protected File get_music_directory(){

        if(dir_user_1==null)build_baisc_folder();
        if(dir_user_1==null)return null;
        if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath()))
            return null;

        return new File(dir_user_1,"music");
    }
    protected File get_picture_directory(){
        if(dir_user_1==null)build_baisc_folder();
        if(dir_user_1==null)return null;
        if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath()))
            return null;

        return new File(dir_user_1,"picture");
    }
    protected File get_gif_directory(){
        if(dir_user_1==null)build_baisc_folder();
        if(dir_user_1==null)return null;
        if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath()))
            return null;

        return new File(dir_user_1,"gif");
    }
    protected File get_doc_directory(){
        if(dir_user_1==null)build_baisc_folder();
        if(dir_user_1==null)return null;
        if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath()))
            return null;

        return new File(dir_user_1,"doc");
    }
    protected File get_apk_directory(){
        if(dir_user_1==null)build_baisc_folder();
        if(dir_user_1==null)return null;
        if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath()))
            return null;

        return new File(dir_user_1,"apk");
    }
    protected File get_other_directory(){
        if(dir_user_1==null)build_baisc_folder();
        if(dir_user_1==null)return null;
        if (!CLTools.isFolderExists(dir_user_1.getAbsolutePath()))
            return null;

        return new File(dir_user_1,"other");
    }

}
