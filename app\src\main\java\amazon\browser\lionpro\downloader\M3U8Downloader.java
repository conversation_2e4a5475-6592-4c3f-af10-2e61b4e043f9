
package amazon.browser.lionpro.downloader;

import static org.jsoup.internal.StringUtil.isNumeric;

import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.util.HttpsSslCertificate;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.channels.FileChannel;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;

import lion.CL;
import lion.CLFileSystem;
import lion.CLTools;

/**
 * Created by leron on 2016/10/28.
 */

public class M3U8Downloader extends CommonDownloader{

    private volatile boolean run=true;
    private boolean not_run_once=true;
    private boolean delete=false;
    private boolean is_start=false;
    private long download_size=0;
    private long last_update_time=0;
    private int speed;
    private long start_time=0;
    //private int crt_status=Eventer.State_Stop;
    private int COUNTMAX = 300;
    private int count = 0;
    private JSONObject _root = null;
    private Map<String, String> list_params = new HashMap<>();

    public static Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            //ffmpeg的一个bug，生成的文件必须要有后缀，所以这里改名为target
            if (msg.obj == null) return;
            Data.StructDLItem data = (Data.StructDLItem) msg.obj;
            if (msg.arg1 == 100) {
                data.show_type = 3;
                data.type_major = Data.Type_Video;
                data.type_minor = Data.Type_Video_MP4;
                //  Server.Update_All_Download(data);
                int crt_status = CommonDownloader.Eventer.State_packet;
                if (data.dler.listener != null)
                    data.dler.listener.on_state_change(data, crt_status);
            } else if (msg.arg1 == 200) {
                String file_path = data.path;
                Server.Remove_Downloaditem_Downloader(data);
                data.show_type = 3;
                data.type_major = Data.Type_Video;
                data.type_minor = Data.Type_Video_MP4;
                data.path = file_path + ".mp4";
                Server.Update_All_Download(data);
                int crt_status = CommonDownloader.Eventer.State_Complete;
                if (data.dler.listener != null)
                    data.dler.listener.on_state_change(data, crt_status);
                File _dir = new File(file_path);
                ((M3U8Downloader) data.dler).deleteDir(_dir);
            }
        }
    };

    public M3U8Downloader(Data.StructDLItem dl_data){
        super(dl_data);

        if (Setting.Share_Setting().get_subscription_flag()) {
            MaxWalker = Setting.Share_Setting().get_thread_number();
        } else {
            if (data.thread_num > 0) {
                MaxWalker = data.thread_num;
            }
        }
    }

    public void go() {
        if(delete)return;
        if(!is_start) {
            is_start=true;
            run=true;
            if (data == null) return;
            if (data.path == null) {
                if (listener != null) listener.on_error(data,Eventer.Error_No_Path);
                is_start=false;
                return;
            }
            not_run_once=false;
            crt_status=Eventer.State_Start;
            if(listener!=null)listener.on_state_change(data,crt_status);
            delete=false;
            download_size=0;
            speed=0;
            start_time=System.currentTimeMillis();
            last_update_time=start_time;
            new Worker().start();
        }
    }

    public void stop() {
        if(delete)return;
        run=false;
        is_start = false;
    }

    public void delete(){
        run=false;
        delete=true;
        if(crt_status==Eventer.State_Complete || crt_status==Eventer.State_Stop){
            File _f=new File(this.data.path);
            if(_f.exists()){
                if(CLFileSystem.Delete_Dir(_f)) {
                    if (this.listener != null)
                        this.listener.on_delete(this.data, true);
                }
            }
        }
    }

    public long get_total_size(){
        return downloadCount.get();
        //return download_size;
    }
    public int get_speed(){
        if(crt_status==Eventer.State_Start){
            long _crt_time=System.currentTimeMillis();
            long _time=_crt_time-start_time;
            return (int)(downloadCount.get()/(_time/1000.0f));
            //return (int)(download_size/(_time/1000.0f));
        }
        return speed;
    }
    public int get_status(){
        return crt_status;
    }


    private boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (int i=0; i<children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        // 目录此时为空，可以删除
        return dir.delete();
    }

    private class Mkey extends JSONObject{
        Mkey() {
            super();
        }
        public String url;
        public byte[] data;
        //  public byte[] iv;
        public String iv;
    }

    private class Item {
        public String url;
        public String path;
        public Mkey key;
        public Map<String, String> header;
        public int byteRangeStart = -2;  // -2:无 -1:上一个ts的结束位置+1 >=0:正常偏移量
        public int byteRangeLength;
    }


    private class Worker extends Thread{
        Boolean bexception_exit = true;

        // 统计需要下载的资源，有#EXT-X-BYTERANGE标记的资源可能只需要下载一个文件
        private ArrayList<Item> statisticsDownloadTS(JSONArray jsData) {
            try {
                downloadItems = new ArrayList<>();
                HashSet<String> fs = new HashSet<>();
                ArrayList<Item> TsItemList = new ArrayList<>();
                Item item = null;
                for (int i = 0; i < jsData.length(); ++i) {
                    JSONObject obj = jsData.getJSONObject(i);
                    String _furl = obj.getString(""+i);

                    if (!fs.contains(_furl)) {
                        fs.add(_furl);
                        DownloadItem di = new DownloadItem();
                        di.url = _furl;
                        di.fileName = String.valueOf(i);
                        int have_key = 0;
                        if (obj.has("key")) {
                            have_key = obj.getInt("key");
                            Mkey key = new Mkey();
                            key.iv = obj.getString("iv");
                            key.url = obj.getString("url");
                            di.key = key;
                        }

                        //di.header = item.header;
                        downloadItems.add(di);
                    }

                    item = new Item();
                    if (obj.has("key")) {
                        item.key = new Mkey();
                        item.key.iv = obj.getString("iv");
                        item.key.url = obj.getString("url");
                    }
                    item.url = _furl;
                    item.path = data.path + File.separator + String.valueOf(i);
                    TsItemList.add(item);
                }
                downloadItemsLength = downloadItems.size();
                return TsItemList;
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return null;
        }




        @Override
        public void run() {
            try{
                //下载m3u8
                CL.CLOGI("m3u8 downloader was start...");
                String _tmp_url = "";
                File _dir=new File(data.path);
                if(!_dir.exists())_dir.mkdirs();


                File[] files = _dir.listFiles();
                int have_download_count = 0;
                if(files != null){ //判断权限
                    for (File _file : files) {
                        if (_file.isFile()) {
                            String name = _file.getName();
                            if (!name.equalsIgnoreCase("om3u8") &&
                                    !name.equalsIgnoreCase("config") &&
                                    !name.equalsIgnoreCase("file_count") &&
                                    !name.equalsIgnoreCase("f_tmp") &&
                                    !name.equalsIgnoreCase("playlist")) {
                                have_download_count++;
                            }
                        }
                    }
                }

                File f_count = new File(_dir,"file_count");
                if (f_count.exists()) {
                    BufferedReader f_br = new BufferedReader(new FileReader(f_count));
                    String s_number = f_br.readLine();

                    if (have_download_count > 0 && !s_number.isEmpty()) {
                        data.pos = have_download_count + 1;
                        data.max = Integer.valueOf(s_number);
                        speed = 100;
                        if (listener != null) listener.on_load_ratio(data, download_size, speed);
                    }
                } else {
                    data.pos = 5;
                    data.max = 100;
                    speed = 100;
                    if(listener!=null)listener.on_load_ratio(data,download_size,speed);
                }


                String [] params = null;
                list_params.clear();
                if (data.headerParams != null && data.headerParams.length() > 0) {
                    String[] array = CLTools.strSplitToArray(data.headerParams, "[this<>map<>list]", false);//entity.originUrl.split("[this<>map<>list]");
                    if (array != null) {
                        for (int i=0; i<array.length; i++) {
                            String tmp = array[i];
                            params = CLTools.strSplitToArray(tmp, "[=+v+=]", false);//tmp.split("[=+v+=]");
                            if (params[0].compareTo("Range") != 0)
                                list_params.put(params[0], params[1]);
                        }
                    } else {
                        params = CLTools.strSplitToArray(data.headerParams, "[=+v+=]", false);//entity.originUrl.split("[=+v+=]");
                        if (params[0].compareTo("Range") != 0)
                            list_params.put(params[0], params[1]);
                    }
                }

                File _om3u8=new File(_dir,"om3u8");
                if(!_om3u8.exists()){
                    HttpURLConnection _conn=null;
                    if(data.url.startsWith("http://")){
                        _conn=(HttpURLConnection)new URL(data.url).openConnection();
                    }else if(data.url.startsWith("https://")){
                        _conn=(HttpsURLConnection)new URL(data.url).openConnection();
                    }
                    _conn.setConnectTimeout(8000);
                    _conn.setReadTimeout(8000);



                    if (data.headerParams != null && data.headerParams.length() > 0) {
                        if (list_params.size() > 0) {
                            for (Map.Entry<String, String> entry : list_params.entrySet()) {
                                String key = entry.getKey();
                                String value = entry.getValue();
                                _conn.setRequestProperty(key, value);
                            }
                        }
                    } else {
                        _conn.setRequestProperty("User-Agent", Global.Crt_UA);//Global.Crt_UA
                    }
                    _conn.connect();
                    InputStream _is=_conn.getInputStream();
                    FileOutputStream _fos=new FileOutputStream(_om3u8);
                    int _c=0;
                    byte[] _buff=new byte[1024*10];
                    while ((_c=_is.read(_buff))!=-1){
                        _fos.write(_buff,0,_c);
                    }
                    _is.close();
                    _conn.disconnect();
                    _fos.flush();
                    _fos.close();
                    // CL.CLOGI("下载m3u8文件完毕:"+_om3u8.length());

                    //解析是否是多分辨率m3u8
                    BufferedReader _br=new BufferedReader(new FileReader(_om3u8));
                    String scheme = CLTools.GetUrlScheme(data.url);
                    String _host2=data.url.substring(0,data.url.lastIndexOf('/')+1);
                    String _host=CLTools.Get_Url_Host(data.url);
                    ;
                    if (scheme == null)
                        scheme = "";
                    _host = scheme +"://" + _host;
                    String _url=null;
                    String orig_line;//_vv;
                    //默认取第一个分辨率的m3u8视频
                    boolean bfind = false;
                    while ((orig_line=_br.readLine())!=null){
                        if (bfind) {
                            _url=orig_line;
                            break;
                        } else {
                            String _tmp = orig_line.trim().toUpperCase();
//                        if(_tmp.startsWith("#ext-x-stream-info") && _tmp.endsWith(",")){
//                            _url=_br.readLine();
//                            break;
//                        }
                            if (_tmp.startsWith("#EXT-X-STREAM-INF")) {
                                bfind = true;
                            }
                        }
                    }
                    _tmp_url = _host2;
                    if(_url!=null) {
                        if (!_url.startsWith("http://") && !_url.startsWith("https://")) {
                            if (_url.indexOf("/") != 0) {

                                _url = _host2 + _url;
                                _tmp_url = _url;
                            }
                            else
                                _url = _host  +_url;
                        }
                    }
                    //下载多分辨率的m3u8原文件
                    if(_url!=null)
                    {
                        HttpURLConnection _conn2=null;
                        if(_url.startsWith("http://")){
                            _conn2=(HttpURLConnection)new URL(_url).openConnection();
                        }else if(data.url.startsWith("https://")){
                            _conn2=(HttpsURLConnection)new URL(_url).openConnection();
                        }
                        _conn2.setConnectTimeout(8000);
                        _conn2.setReadTimeout(8000);


                        params = null;
                       // list.clear();
                        if (list_params.size() > 0) {
                            for (Map.Entry<String, String> entry : list_params.entrySet()) {
                                String key = entry.getKey();
                                String value = entry.getValue();
                                if (key.compareTo("Range") != 0)
                                    _conn.setRequestProperty(key, value);
                            }
                        } else {
                            _conn2.setRequestProperty("User-Agent", Global.Crt_UA);
                        }
                        _conn2.connect();
                        _is=_conn2.getInputStream();
                        _fos=new FileOutputStream(_om3u8);
                        while ((_c=_is.read(_buff))!=-1){
                            _fos.write(_buff,0,_c);
                        }
                        _is.close();
                        _conn2.disconnect();
                        _fos.flush();
                        _fos.close();
                        //  CL.CLOGI("下载m3u8_mul文件完毕:"+_om3u8.length());
                    }

                }

                File _config=new File(_dir,"config");
                if(!_config.exists()){
                    //String _host=data.url.substring(0,data.url.lastIndexOf('/')+1);
                    String scheme = CLTools.GetUrlScheme(data.url);
                    String _host=CLTools.Get_Url_Host(data.url);
                    String _host2=data.url.substring(0,data.url.lastIndexOf('/')+1);
                    if (scheme == null)
                        scheme = "";
                    _host = scheme +"://" + _host;

                    JSONObject _root=null;
                    JSONArray _as=null;
                    BufferedReader _br=new BufferedReader(new FileReader(_om3u8));
                    String origin_line;
                    int _index=0;
                    boolean _end=false;
                    int _duration=0;

                    String mediaSequence = null;
                    Mkey mkey = null;
                    while ((origin_line=_br.readLine())!=null) {
                        String _tmp = origin_line.trim().toLowerCase();
                        String _url = null;
//                        if(_tmp.startsWith("#extinf") && _tmp.contains(",")){
//                            _url=_br.readLine();
//                        }
                        if (_tmp.equals("#extm3u")) {
                            // CL.CLOGI("m3u8_start");
                            _root = new JSONObject();
                        } else if (_tmp.startsWith("#ext-x-media-sequence")) {
                            mediaSequence = origin_line.substring(_tmp.indexOf(":") + 1);
                        }else if(_tmp.equals("#ext-x-endlist")){
                            //  CL.CLOGI("m3u8_end");
                            _end=true;
                            if(_root!=null) {
                                if(_as!=null) {
                                    _root.put("as",_as);
                                    File count = new File(_dir,"file_count");
                                    FileOutputStream _fos_count = new FileOutputStream(count);
                                    _fos_count.write(String.valueOf(_as.length()).getBytes("utf8"));
                                    _fos_count.flush();
                                    _fos_count.close();
                                }
                                FileOutputStream _fos = new FileOutputStream(_config);
                                _fos.write(_root.toString().getBytes("utf8"));
                            }
                        } else if (_tmp.startsWith("#ext-x-key:")) {
                            //原大小字符行
                            String[] keys = origin_line.split(",");
                            if (keys.length > 0 && keys[0].contains("METHOD=")) {
                                if (keys[0].contains("NONE")) {
                                    continue;
                                }

                                mkey = new Mkey();

                                if (keys.length > 1 && keys[1].startsWith("URI")) {
                                    String address = keys[1].substring(keys[1].indexOf("\"") + 1, keys[1].lastIndexOf("\""));
                                    if (!address.startsWith("http")) {
                                        if (!address.startsWith("/")) {
                                            address = _host2 + address;
                                        } else {
                                            address = _host + address;
                                        }
                                    }
                                    mkey.url = address;
                                }
                                if (keys.length > 2 && keys[2].startsWith("IV")) {
                                    String iv = keys[2].replace("IV=", "");
                                    mkey.iv = iv;//ivToBytes(iv);
                                }
                                if (mkey.iv == null) {
                                    mkey.iv = mediaSequence;
                                }
                            }
                        } else if (_tmp.startsWith("#extinf")) {
                            if(_as == null) {
                                _as = new JSONArray();
                            }
                            JSONObject _d = new JSONObject();

                            String cur_line = _br.readLine();
                            //原大小字符
                            if (cur_line.startsWith("#EXT-X-BYTERANGE")) {
//                                String tmp_line = _br.readLine();
//                                _d.put(""+_index, tmp_line);
                                //未来处理
                                continue;
                            } else if (cur_line.startsWith("#EXT-X-PROGRAM-DATE-TIME")) {
                                _url = _br.readLine();
                            } else  {
                                _url = cur_line;
                            }

                            if (!TextUtils.isEmpty(_url)) {
                                String[] _kv = origin_line.split(":");
                                if (_kv == null) {
                                    continue;
                                }
                                if (_kv.length == 2) {
                                    String _tmp_dd = _kv[1].substring(0, _kv[1].length() - 1);
                                    String[] _v = _tmp_dd.split(",");
                                    try {
                                        _duration += (int) Double.parseDouble(_v[0]) * 1000;
                                    } catch (Exception ex) {
                                        _duration = 0;
                                    }

                                    _d.put("d", _v[0]);
                                }
                                if (!_url.startsWith("http")) {
                                    if (!_url.startsWith("/")) {
                                        _url = _host2 + _url;
                                    } else {
                                        _url = _host + _url;
                                    }
                                }
                                _d.put(""+_index, _url);
                                ++_index;
                                if (mkey != null) {
                                    _d.put("key", 1);
                                    _d.put("url", mkey.url);
                                    _d.put("iv", mkey.iv);
                                }
                                _as.put(_d);

                            } else {
                                throw new RuntimeException("parse error");
                            }
                        }

                    }
                    if(!_end) {
                        throw new RuntimeException("illegal format:no end");
                    }
                    data.duration= CLTools.Get_Media_Duration(_duration);
                    Server.Update_Download(data);
                }

                FileInputStream _fis=new FileInputStream(_config);
                ByteArrayOutputStream _baos=new ByteArrayOutputStream();
                int _c=0;
                byte[] _buff=new byte[1024*20];
                while ((_c=_fis.read(_buff))!=-1){
                    _baos.write(_buff,0,_c);
                }
                _fis.close();
                _baos.flush();
                _buff=_baos.toByteArray();
                _baos.close();


                _root=new JSONObject(new String(_buff,"utf8"));
                ArrayList<String> list_ts = new ArrayList<String>();
                if(_root.has("as")) {
                    JSONArray _as = _root.getJSONArray("as");
                    tsdata = statisticsDownloadTS(_as);
                }

                if (tsdata == null) {
                    count = 0;
                    run=false;
                    crt_status = Eventer.State_Stop;
                    if (listener != null) listener.on_state_change(data, crt_status);
                    return;
                }

                        // 多线程下载 5个线程
                startTime = System.currentTimeMillis();
                lastTime = startTime;
                dlLength.set(0);
                downloadCount.set(0);
                dlProgress = 0;
                dlLengthRatio = "";
                stopWalkerCount = 0;
               // MaxWalker = Setting.Share_Setting().get_thread_number();


                for (int i = 0; i < MaxWalker; ++i) {
                    new Thread(new Meu8DownloaderThread(_dir)).start();
                }

                // CL.CLOGI("m3u8 download over");
            }catch (Exception ex){
                // CL.CLOGE("M3U8Downloader error:"+ex.toString(),ex);
              //  ex.printStackTrace();
              //  if (ex instanceof EOFException || ex instanceof SocketTimeoutException) {
                    if (!is_start || !run) {
                        count = 0;
                        crt_status = Eventer.State_Stop;
                        if (listener != null) listener.on_state_change(data, crt_status);
                        return;
                    }
                    if (count++ < COUNTMAX) {
                        CL.CLOGI("count = " + count);
                        if (bexception_exit) {
                            try {
                                Thread.sleep(300);
                            } catch (Exception exx) {}


                            is_start = false;
                            CL.CLOGI("M3U8Downloader.this.go()");
                            M3U8Downloader.this.go();
                            return;
                        }
                    }
                //}
                count = 0;
                run=false;
                is_start = false;
                crt_status = Eventer.State_Stop;
                if (listener != null) listener.on_state_change(data, crt_status);
            }finally {
                //is_start=false;
            }

        }

//        public boolean mergeFiles2(String[] fpaths, String output, CommonCallBack callback) {
//            if (fpaths == null || fpaths.length < 1 || TextUtils.isEmpty(output)) {
//                return false;
//            }
//            if (fpaths.length == 1) {
//                return new File(fpaths[0]).renameTo(new File(output));
//            }
//
//            StringBuilder input = new StringBuilder();
//            for (int i = 0; i < fpaths.length; i ++) {
//                String filename = fpaths[i];
//                input.append(filename);
//                input.append("|");
//            }
//
//            String concatStr = input.substring(0, input.length() - 1);
//            String cmd = "ffmpeg -y -i concat:"+concatStr+" -c copy "+output;
//            FFmpegCommand.runAsync(cmd.split(" "), callback);
//            return true;
//        }






    }

    private class DownloadItem {
        public String url;
        public String fileName;
        public Mkey key;
      //  public Map<String, String> header;
    }


    private static final String SIGNAL = "signal";
    private static final String SIGNAL2 = "signal2";
    private ArrayList<Item> tsdata = new ArrayList<>();
    private ArrayList<DownloadItem> downloadItems;
    private int downloadItemsLength;
    private AtomicLong dlLength = new AtomicLong(0);
    private final AtomicLong downloadCount = new AtomicLong(0);
    private long startTime;
    private long lastTime;
    private float dlProgress;
    private String dlLengthRatio = "";
    private int stopWalkerCount;
    private int MaxWalker = 1;
    String dlSpeed = "...";
    private AtomicInteger nIndex = new AtomicInteger(0);
    private class Meu8DownloaderThread extends Thread {
        private File dir;
        private int retry_number = 0;
        private Meu8DownloaderThread(File dir) {
            this.dir = dir;
        }

        @Override
        public void run() {
            while (run) {
                DownloadItem di = null;
                synchronized (SIGNAL) {
                    if (!downloadItems.isEmpty()) {
                        di = downloadItems.remove(0);
                    }
                }


                if (di != null) {
                    File _f = new File(dir, di.fileName);

                    if (!_f.exists()) {
                        while (run) {
                            try {
                                if (di.key != null && di.key.data == null) {
                                    URL httpUrl = new URL(di.key.url);
                                    // 打开网络链接
                                    HttpURLConnection _conn;
                                    _conn = (HttpURLConnection) httpUrl.openConnection();

                                    if (_conn instanceof HttpsURLConnection) {
                                        SSLContext sc = SSLContext.getInstance("SSL");
                                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                            sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager2()}, new SecureRandom());
                                        } else {
                                            sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager()}, new SecureRandom());
                                        }
                                        ((HttpsURLConnection) _conn).setSSLSocketFactory(sc.getSocketFactory());
                                        ((HttpsURLConnection) _conn).setHostnameVerifier(new HttpsSslCertificate.TrustAnyHostnameVerifier());
                                    }

                                    _conn.setConnectTimeout(8000);
                                    _conn.setReadTimeout(8000);

                                    if (list_params.size() > 0) {
                                        for (Map.Entry<String, String> entry : list_params.entrySet()) {
                                            String key1 = entry.getKey();
                                            String value1 = entry.getValue();
                                            _conn.setRequestProperty(key1, value1);
                                        }
                                    } else {
                                        _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                                        _conn.setRequestProperty("Connection", "keep-alive");
                                    }

                                    _conn.connect();

                                    InputStream _is = _conn.getInputStream();
                                    di.key.data = CLTools.read(_is);
                                    _is.close();
                                    _conn.disconnect();
                                }

                                File _f_tmp = new File(dir, CLTools.Get_MD5(di.url) +"f_tmp");
                                HttpURLConnection _conn = null;
                                _conn = (HttpURLConnection) new URL(di.url).openConnection();


                                if (_conn instanceof HttpsURLConnection) {
                                    SSLContext sc = SSLContext.getInstance("SSL");
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                        sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager2()}, new SecureRandom());
                                    } else {
                                        sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager()}, new SecureRandom());
                                    }
                                    ((HttpsURLConnection) _conn).setSSLSocketFactory(sc.getSocketFactory());
                                    ((HttpsURLConnection) _conn).setHostnameVerifier(new HttpsSslCertificate.TrustAnyHostnameVerifier());
                                }

                                _conn.setConnectTimeout(6000);
                                _conn.setReadTimeout(10000);

                                if (list_params.size() > 0) {
                                    for (Map.Entry<String, String> entry : list_params.entrySet()) {
                                        String key1 = entry.getKey();
                                        String value = entry.getValue();
                                        if (key1.compareTo("Range") != 0)
                                            _conn.setRequestProperty(key1, value);
                                    }
                                } else {
                                    _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                                    _conn.setRequestProperty("Connection", "keep-alive");
                                }

                                _conn.connect();
                                InputStream _is = _conn.getInputStream();
                                FileOutputStream _fos = new FileOutputStream(_f_tmp);
                                int _c = 0;
                                byte[] _buff = new byte[1024*20];
                                while ((_c = _is.read(_buff)) != -1) {
                                    if (!run) {
                                        break;
                                    }

                                    _fos.write(_buff, 0, _c);
                                    downloadCount.addAndGet(_c);
                                    if (retry_number != 0) {
                                        retry_number = 0;
                                    }
                                    synchronized (SIGNAL2) {
                                        long crtTime = System.currentTimeMillis();
                                        if (crtTime > lastTime + 1000) {
                                            CL.CLOGI("Eddy3 this ============> this = " + this);
                                            CL.CLOGI("Eddy3 this ============> downloadCount = " + downloadCount.get() + "this = " + this );
                                            lastTime = crtTime;
                                            speed = (int) (downloadCount.get() / ((crtTime - startTime) / 1000f));
                                            data.pos = dlLength.get();
                                            data.max = downloadItemsLength;
                                            if (listener != null)
                                                listener.on_load_ratio(data, download_size, speed);
                                        }
                                    }
                                }

                                _is.close();
                                _conn.disconnect();
                                _fos.flush();
                                _fos.close();

                                if (!run || delete) {
                                    break;
                                }

                                if (di.key != null && di.key.data != null) {
                                    // 解密
                                    _f_tmp = m3u8Decode(_f_tmp, di.key.data, ivToBytes(di.key.iv));
                                }


                                if (_f_tmp.renameTo(_f)) {
                                    dlLength.addAndGet(1);
                                    downloadCount.addAndGet(_f.length());
                                    synchronized (SIGNAL2) {
                                        long crtTime = System.currentTimeMillis();
                                        if (crtTime > lastTime + 1000) {
                                            lastTime = crtTime;
                                            speed = (int) (downloadCount.get() / ((crtTime - startTime) / 1000f));
                                            data.pos = dlLength.get();
                                            data.max = downloadItemsLength;
                                            if (listener != null)
                                                listener.on_load_ratio(data, download_size, speed);
                                        }
                                    }
                                    break;
                                } else {
                                    // 文件问题，严重失败
                                    if (_f_tmp.exists()) {
                                        _f_tmp.delete();
                                    }
                                    throw new RuntimeException("download file error");
                                }

                            } catch (Exception ex) {
                                CL.CLOGI("Eddy3 this ========================================>" + this);
                                CL.CLOGI("Eddy3 exception =" + ex);
                                if (ex instanceof Exception) {
                                    CL.CLOGI("Eddy3 count =" + count);
                                    if (retry_number++ < COUNTMAX) {
                                        try {
                                            Thread.sleep(100);
                                        } catch (Exception exx) {}

                                    } else {
                                        run = false;
                                        if(listener!=null) {
                                            is_start = false;
                                            crt_status = Eventer.State_Stop;
                                            listener.on_state_change(data, crt_status);
                                        }
                                        return;
                                    }
                                } else  {
                                    run = false;
                                    if(listener!=null) {
                                        is_start = false;
                                        crt_status = Eventer.State_Stop;
                                        listener.on_state_change(data, crt_status);
                                    }
                                    return;
                                }
                            }
                        }
                    } else {
                        synchronized (SIGNAL2) {
                            dlLength.addAndGet(1);
                          //  downloadCount.addAndGet(_f.length());

                            long crtTime = System.currentTimeMillis();

                        //    if (crtTime > lastTime + 1000) {
                                lastTime = crtTime;
                                speed = (int) (downloadCount.get() / (crtTime / 1000.0f));
                                data.pos = dlLength.get();
                                data.max = downloadItemsLength;
                                if (listener != null)
                                    listener.on_load_ratio(data, download_size, speed);
                          //  }
                        }
                    }
                } else {
                    int vvv = 0;
                    vvv = 1;
                    break;
                }

                if (!run || delete) {
                    int vvv = 0;
                    vvv = 1;
                    break;
                }
            }

            if (!run || delete) {
                is_start = false;
                crt_status = Eventer.State_Stop;
                listener.on_state_change(data, crt_status);
                return;
            }
            // 处理停止
            synchronized (SIGNAL) {
                ++stopWalkerCount;
                if (stopWalkerCount != MaxWalker) {
                    return;
                }
            }

            // 是否删除
            if (delete) {
                if (dir != null && dir.exists()) {
                    if (CLFileSystem.Delete_Dir(dir)) {
                        if(listener!=null)listener.on_delete(data,true);
                        download_size=0;
                    }
                }
                if(listener!=null) {
                    is_start = false;
                    crt_status = Eventer.State_Stop;
                    listener.on_state_change(data, crt_status);
                    listener.on_delete(data, false);
                }

                return;
            }

            // 判断是否中断
            if (!run) {
                if(listener!=null) {
                    is_start = false;
                    crt_status = Eventer.State_Stop;
                    listener.on_state_change(data, crt_status);
                }
                return;
            }

            try {
                //生成playlist
                File _playlist = new File(dir, "playlist");
                if (!_playlist.exists()) {
                    FileOutputStream _fos = new FileOutputStream(_playlist);
                    _fos.write("#EXTM3U\r\n".getBytes("utf8"));
                    if (_root.has("#ext-x-targetduration")) {
                        _fos.write(("#EXT-X-TARGETDURATION:" + _root.get("#ext-x-targetduration") + "\r\n").getBytes("utf8"));
                    }
//                    _fos.write("#EXT-X-MEDIA-SEQUENCE:0\r\n".getBytes("utf8"));
                    if (_root.has("as")) {
                        JSONArray _as = _root.getJSONArray("as");
                        for (int i = 0; i < _as.length(); ++i) {
                            JSONObject _jotmp = _as.getJSONObject(i);
                            String _d = _jotmp.getString("d");
                            String _url = "m_" + i + ".ts";
                            _fos.write(("#EXTINF:" + _d + ",\r\n").getBytes("utf8"));
                            _fos.write(_url.getBytes("utf8"));
                            _fos.write("\r\n".getBytes("utf8"));
                        }
                    }
                    _fos.write("#EXT-X-ENDLIST\r\n".getBytes("utf8"));
                }
            } catch (Exception ex) {

                //return;
            }

            int lastRange = 0;
            for (int i = 0; i < tsdata.size(); ++i) {
                Item item = tsdata.get(i);
                // 如果需要取片段，则用此文件名
                File tsTarget = new File(dir, i + ".ts");
                File fileTemp = new File(dir, String.valueOf(i));
                if (!fileTemp.exists()) {
                    is_start = false;
                    crt_status = Eventer.State_Stop;
                    listener.on_state_change(data, crt_status);
                    return;
                } else {
                    if (item.byteRangeStart != -2) {

                    }
                }
            }
            data.type_minor = Data.Type_Video_MERGE;
            if (listener != null) listener.on_state_change(data, crt_status);

            String[] array = new String[tsdata.size()];
            int i = 0;
            for (Item item: tsdata) {
                array[i++] = item.path;
            }
            //String[] array = (String[]) list_ts.toArray(new String[0]);
            download_size = downloadCount.get();
            if (!Setting.Share_Setting().get_m3u8_merge_mode()) {
                mergeFiles(array, dir.getAbsolutePath()+".mp4");
                Server.Remove_Downloaditem_Downloader(data);
                data.show_type = 3;
                data.type_major = Data.Type_Video;
                data.type_minor = Data.Type_Video_MP4;
                data.path = dir.getAbsolutePath()+".mp4";
                data.length=download_size;
                Server.Update_All_Download(data);
                crt_status = Eventer.State_Complete;
                if (listener != null) listener.on_state_change(data, crt_status);
                deleteDir(dir.getAbsoluteFile());
            } else {
                videoMerge2(dir.getAbsolutePath(), dir.getAbsolutePath());
//                Server.Remove_Downloaditem_Downloader(data);
//                data.show_type = 3;
//                data.type_major = Data.Type_Video;
//                data.type_minor = Data.Type_Video_MP4;
//                data.path = dir.getAbsolutePath()+".mp4";
//                data.length=download_size;
//                Server.Update_All_Download(data);
//                crt_status = Eventer.State_Complete;
//                if (listener != null) listener.on_state_change(data, crt_status);
//                deleteDir(dir.getAbsoluteFile());
            }
        }

        public void videoMerge2(String videoDir, String target) {
            // FFmpegCommand.setDebug(false);
            // FFmpegCommand.setDebug(true);
            File file = new File(videoDir);

            String txt_file = target + File.separator + "temp.txt";
            CL.CLOGI("Eddy videoMerge2 1=" + txt_file);
            File targetFile = new File(target, "target");
            // target +=  File.separator + "target.mp4";
            target +=  ".mp4";
            CL.CLOGI("Eddy videoMerge2 2=" + target);
            File[] subFile = file.listFiles();
            List<String> fileList = new ArrayList<>();
            if (subFile == null) {
                crt_status = Eventer.State_Stop;
                if (listener != null) listener.on_state_change(data, crt_status);
                return;
            }

            for (int iFileLength = 0; iFileLength < subFile.length; iFileLength++) {
                if (!subFile[iFileLength].isDirectory()) {
                    String filename = subFile[iFileLength].getName();
                    if (isNumeric(filename)) {
                        fileList.add(filename);
                    }
                }
            }
            if (fileList.size() == 0) {
                crt_status = Eventer.State_Stop;
                if (listener != null) listener.on_state_change(data, crt_status);
                return;
            }
            Collections.sort(fileList, new Comparator() {
                @Override
                public int compare(Object o1, Object o2) {
                    long i1 = Long.parseLong((String) o1);
                    long i2 = Long.parseLong((String) o2);
                    return (int) (i1 - i2);
                }
            });
            List<String> list = new ArrayList<>();
            for (String f : fileList) {
                list.add(f);
            }

            try {
                CLFileSystem.writeLineFile(list, txt_file);
            } catch (IOException ex) {
                crt_status = Eventer.State_Stop;
                if (listener != null) listener.on_state_change(data, crt_status);
            }

            File f = new File(target);
            if (f.exists()) {
                f.delete();
            }
            if (!f.exists()) {
                //  FFmpegCommand.runSync(FFmpegUtils.concatVideo(txt_file, target));
                //FFmpegCommand.INSTANCE.runCmd(FFmpegUtils.INSTANCE.concatVideo(txt_file, target));
                Global.binder.packetData(txt_file, target, data);
            } else {
                String file_path = data.path;
                Server.Remove_Downloaditem_Downloader(data);
                data.show_type = 3;
                data.type_major = Data.Type_Video;
                data.type_minor = Data.Type_Video_MP4;
                data.path = file_path + ".mp4";
                Server.Update_All_Download(data);
                int crt_status = CommonDownloader.Eventer.State_Complete;
                if (listener != null)
                    listener.on_state_change(data, crt_status);
                File _dir = new File(file_path);
                deleteDir(_dir);
            }
        }




        public boolean mergeFiles(String[] fpaths, String resultPath) {
            if (fpaths == null || fpaths.length < 1 || TextUtils.isEmpty(resultPath)) {
                return false;
            }
            if (fpaths.length == 1) {
                return new File(fpaths[0]).renameTo(new File(resultPath));
            }

            File[] files = new File[fpaths.length];
            for (int i = 0; i < fpaths.length; i ++) {
                files[i] = new File(fpaths[i]);
                if (TextUtils.isEmpty(fpaths[i]) || !files[i].exists() || !files[i].isFile()) {
                    return false;
                }
            }

            File resultFile = new File(resultPath);

            try {
                FileChannel resultFileChannel = new FileOutputStream(resultFile, true).getChannel();
                for (int i = 0; i < fpaths.length; i ++) {
                    FileChannel blk = new FileInputStream(files[i]).getChannel();
                    resultFileChannel.transferFrom(blk, resultFileChannel.size(), blk.size());
                    blk.close();
                }
                resultFileChannel.close();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
                return false;
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }

            for (int i = 0; i < fpaths.length; i ++) {
                files[i].delete();
            }

            return true;
        }

        public File m3u8Decode(File src, byte[] key, byte[] iv) throws Exception {
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            IvParameterSpec ivs = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivs);
//            byte[] json = cipher.doFinal(buff, 16, buff.length - 16);

            FileInputStream fis = new FileInputStream(src);
            File target = new File(src.getParent(), "temp2");
            FileOutputStream fos = new FileOutputStream(target);
            int count;
            byte[] buff = new byte[1024*10];
            while ((count = fis.read(buff)) != -1) {
                byte[] result = cipher.update(buff, 0, count);
                fos.write(result, 0, result.length);
            }
            fis.close();
            fos.close();

            return target;
        }

        public byte[] ivToBytes(String value) {
            byte[] iv = new byte[16];
            if (value.startsWith("0x")) {
                value = value.substring(2);
            }

            if (value.length() != 32) {
                for (int j=0; j<16; j++)
                    iv[j] = 0;
                return iv;
            }
            for (int i = value.length(), m = 15; i > 0; i = i - 2, --m) {
                String v;
                int s = i - 2;
                if (s >= 0) {
                    v = value.substring(s, i);
                } else {
                    v = String.valueOf(value.charAt(i));
                }
                iv[m] = (byte) Integer.parseInt(v, 16);
            }

            return iv;
        }
    }
}
