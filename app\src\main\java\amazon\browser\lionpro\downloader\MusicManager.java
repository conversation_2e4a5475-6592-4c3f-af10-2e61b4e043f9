package amazon.browser.lionpro.downloader;

import android.content.Context;
import android.content.SharedPreferences;
import android.media.MediaPlayer;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.primary.Global;

import java.io.File;
import java.util.ArrayList;
import java.util.Random;

import lion.CL;
import lion.CLBus;
import lion.CLToast;

/**
 * Created by leron on 2016/7/19.
 */
public class MusicManager {

    public interface Eventer{
        void on_data_update();
        void on_music_change(Data.StructDLItem item);
        void on_status_change(int state);
    }
    private static ArrayList<Eventer> listeners=new ArrayList<>();
    public static void Add_Listener(Eventer listen){
        synchronized (MusicManager.class){
            if(!listeners.contains(listen))listeners.add(listen);
        }
    }
    public static void Reduce_Listener(Eventer listen){
        synchronized (MusicManager.class){
            listeners.remove(listen);
        }
    }

    private CLBus.CBEventer listener_update_name=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action== Global.Action_rename){
                if(play_list==null)return;
                String _ident=(String)msgs[0];
                String _name=(String)msgs[1];
                CL.CLOGI("rename ident:"+_ident+"  name:"+_name);
                for(int i=0;i<play_list.size();++i){
                    Data.StructDLItem _item=play_list.get(i);
                    if(_item.ident_md5.equals(_ident)){
                        _item.name=_name;
                        synchronized (MusicManager.class) {
                            for (int n = 0; n < listeners.size(); ++n) {
                                listeners.get(n).on_data_update();
                                Data.StructDLItem _crt_item=get_current_music();
                                if(_crt_item!=null && _crt_item.ident_md5.equals(_ident)) {
                                    listeners.get(n).on_music_change(_item);
                                }
                            }
                        }
                        break;
                    }
                }
            }
        }
    };


    protected MusicManager(Context context){
        this.cc=context;
        play_list=DataBase.Share_Instance().get_music_play_list();
        CL.CLOGI("music play list size:"+play_list.size());
        crt_mode=cc.getSharedPreferences("clmusic",Context.MODE_PRIVATE).getInt("play_mode",0);
        CLBus.Share_Instance().register(Global.Group_update_info,listener_update_name,Global.Action_rename);
    }


    private Context cc;
    private ArrayList<Data.StructDLItem> play_list;
    private ArrayList<Integer> mode_sj=new ArrayList<>();
    private int crt_mode=0;  //0-顺序 1-随机
    private int crt_state=0; //0-停止 1-暂停 2-播放中
    private MediaPlayer player;
    private int crt_item=-1;

    private MediaPlayer.OnCompletionListener listener_completion=new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            mp.release();
            crt_state=0;
            int _last_item=crt_item;
            crt_item=-1;
            synchronized (MusicManager.class) {
                for (int i = 0; i < listeners.size(); ++i) {
                    listeners.get(i).on_status_change(crt_state);
                }
            }
            if(_last_item==-1)return;

            if(crt_mode==0){
                int _next_index=-1;
                for(int i=0;i<play_list.size();++i){
                    Data.StructDLItem _t=play_list.get(i);
                    if(_t.ID==_last_item){
                        _next_index=i;
                        break;
                    }
                }
                if(_next_index!=-1){
                    if(_next_index+1<=play_list.size()-1)crt_item=play_list.get(_next_index+1).ID;
                    else if(play_list.size()>0)crt_item=play_list.get(0).ID;
                }else if(play_list.size()>0){
                    crt_item=play_list.get(0).ID;
                }
                if(crt_item!=-1)play_pause();
            }else if(crt_mode==1){
                if (mode_sj.size() == 0) {
                    for (int i = 0; i < play_list.size(); ++i) {
                        mode_sj.add(play_list.get(i).ID);
                    }
                }
                if (mode_sj.size() == 0) return;
                if (mode_sj.size() == 1) {
                    if(play_list.size()>0)crt_item=mode_sj.remove(0);
                } else {
                    Random _random = new Random(System.currentTimeMillis());
                    int _index = _random.nextInt(mode_sj.size() - 1);
                    crt_item = mode_sj.remove(_index);
                }
                for(int i=0;i<play_list.size();++i){
                    if(play_list.get(i).ID==crt_item){
                        play_pause();
                        break;
                    }
                }
            }
        }
    };
    private MediaPlayer.OnErrorListener listener_error=new MediaPlayer.OnErrorListener() {
        @Override
        public boolean onError(MediaPlayer mp, int what, int extra) {
            CL.CLOGI("music mediaplay error:"+what);
            return true;
        }
    };


    public ArrayList<Data.StructDLItem> get_play_list(){
        return play_list;
    }

    public Data.StructDLItem get_current_music(){
        if(crt_item==-1)return null;
        Data.StructDLItem _tmp=null;
        for(int i=0;i<play_list.size();++i){
            Data.StructDLItem _t=play_list.get(i);
            if(_t.ID==crt_item){
                _tmp=_t;
                break;
            }
        }
        return _tmp;
    }

    public int get_current_state(){
        return crt_state;
    }
    public int get_current_duration(){
        if(player==null)return 0;
        return player.getDuration();
    }
    public int get_current_position(){
        if(player==null)return 0;
        return player.getCurrentPosition();
    }

    public int get_play_mode(){
        return crt_mode;
    }
    public void set_play_mode(int mode){
        if(mode == 0 || mode == 1){
            if(crt_mode==mode)return;
            SharedPreferences _sp = cc.getSharedPreferences("clmusic",Context.MODE_PRIVATE);
            _sp.edit().putInt("play_mode",mode).apply();
            crt_mode=mode;
            mode_sj.clear();
        }
    }

    public void play_pause(){
        if(crt_item==-1)return;
        if(crt_state==0){
            try{
                Data.StructDLItem _item=get_current_music();
                if(!new File(_item.path).exists()){
                    CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                    if(play_list.size()>1)play_next();
                    return;
                }
                player=new MediaPlayer();
                player.setOnCompletionListener(listener_completion);
                player.setOnErrorListener(listener_error);
                CL.CLOGI("start music:"+_item.ID+" "+ _item.path);
                player.setDataSource(_item.path);
                player.prepare();
                player.start();
                crt_state=2;
                synchronized (MusicManager.class) {
                    for (int i = 0; i < listeners.size(); ++i) {
                        listeners.get(i).on_music_change(_item);
                        listeners.get(i).on_status_change(crt_state);
                    }
                }
            }catch (Exception ex){
                CL.CLOGE("start error:"+ex.toString(),ex);
            }
        }else if(crt_state==1){
            crt_state=2;
            player.start();
            CL.CLOGI("in start");
            synchronized (MusicManager.class) {
                for (int i = 0; i < listeners.size(); ++i) {
                    listeners.get(i).on_status_change(crt_state);
                }
            }
        }else if(crt_state==2){
            crt_state=1;
            player.pause();
            CL.CLOGI("in pause");
            synchronized (MusicManager.class) {
                for (int i = 0; i < listeners.size(); ++i) {
                    listeners.get(i).on_status_change(crt_state);
                }
            }
        }
    }

    public void stop_music(){
        crt_item=-1;
        mode_sj.clear();
        if(crt_state==1 || crt_state==2) {
            crt_state = 0;
            player.stop();
            player.release();
            player=null;
            synchronized (MusicManager.class) {
                for (int i = 0; i < listeners.size(); ++i) {
                    listeners.get(i).on_status_change(crt_state);
                    listeners.get(i).on_music_change(null);
                }
            }
        }
    }

    public void play_music(Data.StructDLItem item){
        if(item==null)return;
        if(crt_item!=-1 && crt_item==item.ID){
            play_pause();
            return;
        }

        //是否包含
        if(play_list==null || play_list.isEmpty()){
            add_music(item);
        }else {
            for (int i = 0; i < play_list.size(); ++i) {
                if (play_list.get(i).ID == item.ID) {
                    break;
                }
                if (i == play_list.size() - 1) {
                    add_music(item);
                }
            }
        }

        crt_item=item.ID;
        mode_sj.clear();
        if(crt_state==1 || crt_state==2) {
            crt_state = 0;
            player.stop();
            player.release();
            player=null;
            synchronized (MusicManager.class) {
                for (int i = 0; i < listeners.size(); ++i) {
                    listeners.get(i).on_status_change(crt_state);
                }
            }
        }

        CL.CLOGI("goto play music:"+crt_item+" "+item.path);
        play_pause();
    }

    public void play_next(){
        if(crt_item==-1)return;
        if(crt_state==1 || crt_state==2) {
            crt_state = 0;
            player.stop();
            player.release();
            player=null;
            synchronized (MusicManager.class) {
                for (int i = 0; i < listeners.size(); ++i) {
                    listeners.get(i).on_status_change(crt_state);
                }
            }
        }
        int _last_item=crt_item;
        crt_item=-1;
        if(crt_mode==0){
            int _next_index=-1;
            for(int i=0;i<play_list.size();++i){
                Data.StructDLItem _t=play_list.get(i);
                if(_t.ID==_last_item){
                    _next_index=i;
                    break;
                }
            }
            if(_next_index!=-1){
                if(_next_index+1<=play_list.size()-1)crt_item=play_list.get(_next_index+1).ID;
                else if(play_list.size()>0)crt_item=play_list.get(0).ID;
            }else if(play_list.size()>0){
                crt_item=play_list.get(0).ID;
            }
            if(crt_item!=-1)play_pause();
        }else if(crt_mode==1) {
            if (mode_sj.size() == 0) {
                for (int i = 0; i < play_list.size(); ++i) {
                    mode_sj.add(play_list.get(i).ID);
                }
            }
            if (mode_sj.size() == 0) return;
            if (mode_sj.size() == 1) {
                if(play_list.size()>0)crt_item=mode_sj.remove(0);
            } else {
                Random _random = new Random(System.currentTimeMillis());
                int _index = _random.nextInt(mode_sj.size() - 1);
                crt_item = mode_sj.remove(_index);
            }
            for(int i=0;i<play_list.size();++i){
                if(play_list.get(i).ID==crt_item){
                    play_pause();
                    break;
                }
            }
        }
    }

    public void add_music(Data.StructDLItem item){
        CL.CLOGI("add music ID:"+item.ID+" "+item.path);
        DataBase.Share_Instance().set_music_show(item.ID,true);
        play_list=DataBase.Share_Instance().get_music_play_list();
        synchronized (MusicManager.class){
            for(int i=0;i<listeners.size();++i){
                listeners.get(i).on_data_update();
            }
        }
    }

    public void remove_music(Data.StructDLItem item){
        if(item==null)return;
        synchronized (MusicManager.class){
            if(crt_item==-1){
            }else if(crt_item == item.ID){
                crt_item=-1;
                if(player!=null){
                    crt_state = 0;
                    player.stop();
                    player.release();
                    player=null;
                    for (int i = 0; i < listeners.size(); ++i) {
                        listeners.get(i).on_status_change(crt_state);
                        listeners.get(i).on_music_change(null);
                    }
                }
            }

            mode_sj.clear();
            for(int i=0;i<play_list.size();++i){
                if(play_list.get(i).ID==item.ID){
                    play_list.remove(i);
                    break;
                }
            }
            DataBase.Share_Instance().set_music_show(item.ID,false);

            for (int i = 0; i < listeners.size(); ++i) {
                listeners.get(i).on_data_update();
            }
        }
    }

    public void force_update(){
        play_list=DataBase.Share_Instance().get_music_play_list();
        synchronized (MusicManager.class){
            for(int i=0;i<listeners.size();++i){
                listeners.get(i).on_data_update();
            }
        }
        CL.CLOGI("on music manager force update");
    }

}
