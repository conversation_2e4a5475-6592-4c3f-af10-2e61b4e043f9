package amazon.browser.lionpro.downloader;

import android.os.Build;
import android.text.TextUtils;
import android.webkit.MimeTypeMap;

import androidx.annotation.RequiresApi;

import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.extend.WS_general;
import amazon.browser.lionpro.downloader.extend.WS_vimeo;
//import com.matchman.downloader.downloader.extend.WS_youtube;
import amazon.browser.lionpro.downloader.extend.WS_xshr;
import amazon.browser.lionpro.downloader.extend.WebViewCallBack;
import amazon.browser.lionpro.primary.Global;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.Socket;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509ExtendedTrustManager;
import javax.net.ssl.X509TrustManager;

import lion.CL;
import lion.CLTools;

/**
 * Created by leron on 2016/7/14.
 */
public class ResSniffer {

    public static class SniffData{
        private String title;
        private String url;
        private String headerParams;
        private WebViewCallBack iCallback;
        public SniffData(String url, String title, String params){
            this.title=title;
            this.url=url;
            this.headerParams = params;
        }

        public SniffData(String url,  String params, WebViewCallBack callback){
//            this.title=title;
            this.url=url;
            this.headerParams = params;
            this.iCallback = callback;

        }
    }
    public static class SniffDataWebsite{
        public String title;
        public String url_thumb;
        public String provenance;
        public ArrayList<SniffWSData> urls=new ArrayList<>();
    }
    public static class SniffWSData{
        public String quality;
        public String url;
        public String listHeaderParams;
        public String width,height;
        public String wh_string="";
        public String video_size="";
        public SniffWSData(){}
        public SniffWSData(String url,String quality,String width,String height,String params){
            this.url=url;
            this.quality=quality;
            this.width=width;
            this.height=height;
            this.listHeaderParams = params;
        }
    }



    public interface WebSiteSniff{
        boolean on_interest(WebViewCallBack webview, String url, String title)throws Exception;
    }
    public interface SniffEventer{
        void on_sniffer(String type,String url,long length, String title, MultiResolution resolution);
        void on_sniffer(String type,String url,long length, String title);
        void on_sniffer_parse(SniffDataWebsite data);
    }


    protected static SniffEventer listener=null;
    protected ArrayList<WebSiteSniff> websites=new ArrayList<>();
    public static void On_Website_Sniff(SniffDataWebsite data){
        if(listener!=null)listener.on_sniffer_parse(data);
    }

    public static void on_sniffer(String type,String url,long length, String title){
        if(listener!=null)listener.on_sniffer(type, url, length, title);
    }

    public static void on_sniffer(String type,String url,long length, String title, MultiResolution resolution){
        if(listener!=null)listener.on_sniffer(type, url, length, title,resolution);
    }

    protected ResSniffer(){
        //init website
        websites.add(new WS_vimeo());
        //websites.add(new WS_youtube());
        websites.add(new WS_general(null));
        websites.add(new WS_xshr());
        for(int i=0;i<3;++i){
            new Worker().start();
        }
    }

    public void AddGeneralSniffer(WebViewCallBack iCallback) {
        websites.add(new WS_general(iCallback));

    }

    public void del_generalsniff(WebViewCallBack iCallback) {
        websites.remove(iCallback);
    }



    private boolean run=true;
    private ArrayList<SniffData> urls=new ArrayList<>();
    private HashMap<SniffData, Integer> reconnecturls=new HashMap<SniffData, Integer>();

    protected void add_url(SniffData item){
        if(item == null || item.url==null)return;
        synchronized (urls){
          //  CL.CLOGI("add_url"+item.url);
            urls.add(item);
            urls.notifyAll();
        }
    }

    private boolean is_website_sniff(WebViewCallBack callback, String url, String title)throws Exception{
        for(int i=0;i<websites.size();++i){
            if(websites.get(i).on_interest(callback, url, title))return true;
        }
        return false;
    }

    private boolean is_exclude_host(String url){
        if(url.startsWith("http://127.0.0.1"))return true;
        if(url.startsWith("https://127.0.0.1"))return true;
        if(url.endsWith(".jpg")
                || url.endsWith(".jpeg")
                || url.endsWith(".png")
                || url.endsWith(".gif")
                || url.endsWith(".ico")
                || url.endsWith(".js")
                || url.endsWith(".css")
                || url.endsWith(".htm")
                || url.endsWith(".html")
                || url.endsWith(".shtml")
                )return true;
        return false;
    }
    private boolean is_exclude_suffix(String url){
        String _suffix= MimeTypeMap.getFileExtensionFromUrl(url);
        if(_suffix.isEmpty())return false;
        _suffix=_suffix.toLowerCase();
        if(_suffix.equals("png")
                || _suffix.equals("jpg")
                || _suffix.equals("jpeg")
                || _suffix.equals("gif")
                || _suffix.equals("ico")
                || _suffix.equals("js")
                || _suffix.equals("json")
                || _suffix.equals("css")
                || _suffix.equals("htm")
                || _suffix.equals("html")
                || _suffix.equals("shtml")
                ){
            return true;
        }
        return false;
    }

    private class Worker extends Thread{
        @Override
        public void run() {
            while (run){
                try {
                    SniffData _item ;
                    synchronized (urls) {
                        if (!urls.isEmpty()) {
                            _item = urls.remove(0);
                            //reconnecturls.put(_item, Integer.valueOf(0));
                        }
                        else {
                            urls.wait();
                        //    CL.CLOGI("wait:");
                            continue;
                        }
                    }


//                    if (_item.url.contains("mp4")) {
//                        int i;
//                        i = 0;
//                        int j;
//                        j = i ;
//
//                    }

                    if(listener!=null && is_website_sniff(_item.iCallback, _item.url, _item.title))continue;
                    if(is_exclude_host(_item.url))continue;
                    if(is_exclude_suffix(_item.url))continue;
                    if(_item.url.contains("youtube"))continue;
                    if(_item.url.contains("googleusercontent"))continue;
                    if(_item.url.contains(".googlevideo."))continue;
                    if(_item.url.contains("encrypted-vtbn0"))continue;
                    if(listener==null)continue;

                   // CL.CLOGI("process url:"+_item.url);

                    String _type=null,_real_url=null;
                    long _clength=0;
                    byte[] _ident=null;
                    HttpURLConnection _conn=null;
                    CL.CLOGI("Eddy _item.url:"+_item.url);

                    if(_item.url.startsWith("http://")){
                        _conn=(HttpURLConnection) (new URL(_item.url)).openConnection();
                      //  CL.CLOGI("_conn1:"+_conn);
                    }else if(_item.url.startsWith("https://")) {
                        _conn = (HttpsURLConnection) (new URL(_item.url)).openConnection();
                      //  CL.CLOGI("_conn2:"+_conn);
                    }

                    if (_conn instanceof HttpsURLConnection) {
                        SSLContext sc = SSLContext.getInstance("SSL");
                        sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
                        ((HttpsURLConnection) _conn).setSSLSocketFactory(sc.getSocketFactory());
                        ((HttpsURLConnection) _conn).setHostnameVerifier(new TrustAnyHostnameVerifier());
                    }

                  //  CL.CLOGI("_conn3:"+_conn);
                    if (_conn == null)
                        continue;

                    _conn.setDoInput(true);
                    _conn.setUseCaches(false);
                    _conn.setConnectTimeout(10000);

                    _conn.setReadTimeout(10000);

                    CL.CLOGI("Eddy _item2 = " + _item.url);

                    String [] params = null;
                    Map<String, String> list = new HashMap<>();
                    if (_item.headerParams != null && _item.headerParams.length() > 0) {
                        String[] array = CLTools.strSplitToArray(_item.headerParams, "[this<>map<>list]", false);//entity.originUrl.split("[this<>map<>list]");
                        if (array != null) {
                            for (int i=0; i<array.length; i++) {
                                String tmp = array[i];
                                params = CLTools.strSplitToArray(tmp, "[=+v+=]", false);//tmp.split("[=+v+=]");
                                list.put(params[0], params[1]);
                            }
                        } else {
                            params = CLTools.strSplitToArray(_item.headerParams, "[=+v+=]", false);//entity.originUrl.split("[=+v+=]");
                            list.put(params[0], params[1]);
                        }
                        if (list.size() > 0) {
                            for (Map.Entry<String, String> entry : list.entrySet()) {
                                String key = entry.getKey();
                                String value = entry.getValue();
                                _conn.setRequestProperty(key, value);
                            }
                        }
                    } else {
                        _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                    }

                    _conn.connect();



                    int responseCode = _conn.getResponseCode();
                    if (responseCode >= 200 && responseCode <= 206) {
                        _type = _conn.getContentType();

                        if (_type != null) _type = _type.toLowerCase();

                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N)
                            _clength = _conn.getContentLengthLong();
                        else
                            _clength = _conn.getContentLength();




                        _real_url = _conn.getURL().toString();
                        _ident = new byte[32];
                        int _count = _conn.getInputStream().read(_ident);
                        if (_count < 32) _ident = null;

                       // _conn.disconnect();

                        if (_count > 0
                                && (_ident != null && (new String(_ident).toUpperCase().contains("#EXTM3U")))
                                && _real_url != null ) { //&& _real_url.toLowerCase().contains(".m3u8")
                            if (listener != null) {
                                //FileOutputStream _fos=new FileOutputStream(_om3u8);
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                baos.write(_ident);
                                byte[] buff = new byte[4096];
                                int count;
                                while ((count = _conn.getInputStream().read(buff)) != -1) {
                                    baos.write(buff, 0, count);
                                }
                                baos.flush();
                                byte[] m3u8Data = baos.toByteArray();
                                baos.close();

                                BufferedReader reader = new BufferedReader(
                                        new InputStreamReader(new ByteArrayInputStream(m3u8Data)));

                                boolean isStart = false;
                                boolean isEnd = false;
                                String orgin_line;
                                ArrayList<MultiResolution> multiResolutions = new ArrayList<>();
                                MultiResolution result = new MultiResolution();
                                String scheme = CLTools.GetUrlScheme(_real_url);
                                String host = CLTools.Get_Url_Host(_real_url);
                                host = scheme + "://" + host;
                                String host2 = _real_url.substring(0, _real_url.lastIndexOf('/') + 1);
                                while ((orgin_line = reader.readLine()) != null) {
                                    String line = orgin_line.toUpperCase();
                                    if (TextUtils.equals(line, "#EXTM3U")) {
                                        if (!isStart) {
                                            isStart = true;
                                        }
                                    } else if (isStart && line.startsWith("#EXT-X-STREAM-INF")) {
                                        // 多码率
                                        MultiResolution mr = new MultiResolution();
                                        String[] temp = orgin_line.substring(orgin_line.indexOf(":") + 1).split(",");
                                        if (temp.length > 0) {
                                            for (int i = 0; i < temp.length; ++i) {
                                                if (temp[i].startsWith("BANDWIDTH=")) {
                                                    //String tmp = temp[i].substring(temp[i].indexOf("BANDWIDTH=") + "BANDWIDTH=".length(), temp[i].length());
                                                    //mr.quality = tmp;
                                                    mr.quality = temp[i];
                                                    //break;
                                                } else if (temp[i].startsWith("RESOLUTION=")) {
                                                    //mr.resolution = temp[i].substring(temp[i].indexOf("RESOLUTION=") + "RESOLUTION=".length(), temp[i].length());
                                                    mr.resolution = temp[i];
                                                }
                                            }
                                        }
                                        orgin_line = reader.readLine();
                                        if (orgin_line != null) {
                                            mr.url = orgin_line;
                                            if (!mr.url.startsWith("http")) {
                                                if (!mr.url.startsWith("/")) {
                                                    mr.url = host2 + mr.url;
                                                } else {
                                                    mr.url = host + mr.url;
                                                }
                                            }
                                        }

                                        multiResolutions.add(mr);
                                    } else if (isStart && line.startsWith("#EXTINF")) {
                                        // 单码率
                                        result.length += 1;
                                    } else if (isStart && line.startsWith("#EXT-X-ENDLIST")) {
                                        isEnd = true;
                                        break;
                                    }
                                }

                                int index = -1;
                                if (multiResolutions != null && multiResolutions.size() > 0) {
//                                    long max_value = 0;
//                                    for (int i = 0; i < multiResolutions.size(); i++) {
//                                        MultiResolution resolution = multiResolutions.get(i);
//                                        if (resolution.quality != null && resolution.quality.length() > 10) {
//                                            String[] _quality = resolution.quality.split("=");
//                                            if (_quality != null) {
//                                                long quality = Long.parseLong(_quality[1]);
//                                                if (quality > max_value) {
//                                                    max_value = quality;
//                                                    index = i;
//                                                }
//                                            }
//                                        }
//                                    }
                                    for (int i = 0; i < multiResolutions.size(); i++) {
                                        MultiResolution resolution = multiResolutions.get(i);
//                                        if (index != -1) {
//                                            if (i == index) {
//                                                resolution.hasAd = true;
//                                            }
//                                            listener.on_sniffer("m3u8", resolution.url, 0, _item.title, resolution);
//                                        } else
                                        listener.on_sniffer("m3u8", resolution.url, 0, _item.title, resolution);
                                    }
                                } else {
                                    listener.on_sniffer("m3u8", _real_url, 0, _item.title);
                                }
                            }
                            _conn.disconnect();
                            continue;
                        }
                        _conn.disconnect();

                        String _ident_str = CLTools.Parse_Video_Audio_Format(_ident);

                        if (_type != null && _type.contains("mp4"))
                            _ident_str = "video/mp4";
                        else if (_type != null && _type.equals("audio/acc")) {
                            _ident_str = "audio/acc";
                        } else if (_type != null && _type.equals("audio/aac")) {
                            _ident_str = "audio/aac";
                        } else if (_type != null && _type.equals("audio/mpeg")) {
                            _ident_str = "audio/mpeg";
                        } else if (_type != null && _type.equals("application/octet-stream")) {
                            if (_conn.getURL().toString().contains(".mp3")) {
                                _ident_str = "audio/mpeg";
                            }
                        } else if (_type != null && _type.contains("dash")) {
                            continue;
                        }

                        if (_ident_str != null) {
                            if (_ident_str.contains("m3u8")) {
                                if (listener != null)
                                    listener.on_sniffer("m3u8", _real_url, 0, _item.title);
                            } else if (!_real_url.contains(".m4s")) {
                                if (listener != null) {

                                    if (Setting.Share_Setting().get_filter_switch()) {
                                        if (_clength != -1 && _clength > 0) {
                                            long min_size = Setting.Share_Setting().get_filter_mini_size();
                                            long max_size = Setting.Share_Setting().get_filter_max_size();
                                            if (_clength < min_size) {
                                                continue;
                                            }

                                            if (max_size != -1 && _clength > max_size) {
                                                continue;
                                            }
                                        }
                                    }

                                    listener.on_sniffer(_ident_str, _real_url, _clength, _item.title);
                                }
                                continue;
                            }
                        }
                    }
                }catch (Exception ex){
                    CL.CLOGE("sniffer error:"+ex.toString(), ex);
                   // reconnecturls
                }
            }
        }
    }

    private static class TrustAnyTrustManager implements X509TrustManager
    {

        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException
        {
        }

        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException
        {
        }

        public X509Certificate[] getAcceptedIssuers()
        {
            return new X509Certificate[]{};
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public static class TrustAnyTrustManager2 extends X509ExtendedTrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType, Socket socket) throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType, Socket socket) throws CertificateException {

        }

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType, SSLEngine engine) throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType, SSLEngine engine) throws CertificateException {

        }

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {

        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {

        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier
    {
        public boolean verify(String hostname, SSLSession session)
        {
            return true;
        }
    }

}
