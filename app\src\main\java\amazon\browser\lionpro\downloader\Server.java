package amazon.browser.lionpro.downloader;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.PowerManager;

import amazon.browser.lionpro.downloader.extend.WebViewCallBack;
import amazon.browser.lionpro.primary.Global;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.concurrent.CopyOnWriteArrayList;

import lion.CL;
import lion.CLCallback;
import lion.CLTools;

/**
 * Created by leron on 2016/6/25.
 */
public class Server {

    public interface Eventer{
        void on_update_data(CopyOnWriteArrayList<Data.StructDLItem> datas);
        void on_update_downloading_data();
    }



    private static DownloadServer server;
    private static Eventer listener;

    //public static boolean parse_done = false;

    public static void Init(Context context){
        if(server==null){
            server=new DownloadServer(context);
        }
    }

    public static void reInit(Context context) {
        if (server != null) {
            server = null;
        }
        Init(context);
    }

    public static void Set_Listener(Eventer listen){
        listener=listen;
        if(server==null)return;
        server.update_client_data();
    }

    public void Add_generalsniff(WebViewCallBack callback) {
        server.AddgeneraSniffer(callback);
    }

    public void del_generalsniff(WebViewCallBack callback) {
        server.del_generalsniff(callback);
    }

    //public interface
    public static void Add_Confirm_Downloader(int type, int suffix, String title, String url, long length, String params, String quality){
        if(server==null)return;
        //Server.parse_done = true;
        server.add_confirm_item(type,suffix,title,url,length,params, quality);
    }
    public static void Add_Confirm_Downloader(int type, String suffix, String title, String url, long length, String params){
        if(server==null)return;
        //Server.parse_done = true;
        server.add_confirm_item(type,suffix,title,url,length,params);
    }
    public static void Add_Confirm_Downloader(String title, String url, String params, String quality, Boolean hasAd){
        if(server==null)return;
        //Server.parse_done = true;
        server.add_confirm_item_M3U8(title,url,params,quality,hasAd);
    }
    public static void Remove_Confirm_Downloader(Data.StructDLItem item){
        if(server==null)return;
        server.remove_confirm_item(item);
    }
    
    public static void Remove_Downloaditem_Downloader(Data.StructDLItem item){
        if(server==null)return;
        server.remove_downloader_item(item);
    }
    
    public static void Conversion_To_Downloader(Data.StructDLItem item){
        if(server==null)return;
        server.conversion_to_downloader(item);
    }
    public static boolean Add_Sniffer_Download(String title, Bitmap thumb,String url,
                                               int type_major,int type_minor,String provenance,int length, String params){
        if(server==null)return false;
        return server.create_sniffer_downloader(title,thumb,url,type_major,type_minor,provenance,length, params);
    }
    public static void Update_Download(Data.StructDLItem item){
        if(server==null)return;
        DataBase.Share_Instance().update_status(item.ID,item.path,item.duration,item.length,item.downloaded);
    }
    
    public static void Update_All_Download(Data.StructDLItem item) {
        if(server==null)return;
        server.add_downloader_item(item);
        
    }
    public static void Update_Download_Name(Data.StructDLItem item,String name){
        if(server==null)return;
        DataBase.Share_Instance().update_rename(item.ID,name);
    }
    public static void Delete_Download(Data.StructDLItem item){
        if(server==null)return;
        DataBase.Share_Instance().delete_item(item.ID);
        server.delete_item(item);
    }


    public static void Force_Update_DL(){
        if(server==null)return;
        server.force_update_download();
    }


    public static void Set_Sniff_Listener(ResSniffer.SniffEventer listen){
        ResSniffer.listener=listen;
    }
    public static void Sniff_Url(ResSniffer.SniffData d){
        if(server==null)return;
        server.sniffer.add_url(d);
    }

    public static void External_Storage_Picture(int type, String md5, File file, CLCallback.CB_TF cber){
        if(server==null){
            if(cber!=null)cber.on_callback_fail(-12,"system not ready!");
            return;
        }
        server.file_manager.store_picture(type,md5,file,cber);
    }

    public static void clear_completed(){
        if(server==null)return;
        server.clear_completed();
    }

    public static MusicManager Share_Music(){
        if(server==null)return null;
        return server.music;
    }

    public static FileManager Share_FileManager(){
        if(server==null)return null;
        return server.file_manager;
    }


    private static class DownloadServer{

        private Context cc;
        private FileManager file_manager;
        private ResSniffer sniffer;
        private MusicManager music;
        private PowerManager.WakeLock wake_lock;
        private CopyOnWriteArrayList<Data.StructDLItem> datas;

        private DownloadServer(Context context){
            this.cc=context;
            DataBase.Init(context);
            file_manager=new FileManager(context);
            datas=new CopyOnWriteArrayList<>();
            PowerManager pm = (PowerManager) cc.getSystemService(Context.POWER_SERVICE);
            wake_lock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "fd_cn:cldl");
            wake_lock.acquire();
            sniffer=new ResSniffer();
            music=new MusicManager(cc);
            init();
            CL.CLOGI("download server was start!!!");
        }

        public void AddgeneraSniffer(WebViewCallBack iCallback) {
            sniffer.AddGeneralSniffer(iCallback);
        }

        public void del_generalsniff(WebViewCallBack iCallback) {
            sniffer.del_generalsniff(iCallback);
        }

        private void init(){


            Data.StructDLItem _tmp=new Data.StructDLItem();
            _tmp.show_type=2;
            datas.add(_tmp);

            ArrayList<Data.StructDLItem> _dldatas=DataBase.Share_Instance().get_show_download();
            for(int i=0;i<_dldatas.size();++i){
                Data.StructDLItem _item=_dldatas.get(i);
                if (_item == null || _item.path == null) {
                    return;
                }
                if(!new File(_item.path).exists()) {
                    DataBase.Share_Instance().delete_item(_item.ID);
                    continue;
                }
                _item.show_type=3;
                CL.CLOGI("major:"+_item.type_major+" minor:"+_item.type_minor+" duration:"+_item.duration);
                if(!_item.downloaded)_item.dler=create_downloader(_item);
            }
            datas.addAll(_dldatas);

            update_client_data();
        }

        private void force_update_download(){
            ArrayList<Data.StructDLItem> _dldatas=DataBase.Share_Instance().get_show_download();
            if(_dldatas.size()==0){
                for(int i=0;i<datas.size();++i){
                    Data.StructDLItem _item=datas.get(i);
                    if(_item.show_type==3){
                        datas.remove(i);
                        --i;
                    }
                }
            }else {
                for (int i = 0; i < datas.size(); ++i) {
                    Data.StructDLItem _item = datas.get(i);
                    if (_item.show_type == 3) {
                        for (int j = 0; j < _dldatas.size(); ++j) {
                            Data.StructDLItem _tmp=_dldatas.get(j);
                            if(_item.ID==_tmp.ID){
                                break;
                            }
                            if(j==_dldatas.size()-1){
                                datas.remove(i);
                                --i;
                                CL.CLOGI("on server force update:"+_item.ID);
                            }
                        }
                    }
                }
            }
            update_client_data();
        }

        public void delete_item(Data.StructDLItem item) {
            datas.remove(item);
            update_client_data();
        }

        private void update_client_data(){
            synchronized (datas) {
                if (listener != null) listener.on_update_data(datas);
            }
        }
    
        private synchronized void add_downloader_item(Data.StructDLItem dataItem){
            int type, suffix, length;
             String title,url;
    
            type = dataItem.show_type;
            suffix = dataItem.type_minor;
            title = dataItem.title;
            url = dataItem.url;
            length = (int)dataItem.length;
            if(title==null || title.isEmpty() || url==null || url.isEmpty() || length<=0)return;
            if(type==0)return;
            String _ident=create_ident(type,suffix,title,url,length);
            String _ident_md5= CLTools.Get_MD5(_ident);
            for(int i=0;i<datas.size();++i){
                Data.StructDLItem _item=datas.get(i);
                if(_item.ident_md5!=null && _item.ident_md5.equals(_ident_md5))return;
            }
            if(DataBase.Share_Instance().check_file_repeat(_ident_md5))return;
            //Data.StructDLItem item=Data.StructDLItem.Create_New(_ident,title,url,type,suffix,length);
            Data.StructDLItem item = dataItem;
            item.ident_md5=_ident_md5;
//            datas.add(0,_tmp);

            if(datas.remove(item)){
                for(int i=0;i<datas.size();++i){
                    Data.StructDLItem _type2=datas.get(i);
                    CL.CLOGI("_type2.show_type = " + _type2.show_type);
                    if(_type2.show_type==2){
                        if(i+1>datas.size()-1)datas.add(item);
                        else datas.add(i+1,item);
                        //初始化新的下载项目
                        item.ID=DataBase.Share_Instance().insert_download_item(item);
                        item.show_type=3;
                        File _dir=file_manager.get_video_directory();
                        if(item.path==null) {
                            item.path = new File(_dir, item.ident_md5).getAbsolutePath();
                            DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,true);
                        }
                        
                        break;
                    }
                }
            }
            
            update_client_data();
        }

        private synchronized void add_confirm_item(int type, int suffix, String title, String url, long length, String params, String quality){
            if(title==null || title.isEmpty() || url==null || url.isEmpty() || length<=0)return;
            if(type==0)return;
            String _ident=create_ident(type,suffix,title,url,length);
            String _ident_md5= CLTools.Get_MD5(_ident);
            for(int i=0;i<datas.size();++i){
                Data.StructDLItem _item=datas.get(i);
                if(_item.ident_md5!=null && _item.ident_md5.equals(_ident_md5))return;
            }
            if(DataBase.Share_Instance().check_file_repeat(_ident_md5))return;
            Data.StructDLItem _tmp=Data.StructDLItem.Create_New(_ident,title,url,type,suffix,length, params);
            _tmp.ident_md5=_ident_md5;
            _tmp.quality = quality;
            datas.add(0,_tmp);
            update_client_data();
        }
        private synchronized void add_confirm_item(int type, String suffix, String title, String url, long length, String params){
            if(title==null || title.isEmpty() || url==null || url.isEmpty() || length<=0)return;
            if(type==0)return;
            String _ident=create_ident(type,suffix,title,url,length);
            String _ident_md5= CLTools.Get_MD5(_ident);
            for(int i=0;i<datas.size();++i){
                Data.StructDLItem _item=datas.get(i);
                if(_item.ident_md5!=null && _item.ident_md5.equals(_ident_md5))return;
            }
            if(DataBase.Share_Instance().check_file_repeat(_ident_md5))return;
            Data.StructDLItem _tmp=Data.StructDLItem.Create_New(_ident,title,url,type,suffix,length, params);
            _tmp.ident_md5=_ident_md5;
            datas.add(0,_tmp);
            update_client_data();
        }
        private synchronized void add_confirm_item_M3U8(String title, String url, String params, String quality, Boolean hasAd){
            if(title==null || title.isEmpty() || url==null || url.isEmpty())return;
            String _ident=create_ident(Data.Type_M3U8,Data.Type_M3U8,title,url,0);
            String _ident_md5= CLTools.Get_MD5(_ident);
            for(int i=0;i<datas.size();++i){
                Data.StructDLItem _item=datas.get(i);
                if(_item.ident_md5!=null && _item.ident_md5.equals(_ident_md5))return;
            }
            if(DataBase.Share_Instance().check_file_repeat(_ident_md5))return;
            Data.StructDLItem _tmp=Data.StructDLItem.Create_New(_ident,title,url,Data.Type_M3U8,Data.Type_M3U8,0, params);
            _tmp.ident_md5=_ident_md5;
            _tmp.quality = quality;
            _tmp.hasAd = hasAd;
            datas.add(0,_tmp);
            update_client_data();
        }
        private String create_ident(int type,int suffix,String title,String url,long length){
            StringBuffer _sb=new StringBuffer();
            _sb.append(type);
            _sb.append(suffix);
            _sb.append(title);
            _sb.append(url);
            _sb.append(length);
            return _sb.toString();
        }
        private String create_ident(int type,String suffix,String title,String url,long length){
            StringBuffer _sb=new StringBuffer();
            _sb.append(type);
            _sb.append(suffix);
            _sb.append(title);
            _sb.append(url);
            _sb.append(length);
            return _sb.toString();
        }
        private synchronized void remove_confirm_item(Data.StructDLItem item){
            if(item != null && item.dler==null && item.show_type==1){
                datas.remove(item);
                update_client_data();
            }
        }
    
        private synchronized void remove_downloader_item(Data.StructDLItem item){
            if(item != null && item.dler!=null && item.show_type==3){
                DataBase.Share_Instance().delete_item(item.ID);
                update_client_data();
            }
        }
        private synchronized void conversion_to_downloader(Data.StructDLItem item){
            if(item==null)return;
//            if(item.type_major==Data.Type_Video)Global.Flurry_Send_Event("dl_video");
//            else if(item.type_major==Data.Type_Music)Global.Flurry_Send_Event("dl_music");
//            else if(item.type_major==Data.Type_APK)Global.Flurry_Send_Event("dl_apk");
//            else if(item.type_major==Data.Type_Doc)Global.Flurry_Send_Event("dl_doc");
//            else if(item.type_major==Data.Type_M3U8)Global.Flurry_Send_Event("dl_m3u8");
//            else Global.Flurry_Send_Event("dl_other");
            if(datas.remove(item)){
                for(int i=0;i<datas.size();++i){
                    Data.StructDLItem _type2=datas.get(i);
                    if(_type2.show_type==2){
                        if(i+1>datas.size()-1)
                            datas.add(item);
                        else
                            datas.add(i+1,item);
                        //初始化新的下载项目
                        item.ID=DataBase.Share_Instance().insert_download_item(item);
                        item.show_type=3;
                        item.dler=create_downloader(item);
                        if(item.dler!=null)item.dler.go();
                        break;
                    }
                }
                update_client_data();
            }
        }
        private CommonDownloader create_downloader(Data.StructDLItem item){
            if(item.type_major==Data.Type_Video){
                File _dir=file_manager.get_video_directory();
                CLTools.isFolderExists(_dir.getAbsolutePath());
                if(item.path==null) {

                    String _suffix=null;
                    if(item.type_major==Data.Type_M3U8)_suffix="M3U8";
                    else _suffix=Data.Get_Type_Suffix(item.type_minor);
                    item.path = new File(_dir, item.ident_md5+"."+_suffix).getAbsolutePath();
                    DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,false);
                }
                return new CommonDownloader(item);
            }if(item.type_major==Data.Type_M3U8){
                File _dir=file_manager.get_video_directory();
                CLTools.isFolderExists(_dir.getAbsolutePath());
                if(item.path==null) {
//                    String _suffix=null;
//                    if(item.type_major==Data.Type_M3U8)
//                        _suffix="M3U8";

                   // _dir = new File(Setting.Share_Setting().get_export_dir());
                    item.path = new File(_dir, item.ident_md5).getAbsolutePath();
                    DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,false);
                }
                return new M3U8Downloader(item);
            }else if(item.type_major==Data.Type_Music){
                File _dir=file_manager.get_music_directory();
                CLTools.isFolderExists(_dir.getAbsolutePath());
                if(item.path==null) {
                    String _suffix=null;

                    _suffix=Data.Get_Type_Suffix(item.type_minor);
                    item.path = new File(_dir, item.ident_md5+"."+_suffix).getAbsolutePath();
                    DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,false);
                }
                return new CommonDownloader(item);
            }else if(item.type_major==Data.Type_Doc){
                File _dir=file_manager.get_doc_directory();
                CLTools.isFolderExists(_dir.getAbsolutePath());
                if(item.path==null) {
                    String _suffix=null;

                    _suffix=Data.Get_Type_Suffix(item.type_minor);
                    item.path = new File(_dir, item.title+"."+_suffix).getAbsolutePath();
                    DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,false);
                }
                return new CommonDownloader(item);
            }else if(item.type_major==Data.Type_APK){
                File _dir=file_manager.get_apk_directory();
                CLTools.isFolderExists(_dir.getAbsolutePath());
                if(item.path==null) {
                    String _suffix=null;
                    _suffix=Data.Get_Type_Suffix(item.type_minor);
                    item.path = new File(_dir, item.ident_md5+"."+_suffix).getAbsolutePath();
                    DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,false);
                }
                return new CommonDownloader(item);
            }else if(item.type_major==Data.Type_Other){
                File _dir=file_manager.get_other_directory();
                CLTools.isFolderExists(_dir.getAbsolutePath());
                if(item.path==null) {
                    String _suffix=null;
                    _suffix=Data.Get_Type_Suffix(item.type_minor);
                    item.path = new File(_dir, item.title+"."+_suffix).getAbsolutePath();
                    DataBase.Share_Instance().update_status(item.ID, item.path, item.duration, item.length,false);
                }
                return new CommonDownloader(item);
            }
            return null;
        }

        private synchronized boolean create_sniffer_downloader(String title, Bitmap thumb,String url,
                                                           int type_major,int type_minor,String provenance,int length, String params){
            if(type_major<=0 || type_minor<=0 || length<=0 || title==null || url==null || provenance==null)return false;
           //Global.Flurry_Send_Event("dl_video");
            //生成md5
            StringBuffer _sb=new StringBuffer();
            _sb.append(type_major);
            _sb.append(type_minor);
            _sb.append(title);
            _sb.append(url);
            _sb.append(length);
            _sb.append(System.currentTimeMillis());
            String _ident=_sb.toString();
            String _ident_md5= CLTools.Get_MD5(_ident);
            Data.StructDLItem _item=Data.StructDLItem.Create_New(_ident,title,url,type_major,type_minor,length,params);
            _item.ident_md5=_ident_md5;

            for(int i=0;i<datas.size();++i){
                Data.StructDLItem _type2=datas.get(i);
                if(_type2.show_type==2){
                    if(i+1>datas.size()-1)datas.add(_item);
                    else datas.add(i+1,_item);
                    //初始化新的下载项目
                    _item.ID=DataBase.Share_Instance().insert_download_item(_item);
                    _item.show_type=3;

                    File _dir=file_manager.get_video_directory();
                    _item.path = new File(_dir, _item.ident_md5).getAbsolutePath();
                    DataBase.Share_Instance().update_status(_item.ID, _item.path, _item.duration, _item.length,false);
                    if(thumb!=null){
                        try {
                            final File _thumb=new File(Global.Dir_thum,_item.path.replace('/','_'));
                            thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                        }catch (Exception ex){}
                    }
                    _item.dler=new CommonDownloader(_item);
                    if(_item.dler!=null)_item.dler.go();
                    break;
                }
            }
            update_client_data();
            return true;
        }

        private void clear_completed(){
            DataBase.Share_Instance().clear_completed();
            for(int i=0;i<datas.size();++i){
                Data.StructDLItem _item=datas.get(i);
                if(_item.show_type==3 && _item.downloaded){
                    datas.remove(i);
                    --i;
                }
            }
            update_client_data();
        }

    }
}
