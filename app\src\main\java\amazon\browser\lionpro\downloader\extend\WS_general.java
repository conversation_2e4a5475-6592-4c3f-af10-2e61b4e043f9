package amazon.browser.lionpro.downloader.extend;

import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.screen.Home;

import lion.widget.CLWebkit;

/**
 * Created by EddyHu on 2016/12/18.
 */

public class WS_general implements ResSniffer.WebSiteSniff {
    WebViewCallBack iCallback;
    public WS_general(WebViewCallBack iCallback){
        this.iCallback = iCallback;
    }
    public boolean on_interest(WebViewCallBack callback, String url, String title)throws Exception {
        int vv = url.indexOf(Global.JS_Find);
/*        if (!Server.parse_done && vv!=-1) {
            int a;
            a = 1;
            int b;
            b = a;
            callback.Events(url);
            return true;
        }
        */

        boolean b = Home.s_home.is_have_life((CLWebkit)iCallback);
        //CL.CLOGI("Eddy WS_general b = " + b);
        if (vv!=-1 && b) {
            callback.Events(url);
            return true;
        } else {
          //  CL.CLOGI("Eddy WS_general not run");
        }
        return false;
    }



    public void set_callback(WebViewCallBack iCallback) {
        iCallback = iCallback;
    }
}
