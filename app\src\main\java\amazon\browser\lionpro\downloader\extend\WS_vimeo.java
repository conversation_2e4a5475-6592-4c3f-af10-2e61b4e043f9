package amazon.browser.lionpro.downloader.extend;

import android.text.TextUtils;

import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.Tools;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Iterator;
import java.util.regex.Pattern;

import javax.net.ssl.HttpsURLConnection;

import lion.CL;

/**
 * Created by leron on 2016/9/12.
 */
public class WS_vimeo implements ResSniffer.WebSiteSniff {

    private Pattern p;
    private Pattern p2;
    private Pattern p3;
    public WS_vimeo(){
        p=Pattern.compile(".*://player\\.vimeo\\.com/video/.*/config\\?.+");
        p2 = Pattern.compile(".*://player\\.vimeo\\.com/video/.+");
        p3 = Pattern.compile(".*://player\\.vimeo\\.com/video/.*\\?autoplay=1");
    }

    @Override
    public boolean on_interest(WebViewCallBack webview, String url, String title) throws Exception {
//        CL.CLOGI("filter url:"+url);
        if(p.matcher(url).find()){
            CL.CLOGI("on website interest:"+url);
            int _clength=0;
            HttpURLConnection _conn=null;
            if(url.startsWith("http://")){
                _conn=(HttpURLConnection) new URL(url).openConnection();
            }else if(url.startsWith("https://")) {
                _conn = (HttpsURLConnection) new URL(url).openConnection();
            }
            _conn.setConnectTimeout(5000);
            _conn.setReadTimeout(5000);
            _conn.setRequestProperty("User-Agent", Global.Crt_UA);
            _conn.connect();
            String _real_url=_conn.getURL().toString();
            byte[] _buff=new byte[8192];
            int _count=0;
            InputStream _is=_conn.getInputStream();
            ByteArrayOutputStream _baos=new ByteArrayOutputStream();
            while ((_count=_is.read(_buff))!=-1) {
                _baos.write(_buff,0,_count);
            }
            _conn.disconnect();
            _baos.flush();
            _buff=_baos.toByteArray();
            String _json=new String(_buff,"utf8");
            parse_json(_json);
            return true;
        }  else if (p3.matcher(url).find()) {
            try {
                CL.CLOGI("on website interest:" + url);
                int _clength = 0;
                HttpURLConnection _conn = null;
                if (url.startsWith("http://")) {
                    _conn = (HttpURLConnection) new URL(url).openConnection();
                } else if (url.startsWith("https://")) {
                    _conn = (HttpsURLConnection) new URL(url).openConnection();
                }
                _conn.setConnectTimeout(5000);
                _conn.setReadTimeout(5000);
                _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                _conn.setRequestProperty("Referer", "https://pr.inversapub.com/");
                _conn.connect();
                String _real_url = _conn.getURL().toString();
                byte[] _buff = new byte[8192];
                int _count = 0;
                InputStream _is = _conn.getInputStream();
//            ByteArrayOutputStream _baos=new ByteArrayOutputStream();
//            while ((_count=_is.read(_buff))!=-1) {
//                _baos.write(_buff,0,_count);
//            }
//            _conn.disconnect();
//            _baos.flush();
                //          _buff=_baos.toByteArray();

                InputStreamReader isr = new InputStreamReader(_is);
                BufferedReader reader = new BufferedReader(isr);
                String line;
                while ((line = reader.readLine()) != null) {
                    String temp = line.trim();
                    if (temp.startsWith("var config")) {
                        line = temp.substring(temp.indexOf("{"), temp.lastIndexOf("}") + 1);
                        break;
                    }
                }
                isr.close();
                reader.close();
                _conn.disconnect();
               // String _json = new String(_buff, "utf8");
                //parse_json(_json);
                if (!TextUtils.isEmpty(line)) {
                    parse_json(line);
                }
            } catch (Exception ex) {}
            return true;
        }  else if (p2.matcher(url).find()) {
            try {
                CL.CLOGI("on website interest:" + url);
                int _clength = 0;
                HttpURLConnection _conn = null;
                if (url.startsWith("http://")) {
                    _conn = (HttpURLConnection) new URL(url).openConnection();
                } else if (url.startsWith("https://")) {
                    _conn = (HttpsURLConnection) new URL(url).openConnection();
                }
                _conn.setConnectTimeout(5000);
                _conn.setReadTimeout(5000);
                _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                _conn.connect();
                String _real_url = _conn.getURL().toString();
                byte[] _buff = new byte[8192];
                int _count = 0;
                InputStream _is = _conn.getInputStream();
//            ByteArrayOutputStream _baos=new ByteArrayOutputStream();
//            while ((_count=_is.read(_buff))!=-1) {
//                _baos.write(_buff,0,_count);
//            }
//            _conn.disconnect();
//            _baos.flush();
                //          _buff=_baos.toByteArray();

                InputStreamReader isr = new InputStreamReader(_is);
                BufferedReader reader = new BufferedReader(isr);
                String line;
                while ((line = reader.readLine()) != null) {
                    String temp = line.trim();
                    if (temp.startsWith("var config")) {
                        line = temp.substring(temp.indexOf("{"), temp.lastIndexOf("}") + 1);
                        break;
                    }
                }
                isr.close();
                reader.close();
                _conn.disconnect();
                // String _json = new String(_buff, "utf8");
                //parse_json(_json);
                if (!TextUtils.isEmpty(line)) {
                    parse_json(line);
                }
            } catch (Exception ex) {}
            return true;
        }
        return false;
    }

    private void parse_json(String json)throws Exception{
        ResSniffer.SniffDataWebsite _data=new ResSniffer.SniffDataWebsite();
        JSONObject _root=new JSONObject(json);
        JSONObject _video=_root.getJSONObject("video");
        JSONObject _thumbs=_video.getJSONObject("thumbs");
        JSONObject _request=_root.getJSONObject("request").getJSONObject("files");
        JSONArray _progressive=_request.getJSONArray("progressive");
        if(_progressive!=null && _progressive.length()>0){
            for(int i=0;i<_progressive.length();++i){
                JSONObject _tmp=_progressive.getJSONObject(i);
                if(_tmp.getString("mime").equals("video/mp4")) {
                    _data.urls.add(new ResSniffer.SniffWSData(
                            _tmp.getString("url"),
                            _tmp.getString("quality"),
                            _tmp.getString("width"),
                            _tmp.getString("height"),
                            ""
                    ));
                }
            }
        }
        int _min=222222;
        String _thumb=null;
        if(_thumbs!=null){
            Iterator<String> _itor=_thumbs.keys();
            while (_itor.hasNext()){
                String _key=_itor.next();
                String _v=_thumbs.getString(_key);
                try {
                    int _quality = Integer.parseInt(_key);
                    if (_quality < _min) {
                        _min = _quality;
                        _thumb = _v;
                    }
                }catch (Exception ex){}
            }
            if(_thumb!=null) _data.url_thumb=_thumb;
        }
        if(_video!=null) {
            String _title = _video.getString("title");
            _title = Tools.ReplaceBadFilename(_title);

            if (!Tools.CheckFileName(_title)) {
                _title = Tools.FixFileName(_title);
            }
            _data.title=_title;
        }
        _data.provenance="vimeo";
        if(_data.urls.size()>0)ResSniffer.On_Website_Sniff(_data);
    }
}

//    {
//      "name": "youtube",
//      "image": "picture/youtube_xx.png",
//      "url": "http://www.youtube.com"
//    },