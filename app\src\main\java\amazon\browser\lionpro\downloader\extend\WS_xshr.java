package amazon.browser.lionpro.downloader.extend;

import android.os.Build;
import android.text.TextUtils;

import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.MultiResolution;
import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.util.HttpsSslCertificate;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.regex.Pattern;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;

import lion.CLTools;

public class WS_xshr implements ResSniffer.WebSiteSniff {
    private Pattern p;
    private Pattern p1;
    public WS_xshr(){
        p = Pattern.compile(".*://delivery.+\\.akamai-cdn-content\\.com/hls2/.+");
        p1 = Pattern.compile(".*://delivery.+\\.akamai-cdn-content\\.com/hls2/.+");
    }
    @Override
    public boolean on_interest(WebViewCallBack webview, String url, String title) throws Exception {
        if (p.matcher(url).find()) {
//            int index = url.indexOf("?");
//            String tmp_url = url.substring(index);
//            String tmp_url1 = url.substring(0,index);
//            tmp_url = URLEncoder.encode(tmp_url,"UTF-8");
//            tmp_url = tmp_url1 + tmp_url;
            HttpURLConnection _conn = null;
            URL tmp_url = new URL(url);
                if (url.startsWith("http://")) {
                    _conn = (HttpURLConnection) new URL(url).openConnection();
                } else if (url.startsWith("https://")) {
                    _conn = (HttpsURLConnection) new URL(url).openConnection();
                }

                _conn.setConnectTimeout(5000);
                _conn.setReadTimeout(5000);
//                _conn.setDoInput(true);
//                _conn.setDoOutput(true);
                _conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Linux; Android 8.0; Galaxy Nexus Build/IMM76B) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/75.0.3770.100 Mobile Safari/535.19");
                _conn.setRequestMethod("GET");
                _conn.setRequestProperty("Accept-Language", "en-US,en;q=0.9");
               // _conn.setRequestProperty("Content-Type", "application/octet-stream");
               // _conn.setRequestProperty("Host", tmp_url.getHost());

                if (_conn instanceof HttpsURLConnection) {
                    SSLContext sc = SSLContext.getInstance("SSL");
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager2()}, new SecureRandom());
                    } else {
                        sc.init(null, new TrustManager[]{new HttpsSslCertificate.TrustAnyTrustManager()}, new java.security.SecureRandom());
                    }
                    ((HttpsURLConnection) _conn).setSSLSocketFactory(sc.getSocketFactory());
                    ((HttpsURLConnection) _conn).setHostnameVerifier(new HttpsSslCertificate.TrustAnyHostnameVerifier());
                }

                _conn.connect();
                int responseCode = _conn.getResponseCode();
                String _type=null,_real_url=null;
                long _clength=0;
                byte[] _ident=null;
            if (responseCode >= 200 && responseCode <= 206) {
                _type = _conn.getContentType();

                if (_type != null) _type = _type.toLowerCase();

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N)
                    _clength = _conn.getContentLengthLong();
                else
                    _clength = _conn.getContentLength();




                _real_url = _conn.getURL().toString();
                _ident = new byte[32];
                int _count = _conn.getInputStream().read(_ident);
                if (_count < 32) _ident = null;

                // _conn.disconnect();

                if (_count > 0
                        && (_ident != null && (new String(_ident).toUpperCase().contains("#EXTM3U")))
                        && _real_url != null ) { //&& _real_url.toLowerCase().contains(".m3u8")
                        //FileOutputStream _fos=new FileOutputStream(_om3u8);
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        baos.write(_ident);
                        byte[] buff = new byte[4096];
                        int count;
                        while ((count = _conn.getInputStream().read(buff)) != -1) {
                            baos.write(buff, 0, count);
                        }
                        baos.flush();
                        byte[] m3u8Data = baos.toByteArray();
                        baos.close();

                        BufferedReader reader = new BufferedReader(
                                new InputStreamReader(new ByteArrayInputStream(m3u8Data)));

                        boolean isStart = false;
                        boolean isEnd = false;
                        String orgin_line;
                        ArrayList<MultiResolution> multiResolutions = new ArrayList<>();
                        MultiResolution result = new MultiResolution();
                        String scheme = CLTools.GetUrlScheme(_real_url);
                        String host = CLTools.Get_Url_Host(_real_url);
                        host = scheme + "://" + host;
                        String host2 = _real_url.substring(0, _real_url.lastIndexOf('/') + 1);
                        while ((orgin_line = reader.readLine()) != null) {
                            String line = orgin_line.toUpperCase();
                            if (TextUtils.equals(line, "#EXTM3U")) {
                                if (!isStart) {
                                    isStart = true;
                                }
                            } else if (isStart && line.startsWith("#EXT-X-STREAM-INF")) {
                                // 多码率
                                MultiResolution mr = new MultiResolution();
                                String[] temp = orgin_line.substring(orgin_line.indexOf(":") + 1).split(",");
                                if (temp.length > 0) {
                                    for (int i = 0; i < temp.length; ++i) {
                                        if (temp[i].startsWith("BANDWIDTH=")) {
                                            //String tmp = temp[i].substring(temp[i].indexOf("BANDWIDTH=") + "BANDWIDTH=".length(), temp[i].length());
                                            //mr.quality = tmp;
                                            mr.quality = temp[i];
                                            //break;
                                        } else if (temp[i].startsWith("RESOLUTION=")) {
                                            //mr.resolution = temp[i].substring(temp[i].indexOf("RESOLUTION=") + "RESOLUTION=".length(), temp[i].length());
                                            mr.resolution = temp[i];
                                        }
                                    }
                                }
                                orgin_line = reader.readLine();
                                if (orgin_line != null) {
                                    mr.url = orgin_line;
                                    if (!mr.url.startsWith("http")) {
                                        if (!mr.url.startsWith("/")) {
                                            mr.url = host2 + mr.url;
                                        } else {
                                            mr.url = host + mr.url;
                                        }
                                    }
                                }

                                multiResolutions.add(mr);
                            } else if (isStart && line.startsWith("#EXTINF")) {
                                // 单码率
                                result.length += 1;
                            } else if (isStart && line.startsWith("#EXT-X-ENDLIST")) {
                                isEnd = true;
                                break;
                            }
                        }

                        int index = -1;
                        if (multiResolutions != null && multiResolutions.size() > 0) {
//                                    long max_value = 0;
//                                    for (int i = 0; i < multiResolutions.size(); i++) {
//                                        MultiResolution resolution = multiResolutions.get(i);
//                                        if (resolution.quality != null && resolution.quality.length() > 10) {
//                                            String[] _quality = resolution.quality.split("=");
//                                            if (_quality != null) {
//                                                long quality = Long.parseLong(_quality[1]);
//                                                if (quality > max_value) {
//                                                    max_value = quality;
//                                                    index = i;
//                                                }
//                                            }
//                                        }
//                                    }
                            for (int i = 0; i < multiResolutions.size(); i++) {
                                MultiResolution resolution = multiResolutions.get(i);
//                                        if (index != -1) {
//                                            if (i == index) {
//                                                resolution.hasAd = true;
//                                            }
//                                            listener.on_sniffer("m3u8", resolution.url, 0, _item.title, resolution);
//                                        } else
                                ResSniffer.on_sniffer("m3u8", resolution.url, 0, title, resolution);
                            }
                        } else {
                            ResSniffer.on_sniffer("m3u8", _real_url, 0, title);
                        }

                    _conn.disconnect();
                    return false;
                }
                _conn.disconnect();

                String _ident_str = CLTools.Parse_Video_Audio_Format(_ident);

                if (_type != null && _type.contains("mp4"))
                    _ident_str = "video/mp4";
                else if (_type != null && _type.equals("audio/acc")) {
                    _ident_str = "audio/acc";
                } else if (_type != null && _type.equals("audio/aac")) {
                    _ident_str = "audio/aac";
                } else if (_type != null && _type.equals("audio/mpeg")) {
                    _ident_str = "audio/mpeg";
                } else if (_type != null && _type.equals("application/octet-stream")) {
                    if (_conn.getURL().toString().contains(".mp3")) {
                        _ident_str = "audio/mpeg";
                    }
                } else if (_type != null && _type.contains("dash")) {
                    return false;
                }

                if (_ident_str != null) {
                    if (_ident_str.contains("m3u8")) {
                      //  if (listener != null)
                            ResSniffer.on_sniffer("m3u8", _real_url, 0, title);
                    } else if (!_real_url.contains(".m4s")) {


                            if (Setting.Share_Setting().get_filter_switch()) {
                                if (_clength != -1 && _clength > 0) {
                                    long min_size = Setting.Share_Setting().get_filter_mini_size();
                                    long max_size = Setting.Share_Setting().get_filter_max_size();
                                    if (_clength < min_size) {
                                        return false;
                                    }

                                    if (max_size != -1 && _clength > max_size) {
                                        return false;
                                    }
                                }
                            }

                            ResSniffer.on_sniffer(_ident_str, _real_url, _clength, title);

                        return false;
                    }
                }
            }


//                String _real_url=_conn.getURL().toString();
//                byte[] _buff = new byte[8192];
//                int _count = 0;
//                InputStream _is =_conn.getInputStream();
//                ByteArrayOutputStream _baos = new ByteArrayOutputStream();
//                while ((_count=_is.read(_buff))!=-1) {
//                    _baos.write(_buff,0,_count);
//                }
//                _conn.disconnect();
//                _baos.flush();
//                _buff=_baos.toByteArray();
//                String _json=new String(_buff,"utf8");
                // parse_json(_json);

        }
        return false;
    }

}
