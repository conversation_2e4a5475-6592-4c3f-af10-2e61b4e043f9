package amazon.browser.lionpro.downloader.extend;

import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.primary.Global;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.regex.Pattern;

import javax.net.ssl.HttpsURLConnection;

import lion.CLTools;

/**
 * Created by leron on 2016/10/23.
 */

public class WS_youtube implements ResSniffer.WebSiteSniff {

    private Pattern p;

    public WS_youtube(){
        p=Pattern.compile(".*.youtube.com/watch\\?.+");
    }

    @Override
    public boolean on_interest(WebViewCallBack webview, String url, String title) throws Exception {
//        CL.CLOGI("filter url:"+url);
        if(p.matcher(url).find()){
//            CL.CLOGI("on website interest:"+url);
            String[] _args=url.split("&");
            String _id=null;
            for(int i=0;i<_args.length;++i){
                if(_args[i].startsWith("v=")){
                    _id=_args[i].substring(2);
                    break;
                }
            }
            if(_id==null)return false;

            String _url="http://youtube.com/get_video_info?video_id="+_id;
            int _clength=0;
            HttpURLConnection _conn=null;
            if(_url.startsWith("http://")){
                _conn=(HttpURLConnection) new URL(_url).openConnection();
            }else if(_url.startsWith("https://")) {
                _conn = (HttpsURLConnection) new URL(_url).openConnection();
            }
            _conn.setConnectTimeout(5000);
            _conn.setReadTimeout(5000);
            _conn.setRequestProperty("User-Agent", Global.Crt_UA);
            _conn.connect();
            String _real_url=_conn.getURL().toString();
            byte[] _buff=new byte[8192];
            int _count=0;
            InputStream _is=_conn.getInputStream();
            ByteArrayOutputStream _baos=new ByteArrayOutputStream();
            while ((_count=_is.read(_buff))!=-1) {
                _baos.write(_buff,0,_count);
            }
            _conn.disconnect();
            _baos.flush();
            _buff=_baos.toByteArray();
            String _json=new String(_buff,"utf8");
            parse_json(_json);
            return true;
        }
        return false;
    }

    private void parse_json(String json)throws Exception{
        String[] _infos=json.split("&");
        ResSniffer.SniffDataWebsite _data=new ResSniffer.SniffDataWebsite();
        _data.provenance="youtube";
        for(int i=0;i<_infos.length;++i){
            if(_infos[i].startsWith("title")){
                _data.title=URLDecoder.decode(_infos[i].substring(_infos[i].indexOf('=')+1),"utf8");
            }
            else if(_infos[i].startsWith("thumbnail_url")){
                _data.url_thumb=URLDecoder.decode(_infos[i].substring(_infos[i].indexOf('=')+1),"utf8");
            }
            else if(_infos[i].startsWith("adaptive_fmts")){
                String _s=_infos[i].substring(_infos[i].indexOf('=')+1);
                _s= URLDecoder.decode(_s,"utf8");
                String[] _sgroups=_s.split(",");
                for(int n=0;n<_sgroups.length;++n){
                    if(!_sgroups[n].contains("type=video"))continue;
                    String[] _sargs=_sgroups[n].split("&");
                    ResSniffer.SniffWSData _item=new ResSniffer.SniffWSData();
                    for(int m=0;m<_sargs.length;++m){
                        if(_sargs[m].startsWith("url=")){
                            _item.url=URLDecoder.decode(_sargs[m].substring(_sargs[m].indexOf('=')+1),"utf8");
                        }else if(_sargs[m].startsWith("type=")){
                            String _mime=URLDecoder.decode(_sargs[m].substring(_sargs[m].indexOf('=')+1),"utf8");
                            if(!_mime.contains("video/mp4")){
                                _item.quality=null;
                                break;
                            }
                        }else if(_sargs[m].startsWith("quality_label=")){
                            _item.quality=URLDecoder.decode(_sargs[m].substring(_sargs[m].indexOf('=')+1),"utf8");
                        }
                        else if(_sargs[m].startsWith("size=")){
                            _item.wh_string=URLDecoder.decode(_sargs[m].substring(_sargs[m].indexOf('=')+1),"utf8");
                        }
                        else if(_sargs[m].startsWith("clen=")){
                            String _size=_sargs[m].substring(_sargs[m].indexOf('=')+1);
                            try {
                                int _ssize = Integer.parseInt(_size);
                                _item.video_size= CLTools.Get_Capacity_Format(_ssize);
                            }catch (Exception ex){}
                        }
                    }
                    if(_item.quality!=null)_data.urls.add(_item);
                }
            }
        }
        if(_data.urls.size()>0)ResSniffer.On_Website_Sniff(_data);
    }
}
