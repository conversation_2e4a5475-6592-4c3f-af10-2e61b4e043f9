package amazon.browser.lionpro.primary;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;

import lion.CL;
import lion.CLController;

/**
 * Created by leron on 2016/10/12.
 */

public class AcyEvaluate extends Activity {


    private FrameLayout fl_main;
    private long start_time=0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        fl_main =new FrameLayout(this){
            @Override
            protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
                super.onLayout(changed, left, top, right, bottom);
                if(changed){
                    int _w=this.getWidth();
                    int _h=this.getHeight();
                    if(_w<_h)setPadding((int)(_w*0.1f),getPaddingTop(),(int)(_w*0.1f),0);
                    else setPadding((int)(_h*0.35f),getPaddingTop(),(int)(_h*0.35f),0);
                    setVisibility(View.GONE);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            setVisibility(View.VISIBLE);
                        }
                    },100);
                }
            }
        };
        fl_main.setBackgroundColor(0x22000000);
        this.setContentView(fl_main);

        LinearLayout _bg=get_common_bg(this);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        fl_main.addView(_bg, CL.Get_FLLP(CL.MP, CL.WC, Gravity.CENTER));
        TextView _tv_title=new TextView(this);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC, CL.WC,0, CL.DIP2PX_INT(12),0, CL.DIP2PX_INT(12)));
        _tv_title.setText(R.string.dialog_title_tip);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);

        TextView _tv_msg=new TextView(this);
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC, CL.DIP2PX_INT(15), CL.DIP2PX_INT(2), CL.DIP2PX_INT(15), CL.DIP2PX_INT(18)));
        _tv_msg.setText(this.getResources().getString(R.string.desc_goto_comment));
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);

        LinearLayout _ll_btns=new LinearLayout(this);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC,0,0, CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);

        CLController.DiscolourButton _btn_cancel= CLController.Get_Discolour_Button(this, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                this.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onBackPressed();
                    }
                });
        _btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_cancel);
        //0xff005880
        CLController.DiscolourButton _btn_ok= CLController.Get_Discolour_Button(this, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                this.getString(R.string.yes), 16, 0xffff0000, 0xaaff0000, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {


                            if(Build.VERSION.SDK_INT>=24) { //判读版本是否在7.0以上
                                String mAddress = "market://details?id=" + getPackageName();
                                //Intent intent = new Intent("android.intent.action.VIEW");
                                Intent intent = new Intent(Intent.ACTION_VIEW);
                                intent.setData(Uri.parse(mAddress));
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                //startActivity(intent);
                                startActivity(Intent.createChooser(intent, ""));
                            } else {
                                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + getPackageName()));
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivity(intent);
                            }

                            start_time=System.currentTimeMillis();
                        }catch (Exception ex){
                            onBackPressed();
                        }
                    }
                });
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));


        RelativeLayout rootlayout = new RelativeLayout(this);
        rootlayout.setLayoutParams(new RelativeLayout.LayoutParams(
                CL.WC, CL.WC));
        _btn_ok.setId(1001);
        rootlayout.addView(_btn_ok);

       /* ImageView imageView = new ImageView(this);
        RelativeLayout.LayoutParams image_Params = new RelativeLayout.LayoutParams(
                CL.WC, CL.WC);
        image_Params.setMargins(0, 0, 60, 0);
        image_Params.addRule(RelativeLayout.ALIGN_RIGHT, 1001);
        image_Params.addRule(RelativeLayout.ALIGN_TOP, 1001);
        // image_Params.setMargins(dip2px(20), dip2px(25), 0, dip2px(10));
        imageView.setLayoutParams(image_Params);

        imageView.setImageResource(R.mipmap.red_position);
        rootlayout.addView(imageView);*/
        _ll_btns.addView(rootlayout);
        //_ll_btns.addView(_btn_ok);

    }

    private LinearLayout get_common_bg(Context cc){
        LinearLayout _ll=new LinearLayout(cc);
        RoundRectShape _shape=new RoundRectShape(new float[]{8,8,8,8,8,8,8,8}, null, null);
        ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
        //_dwe_bg.getPaint().setColor(0xffa0a0a0);
        _dwe_bg.getPaint().setColor(0xffffffff);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
        _ll.setBackground(_dwe_bg);
        return _ll;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        SerMain.reRunEvaluateServer();
        overridePendingTransition(0,0);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
        if(start_time>0) {
            if (System.currentTimeMillis()-start_time>1500){
                Setting.Share_Setting().set_evaluate(true);
            }
            onBackPressed();
        }
    }
}
