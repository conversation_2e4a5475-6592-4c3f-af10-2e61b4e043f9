package amazon.browser.lionpro.primary;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import lion.CL;

/**
 * Created by leron on 2016/8/12.
 */
public class AcyIntent extends Activity{


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent _intent=this.getIntent();
        if(_intent.getData()!=null)process_open_url(_intent.getData());
        finish();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        CL.CLOGI("on new intent");
    }

    private void process_open_url(Uri uri){
        CL.CLOGI("open url:"+uri.toString());
        if(Global.Acy_Main==null){
            Intent _intent=new Intent(this,AcyMain.class);
            _intent.setData(uri);
            startActivity(_intent);
        }else{
            Intent _intent_activty=new Intent(this,AcyMain.class);
            _intent_activty.setAction(Intent.ACTION_MAIN);
            _intent_activty.addCategory(Intent.CATEGORY_LAUNCHER);
            _intent_activty.addFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                    | Intent.FLAG_ACTIVITY_NEW_TASK
                    | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
            this.startActivity(_intent_activty);


            startActivity(_intent_activty);
            Intent _intent=new Intent("cl_open_url");
            _intent.putExtra("url",uri.toString());
            sendBroadcast(_intent);
        }
    }
}
