package amazon.browser.lionpro.primary;

import static lion.CL.SDK;

import android.Manifest;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;

import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
//import androidx.core.app.ActivityCompat;
//import androidx.core.content.ContextCompat;

import android.provider.Settings;
import android.view.Gravity;

import android.view.View;
import android.view.Window;
import android.widget.FrameLayout;

import eddy.android.billing.BillingManager;
import com.android.billingclient.api.BillingResult;
//import com.applovin.sdk.AppLovinSdk;
import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.screen.Home;
//import com.browser.lionpro.util.IabHelper;
//import com.browser.lionpro.util.IabResult;
//import com.browser.lionpro.util.Inventory;
//import com.browser.lionpro.util.Purchase;
//import com.browser.lionpro.util.SkuDetails;
//import com.facebook.ads.AudienceNetworkAds;
//import com.google.android.gms.ads.formats.UnifiedNativeAdView;
//import com.tapjoy.TJActionRequest;
//import com.tapjoy.TJConnectListener;
//import com.tapjoy.TJEarnedCurrencyListener;
//import com.tapjoy.TJError;
//import com.tapjoy.TJGetCurrencyBalanceListener;
//import com.tapjoy.TJPlacement;
//import com.tapjoy.TJPlacementListener;
//import com.tapjoy.TJPlacementVideoListener;
//import com.tapjoy.Tapjoy;
//import com.tapjoy.TapjoyConnectFlag;
//import com.tencent.bugly.crashreport.CrashReport;

import java.net.CookieHandler;
import java.net.CookieManager;
import java.net.CookiePolicy;
import java.util.Hashtable;
import java.util.List;

import gp.BillingActivity;
import gp.BillingPurchaseDetails;
import gp.BillingViewModel;
import lion.CL;

import lion.CLActivity;
import lion.CLBus;

import lion.CLCallback;
import lion.CLDialog;
import lion.CLToast;
import lion.FunctionRunTimeCallBack;

//import static com.browser.lionpro.util.IabHelper.BILLING_RESPONSE_RESULT_ERROR;

/**
 * Created by leron on 2016/4/5.
 */
public class AcyMain extends CLActivity implements BillingManager.BillingUpdatesListener {
    private boolean earnedCurrency = false;
    //private TJPlacement directPlayPlacement;
    private FrameLayout fl_main;
    private FrameLayout fl_video;
    private Home layer_home;
    private int statebar_height;
    private boolean in_activity=true;
   // private IabHelper mHelper;
    boolean mIsPremium = false;
    public static BillingViewModel viewModel;
    private final static int REQUESTCODE = 1; // 返回的结果码
    private BillingManager billingManager;
    private ServiceConnection conn_service=new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {}
        @Override
        public void onServiceDisconnected(ComponentName name) {}
    };


    private ServiceConnection conn_merge_service=new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Global.binder = (M3u8MergeServer.WorkerBinder)service;
        }
        @Override
        public void onServiceDisconnected(ComponentName name) {}
    };

    private BroadcastReceiver recevier_open_url=new BroadcastReceiver() {
        @Override
        public void onReceive(final Context context, Intent intent) {
            if(intent.getAction().equals("cl_open_url")){
                String _url=intent.getStringExtra("url");
                if(_url!=null){
                    layer_home.load_url(_url);
                    fl_main.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if(!in_activity) {
                                CLToast.Show(AcyMain.this,AcyMain.this.getResources().getString(R.string.tip_open_url),true);
                            }
                        }
                    },200);
                }
            }
        }
    };

    @Override
    protected void onStart() {
        super.onStart();
       // Tapjoy.onActivityStart(this);
    }

    @Override
    protected void onStop() {
       // Tapjoy.onActivityStop(this);
        super.onStop();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (SDK >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            Window window = getWindow();

            // 1. 允许内容延伸到系统栏区域（全屏）
            WindowCompat.setDecorFitsSystemWindows(window, false);

            // 2. 设置状态栏/导航栏图标颜色（根据背景亮度）
            WindowInsetsControllerCompat controller =
                    new WindowInsetsControllerCompat(window, window.getDecorView());

            controller.setAppearanceLightStatusBars(true); // 状态栏图标为深色（背景浅时）
            controller.setAppearanceLightNavigationBars(true); // 导航栏图标为深色
        }


        Global.Acy_Main=this;
        this.bindService(new Intent(this,SerMain.class),conn_service, Context.BIND_AUTO_CREATE);
        this.bindService(new Intent(this, M3u8MergeServer.class), conn_merge_service, Context.BIND_AUTO_CREATE);
        //statebar_height=CL.Set_Translucent_StatusBar(this.getWindow());
        fl_main=new FrameLayout(this);
        fl_main.setBackgroundColor(getResources().getColor(R.color.bg_main_title));


        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            statebar_height = CL.Set_Translucent_StatusBar(this.getWindow());
        } else {
            EdgeToEdge.enable(this);
            View root = fl_main;

            ViewCompat.setOnApplyWindowInsetsListener(root, (v, insets) -> {
                Insets statusBar = insets.getInsets(WindowInsetsCompat.Type.systemBars());
                v.setPadding(0, statusBar.top, 0, statusBar.bottom);
                return insets;
            });
        }

        this.setContentView(fl_main);
        Setting.Init(this);
        init();

        Global.initMobileAdsSdk(this);

        Intent _intent=this.getIntent();
        if(_intent.getData()!=null){
            layer_home. load_url(_intent.getData().toString());
        }

        // 安全地注册广播接收器，兼容不同 Android 版本
        IntentFilter filter = new IntentFilter("cl_open_url");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 需要明确指定 RECEIVER_EXPORTED
            try {
                this.registerReceiver(recevier_open_url, filter, Context.RECEIVER_EXPORTED);
            } catch (Exception e) {
                // 如果出现异常，回退到传统方法
                this.registerReceiver(recevier_open_url, filter);
            }
        } else {
            // Android 13 以下版本使用传统方法
            this.registerReceiver(recevier_open_url, filter);
        }


        CookieManager manager = new CookieManager();
        //设置cookie策略，只接受与你对话服务器的cookie，而不接收Internet上其它服务器发送的cookie
        manager.setCookiePolicy(CookiePolicy.ACCEPT_ORIGINAL_SERVER);
        CookieHandler.setDefault(manager);


        int count = Setting.Share_Setting().get_app_run_count();
        if (Setting.Share_Setting().get_update() != 0) {
            if (!Setting.Share_Setting().get_subscription_flag() && count > Setting.Share_Setting().get_update_count()) {
                showNormalDialog();
            }
        }
        count++;
        Setting.Share_Setting().set_app_run_count(count);

     //   Intent serviceIntent = new Intent("com.android.vending.billing.InAppBillingService.BIND");

        //if (getPackageManager().queryIntentServices(serviceIntent, 0)!=null)
          //  initbilling();
        otherinit();

    }

    private void otherinit() {
        Handler h = new Handler();
        h.postDelayed(()->{
            initThirtyAds();
            if (checkSDCard()) {
                Global.InitStore(Global.Acy_Main);
                Server.Init(Global.Acy_Main.getApplicationContext());
            }

            viewModel = new ViewModelProvider(this).get(BillingViewModel.class);
            //  viewModel.setUpdateCallback(this);
            this.getLifecycle().addObserver(viewModel);


            BillingActivity.viewModel = viewModel;

            if (viewModel != null) {
                viewModel.mProductEvent.observe(this, new Observer<List<BillingPurchaseDetails>>() {
                    @Override
                    public void onChanged(List<BillingPurchaseDetails> list) {
                        //保证thread number数据正确
                        if (Setting.Share_Setting().get_free_complete()) {
                            Setting.Share_Setting().set_free_complete(false);
                        }

                        Setting.Share_Setting().set_subscription_flag(false);
                    }
                });
                viewModel.mHaveSubscribeEvent.observe(this, new Observer<String>() {
                    @Override
                    public void onChanged(String list) {
                        //Setting.Share_Setting().set_thread_number(backup_thread_number);
                        Setting.Share_Setting().set_subscription_flag(true);
                        if (layer_home != null) {
                            layer_home.updateAdsStatus();
                        }
                    }
                });
            }


        }, 500);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 123) {

            if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 检查该权限是否已经获取
                int i = ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE);
                // 权限是否已经 授权 GRANTED---授权  DINIED---拒绝
                if (i != PackageManager.PERMISSION_GRANTED) {
                    // 提示用户应该去应用设置界面手动开启权限
                    if (Global.Acy_Main == null) {
                        return;
                    }
                    CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.tip_open_storage), new CLCallback.CB_TF() {
                        @Override
                        public void on_callback_success() {
                            goAppSetting();
                        }
                        @Override
                        public void on_callback_fail(int code, String msg) {
                            finish();
                        }
                    }).show();
                } else {
                    Global.reInitStore(this);
                    Server.Init(this.getApplicationContext());
                }
            }
        } else if (resultCode == 10004) {
//            if (Setting.Share_Setting().get_subscription_flag()) {
//                UnifiedNativeAdView adView = Global.get_native_view();
//                if (adView !=null) {
//                    adView.setVisibility(View.GONE);
//                }
//            }
        }
    }

    public void goAppSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        startActivityForResult(intent, 123);
    }

    public boolean checkSDCard() {
        if (Global.IsAndroid10()) {
            return true;
        }

        if (!checkPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            if (checkPermissionRationale(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.tip_open_storage), new CLCallback.CB_TF() {
                    @Override
                    public void on_callback_success() {
                        goAppSetting();
                    }
                    @Override
                    public void on_callback_fail(int code, String msg) {
                        finish();
                    }
                }).show();
            } else {
                this.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                    @Override
                    public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                        if (grant_results[0] == PackageManager.PERMISSION_GRANTED) {
                            //  show_dialog_export();
                            Global.reInitStore(Global.Acy_Main);
                            Server.Init(Global.Acy_Main.getApplicationContext());

                        } else {
                            CLDialog.Get_Alert_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.tip_open_storage)).show();
                        }
                    }
                });
            }
            return false;
        } else {
            return true;
        }
    }

    @Override
    public void onBillingClientSetupFinished() {

    }

    @Override
    public void onConsumeFinished(BillingResult billingResult, String purchaseToken) {

    }

    @Override
    public void onPurchasesUpdated(List<com.android.billingclient.api.Purchase> purchases) {
        if (purchases.size() > 0) {
            Setting.Share_Setting().set_subscription_flag(true);
//            UnifiedNativeAdView adView = Global.get_native_view();
//            if (adView !=null) {
//                adView.setVisibility(View.GONE);
//            }
        } else {
            Setting.Share_Setting().set_subscription_flag(false);
        }
        layer_home.on_activity_resume();
    }

    @Override
    public void onBillingError(@NonNull String error) {

    }

    private void initThirtyAds() {
//        initfacebook(this);
//        AppLovinSdk.initializeSdk(this);
//        connectToTapjoy();
      //  Tapjoy.setDebugEnabled(true);
      //  MediationTestSuite.launch(this, "ca-app-pub-3064461767247622~8952288761");
        //AppLovinSdk.initializeSdk(this);
        //AudienceNetworkInitializeHelper.initialize(this);
      //  initInMobi();
      //  connectToTapjoy();
    }


    /**
     * Handles a failed connect to Tapjoy
     */
    public void onConnectFail() {
    }

    public interface EventPermissions{
        void on_request_permissions_result(String[] permissions,int[] grant_results);
    }

    public void request_permissions(String[] reqs, EventPermissions cber){
        if(cber==null)return;
        this.crt_req_permission=cber;
        ActivityCompat.requestPermissions(this,reqs,1001);
    }



    private void showNormalDialog(){
        Intent _intent=new Intent(this,UpdateActivity.class);
        _intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        this.startActivity(_intent);
    }


    private void initPlayBilling() {
        if (billingManager != null) {
            billingManager.destroy();
            billingManager = null;
        }
        billingManager = new BillingManager(this.getApplication(), this);
    }

    private void init(){
        layer_home=new Home(this);

        layer_home.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP, Gravity.FILL,0, statebar_height, 0, 0));
        fl_main.addView(layer_home);

        fl_video=new FrameLayout(this);
        fl_video.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL));
        CLBus.Share_Instance().register(Global.Group_web_video,listener_clbus,
                Global.Action_show_video,Global.Action_hide_video);
    }

    private long last_try_exit_time=0;
    private boolean go_exit(){
        if(layer_home != null && layer_home.can_go_back())return false;
        long _crt_time=System.currentTimeMillis();
        if(_crt_time-last_try_exit_time<2000){
            return true;
        }else {
            CLToast.Show(this,this.getResources().getString(R.string.tip_exit),false);
            last_try_exit_time=_crt_time;
            return false;
        }
    }

    @Override
    public void onBackPressed() {
        if(!go_exit())return;
        super.onBackPressed();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if(!Setting.Share_Setting().get_horizontal_full_screen()) {
            return;
        }
        if(newConfig.orientation==Configuration.ORIENTATION_LANDSCAPE){
            layer_home.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP, Gravity.FILL,0, 0, 0, 0));
            layer_home.on_activity_orientation(true);
        }else if(newConfig.orientation==Configuration.ORIENTATION_PORTRAIT){
            layer_home.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP, Gravity.FILL,0, statebar_height, 0, 0));
            layer_home.on_activity_orientation(false);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
//        Global.Youmeng.YM_on_pause(this);
//        Global.Youmeng.YM_end_page("mainly");
       // Global.Flurry_Activity_End(this);
        if (layer_home != null)
            layer_home.on_activity_pause();
        in_activity=false;
    }
    @Override
    protected void onResume() {
        super.onResume();
//        Global.Youmeng.YM_on_resume(this);
//        Global.Youmeng.YM_start_page("mainly");
     //   Global.Flurry_Activity_Start(this);
        if (layer_home != null)
         layer_home.on_activity_resume();
        in_activity=true;

        initPlayBilling();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        this.unbindService(conn_service);
        CLToast.Hide();
        if (layer_home != null)
            layer_home.on_activity_destory();
        CLBus.Share_Instance().unregister(Global.Group_web_video,listener_clbus);
        //Global.Acy_Main=null;
        this.unregisterReceiver(recevier_open_url);
    }

    private CLBus.CBEventer listener_clbus=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action==Global.Action_finsh_app){
                finish();
            } else if (action == Global.Action_go_new_version) {

            }
        }
    };


    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        CLBus.Share_Instance().register(Global.Group_app_status, listener_clbus,
                Global.Action_finsh_app);
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        CLBus.Share_Instance().unregister(Global.Group_app_status,listener_clbus);
    }

    private EventPermissions crt_req_permission;
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 1001) {
            //  if(crt_req_permission!=null)crt_req_permission.on_request_permissions_result(permissions,grantResults);
            if (grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.tip_open_storage), new CLCallback.CB_TF() {
                    @Override
                    public void on_callback_success() {
                        goAppSetting();
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {
                        finish();
                    }
                }).show();
            } else {
                Global.reInitStore(this);
                if (Global.Acy_Main == null) {
                    Global.Acy_Main = this;
                }
                Server.Init(Global.Acy_Main.getApplicationContext());
            }
        }

    }

}
