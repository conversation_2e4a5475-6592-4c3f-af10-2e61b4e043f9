package amazon.browser.lionpro.primary;

import android.os.Bundle;
import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.screen.RStorage;
import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/6/21.
 */
public class AcyStorage extends CLActivity {


    private CLFlipper flipper;
    private RStorage layer_storage;

    private CLCallback.CB_Activity cber_acy=new CLCallback.CB_Activity() {
        @Override
        public void on_close() {
            finish();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int _hh=CL.Set_Translucent_StatusBar(this.getWindow());

        flipper=new CLFlipper(this, new CLCallback.CB() {
            @Override
            public void on_callback() {
                finish();
            }
        });
        flipper.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        flipper.setPadding(0,_hh,0,0);
        this.setContentView(flipper);

        layer_storage=new RStorage(this,flipper,cber_acy);
        flipper.addView(layer_storage);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }
}
