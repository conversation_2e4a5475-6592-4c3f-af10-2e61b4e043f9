package amazon.browser.lionpro.primary;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.StateListDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.toys.CommonBackButton;

import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLInputer;
import lion.CLToast;
import lion.widget.IosCheckBox;

/**
 * Created by leron on 2016/8/31.
 */
public class AcyWifiShare extends CLActivity {


    private CLActivity cc;
    private ImageView iv_state;
    private TextView tv_state;
    private FrameLayout fl_wifi;
    private TextView tv_ip_tip,tv_ip;
    private LinearLayout ll_fwm;
    private TextView tv_fwm;
    private IosCheckBox cb_fwm;
    private Button btn_service;
    private StateListDrawable dwe_start,dwe_end;
    private CLDialog dialog_force;
    private CommonBackButton backbutton;
    //0--无状态 1--wifi可用无ip 2--wifi可用有ip 3--ap可用
    private int crt_ip=0;
    private String access_pwd=null;
    private boolean server_state=false;

    private Handler handler=new Handler(Looper.getMainLooper()){
        @Override
        public void handleMessage(Message msg) {
            if(msg.what==1000){
                check_wifi_state();
                sendEmptyMessageDelayed(1000,3000);
            }
        }
    };

    private View.OnClickListener listener_fwm=new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if(!server_state && cb_fwm.get_check()){
                CLInputer.Get_Digital_Line(cc, 6, new CLCallback.CB_TFO<String>() {
                    @Override
                    public boolean on_callback_success(String obj, String msg) {
                        access_pwd=obj;
                        if(access_pwd==null || access_pwd.trim().isEmpty()){
                            access_pwd=null;
                            tv_fwm.setText(get_string(R.string.tip_web_not_has_access_pwd));
                            cb_fwm.set_check(false);
                        }else {
                            tv_fwm.setText(get_string(R.string.tip_web_has_access_pwd) + obj);
                        }
                        return false;
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {
                        if(access_pwd==null)cb_fwm.set_check(false);
                    }
                }).set_btn_text(cc.getResources().getString(R.string.yes),
                        cc.getResources().getString(R.string.cancel)).show(get_string(R.string.tip_web_enter_pwd),access_pwd);
            }
        }
    };

    private IosCheckBox.CBCheckEvent listener_check_box=new IosCheckBox.CBCheckEvent() {
        @Override
        public void on_change(IosCheckBox box, boolean check) {
            if(check){
                listener_fwm.onClick(tv_fwm);
            }else{
                access_pwd=null;
                tv_fwm.setText(get_string(R.string.tip_web_not_has_access_pwd));
            }
        }
    };

    private View.OnClickListener listener_service=new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if(Server.Share_FileManager().get_datas()==null){
                CLToast.Show(cc,get_string(R.string.tip_web_file_init),true);
                return;
            }

            if(!server_state){
                dialog_force.show();
                SerWifiShare.Start_Web_Server(access_pwd, new CLCallback.CB_TF() {
                    @Override
                    public void on_callback_success() {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                server_state=true;
                                if(dialog_force.isShowing())dialog_force.dismiss();
                                tv_ip_tip.setVisibility(View.VISIBLE);
                                tv_ip.setVisibility(View.VISIBLE);
                                cb_fwm.setVisibility(View.GONE);
                                btn_service.setBackground(dwe_end);
                                btn_service.setText(get_string(R.string.tip_web_service_stop));
                                backbutton.setVisibility(View.INVISIBLE);
                            }
                        });
                    }
                    @Override
                    public void on_callback_fail(int code, String msg) {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                server_state=false;
                                if(dialog_force.isShowing())dialog_force.dismiss();
                                CLToast.Show(cc,get_string(R.string.tip_web_service_fail),false);
                                backbutton.setVisibility(View.VISIBLE);
                            }
                        });
                    }
                });
               // Global.Flurry_Send_Event("wifi share");
            }else{
                SerWifiShare.Destory_Web_Server();
                server_state=false;
                btn_service.setBackground(dwe_start);
                btn_service.setText(get_string(R.string.tip_web_service_start));
                tv_ip_tip.setVisibility(View.GONE);
                tv_ip.setVisibility(View.GONE);
                cb_fwm.setVisibility(View.VISIBLE);
                backbutton.setVisibility(View.VISIBLE);
            }
        }
    };


    private ServiceConnection connection_clhs=new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName, IBinder iBinder) {

        }
        @Override
        public void onServiceDisconnected(ComponentName componentName) {

        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.cc=this;

        this.bindService(new Intent(this,SerWifiShare.class),connection_clhs, Context.BIND_AUTO_CREATE);

        int _hh=CL.Set_Translucent_StatusBar(this.getWindow());

        LinearLayout _ll_main=new LinearLayout(cc);
        _ll_main.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll_main.setPadding(0,_hh,0,0);
        _ll_main.setOrientation(LinearLayout.VERTICAL);
        this.setContentView(_ll_main);


        FrameLayout _fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        _ll_main.addView(_fl_header);
        backbutton = new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                onBackPressed();
            }
        });
        _fl_header.addView(backbutton,CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        _fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.wifi_share).toString(),0xffd0d0d0,18,null));
       // _ll_main.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));

        LinearLayout _ll_conten=new LinearLayout(cc);
        _ll_conten.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC));
        _ll_conten.setOrientation(LinearLayout.VERTICAL);
        _ll_conten.setGravity(Gravity.CENTER_HORIZONTAL);
        _ll_main.addView(_ll_conten);

        iv_state=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(60),0,0),null,null);
        iv_state.setBackgroundResource(R.mipmap.wifi_gray);
        _ll_conten.addView(iv_state);

        _ll_conten.addView(CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(32),0,0),get_string(R.string.tip_web_network_state),Color.WHITE,23,null));
        tv_state=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(8),0,0),"",Color.WHITE,16,null);
        _ll_conten.addView(tv_state);

        fl_wifi =new FrameLayout(cc);
        fl_wifi.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f,0,CL.DIP2PX_INT(26),0,0));
        _ll_main.addView(fl_wifi);
        tv_ip_tip=CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC, CL.WC,Gravity.CENTER_HORIZONTAL|Gravity.TOP),get_string(R.string.tip_web_pc_input),0xffeeeeee,14,null);
        tv_ip_tip.setVisibility(View.GONE);
        fl_wifi.addView(tv_ip_tip);
        tv_ip=CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC, CL.WC,Gravity.CENTER_HORIZONTAL|Gravity.TOP,0,CL.DIP2PX_INT(26),0,0),"",Color.WHITE,18,null);
        tv_ip.setVisibility(View.GONE);
        fl_wifi.addView(tv_ip);

        ll_fwm=new LinearLayout(cc);
        ll_fwm.setLayoutParams(CL.Get_FLLP(CL.MP,CL.WC,Gravity.BOTTOM,0,0,0,CL.DIP2PX_INT(78)));
        ll_fwm.setOrientation(LinearLayout.HORIZONTAL);
        ll_fwm.setGravity(Gravity.CENTER_VERTICAL);
        ll_fwm.setBackground(new ColorDrawable(){
            @Override
            public void draw(Canvas canvas) {
                Paint _p=new Paint(Paint.ANTI_ALIAS_FLAG);
                _p.setColor(0xff444444);
                canvas.drawLine(0,0,canvas.getWidth(),0,_p);
                canvas.drawLine(0,canvas.getHeight()-1,canvas.getWidth(),canvas.getHeight()-1,_p);
            }
        });
        ll_fwm.setPadding(CL.DIP2PX_INT(15),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15));
        tv_fwm=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC, CL.WC,1.0f,0,0,0,0),get_string(R.string.tip_web_not_has_access_pwd),Color.WHITE,16,listener_fwm);
        ll_fwm.addView(tv_fwm);
        cb_fwm=new IosCheckBox(cc,null);
        cb_fwm.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),CL.DIP2PX_INT(15),0,0,0));
        cb_fwm.set_check_listener(listener_check_box);
        ll_fwm.addView(cb_fwm);
        fl_wifi.addView(ll_fwm);

        float _rr=CL.DIP2PX(20);
        RoundRectShape _shape=new RoundRectShape(new float[]{_rr,_rr,_rr,_rr,_rr,_rr,_rr,_rr}, null, null);
        ShapeDrawable _drawable1=new ShapeDrawable(_shape);
        _drawable1.getPaint().setColor(0xff4cd864);
        _drawable1.getPaint().setStyle(Paint.Style.FILL);
        ShapeDrawable _drawable2=new ShapeDrawable(_shape);
        _drawable2.getPaint().setColor(0xff22612d);
        _drawable2.getPaint().setStyle(Paint.Style.FILL);
        dwe_start=CL.Get_StateList_Drawable(_drawable1, _drawable2);
        ShapeDrawable _drawable3=new ShapeDrawable(_shape);
        _drawable3.getPaint().setColor(0xfff31010);
        _drawable3.getPaint().setStyle(Paint.Style.FILL);
        ShapeDrawable _drawable4=new ShapeDrawable(_shape);
        _drawable4.getPaint().setColor(0xff7b0808);
        _drawable4.getPaint().setStyle(Paint.Style.FILL);
        dwe_end=CL.Get_StateList_Drawable(_drawable3, _drawable4);

        btn_service=CLController.Get_Button(cc, CL.Get_FLLP(CL.WC, CL.DIP2PX_INT(40), Gravity.BOTTOM|Gravity.CENTER_HORIZONTAL, 0, 0, 0, CL.DIP2PX_INT(18)),
                get_string(R.string.tip_web_service_start), Color.WHITE, 16, dwe_start, listener_service);
        btn_service.setPadding(CL.DIP2PX_INT(36),btn_service.getPaddingTop(),CL.DIP2PX_INT(36),btn_service.getPaddingBottom());
        fl_wifi.addView(btn_service);

        dialog_force=CLDialog.Get_Force_Wait(cc);
    }

    @Override
    protected void onResume() {
        super.onResume();
        handler.sendEmptyMessage(1000);
    }

    @Override
    protected void onPause() {
        super.onPause();
        handler.removeMessages(1000);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }

    @Override
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
       // Global.Show_Interstitial(cc,null);
        Global.showInterstitial_donow(cc,null);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        this.unbindService(connection_clhs);
    }
    private String get_string(int id){
        return cc.getResources().getString(id);
    }

    private void check_wifi_state(){
        int _state=0;
        String _ssid=null,_ip=null;
        try{
            WifiManager _wifi = (WifiManager) cc.getApplicationContext().getSystemService(cc.WIFI_SERVICE);
            if (!_wifi.isWifiEnabled()) {
                int i = ((Integer) _wifi.getClass().getMethod("getWifiApState", new Class[0]).invoke(_wifi, new Object[0])).intValue();
                if (i == 13) {
                    _state=3;
                    _ssid="AP";
                    _ip="************";
                }
            }else {
                WifiInfo _info = _wifi.getConnectionInfo();
                crt_ip = _info.getIpAddress();
                if(crt_ip==0){
                    _state=1;
                    _ssid="waiting...";
                }
                else {
                    _state=2;
                    _ssid=_info.getSSID();
                    _ip=(crt_ip & 0xFF) + "." + ((crt_ip >> 8) & 0xFF) + "." + ((crt_ip >> 16) & 0xFF) + "." + ((crt_ip >> 24) & 0xFF);
                }
            }
        }catch (Exception ex){}
        if(_state==0){
            iv_state.setBackgroundResource(R.mipmap.wifi_gray);
            tv_state.setText(cc.getResources().getString(R.string.tip_open_wifi_ap));
            if(fl_wifi.getVisibility()!=View.GONE) fl_wifi.setVisibility(View.GONE);
        }else if(_state==1){
            iv_state.setBackgroundResource(R.mipmap.wifi_gray);
            tv_state.setText(_ssid);
            if(fl_wifi.getVisibility()!=View.GONE) fl_wifi.setVisibility(View.GONE);
        }else if(_state==2 || _state==3){
            iv_state.setBackgroundResource(R.mipmap.wifi_blue);
            tv_state.setText(_ssid);
            tv_ip.setText("http://"+_ip+":8200");
            if(fl_wifi.getVisibility()!=View.VISIBLE) fl_wifi.setVisibility(View.VISIBLE);
        }
    }
}
