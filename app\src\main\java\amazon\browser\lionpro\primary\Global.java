package amazon.browser.lionpro.primary;


import static androidx.core.app.ActivityCompat.invalidateOptionsMenu;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
//import androidx.annotation.RecentlyNonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import amazon.browser.ad.GoogleAdmob;
import amazon.browser.ad.GoogleMobileAdsConsentManager;
import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.util.SkuDetails;

import amazon.browser.lionpro.util.ViewUtils;
import amazon.browser.lionpro.views.KProgressHUD.KProgressHUD;
//import com.flurry.android.FlurryAgent;
//import com.flurry.android.FlurryAgentListener;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.toys.Tools;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.VideoController;
import com.google.android.gms.ads.VideoOptions;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.nativead.NativeAdOptions;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.google.android.gms.ads.rewarded.RewardedAd;
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback;
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAd;
import com.google.android.gms.ads.rewardedinterstitial.RewardedInterstitialAdLoadCallback;
import com.google.android.ump.FormError;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicBoolean;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLFileSystem;
import lion.CLPictureDownloader;
import lion.ndk_tools;

/**
 * Created by leron on 2016/4/12.
 */
public class Global {

    static public int AD_FULL_SCREEN                = 100;
    static public int AD_LOADED                     = 101;
    static public int AD_EARNED                     = 102;
    static public int AD_CLOSE_EARLY                = 103;
    static public int AD_LOADED_FAILED              = 104;
    static public int AD_LOADED_EARNED_SUCCESS      = 105;
    static public int AD_FULL_SCREEN_DISMISSED      = 106;

    static public int AD_REWARD_INITERTITIAL_FULL_SCREEN                     = 210;
    static public int AD_REWARD_INITERTITIAL_FULL_SCREEN_DISMISSED                      = 211;

    public static final String Group_favorite="group_favorite";
    public static final int Action_favorite_add=10001;
    public static final int Action_favorite_del=10002;

    public static final String Group_open_url="group_open_url";
    public static final int Action_open_url_from_fav_history=20001;
    public static final int Action_open_url_in_current_window=20002;

    public static final String Group_web_video="group_web_video";
    public static final int Action_show_video=30001;
    public static final int Action_hide_video=30002;

    public static final String Group_update_info="group_update_name";
    public static final int Action_rename=40001;

    public static final String Group_Change_UA="group_change_UA";
    public static final int Action_change_UA=50001;

    public static final String Group_main_menu="group_main_menu";
    public static final int Action_main_menu=60001;

    public static final String Group_win_event="group_win_event";
    public static final int Action_win_del=50003;
    public static final int Action_win_add=50004;
    public static final int Action_win_change=50005;
    public static final int Action_win_update_num=50006;
	
	public static final String Group_main_ui = "group_main_ui";
    public static final int Action_night_mode = 70001;
    public static final int Action_fullscreen = 70002;
    public static final int Action_main_favroite_refresh = 70003;
    public static final int Action_close_ad = 70004;

    public static final String Group_app_status = "group_app_status";
    public static final int Action_finsh_app = 80001;
    public static final int Action_go_new_version = 80002;

    public static final boolean AD_Test = false;
  //  private static UnifiedNativeAd nativeAd;
    public static AdView adView;
    public static M3u8MergeServer.WorkerBinder binder;
//samsung
//    public static String _admob_app_id = "ca-app-pub-3064461767247622~3013956327";
//    public static String _admob_banner= "ca-app-pub-3064461767247622/4252862289";
//    public static String _admob_interstitial="ca-app-pub-3064461767247622/8270276140";
//    public static String FLURRY_APP_KEY = "9THZ5JSM8H74W53543DD";
//google play
  //  public static String _admob_app_id = "ca-app-pub-3940256099942544~3347511713";
  //  public static String _admob_banner= "ca-app-pub-3940256099942544/6300978111";
    public static String _admob_openscreen = "ca-app-pub-8603317425868964/5608152610";
    public static String _admob_app_id = "ca-app-pub-3064461767247622~8952288761";//"ca-app-pub-3064461767247622~9204542388";
  //  public static String _admob_app_id = "ca-app-pub-3064461767247622~9204542388";
    public static String _admob_banner= "ca-app-pub-8603317425868964/9448936767";//"ca-app-pub-3064461767247622/5911152877";//"ca-app-pub-3064461767247622/4417189525";////"ca-app-pub-3940256099942544/6300978111";//;//"ca-app-pub-3064461767247622/5911152877";//"ca-app-pub-3064461767247622/1001367592";//"ca-app-pub-7019514947116233/8055804106";//
    public static String _admob_interstitial="ca-app-pub-8603317425868964/5352878698";//"ca-app-pub-3064461767247622/4601940637";//";////"ca-app-pub-3940256099942544/1033173712";////"ca-app-pub-3940256099942544/3325725716";//"ca-app-pub-3064461767247622/4601940637";//"ca-app-pub-3064461767247622/3954833994";//"ca-app-pub-7019514947116233/3067201303";//
    public static String _admob_reward= "ca-app-pub-8603317425868964/2632553804";
    public static String _admob_interstitial_reward= "ca-app-pub-8603317425868964/4196610082";
    public static String _admob_native = "ca-app-pub-8603317425868964/6887186752";
    public static String FLURRY_APP_KEY = "XNP3FDVKC35HBGMN8RRM";

    //public static String FLURRY_APP_KEY = "G6NK6MKFHMJ3Y56THFMM";//gp

    //public static String _admob_app_id = "ca-app-pub-3064461767247622~7047901191";
    //public static String _admob_banner="ca-app-pub-3064461767247622/1001367592";//"ca-app-pub-7019514947116233/8055804106";//
    //public static String _admob_interstitial="ca-app-pub-3064461767247622/3954833994";//"ca-app-pub-7019514947116233/3067201303";//
    ////public static String FLURRY_APP_KEY = "SFHXP6ZT7SGJWCMW9QMM";//lion signal
    //public static String FLURRY_APP_KEY = "G6NK6MKFHMJ3Y56THFMM";//multi

    public static String Main_Page_Url=null;
    public static final String Google_Searcher="https://www.google.com/search?q=%s";
    public static final String Baidu_Searcher="http://www.baidu.com/#ie=UTF-8&wd=%s";
    public static String homepage="https://www.google.com";
    //public static final String homepage="https://www.baidu.com";
    public static String Crt_UA=null;
    //public static final String UA_Chrome="Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.101 Safari/537.36";
    public static final String UA_Chrome="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/37.0.2049.0 Safari/537.36";
    //public static final String UA_Android = "Mozilla/5.0 (Linux; Android 8.0.1; HW Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36";
   // public static final String UA_Android = "Mozilla/5.0 (Linux; Android 8.0.1; HW Build/MMB29M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36";
   // public static String UaAndroid_Facebook = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2490.76 Mobile Safari/537.36";
    public static String UA_Android = "Mozilla/5.0 (Linux; Android 8.0; Galaxy Nexus Build/IMM76B) AppleWebKit/535.19 (KHTML, like Gecko) Chrome/75.0.3770.100 Mobile Safari/535.19";

    public static final String UA_Iphone="Mozilla/5.0 (iPhone; CPU iPhone OS 9_0 like Mac OS X)AppleWebKit/420+ (KHTML, like Gecko) Version/3.0 Mobile/1C28 Safari/419.3";
    public static final String JS_Find = "okiamfindevent";
    //public static final String base64EncodedPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo++KuHWcwaPejzqT2dqCQ6LMRtZAlhTiTPvJqLNW7PMuImMQPIfU3/BW8Dm2FxGxXCbMAQJ6J5W7cgKnBlPJ/dac73pxyxlHlYGYMSgEjIjBH3rcsBGE3tllnMqjf8IP/Ukw6soHgAbunc2CFyvaJAOWGe37tZZ+03c3zzQxHqrZkCuGHN9wWtMz8XMt9ZQ6HgQXHcFTSVgiBK46ze2sLwK63M4Iw8BNubOqw1zyP3GsgbZf5Nz2CKmfc4oBy6A/u7z5CIec8BO3mgeKYBRL70Mb3I2P5vNvJkm3yMY8B7S/m3694NvbckRBrNmay6eYVTwte1n0yf9Heq305WmMaQIDAQAB";
    public static final String base64EncodedPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzN45EZohTPAyqF6Q21itzhOtn4tTi3A02qVY1jHSNxzSm50L23WHjOoriPQjLbby6qhsrFy5Kt5Ju2c2n1aEK62eooTeQvxBrFpPoWLBleId0tzPzgY5662AUUMacYjVxBmmpMfFFvvZ9YucMLizeHo9BkyerdHlmaVR4vymh8/26KEvw0EIj0sHTTxKH2dFhCl0elQHG5akZoN3qNjmvC/WeWR9OjXhe9GzS+k2bgEoMl1yC+g1HMIWL/o8lesGioxK2bWJz24B2nt/Pz8RjmhftznoZUcWDoV70IOtxsSege6t2s9JmqEb5c3MNU0VhJS2YY9pEWOcnp868NsErQIDAQAB";
    public static final String[] SKUS = new String[]{"sub1", "sub2", "sub3", "sub4", "sub5"};//一周，一月，三个，半年，一年
    //public static final String[] SKUS = new String[]{"test_sub2", "test_sub3", "sub3", "sub4", "sub5"};//一周，一月，三个，半年，一年
    public static final String SKU_PREMIUM = "premium";
    public static final  HashMap<String, SkuDetails> iListSubData = new HashMap<String, SkuDetails>(10);
    public static final String Install_package_name = "amazon.browser.video.downloader.fileprovider";
    //public static final String Install_package_name = "com.browser.lionpro.fileprovider";
    public static AcyMain Acy_Main=null;
    public static boolean Switch_AD=true;
    private static AtomicBoolean isMobileAdsInitializeCalled = new AtomicBoolean(false);
    public static File DirRoot;
    public static File DirMedia;
    public static File DirPicture;
    public static File DirOther;
    public static File DirWebStore;
    public static File DirLive;

    public static CLPictureDownloader Downloader;
    public static File Dir_Root_1,Dir_thum,Dir_Download;
    public static ndk_tools _ndk;
    public static String js_inject;
    public static String js_inject_night;
    public static String json_bookmarks = "common/bookmarks.json";
    public static String path_website = "picture/website_xx.png";
    public static int country = 0;//0 gp 1 cn
    public static Context cc;
    public static String downloadPathName = "liondata";
    public static boolean bEarned = false;
    public static final String[] REQUIRED_PERMISSIONS = new String[] {

            Manifest.permission.INTERNET,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.ACCESS_WIFI_STATE,
            Manifest.permission.WAKE_LOCK,
            Manifest.permission.ACCESS_FINE_LOCATION,
          //  Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.READ_LOGS,
            Manifest.permission.SYSTEM_ALERT_WINDOW
    };

    public static Random rand = new Random();

    public static void Init(Context context){
        cc = context;
        Downloader=new CLPictureDownloader();
        if (country == 1)
            homepage = "https://www.baidu.com";
        else
            homepage = "https://www.google.com";
       // Setup_Store(context);
        Setting.Init(context);

        InitStore(context);
        //InitAds(context);

        Process_Main_Page_Url(Setting.Share_Setting().get_main_page());

    }


    public static void initMobileAdsSdk(Activity cc) {
        GoogleMobileAdsConsentManager googleMobileAdsConsentManager = GoogleMobileAdsConsentManager.Companion.getInstance(cc);
        googleMobileAdsConsentManager.gatherConsent(cc,
                new GoogleMobileAdsConsentManager.OnConsentGatheringCompleteListener() {
                    @Override
                    public void consentGatheringComplete(@Nullable FormError error) {
                        if (googleMobileAdsConsentManager.getCanRequestAds()) {
                            initializeMobileAdsSdk(cc);
                        }

                        if (googleMobileAdsConsentManager.isPrivacyOptionsRequired()) {
                            // Regenerate the options menu to include a privacy setting.
                         //   invalidateOptionsMenu(cc);
                        }
                    }
                });
    }


    public static void initializeMobileAdsSdk(Context cc) {
        if (isMobileAdsInitializeCalled.getAndSet(true)) {
            return;
        }

        // Initialize the Google Mobile Ads SDK.
        MobileAds.initialize(cc, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) {
                LoadInterstitial(cc);
                LoadReward(cc);
                LoadRewardInterstitial(cc);
                LoadOpenAd(cc);
            }
        });
        MobileAds.setAppVolume(0.0f);
    }


    public static void InitAds(Context context) {
        MobileAds.initialize(context, new OnInitializationCompleteListener() {
            @Override
            public void onInitializationComplete(InitializationStatus initializationStatus) {
                LoadInterstitial(context);
                LoadReward(context);
                LoadRewardInterstitial(context);
            }
        });
        MobileAds.setAppVolume(0.0f);

    }

    public static void Initjs(Context context) {
        try {
//            InputStream is = context.getAssets().open("common/sv.js");
//            js_inject = CLTools.readTextFromSDcard(is);
//            is.close();


/*              InputStream is = context.getAssets().open("common/inject_nightmode.js");
            js_inject_night = CLTools.readTextFromSDcard(is);
            is.close();
*/
           // _ndk = new ndk_tools();
           // String tmp;
//            String values = "var vcss = document.createElement('style');\n" +
//                    " vcss.textContent=\"@-webkit-keyframes nodeInserted{from{outline-color:#fff}to{outline-color:#000}}video{-webkit-animation-duration:0.01s;-webkit-animation-name:nodeInserted}\\\n" +
//                    " @-webkit-keyframes nodeInserted_flash{from{outline-color:#fff}to{outline-color:#000}}embed{-webkit-animation-duration:0.01s;-webkit-animation-name:nodeInserted_flash}object{-webkit-animation-duration:0.01s;-webkit-animation-name:nodeInserted_flash}\";\n" +
//                    " vcss.type = \"text/css\";\n" +
//                    " vcss.id = '__ks_sv_video';\n" +
//                    "var cur_url = '';\n" +
//                    "var bstart = false;\n" +
//                    "var bRun = false;\n" +
//                    "var _count = 0;\n" +
//                    "var injectcss = function(){\n" +
//                    "console.trace('=============================================================');\n" +
//                    "    if(document.readyState=='interactive'||document.readyState=='complete'){\n" +
//                    "        if(document.getElementById('__ks_sv_video')){\n" +
//                    "\n" +
//                    "             if (window.__ks_findvideo != null) {\n" +
//                    "                  clearInterval(window.__ks_findvideo );\n" +
//                    "             }\n" +
//                    "\t\t\t window.__ks_findvideo = setInterval(_hook_findvideo,2000);\n" +
//                    "            return;\n" +
//                    "        } else {\n" +
//                    "\n" +
//                    "        }\n" +
//                    "\n" +
//                    "        try{\n" +
//                    "             if (window.__ks_findvideo != null) {\n" +
//                    "                  clearInterval(window.__ks_findvideo );\n" +
//                    "             }\n" +
//                    "\t\t\t window.__ks_findvideo = setInterval(_hook_findvideo,2000);\n" +
//                    "             bstart = true;\n" +
//                    "            if (bstart) {\n" +
//                    "                document.body.appendChild(vcss);\n" +
//                    "                console.trace('appendchild document');\n" +
//                    "            } else {\n" +
//                    "                window._ks_timeout = setTimeout(injectcss,1000);\n" +
//                    "            }\n" +
//                    "\n" +
//                    "        }catch(e){\n" +
//                    "            console.trace('eeeeeeeeeeeeeeeeee');\n" +
//                    "            setTimeout(injectcss,1000);\n" +
//                    "        }\n" +
//                    "    }else{\n" +
//                    "        console.trace('interactive not complete');\n" +
//                    "        setTimeout(injectcss,1000);\n" +
//                    "    }\n" +
//                    "};\n" +
//                    "\n" +
//                    "\n" +
//                    "var _hook_findvideo = function() {\n" +
//                    "    try{\n" +
//                    "\n" +
//                    "       console.trace('_hook_findvideo');\n" +
//                    "        var videos=document.getElementsByTagName('video');\n" +
//                    "        if (videos != null && videos.length > 0) {\n" +
//                    "\n" +
//                    "            if(document.URL!=null&&document.URL.indexOf('www.tumblr.com')>-1) {\n" +
//                    "                             for (var i=0; i<videos.length; i++) {\n" +
//                    "                                 var class_name = videos[i].parentNode.className;\n" +
//                    "                                 if (class_name.indexOf('crt-video crt-skin-default embed-57c588ab9982b749246049-dimensions vjs-controls-enabled vjs-workinghover vjs-has-started vjs-user-active vjs-playing')>-1) {\n" +
//                    "                                     var source_object = videos[i].getElementsByTagName('source');\n" +
//                    "                                     if (source_object != null && source_object.length > 0) {\n" +
//                    "                                         var v_src = source_object[0].src;\n" +
//                    "                                         if (v_src != null && (window.lastURL == null || window.lastURL != v_src)) {\n" +
//                    "                                             window.lastURL = v_src;\n" +
//                    "                                             window.jsvideotag.finded(document.URL, src_object, \"mp4\");\n" +
//                    "                                             break\n" +
//                    "                                         }\n" +
//                    "                                     }\n" +
//                    "                                 }\n" +
//                    "                             }\n" +
//                    "                         } else if(document.URL!=null&&document.URL.indexOf('www.instagram.com')>-1) {\n" +
//                    "                             for (var i=0; i<videos.length; i++) {\n" +
//                    "\n" +
//                    "                                 var v_loop = videos[i].getAttribute('loop');\n" +
//                    "                                 if (v_loop != null) {\n" +
//                    "                                     var src_object = videos[i].getAttribute('src');\n" +
//                    "                                     if (src_object != null && (window.lastURL == null || window.lastURL != src_object)) {\n" +
//                    "                                         window.lastURL = src_object;\n" +
//                    "                                         window.jsvideotag.finded(document.URL, src_object, \"mp4\");\n" +
//                    "                                         break;\n" +
//                    "                                     }\n" +
//                    "                                 }\n" +
//                    "                             }\n" +
//                    "                         } else {\n" +
//                    "\n" +
//                    "\n" +
//                    "\n" +
//                    "                             var src_object = videos[0].getAttribute('src');\n" +
//                    "                             console.trace(src_object);\n" +
//                    "                             if (src_object != null && (window.lastURL == null || window.lastURL != src_object)) {\n" +
//                    "                                 window.lastURL = src_object;\n" +
//                    "                                 window.jsvideotag.finded(document.URL, src_object, \"mp4\");\n" +
//                    "                             } else {\n" +
//                    "                                 var sources=document.getElementsByTagName('source');\n" +
//                    "\n" +
//                    "                                 if (sources != null && sources.length > 0) {\n" +
//                    "                                      var src_object = sources[0].src;\n" +
//                    "                                      if (src_object != null && (window.lastURL == null || window.lastURL != src_object)) {\n" +
//                    "                                              window.lastURL = src_object;\n" +
//                    "                                              window.jsvideotag.finded(document.URL, src_object, \"mp4\");\n" +
//                    "                                      }\n" +
//                    "\n" +
//                    "                                 }\n" +
//                    "                             }\n" +
//                    "                         }\n" +
//                    "            \n" +
//                    "        } else {\n" +
//                    "            var sources=document.getElementsByTagName('source');\n" +
//                    "\n" +
//                    "            if (sources != null && sources.length > 0) {\n" +
//                    "                var src_object = sources[0].src;\n" +
//                    "                var type = sources[0].type;\n" +
//                    "                if (src_object != null && (window.lastURL == null || window.lastURL != src_object)) {\n" +
//                    "                     window.lastURL = src_object;\n" +
//                    "                     window.jsvideotag.finded(document.URL, src_object, type);\n" +
//                    "                }\n" +
//                    "            }\n" +
//                    "        }\n" +
//                    "    } catch(e) {\n" +
//                    "\n" +
//                    "         //   window.jsvideotag.finded(null, null, null);\n" +
//                    "         if (window.__ks_findvideo != null) {\n" +
//                    "              clearInterval(window.__ks_findvideo);\n" +
//                    "              window.__ks_findvideo = null;\n" +
//                    "         }\n" +
//                    "          setTimeout(injectcss,1000);\n" +
//                    "    }\n" +
//                    "};\n" +
//                    "\n" +
//                    "injectcss()\n";
/*
            String kye = _ndk.gkey(0);
            DESPlus des = new DESPlus(kye);

          //  tmp = des.encrypt(values);
            js_inject = des.decrypt(_ndk.g("0"));
            */
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    
    public static void setup_sdcard_stroe(Context context){
        String[] _paths=null;
        if(!CLFileSystem.Whether_Availability()){
            CLDialog.Get_Alert_Dialog(context,context.getResources().getString(R.string.not_found_SD)).show();
            //Setup_Store(context);
            InitStore(context);
            return;
        }
        _paths=new String[2];
        Dir_Root_1 = new File(Environment.getExternalStorageDirectory().getAbsolutePath(),Global.downloadPathName);
        _paths[0]=Dir_Root_1.getAbsolutePath();
        _paths[1]= Environment.getExternalStorageDirectory().getAbsolutePath();
        if(_paths==null) {
            CLDialog.Get_Alert_Dialog(context,context.getResources().getString(R.string.not_found_SD)).show();
            return;
        }
        Dir_thum = new File(Dir_Root_1,"thum");
        if(!Dir_thum.exists())Dir_thum.mkdirs();
        Dir_Download=new File(Dir_Root_1,"download");
        if(!Dir_Download.exists())Dir_Download.mkdirs();
    }

    public static void Setup_Store(Context cc){
        File[] _dirs=CLFileSystem.Get_App_Store(cc);
        if(_dirs==null)return;
        Dir_Root_1=_dirs[0];
        if(Dir_Root_1!=null && Dir_Root_1.exists()){
            CL.CLOGI("root1:"+Dir_Root_1.getAbsolutePath());
            if(!Dir_Root_1.exists()) Dir_Root_1.mkdirs();
            Dir_thum=new File(Dir_Root_1,"/" + Global.downloadPathName + "/thum");
            if(!Dir_thum.exists())Dir_thum.mkdirs();
            Dir_Download=new File(Dir_Root_1,"/" + Global.downloadPathName + "/download");
            if(!Dir_Download.exists())Dir_Download.mkdirs();
        }
    }

    public static void reInitStore(Context context) {
        if (Dir_Root_1 != null) {
            Dir_Root_1 = null;
        }

        if (Dir_thum != null) {
            Dir_thum = null;
        }
        if (Dir_Download != null) {
            Dir_Download = null;
        }
        InitStore(context);
    }

    public static boolean InitStore(Context context) {
        if (IsAndroid10()) {
            File rootDir = context.getExternalFilesDir(null);

            if (rootDir == null) {
                return false;
            }

            if (Dir_Root_1 != null) {
                return true;
            }

            //   Dir_Root_1 = new File(Environment.getExternalStorageDirectory(), downloadPathName);
            Dir_Root_1 = rootDir;
            if (!Global.Dir_Root_1.exists()) {
                Global.Dir_Root_1.mkdir();
            }

            Dir_thum = new File(Dir_Root_1, "/thum");

            if (!Global.Dir_thum.exists()) {
                Global.Dir_thum.mkdir();
            }

            Dir_Download = new File(Dir_Root_1, "/download");

            if (!Global.Dir_Download.exists()) {
                Global.Dir_Download.mkdir();
            }
        } else {

            File rootDir = context.getExternalFilesDir(null);

            if (rootDir == null) {
                return false;
            }

            if (Dir_Root_1 != null) {
                return true;
            }

//        if (Setting.Share_Setting().get_download_path_mode()) {
//            Dir_Root_1 = new File(Setting.Share_Setting().get_export_dir());
//        } else {
//            Dir_Root_1 = new File(Environment.getExternalStorageDirectory(), downloadPathName);
//        }
            Dir_Root_1 = new File(Setting.Share_Setting().get_export_dir());
            // Dir_Root_1 = new File(Environment.getExternalStorageDirectory(), downloadPathName);
            if (!Global.Dir_Root_1.exists()) {
                Global.Dir_Root_1.mkdir();
            }

            Dir_thum = new File(Dir_Root_1, "/thum");

            if (!Global.Dir_thum.exists()) {
                Global.Dir_thum.mkdir();
            }

            Dir_Download = new File(Dir_Root_1, "/download");

            if (!Global.Dir_Download.exists()) {
                Global.Dir_Download.mkdir();
            }
        }
        return true;
    }

    public static void Process_Main_Page_Url(String url){
        if(url==null){
            Main_Page_Url=null;
            return;
        }
      //  url=url.toLowerCase();
        String _fix_url= Tools.Was_Web_Site(url);
//        if(_fix_url==null) {
//            _fix_url = Tools.Fix_Url(url);
//            String _fix_url2=Tools.Was_Web_Site(_fix_url);
//            if(_fix_url2==null)_fix_url=null;
//        }
        Main_Page_Url=_fix_url;
    }




    public static boolean Get_banner(Activity cc, ViewGroup parent, int pos, int banner_type, final CLCallback.CB cber) {

        if (Setting.Share_Setting().get_subscription_flag()) {
            return false;
        }

        int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
        View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(2)), colors);
      //  int pos = Setting.Share_Setting().get_ads_pos();

     //   if (pos != 1)
      //      parent.addView(divid);

        AdSize adsize;
       // int banner_type = Setting.Share_Setting().get_banner_type();
      //  banner_type = 6;
        if (banner_type == 0)
            adsize=AdSize.BANNER;
        else if (banner_type == 1)
            adsize=AdSize.FULL_BANNER;
        else if (banner_type == 2)
            adsize=AdSize.LARGE_BANNER;
        else if (banner_type == 3)
            adsize=AdSize.LEADERBOARD;
        else if (banner_type == 4)
            adsize=AdSize.MEDIUM_RECTANGLE;
        else if (banner_type == 5)
            adsize=AdSize.WIDE_SKYSCRAPER;
        else if (banner_type == 6)
            adsize=AdSize.BANNER;
        else {
            if (cc instanceof AppCompatActivity) {
                adsize = getAdSize(cc);
            } else {
                adsize = AdSize.LEADERBOARD;
            }
        }

        AdView _v1;
        if (Global.adView != null) {

            _v1 = Global.adView;
            ViewUtils.removeSelfFromParent(_v1);
        } else {
            _v1=Global.Get_Banner(cc, adsize, cber);
            Global.adView = _v1;
        }
//        AdView _v1=Global.Get_Banner(cc, adsize, cber);
        if (_v1 != null) {
            parent.addView(_v1);
        }

        if (pos != 1) {
            int index = parent.indexOfChild(_v1);
            parent.addView(divid, index);
        } else if (pos == 1)
            parent.addView(divid);

        return true;
    }


    private static AdSize getAdSize(Activity cc) {
        // Step 2 - Determine the screen width (less decorations) to use for the ad width.
        Display display = cc.getWindowManager().getDefaultDisplay();
        DisplayMetrics outMetrics = new DisplayMetrics();
        display.getMetrics(outMetrics);

        float widthPixels = outMetrics.widthPixels;
        float density = outMetrics.density;

        int adWidth = (int) (widthPixels / density);

        // Step 3 - Get adaptive ad size and return for setting on the ad view.
        return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(cc, adWidth);
    }



    public static AdView Get_Banner(Context context, AdSize size,  final CLCallback.CB cber){


        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {

            //int count = Setting.Share_Setting().get_app_run_count();
            if (Setting.Share_Setting().get_subscription_flag() ) {
                return null;
            }
        } else {
            return null;
        }

        AdView _ad_banner_view=new AdView(context);
        //_ad_banner_view.setLayoutParams(_lp);
        _ad_banner_view.setAdSize(size);
/*
        String id = Setting.Share_Setting().get_banner_id();
        if (id == null || id.isEmpty())
            _ad_banner_view.setAdUnitId(Global._admob_banner);
        else
            _ad_banner_view.setAdUnitId(id);
*/

        if (AD_Test) {
            _ad_banner_view.setAdUnitId("ca-app-pub-3940256099942544/6300978111");
        } else {
            _ad_banner_view.setAdUnitId(_admob_banner);
        }


        //_ad_banner_view.setAdUnitId(Global._admob_banner);
        AdRequest req_ad=new AdRequest.Builder().build();
        _ad_banner_view.loadAd(req_ad);
        _ad_banner_view.setTag(new Integer(0));
        _ad_banner_view.setAdListener(new AdListener() {
            @Override
            public void onAdFailedToLoad(LoadAdError adError) {

                    if (cber != null) {
                        cber.on_callback();

                        return;
                    }
            }
            @Override
            public void onAdOpened() {
            }

        });
        return _ad_banner_view;
    }

   public static AdView _ad_banner_view;
    public static AdView Get_Banner(Activity context, ViewGroup.LayoutParams _lp, final CLCallback.CB cber){
        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            int count = Setting.Share_Setting().get_app_run_count();
            if (Setting.Share_Setting().get_subscription_flag() || count < Setting.Share_Setting().get_open_ad_count()) {
                CL.CLOGI("Eddy Get_Banner null");
                return null;
            }
        } else {
            return null;
        }

        _ad_banner_view=new AdView(context);
        _ad_banner_view.setLayoutParams(_lp);
        AdSize adsize = getAdSize(context);

        _ad_banner_view.setAdSize(adsize);
        String id = Setting.Share_Setting().get_banner_id();
        if (id == null || id.isEmpty())
            _ad_banner_view.setAdUnitId(Global._admob_banner);
        else
            _ad_banner_view.setAdUnitId(id);

        //_ad_banner_view.setAdUnitId(Global._admob_banner);
        AdRequest req_ad=new AdRequest.Builder().build();
        _ad_banner_view.loadAd(req_ad);
        _ad_banner_view.setTag(new Integer(0));
        _ad_banner_view.setAdListener(new AdListener() {
            @Override
            public void onAdFailedToLoad(LoadAdError adError) {

                if (cber != null) {
                    cber.on_callback();

                    return;
                }
            }
            @Override
            public void onAdOpened() {
            }

        });
        return _ad_banner_view;
       // return null;
    }

    public static int interstitialAdCount = 0;
    public static int interstitialAdMax = 2;
    public static InterstitialAd ad_interstitial;
    public static String AD_INTERSTITIAL_UNIT_ID = null;

    private static void LoadInterstitial(final Context cc){

        AdRequest adRequest = new AdRequest.Builder().build();
        if (AD_Test) {
            AD_INTERSTITIAL_UNIT_ID = "ca-app-pub-3940256099942544/8691691433";//"ca-app-pub-3940256099942544/1033173712";//"ca-app-pub-3940256099942544/8691691433";//
        } else {
            AD_INTERSTITIAL_UNIT_ID = Global._admob_interstitial;
        }

        InterstitialAd.load(
                cc,
                AD_INTERSTITIAL_UNIT_ID,
                adRequest,
                new InterstitialAdLoadCallback() {
                    @Override
                    public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                        // The mInterstitialAd reference will be null until
                        // an ad is loaded.
                        ad_interstitial = interstitialAd;
                        //Log.i(TAG, "onAdLoaded");
                        interstitialAd.setFullScreenContentCallback(
                                new FullScreenContentCallback() {
                                    @Override
                                    public void onAdDismissedFullScreenContent() {
                                        // Called when fullscreen content is dismissed.
                                        // Make sure to set your reference to null so you don't
                                        // show it a second time.
                                          ad_interstitial  = null;
                                          LoadInterstitial(cc);
                                    }

                                    @Override
                                    public void onAdFailedToShowFullScreenContent(AdError adError) {
                                        // Called when fullscreen content failed to show.
                                        // Make sure to set your reference to null so you don't
                                        // show it a second time.
                                        // ad_interstitial  = null;
                                        ad_interstitial  = null;
                                        LoadInterstitial(cc);
                                    }

                                    @Override
                                    public void onAdShowedFullScreenContent() {
                                        // Called when fullscreen content is shown.
                                        //  LoadInterstitial(cc);
                                      //  ad_interstitial  = null;
                                      //  LoadInterstitial(cc);
                                    }
                                });
                    }

                    @Override
                    public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                        // Handle the error
                        //  ad_interstitial = null;
                        // LoadInterstitial(cc);

          /*              if (interstitialAdCount++ < interstitialAdMax) {
                            ad_interstitial = null;
                            LoadInterstitial(cc);
                        }


           */
                    }
                });
    }

    public static void showInterstitial_donow(Context cc, CLCallback.CB cber) {
        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag()) {
            if(cber!=null)cber.on_callback();
            return;
        }


        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            int count = Setting.Share_Setting().get_app_run_count();
            if (Setting.Share_Setting().get_subscription_flag() || count < Setting.Share_Setting().get_open_ad_count()) {
                if(cber!=null)cber.on_callback();
                return;
            }
        } else {
            if(cber!=null)cber.on_callback();
            return;
        }

        if (ad_interstitial != null) {
            ad_interstitial.show((Activity) cc);
        }

        if(cber!=null)cber.on_callback();
    }

    public static void showInterstitial(Context cc, CLCallback.CB cber) {
        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag() ) {
            if(cber!=null)cber.on_callback();
            return;
        }


        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            int count = Setting.Share_Setting().get_app_run_count();
            if (Setting.Share_Setting().get_subscription_flag() || count < Setting.Share_Setting().get_open_ad_count()) {
                if(cber!=null)cber.on_callback();
                return;
            }
        } else {
            if(cber!=null)cber.on_callback();
            return;
        }


        int r = rand.nextInt(10)+1;
        if (r<Setting.Share_Setting().get_frequency_show()) {
            if (ad_interstitial != null) {
                ad_interstitial.show((Activity) cc);
                if(cber!=null)cber.on_callback();
            }
        } else {
            if(cber!=null)cber.on_callback();
        }


    }

    public static void  Get_Native_Ad(final Activity cc, final ViewGroup root, final GoogleAdmob.NativeEventer _add) {
        long run_time = Setting.Share_Setting().get_app_run_time();
        //Eddy

        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag()) {
            return;
        }

        AdLoader.Builder builder;
        if (AD_Test) {
            builder = new AdLoader.Builder(cc, "ca-app-pub-3940256099942544/1044960115");
        } else {
            builder = new AdLoader.Builder(cc, "ca-app-pub-8603317425868964/6887186752");
        }

        builder.forNativeAd(
                new NativeAd.OnNativeAdLoadedListener() {
                    // OnLoadedListener implementation.
                    @Override
                    public void onNativeAdLoaded(NativeAd nativeAd) {
                        // If this callback occurs after the activity is destroyed, you must call
                        // destroy and return or you may get a memory leak.
                        boolean isDestroyed = false;

                        // You must call destroy on old ads when you are done with them,
                        // otherwise you will have a memory leak.
                        if (GoogleAdmob.nativeAd != null) {
                            GoogleAdmob.nativeAd.destroy();
                        }
                        GoogleAdmob.nativeAd = nativeAd;

                        NativeAdView adView =
                                (NativeAdView) cc.getLayoutInflater().inflate(R.layout.ad_unified, null);
                        populateUnifiedNativeAdView(nativeAd, adView);
                        root.removeAllViews();
                        root.addView(adView);
                        _add.onAddedCallback(adView.findViewById(R.id.native_layout));
                    }
                });

        VideoOptions videoOptions = new VideoOptions.Builder()
                .setStartMuted(true)
                .build();

        NativeAdOptions adOptions = new NativeAdOptions.Builder()
                .setVideoOptions(videoOptions)
                .build();

        builder.withNativeAdOptions(adOptions);

        AdLoader adLoader = builder.withAdListener(new AdListener() {
            @Override
            public void onAdFailedToLoad(LoadAdError errorCode) {
            }
        }).build();

        AdRequest adRequest = GetAdRequest();
        adLoader.loadAd(adRequest);
    }

    public static AdRequest GetAdRequest() {
        AdRequest req = null;
        if (AD_Test) {//3782C414BB19D4729A4161E42F7ED907
            //req = new AdRequest.Builder().addTestDevice("33BE2250B43518CCDA7DE426D04EE231").build();
            List<String> testDeviceIds = Arrays.asList("DABE8D045C3EA23F5133CF880B1EDD56");
            RequestConfiguration configuration =
                    new RequestConfiguration.Builder().setTestDeviceIds(testDeviceIds).build();
            MobileAds.setRequestConfiguration(configuration);
            req=new AdRequest.Builder().build();
        } else {
            req=new AdRequest.Builder().build();
        }
        return req;
    }


    private static void populateUnifiedNativeAdView(NativeAd nativeAd, NativeAdView adView) {
        // Set the media view. Media content will be automatically populated in the media view once
        // adView.setNativeAd() is called.
        com.google.android.gms.ads.nativead.MediaView mediaView = adView.findViewById(R.id.ad_media);
        adView.setMediaView(mediaView);

        // Set other ad assets.
        adView.setHeadlineView(adView.findViewById(R.id.ad_headline));
        adView.setBodyView(adView.findViewById(R.id.ad_body));
        adView.setCallToActionView(adView.findViewById(R.id.ad_call_to_action));
        adView.setIconView(adView.findViewById(R.id.ad_app_icon));
        adView.setPriceView(adView.findViewById(R.id.ad_price));
        adView.setStarRatingView(adView.findViewById(R.id.ad_stars));
        adView.setStoreView(adView.findViewById(R.id.ad_store));
        adView.setAdvertiserView(adView.findViewById(R.id.ad_advertiser));

        // The headline is guaranteed to be in every UnifiedNativeAd.
        ((TextView) adView.getHeadlineView()).setText(nativeAd.getHeadline());

        // These assets aren't guaranteed to be in every UnifiedNativeAd, so it's important to
        // check before trying to display them.
        if (nativeAd.getBody() == null) {
            adView.getBodyView().setVisibility(View.INVISIBLE);
        } else {
            adView.getBodyView().setVisibility(View.VISIBLE);
            ((TextView) adView.getBodyView()).setText(nativeAd.getBody());
        }

        if (nativeAd.getCallToAction() == null) {
            adView.getCallToActionView().setVisibility(View.INVISIBLE);
        } else {
            adView.getCallToActionView().setVisibility(View.VISIBLE);
            ((Button) adView.getCallToActionView()).setText(nativeAd.getCallToAction());
        }

        if (nativeAd.getIcon() == null) {
            adView.getIconView().setVisibility(View.GONE);
        } else {
            ((ImageView) adView.getIconView()).setImageDrawable(
                    nativeAd.getIcon().getDrawable());
            adView.getIconView().setVisibility(View.VISIBLE);
        }

        if (nativeAd.getPrice() == null) {
            adView.getPriceView().setVisibility(View.INVISIBLE);
        } else {
            adView.getPriceView().setVisibility(View.VISIBLE);
            ((TextView) adView.getPriceView()).setText(nativeAd.getPrice());
        }

        if (nativeAd.getStore() == null) {
            adView.getStoreView().setVisibility(View.INVISIBLE);
        } else {
            adView.getStoreView().setVisibility(View.VISIBLE);
            ((TextView) adView.getStoreView()).setText(nativeAd.getStore());
        }

        if (nativeAd.getStarRating() == null) {
            adView.getStarRatingView().setVisibility(View.INVISIBLE);
        } else {
            ((RatingBar) adView.getStarRatingView())
                    .setRating(nativeAd.getStarRating().floatValue());
            adView.getStarRatingView().setVisibility(View.VISIBLE);
        }

        if (nativeAd.getAdvertiser() == null) {
            adView.getAdvertiserView().setVisibility(View.INVISIBLE);
        } else {
            ((TextView) adView.getAdvertiserView()).setText(nativeAd.getAdvertiser());
            adView.getAdvertiserView().setVisibility(View.VISIBLE);
        }

        // This method tells the Google Mobile Ads SDK that you have finished populating your
        // native ad view with this native ad. The SDK will populate the adView's MediaView
        // with the media content from this native ad.
        adView.setNativeAd(nativeAd);

        // Get the video controller for the ad. One will always be provided, even if the ad doesn't
        // have a video asset.
        VideoController vc = nativeAd.getMediaContent().getVideoController();

        // Updates the UI to say whether or not this ad has a video asset.
        if (vc.hasVideoContent()) {
            // Create a new VideoLifecycleCallbacks object and pass it to the VideoController. The
            // VideoController will call methods on this object when events occur in the video
            // lifecycle.
            vc.setVideoLifecycleCallbacks(new VideoController.VideoLifecycleCallbacks() {
                @Override
                public void onVideoEnd() {
                    // Publishers should allow native ads to complete video playback before
                    // refreshing or replacing them with another ad in the same UI location.
                    super.onVideoEnd();
                }
            });
        } else {
        }
    }

    public static CLCallback.CB_TFO<Integer> mycb;
    public static RewardedAd rewardedAd;
    public static boolean rewardLoading;
    public static Boolean rewardedAdLoadFailed;
    public static RewardedInterstitialAd rewardedInterstitialAd;
    public static boolean rewardedInterstitialLoading;
    public static Boolean rewardedInterstitialAdAdLoadFailed;

    public static void LoadOpenAd(Context context) {
        Mainly application = Mainly.getInstance();
        application.loadAd(context);
        Mainly.bOpenAdInited = true;
    }

    public static void LoadRewardInterstitial(Context context) {
        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag())
            return;
        if (rewardedInterstitialAd == null) {
            final AdRequest adRequest = new AdRequest.Builder().build();
            final RewardedInterstitialAdLoadCallback rewardedAdListener = new RewardedInterstitialAdListener();
            if (Global.AD_Test) {//ca-app-pub-3940256099942544/5354046379    ca-app-pub-3940256099942544/5224354917
                RewardedInterstitialAd.load(context, "ca-app-pub-3940256099942544/5354046379", adRequest, rewardedAdListener);
            } else {
                RewardedInterstitialAd.load(context, _admob_interstitial_reward, adRequest, rewardedAdListener);
            }

        }
    }

    public static void LoadReward(Context context) {
        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag())
            return;
        if (rewardedAd == null) {
            final AdRequest adRequest = new AdRequest.Builder().build();
            final RewardedAdListener rewardedAdListener = new RewardedAdListener();
            //RewardedAd.load(context, "ca-app-pub-3064461767247622/1277576354", adRequest, rewardedAdListener);
            if (Global.AD_Test) {//ca-app-pub-3940256099942544/5354046379    ca-app-pub-3940256099942544/5224354917
                RewardedAd.load(context, "ca-app-pub-3940256099942544/5224354917", adRequest, rewardedAdListener);
            } else {
                RewardedAd.load(context, _admob_reward, adRequest, rewardedAdListener);
            }
        }
    }

    public static void LoadReward(Context context, CLCallback.CB_TFO<Integer> cb) {
        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag())
            return;
        if (rewardedAd == null) {
            final AdRequest adRequest = new AdRequest.Builder().build();
            final RewardedAdListener rewardedAdListener = new RewardedAdListener();
            //RewardedAd.load(context, "ca-app-pub-3064461767247622/1277576354", adRequest, rewardedAdListener);
            if (Global.AD_Test) {//    ca-app-pub-3940256099942544/5224354917
                RewardedAd.load(context, "ca-app-pub-3940256099942544/5224354917", adRequest, rewardedAdListener);
            } else {
                RewardedAd.load(context, "ca-app-pub-3064461767247622/1277576354", adRequest, rewardedAdListener);
            }
            mycb = cb;
        }
    }

    public static void setRewardCB(CLCallback.CB_TFO<Integer> cb) {
        mycb = cb;
    }

    private static FullScreenContentCallback fullScreenContentRewardInterstitialCallback =
            new FullScreenContentCallback() {
                @Override
                public void onAdShowedFullScreenContent() {
                    // Code to be invoked when the ad showed full screen content.
                    if (Global.mycb != null) {
                        Global.mycb.on_callback_success(AD_REWARD_INITERTITIAL_FULL_SCREEN, "");
                    }
                }

                @Override
                public void onAdDismissedFullScreenContent() {
                    // Code to be invoked when the ad dismissed full screen content.
                    CL.CLOGI("Eddy onAdDismissedFullScreenContent");
                    if (Global.mycb != null) {
                        Global.mycb.on_callback_fail(AD_REWARD_INITERTITIAL_FULL_SCREEN_DISMISSED, "");
                    }
                }
            };

    private static FullScreenContentCallback fullScreenContentCallback =
            new FullScreenContentCallback() {
                @Override
                public void onAdShowedFullScreenContent() {
                    // Code to be invoked when the ad showed full screen content.
                    if (Global.mycb != null) {
                        Global.mycb.on_callback_success(AD_FULL_SCREEN, "");
                    }
                }

                @Override
                public void onAdDismissedFullScreenContent() {
                    // Code to be invoked when the ad dismissed full screen content.
                    CL.CLOGI("Eddy onAdDismissedFullScreenContent");
                    if (Global.mycb != null) {
                        Global.mycb.on_callback_fail(AD_FULL_SCREEN_DISMISSED, "");
                    }
                }
            };

    private static class RewardedAdListener extends RewardedAdLoadCallback {

        @Override
        public void onAdLoaded(@NonNull final RewardedAd rewardedAd) {
            super.onAdLoaded(rewardedAd);
            Global.rewardedAd = rewardedAd;
            Global.rewardedAdLoadFailed = false;
            Global.rewardedAd.setFullScreenContentCallback(fullScreenContentCallback);
            if (Global.mycb != null) {
                Global.mycb.on_callback_success(AD_LOADED, "");
            }
        }

        @Override
        public void onAdFailedToLoad(@NonNull final LoadAdError loadAdError) {
            super.onAdFailedToLoad(loadAdError);
            Global.rewardedAd = null;
            Global.rewardedAdLoadFailed = true;
            if (Global.mycb != null) {
                Global.mycb.on_callback_fail(AD_LOADED_FAILED, null);
            }
        }
    }

    private static class RewardedInterstitialAdListener extends RewardedInterstitialAdLoadCallback {

        public void onAdLoaded(RewardedInterstitialAd var1) {
            Global.rewardedInterstitialAd = var1;
            Global.rewardedInterstitialAdAdLoadFailed = false;
            Global.rewardedInterstitialAd.setFullScreenContentCallback(fullScreenContentRewardInterstitialCallback);
            if (Global.mycb != null) {
                Global.mycb.on_callback_success(AD_LOADED, "");
            }
        }

        public void onAdFailedToLoad(LoadAdError var1) {
            Global.rewardedInterstitialAd = null;
            Global.rewardedInterstitialAdAdLoadFailed = true;
            if (Global.mycb != null) {
                Global.mycb.on_callback_fail(AD_LOADED_FAILED, null);
            }
        }

    }

//    public static void Init_Flurry(Context context){
//        new FlurryAgent.Builder().withLogEnabled(true)
//                .withListener(new FlurryAgentListener() {
//                    @Override
//                    public void onSessionStarted() {
//                        CL.CLOGI("on flurry session started!");
//                    }
//                })
//                .build(context, Global.FLURRY_APP_KEY);
//    }
//    public static void Flurry_Activity_Start(Context cc){
//        FlurryAgent.onStartSession(cc);
//    }
//    public static void Flurry_Activity_End(Context cc){
//        FlurryAgent.onEndSession(cc);
//    }
//    public static void Flurry_Send_Event(String event){
//        FlurryAgent.logEvent(event);
//    }

    private static KProgressHUD hud;
    public static void showWait(Context cc) {
        CL.CLOGI("Eddy showWait");
        if (hud == null) {
            hud = KProgressHUD.create(cc)
                    .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE);
            hud.setCancellable(false);
        }

        if (hud != null) {
            hud.show();
        }
    }

    public static void hideWait() {
        if (hud != null) {
            hud.dismiss();
        }
    }

    //R现在是androi 10
    public static boolean IsAndroid10() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q;
    }
}
