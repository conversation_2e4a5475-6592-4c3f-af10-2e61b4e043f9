package amazon.browser.lionpro.primary;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import java.util.ArrayList;

import lion.widget.CLGif;
import lion.widget.CLImager;


public class LookPicture extends Activity {
	
	private LookPicture cc;

	private CLImager looker_picture;
	private String[] thums;
    private String[] names;
    private String dir;
    private boolean show_page_number=true;

    private int index;

    private CLGif looker_gif;
    private String[] paths;
	
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		this.cc=this;

		Intent _intent=this.getIntent();
        String _type=_intent.getStringExtra("type");
        if(_type.equals("picture")) {
            index = _intent.getIntExtra("index", 0);
            thums = _intent.getStringArrayExtra("thums");
            names = _intent.getStringArrayExtra("names");
            dir = _intent.getStringExtra("dir");
            show_page_number = _intent.getBooleanExtra("show_page_number", true);
            init_picture();
        }else if(_type.equals("gif")){
            index = _intent.getIntExtra("index", 0);
            paths = _intent.getStringArrayExtra("paths");
            init_gif();
        }
	}

	@Override
	protected void onResume() {
		super.onResume();
	}

	@Override
	protected void onDestroy() {
		super.onDestroy();
        if(looker_picture!=null)looker_picture.exit();
	}
	
	private void init_picture(){
		
		looker_picture =new CLImager(cc,CLImager.Type_Memory_MED);
		looker_picture.set_can_zoom(true);
		looker_picture.set_loop(true);
		if(show_page_number) looker_picture.set_show_page_number(CLImager.Type_Page_Number_Digit);
		looker_picture.set_listen_eventer(new CLImager.OnEventer() {
			@Override
			public void on_page_change(int index) {
			}
			@Override
			public void on_item_click(int index, CLImager.DataItem item) {
				finish();
			}
			@Override
			public void on_data_empty() {
			}
			@Override
			public void on_data_delete(int index) {
			}
		});
		this.setContentView(looker_picture);
		
		ArrayList<CLImager.DataItem> _datas=new ArrayList<CLImager.DataItem>();
		for(int i=0;i<names.length;++i){
			_datas.add(new CLImager.DataItem(
					(thums!=null?thums[i]:null),
					names[i], 
					dir, 
					null)
			);
		}
		looker_picture.set_data(_datas, index);
		
	}

    private void init_gif(){
        if(paths==null)return;
        looker_gif=new CLGif(this);
        ArrayList<String> _ps=new ArrayList<>();
        for(int i=0;i<paths.length;++i){
            _ps.add(paths[i]);
        }
        looker_gif.set_data(_ps,index);
        this.setContentView(looker_gif);
    }
}
