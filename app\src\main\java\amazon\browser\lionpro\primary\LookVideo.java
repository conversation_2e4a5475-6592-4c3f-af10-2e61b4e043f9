package amazon.browser.lionpro.primary;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.toys.CommonBackButton;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLToast;
import lion.web.CLHS;
import lion.web.CLRequest;
import lion.web.CLResponse;
import lion.widget.VideoProgressController;

/**
 * Created by leron on 2015/12/8.
 */
public class LookVideo extends Activity {

    private final int Handler_video_start=1001;
    private final int Handler_video_play=1002;
    private final int Handler_video_pause=1003;
    private final int Handler_video_pulse =1004;
    private final int Handler_video_start_buffing=1005;
    private final int Handler_video_end_buffing=1006;
    private final int Handler_video_completion=1007;
    private final int Handler_video_show_menu=1010;
    private final int Handler_video_hide_menu=1011;
    private final int Handler_video_show_menu_always=1012;
    private Handler handler=new Handler(Looper.getMainLooper()){
        @Override
        public void handleMessage(Message msg) {
            if(msg.what==Handler_video_start){
                try{
                    player=new MediaPlayer();
                    player.setOnCompletionListener(listen_media_on_completion);
                    player.setOnErrorListener(listen_media_on_error);
                    player.setOnPreparedListener(listen_media_on_prepared);
                    player.setOnBufferingUpdateListener(listen_media_on_buffering);
                    player.setDataSource(video_path);
                    video_view=new SurfaceView(cc);
                    video_view.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP, Gravity.FILL));
                    video_view.getHolder().addCallback(listen_surface_holder);
                    fl_video.addView(video_view,0);
                    player.prepare();
                    player.start();
                }catch (Exception ex){
                    CL.CLOGE("voide start play error:"+ex.toString(),ex);
                    go_exit();
                }
                try {
                    video_duration = player.getDuration();
                    tv_progress_now.setText(get_time_string(player.getCurrentPosition()));
                    tv_progress_all.setText(get_time_string(video_duration));
                    ctm_progress.set_data(0, video_duration);
                    handler.sendEmptyMessage(Handler_video_play);
                }catch (Exception ex){}
            }
            else if(msg.what==Handler_video_play){
                ih_play=true;
                player.start();
                btn_play_pause.setBackgroundResource(R.mipmap.icon_music_pause);
                handler.sendEmptyMessage(Handler_video_pulse);
                handler.sendEmptyMessage(Handler_video_show_menu);
            }
            else if(msg.what==Handler_video_pause){
                ih_play=false;
                player.pause();
                btn_play_pause.setBackgroundResource(R.mipmap.icon_music_play);
                handler.sendEmptyMessage(Handler_video_show_menu_always);
            }
            else if(msg.what== Handler_video_pulse){
                if(!ih_play)return;
                if(player==null)return;
                int _v=player.getCurrentPosition();
                tv_progress_now.setText(get_time_string(_v));
                ctm_progress.set_data(_v,video_duration);
                ctm_progress.set_buff_data(buff_ratio);
                handler.sendEmptyMessageDelayed(Handler_video_pulse, 500);
            }
            else if(msg.what==Handler_video_start_buffing){
                ih_ui_buffing=true;
                btn_play_pause.setBackgroundResource(R.mipmap.icon_music_play);
            }
            else if(msg.what==Handler_video_end_buffing){
                ih_ui_buffing=false;
                btn_play_pause.setBackgroundResource(R.mipmap.icon_music_pause);
            }
            else if(msg.what==Handler_video_completion){
                ih_play=false;
                player.stop();
                player.release();
                player=null;
                btn_play_pause.setBackgroundResource(R.mipmap.icon_music_play);
                fl_video.removeView(video_view);
                handler.sendEmptyMessage(Handler_video_show_menu_always);
            }
            else if(msg.what==Handler_video_show_menu){
                CL.CLOGI("on show menu");
                ih_show_menu=true;
                ll_video_controller.setVisibility(View.VISIBLE);
                fl_header.setVisibility(View.VISIBLE);
                handler.removeMessages(Handler_video_hide_menu);
                if(ih_play)handler.sendEmptyMessageDelayed(Handler_video_hide_menu, 5000);
            }
            else if(msg.what==Handler_video_hide_menu){
                CL.CLOGI("on hide menu");
                ih_show_menu=false;
                ll_video_controller.setVisibility(View.INVISIBLE);
                fl_header.setVisibility(View.GONE);
            }
            else if(msg.what==Handler_video_show_menu_always){
                handler.removeMessages(Handler_video_hide_menu);
                ih_show_menu=true;
                ll_video_controller.setVisibility(View.VISIBLE);
                fl_header.setVisibility(View.VISIBLE);
            }
        }
    };
//    private boolean ih_ready;
    private boolean ih_play;
    private boolean ih_ui_buffing;
    private boolean ih_show_menu;
    private int buff_ratio=0;
    private int video_duration;
    private String video_name,video_path;
    private boolean is_m3u8=false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.cc=this;

        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        is_m3u8=this.getIntent().getBooleanExtra("m3u8",false);
        video_name=this.getIntent().getStringExtra("name");
        video_path=this.getIntent().getStringExtra("path");

        if(!is_m3u8 && !new File(video_path).exists()){
            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
            finish();
            return;
        }
        if(is_m3u8){
            start_server();
        }

        init_UI();
        if(is_m3u8)handler.sendEmptyMessageDelayed(Handler_video_start,300);
        else handler.sendEmptyMessage(Handler_video_start);
        handler.sendEmptyMessageDelayed(Handler_video_hide_menu, 5000);
        //setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
    }


    @Override
    protected void onRestart() {
        super.onRestart();
        /*
        Global.Show_Interstitial_IM(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                if(player!=null && !player.isPlaying())listen_click_btn_play_pause.onClick(null);
            }
        });

         */
    }

    @Override
    protected void onResume() {
        super.onResume();
        update_video_size();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (player != null && player.isPlaying()) listen_click_btn_play_pause.onClick(null);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        update_video_size();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CL.CLOGI("on destory");
        handler.removeMessages(Handler_video_pulse);
        if(player!=null){
            player.release();
            player=null;
        }
        if(is_m3u8)destroy_server();
    }

    private CLHS server;
    private void start_server(){
        server=new CLHS(new CLHS.Eventer() {
            @Override
            public void on_server_start() {
                CL.CLOGI("on hs start");
            }
            @Override
            public void on_server_exit() {
                CL.CLOGI("on hs exit");
            }
            @Override
            public void on_handle(CLRequest req, CLResponse rsp) throws Exception {
                if(!req.uri.startsWith("/m3u8"))return;
                CL.CLOGI("req uri:"+req.uri);
                String _dir=req.uri.substring(req.uri.indexOf('/',req.uri.indexOf('/')+1)+1,req.uri.lastIndexOf('/'));
                String _f=req.uri.substring(req.uri.lastIndexOf('/')+1);
                CL.CLOGI("dir:"+_dir);
                CL.CLOGI("f:"+_f);

                File _root=Server.Share_FileManager().get_major_dir(Data.Type_Video);
                File _fdir=new File(_root,_dir);
                CL.CLOGI("fdir:"+_fdir.getAbsolutePath());
                if(_fdir.exists()){
                    if(_f.equals("plist")) {
                        File _fpl = new File(_fdir, "playlist");
                        if (_fpl.exists()) {
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.set_content_type("application/x-mpegurl");
                            rsp.set_content_length(String.valueOf(_fpl.length()));
                            rsp.do_response();
                            OutputStream _os = rsp.get_output_stream();
                            FileInputStream _fis = new FileInputStream(_fpl);
                            int _c = 0;
                            byte[] _buff = new byte[8192];
                            while ((_c = _fis.read(_buff)) != -1) {
                                _os.write(_buff, 0, _c);
                            }
                            _fis.close();
                            CL.CLOGI("发送清单完毕");
                        }
                    }else{
//                        Thread.sleep(5000);
                        if(req.request_range!=null)
                            CL.CLOGI("range:"+req.request_range.range_start+"-"+req.request_range.range_end);
                        File _fpl = new File(_fdir, _f);
                        CL.CLOGI("ts:"+_fpl.getAbsolutePath());
                        if (_fpl.exists()) {
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.set_content_type("video/mp2ts");
                            rsp.set_content_length(String.valueOf(_fpl.length()));
                            rsp.do_response();
                            OutputStream _os = rsp.get_output_stream();
                            FileInputStream _fis = new FileInputStream(_fpl);
                            int _c = 0;
                            byte[] _buff = new byte[8192];
                            while ((_c = _fis.read(_buff)) != -1) {
                                _os.write(_buff, 0, _c);
                            }
                            _fis.close();
                            CL.CLOGI("发送TS完毕:"+_f);
                        }
                    }
                }
            }
        });
        server.go();
        try{
            Thread.sleep(200);
        }catch (Exception ex){}
    }
    private void destroy_server(){
        if(server!=null)server.stop();
    }



    private Context cc;
    private FrameLayout fl_video;
    private MediaPlayer player;
    private SurfaceView video_view;
    private FrameLayout fl_header;
    private LinearLayout ll_video_controller;
    private ImageView btn_play_pause;
    private TextView tv_progress_now,tv_progress_all;
    private VideoProgressController ctm_progress;
    private View.OnClickListener listen_click_video_menu=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(ih_show_menu)handler.sendEmptyMessage(Handler_video_hide_menu);
            else handler.sendEmptyMessage(Handler_video_show_menu);
        }
    };
    private VideoProgressController.CLVideoProgressListener listen_progress=new VideoProgressController.CLVideoProgressListener() {
        @Override
        public void on_progress_move(float progress) {
//			video_will_position=(int)(video_duration*progress);
            handler.sendEmptyMessage(Handler_video_show_menu_always);
        }
        @Override
        public void on_progress_click(float progress) {
            if(ih_ui_buffing || !ih_play)return;
            int _v=(int)(video_duration*progress);
            if(_v < 3000)_v=3000;
            else if(_v>video_duration-3000)_v=video_duration-3000;
            player.seekTo(_v);
            handler.sendEmptyMessage(Handler_video_show_menu);
        }
    };

    private View.OnClickListener listen_click_btn_play_pause=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(player==null){
                handler.sendEmptyMessage(Handler_video_start);
                return;
            }

            if(!ih_ui_buffing){
                if(ih_play){
                    handler.sendEmptyMessage(Handler_video_pause);
                }
                else{
                    handler.sendEmptyMessage(Handler_video_play);
                }
            }
        }
    };
    private MediaPlayer.OnPreparedListener listen_media_on_prepared=new MediaPlayer.OnPreparedListener() {
        @Override
        public void onPrepared(MediaPlayer mp) {
            CL.CLOGI("on prepared*****************************");
            ctm_progress.setClickable(true);
            update_video_size();
            mp.setOnInfoListener(listen_media_on_info);
        }
    };
    private MediaPlayer.OnInfoListener listen_media_on_info=new MediaPlayer.OnInfoListener() {
        @Override
        public boolean onInfo(MediaPlayer mp, int what, int extra) {
//            CL.CLOGI("on info:" + "what-" + what + " extra-" + extra);
            if(what==MediaPlayer.MEDIA_INFO_BUFFERING_START){
                ih_ui_buffing=true;
                handler.sendEmptyMessage(Handler_video_start_buffing);
            }
            else if(what==MediaPlayer.MEDIA_INFO_BUFFERING_END){
                ih_ui_buffing=false;
                handler.sendEmptyMessage(Handler_video_end_buffing);
            }
            return true;
        }
    };
    private MediaPlayer.OnErrorListener listen_media_on_error=new MediaPlayer.OnErrorListener() {
        @Override
        public boolean onError(MediaPlayer mp, int what, int extra) {
            if(player==null)return true;
            CL.CLOGI("on error:" + what + ":" + extra);
            player.release();
            player=null;
            CLToast.Show(cc, "无法播放此视频,请重试", false);
            go_exit();
            return true;
        }
    };
    private MediaPlayer.OnCompletionListener listen_media_on_completion=new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            CL.CLOGI("on completion");
            handler.sendEmptyMessage(Handler_video_completion);
        }
    };
    private MediaPlayer.OnBufferingUpdateListener listen_media_on_buffering=new MediaPlayer.OnBufferingUpdateListener() {
        @Override
        public void onBufferingUpdate(MediaPlayer mp, int percent) {
            buff_ratio=percent;
        }
    };
    private SurfaceHolder.Callback listen_surface_holder=new SurfaceHolder.Callback() {
        @Override
        public void surfaceDestroyed(SurfaceHolder holder) {
            CL.CLOGI("on surface destoryed");
        }
        @Override
        public void surfaceCreated(SurfaceHolder holder) {
            CL.CLOGI("on surface created");
            player.setDisplay(holder);
        }
        @Override
        public void surfaceChanged(SurfaceHolder holder, int format, int width,
                                   int height) {
            CL.CLOGI("on surface changed:" + width + "/" + height);
        }
    };
    private void init_UI(){
        fl_video=new FrameLayout(cc);
        fl_video.setBackgroundColor(Color.BLACK);
        fl_video.setClickable(true);
        fl_video.setOnClickListener(listen_click_video_menu);
        this.setContentView(fl_video);

        player=new MediaPlayer();
        player.setOnCompletionListener(listen_media_on_completion);
        player.setOnErrorListener(listen_media_on_error);
        player.setOnPreparedListener(listen_media_on_prepared);
        player.setOnBufferingUpdateListener(listen_media_on_buffering);

        //header
        fl_header= CLController.Get_FrameLayout(cc, CL.Get_FLLP(CL.MP,CL.DIP2PX_INT(38),Gravity.TOP), 0x66000000,null);
        fl_video.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                finish();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER,CL.DIP2PX_INT(80),0,CL.DIP2PX_INT(60),0),
                video_name,0xffd0d0d0,12,null));


        ll_video_controller= CLController.Get_LinearLayout(cc, CL.Get_FLLP(CL.MP, CL.WC, Gravity.BOTTOM), LinearLayout.HORIZONTAL, null);
        ll_video_controller.setBackgroundColor(0x88000000);
        ll_video_controller.setPadding(CL.DIP2PX_INT(8), CL.DIP2PX_INT(4), CL.DIP2PX_INT(8), CL.DIP2PX_INT(4));
        ll_video_controller.setGravity(Gravity.CENTER_VERTICAL);
        fl_video.addView(ll_video_controller);
        btn_play_pause=CLController.Get_ImageView(cc, CL.Get_LP(CL.DIP2PX_INT(26), CL.DIP2PX_INT(26)),null, listen_click_btn_play_pause);
        btn_play_pause.setBackgroundResource(R.mipmap.icon_music_play);
        ll_video_controller.addView(btn_play_pause);

        tv_progress_now=new TextView(cc);
        tv_progress_now.setLayoutParams(CL.Get_LLLP(CL.WC, CL.WC, CL.DIP2PX_INT(8), 0, CL.DIP2PX_INT(8), 0));
        tv_progress_now.setTextColor(Color.WHITE);
        tv_progress_now.setTextSize(14);
        tv_progress_now.setText(get_time_string(0));
        ll_video_controller.addView(tv_progress_now);

        ctm_progress=new VideoProgressController(cc, listen_progress);
        ctm_progress.setLayoutParams(CL.Get_LLLP(CL.WC, CL.MP, 1.0f));
        ctm_progress.setClickable(false);
        ll_video_controller.addView(ctm_progress);

        tv_progress_all=new TextView(cc);
        tv_progress_all.setLayoutParams(CL.Get_LLLP(CL.WC, CL.WC, CL.DIP2PX_INT(8), 0, CL.DIP2PX_INT(8), 0));
        tv_progress_all.setTextColor(Color.WHITE);
        tv_progress_all.setTextSize(14);
        tv_progress_all.setText(get_time_string(0));
        ll_video_controller.addView(tv_progress_all);
    }
    private String get_time_string(int time){
        StringBuffer _sb=new StringBuffer();

        int _hh=time/3600000;
        if(_hh>0){
            _sb.append(_hh);
            _sb.append(':');
        }
        int _mm=(time%3600000)/60000;
        if(_mm<10){
            _sb.append(0);
            _sb.append(_mm);
            _sb.append(':');
        }
        else{
            _sb.append(_mm);
            _sb.append(':');
        }
        int _ss=(time%60000)/1000;
        if(_ss<10){
            _sb.append(0);
            _sb.append(_ss);
        }
        else{
            _sb.append(_ss);
        }
        return _sb.toString();
    }
    private void update_video_size(){
        if(player==null)return;
        float _vw=player.getVideoWidth();
        float _vh=player.getVideoHeight();
        if(_vw == 0 || _vh==0){
            return;
        }
        float _w=CL.Get_Screen_Width(cc);
        float _h=CL.Get_Screen_Height(cc);
        float _sw=_vw/_w;
        float _sh=_vh/_h;
        float _ss=(_sw>_sh?_sw:_sh);

        float _rw=_vw/_ss;
        float _rh=_vh/_ss;

        video_view.setLayoutParams(CL.Get_FLLP((int)_rw, (int)_rh, Gravity.CENTER));
        if(ih_play)handler.sendEmptyMessage(Handler_video_show_menu);
        else handler.sendEmptyMessage(Handler_video_show_menu_always);
    }
    private void go_exit(){
        if(player!=null){
            player.release();
            player=null;
            fl_video.removeView(video_view);
        }
        finish();
    }
}
