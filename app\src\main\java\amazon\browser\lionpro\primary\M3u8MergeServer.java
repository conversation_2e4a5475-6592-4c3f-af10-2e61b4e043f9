package amazon.browser.lionpro.primary;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.os.Message;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.M3U8Downloader;

import com.coder.ffmpeg.call.IFFmpegCallBack;
import com.coder.ffmpeg.jni.FFmpegCommand;
import com.coder.ffmpeg.utils.FFmpegUtils;

import java.io.File;
import java.util.concurrent.ConcurrentLinkedQueue;

public class M3u8MergeServer extends Service  {
    private WorkerBinder mBinder;
    private static ConcurrentLinkedQueue<PackStruct> packDatas = new ConcurrentLinkedQueue<>();
    @Override
    public void onCreate() {
        super.onCreate();
        mBinder = new WorkerBinder();
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        if (mBinder != null) {
            return mBinder;
        }
        return null;
    }

    public class WorkerBinder extends Binder {

        public WorkerBinder() {
            new PackWorker().start();
        }

        @RequiresApi(api = Build.VERSION_CODES.Q)
        public WorkerBinder(@Nullable @org.jetbrains.annotations.Nullable String descriptor) {
                super(descriptor);
        }

        public void packetData(String key, String vlaue, Data.StructDLItem item) {
            synchronized (packDatas) {
                PackStruct pack = new PackStruct();
                pack.key = key;
                pack.vlaue = vlaue;
                pack.dlData = item;
                packDatas.add(pack);
                packDatas.notifyAll();
            }
        }

        private class PackWorker extends Thread implements IFFmpegCallBack {
            private Data.StructDLItem entity;
            @Override
            public void run() {
                while (true) {
                    try {
                        PackStruct values;
                        synchronized (packDatas) {
                            if (packDatas.isEmpty()) {
                                packDatas.wait();
                                continue;
                            }
                            values = packDatas.poll();
                        }

                        if (values != null) {
                            entity = values.dlData;

                            FFmpegCommand.INSTANCE.runCmd(FFmpegUtils.INSTANCE.concatVideo(values.key, values.vlaue), this);
                            File f = new File(values.vlaue);
                            if (f.exists()) {
                                Message message = new Message();
                                entity.pos = entity.max;
                                entity.length = f.length();
                                message.arg1 = 200;
                                message.obj = entity;
                                M3U8Downloader.handler.sendMessage(message);
                            }
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }


            @Override
            public void onStart() {
                Message message = new Message();
                entity.pos = 100;
                message.arg1 = 100;
                message.obj = entity;
                M3U8Downloader.handler.sendMessage(message);
            }

            @Override
            public void onProgress(int progress, long pts) {
            }

            @Override
            public void onCancel() {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onError(int errorCode, @org.jetbrains.annotations.Nullable String errorMsg) {
            }
        }
    }


}
