package amazon.browser.lionpro.primary;
import android.content.Context;
import android.content.Intent;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import com.bumptech.glide.Glide;

import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;

public class MoreApps extends CLActivity {
    private  RecyclerView listView;
    private AdapterForItem adapter;
    private static Context cc;
    private static ArrayList<Setting.ApkInfo> data;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        cc = this;
        int _hh= CL.Set_Translucent_StatusBar(this.getWindow());

        this.setContentView(R.layout.back_layout);
        findViewById(R.id.root).setPadding(0,_hh,0,0);
        findViewById(R.id.root).setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        findViewById(R.id.back).setOnClickListener((v)->{
            finish();
        });

        ArrayList<Setting.ApkInfo> alist = (ArrayList<Setting.ApkInfo>) this.getIntent().getSerializableExtra("list");
        if (alist != null) {
            data = alist;
            listView = (RecyclerView) findViewById(R.id.apps_list);
            LinearLayoutManager layoutManager = new LinearLayoutManager(this);
            layoutManager.setOrientation(LinearLayoutManager.VERTICAL);
            listView.setLayoutManager(layoutManager);
           // listView.setItemAnimator(new DefaultItemAnimator());
        //    listView.addItemDecoration(new RecycleViewDivider(
          //          cc, LinearLayoutManager.VERTICAL, R.drawable.divider));
            listView.addItemDecoration(new RecycleViewDivider(
                    cc, LinearLayoutManager.HORIZONTAL, R.drawable.divider));
            adapter = new AdapterForItem();
            listView.setAdapter(adapter);
        }
    }

    static class AdapterForItem extends RecyclerView.Adapter<MoreAppWindowHolder> {

        @NonNull
        @Override
        public MoreAppWindowHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            LinearLayout rl = (LinearLayout) LayoutInflater.from(cc).inflate(R.layout.layout_app_item, parent, false);
            return new MoreAppWindowHolder(rl);
        }

        @Override
        public void onBindViewHolder(@NonNull MoreAppWindowHolder holder, int position) {
            holder.setData(data.get(position));
        }

        @Override
        public int getItemCount() {
            return data.size();
        }
    }

    static class MoreAppWindowHolder extends RecyclerView.ViewHolder {
        LinearLayout rlRoot;
        ImageView left_img;
        TextView right_txt;
        Setting.ApkInfo data;
        public MoreAppWindowHolder(@NonNull View itemView) {
            super(itemView);
            rlRoot = (LinearLayout) itemView;
            left_img = rlRoot.findViewById(R.id.left_img);
            right_txt= rlRoot.findViewById(R.id.right_txt);
            rlRoot.setOnClickListener(
                    (v)-> {
                        cc.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id="+data.getPack_name())));
                    }
            );
        }

        public void setData(final Setting.ApkInfo d) {
            this.data = d;
            right_txt.setText(this.data.getTitle());
            if (this.data != null && this.data.getImage() != null) {
                Glide.with(cc).load(this.data.getImage()).into(left_img);
            }
        }
    }

    static public class RecycleViewDivider extends RecyclerView.ItemDecoration {

        private Paint mPaint;
        private Drawable mDivider;
        private int mDividerHeight = 2;//分割线高度，默认为1px
        private int mOrientation;//列表的方向：LinearLayoutManager.VERTICAL或LinearLayoutManager.HORIZONTAL
        private static final int[] ATTRS = new int[]{android.R.attr.listDivider};

        /**
         * 默认分割线：高度为2px，颜色为灰色
         *
         * @param context
         * @param orientation 列表方向
         */
        public RecycleViewDivider(Context context, int orientation) {
            if (orientation != LinearLayoutManager.VERTICAL && orientation != LinearLayoutManager.HORIZONTAL) {
                throw new IllegalArgumentException("请输入正确的参数！");
            }
            mOrientation = orientation;

            final TypedArray a = context.obtainStyledAttributes(ATTRS);
            mDivider = a.getDrawable(0);
            a.recycle();
        }

        /**
         * 自定义分割线
         *
         * @param context
         * @param orientation 列表方向
         * @param drawableId  分割线图片
         */
        public RecycleViewDivider(Context context, int orientation, int drawableId) {
            this(context, orientation);
            mDivider = ContextCompat.getDrawable(context, drawableId);
            mDividerHeight = mDivider.getIntrinsicHeight();
        }

        /**
         * 自定义分割线
         *
         * @param context
         * @param orientation   列表方向
         * @param dividerHeight 分割线高度
         * @param dividerColor  分割线颜色
         */
        public RecycleViewDivider(Context context, int orientation, int dividerHeight, int dividerColor) {
            this(context, orientation);
            mDividerHeight = dividerHeight;
            mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            mPaint.setColor(dividerColor);
            mPaint.setStyle(Paint.Style.FILL);
        }


        //获取分割线尺寸
        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            super.getItemOffsets(outRect, view, parent, state);
            outRect.set(0, 0, 0, mDividerHeight);
        }

        //绘制分割线
        @Override
        public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
            super.onDraw(c, parent, state);
            if (mOrientation == LinearLayoutManager.VERTICAL) {
                drawVertical(c, parent);
            } else {
                drawHorizontal(c, parent);
            }
        }

        //绘制横向 item 分割线
        private void drawHorizontal(Canvas canvas, RecyclerView parent) {
            final int left = parent.getPaddingLeft();
            final int right = parent.getMeasuredWidth() - parent.getPaddingRight();
            final int childSize = parent.getChildCount();
            for (int i = 0; i < childSize; i++) {
                final View child = parent.getChildAt(i);
                RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) child.getLayoutParams();
                final int top = child.getBottom() + layoutParams.bottomMargin;
                final int bottom = top + mDividerHeight;
                if (mDivider != null) {
                    mDivider.setBounds(left+10, top, right-10, bottom);
                    mDivider.draw(canvas);
                }
                if (mPaint != null) {
                    canvas.drawRect(left, top, right, bottom, mPaint);
                }
            }
        }

        //绘制纵向 item 分割线
        private void drawVertical(Canvas canvas, RecyclerView parent) {
            final int top = parent.getPaddingTop();
            final int bottom = parent.getMeasuredHeight() - parent.getPaddingBottom();
            final int childSize = parent.getChildCount();
            for (int i = 0; i < childSize; i++) {
                final View child = parent.getChildAt(i);
                RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) child.getLayoutParams();
                final int left = child.getRight() + layoutParams.rightMargin;
                final int right = left + mDividerHeight;
                if (mDivider != null) {
                    mDivider.setBounds(left, top, right, bottom);
                    mDivider.draw(canvas);
                }
                if (mPaint != null) {
                    canvas.drawRect(left, top, right, bottom, mPaint);
                }
            }
        }
    }
}
