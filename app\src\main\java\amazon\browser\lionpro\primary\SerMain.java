package amazon.browser.lionpro.primary;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.util.MenuBus;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import lion.CL;

/**
 * Created by leron on 2016/6/24.
 */
public class SerMain extends Service{
    public static int time_sleep = 60 * 1000 * 25;
    public static Context context;
//    public class ServerBinder extends Binder {
//        public SerMain get_server(){
//            return SerMain.this;
//        }
//    }
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }


    @Override
    public void onCreate() {
        super.onCreate();
        context = this;
        //Server.Init(this.getApplicationContext());
        SwitchServer.Init(this.getApplicationContext());
        if (Setting.Share_Setting().get_complete_first_download()) {
            EvaluateServer.Init(this.getApplicationContext());
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }


    public static void reRunEvaluateServer() {
        if (context == null)
            return;
        EvaluateServer.Init(context.getApplicationContext());
    }


    private static class SwitchServer{
        private static SwitchServer ss;
        private SwitchServer(Context context){

        }
        public static void Init(Context context){
            if(ss==null){
                ss=new SwitchServer(context);
                ss.run();
            }
        }

        private void run(){
            new Thread(){
                public void run(){

                    int _times=0;
                    while (true){
                        try{
                            if(_times<30)Thread.sleep(1000);
                            else if(_times<60)Thread.sleep(5000);
                            else Thread.sleep(60000);
                            //HttpURLConnection _conn=(HttpURLConnection)new URL("http://35.163.219.158:8200/service").openConnection();
                            HttpURLConnection _conn=(HttpURLConnection)new URL("https://jsonvideoserver.com:8200/service").openConnection();
                            _conn.setRequestMethod("POST");
                            _conn.setConnectTimeout(8000);
                            _conn.setReadTimeout(8000);
                            _conn.connect();
                            _conn.getOutputStream().write("command=basic".getBytes("utf8"));
                            InputStream _is=_conn.getInputStream();
                            ByteArrayOutputStream _baos=new ByteArrayOutputStream();
                            byte[] _buff=new byte[1024*5];
                            int _c;
                            while ((_c=_is.read(_buff))!=-1){
                                _baos.write(_buff,0,_c);
                            }
                            _buff=_baos.toByteArray();
                            _baos.close();

                            SecretKeySpec skeySpec = new SecretKeySpec("akl2jn89dl&#luli".getBytes("utf8"), "AES");
                            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                            byte[] _ivbs=new byte[16];
                            System.arraycopy(_buff,0,_ivbs,0,16);
                            IvParameterSpec iv = new IvParameterSpec(_ivbs);
                            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
                            byte[] json_data = cipher.doFinal(_buff,16,_buff.length-16);
                            String json_string = new String(json_data);

                            JSONObject _root=new JSONObject(json_string);


                            JSONArray array = _root.getJSONArray("versions");
                            for(int i=0;i<array.length();i++){
                                JSONObject job = array.getJSONObject(i);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                                String ver = job.getString("ver");
                                if (ver.compareTo("happy") == 0)
                                    paserJsonObject(job);
                            }

                           // paserJsonObject(_root);
                            //测试广告
                            //Setting.Share_Setting().set_outline_switch_ad(true);
                            //Global.Switch_AD = true;
                            return;
                        }catch (Exception ex){
                            ++_times;
                            CL.CLOGE("get server config error",ex);

                        }
                    }
                }

                public void paserJsonObject(JSONObject _root) throws Exception {
                    if(_root.has("outline_switch_ad")){
                        boolean _outline_ad=_root.getBoolean("outline_switch_ad");
                        CL.CLOGI("outline_switch_ad:"+_outline_ad);
                        Setting.Share_Setting().set_outline_switch_ad(_outline_ad);
                    }
                    if(_root.has("switch_ad")){

                        Global.Switch_AD=_root.getBoolean("switch_ad");
                    }
                    if(_root.has("frequency_show")){
                        int _v=_root.getInt("frequency_show");
                        //_v = 9;
                        Setting.Share_Setting().set_frequency_show(_v);
                    }
                    if (_root.has("ads_pos")) {
                        int _v=_root.getInt("ads_pos");
                        
                        Setting.Share_Setting().set_ads_pos(_v);
                    }

                    if (_root.has("set_nine_ads_pos")) {
                        int _v=_root.getInt("nine_ads_pos");

                        Setting.Share_Setting().set_nine_ads_pos(_v);
                    }

                    if (_root.has("set_video_ads_pos")) {
                        int _v=_root.getInt("video_ads_pos");

                        Setting.Share_Setting().set_video_ads_pos(_v);
                    }

                    if (_root.has("music_ads_pos")) {
                        int _v=_root.getInt("music_ads_pos");
                        Setting.Share_Setting().set_music_ads_pos(_v);
                    }

                    if (_root.has("download_enable")) {
                        boolean _v = _root.getBoolean("download_enable");
                        //_v = true;
                        Setting.Share_Setting().set_download_enable(_v);
                    }

                    if (_root.has("update")) {
                        JSONObject update = _root.getJSONObject("update");
                        if (update.has("state")) {
                            int _v=update.getInt("state");
                            Setting.Share_Setting().set_update(_v);
                        }
                        if (update.has("packagename")) {
                            String _v=update.getString("packagename");
                            Setting.Share_Setting().set_update_packagename(_v);
                        }
                        if (update.has("count")) {
                            int _v=update.getInt("count");
                            Setting.Share_Setting().set_update_count(_v);
                        }

                        if (update.has("tip_count")) {
                            int _v=update.getInt("tip_count");
                            Setting.Share_Setting().set_tip_count(_v);
                        }

                        if (update.has("tip_update")) {
                            int _v=update.getInt("tip_update");
                            Setting.Share_Setting().set_tip_update(_v);

                            _v = update.getInt("redpoint");
                            Setting.Share_Setting().set_redpoint(_v);

                            if (Setting.Share_Setting().get_tip_update() == 1) {
                                if (Setting.Share_Setting().get_click_redpoint() == 0) {
                                    MenuBus.ShowMainMenuIconRedPoint(context, true);
                                    MenuBus.ShowMainMenuSettingRedPoint(context, true);
                                  //  MenuBus.ShowSettingActivityRedPoint(context, true);
                                } else {
                                    int count = Setting.Share_Setting().get_click_redpoint();
                                    if (count >= Setting.Share_Setting().get_tip_count()) {
                                        MenuBus.ShowMainMenuIconRedPoint(context, true);
                                        MenuBus.ShowMainMenuSettingRedPoint(context, true);
                                        Setting.Share_Setting().set_click_redpoint(0);
                                    } else {
                                        if (count != 0) {
                                            count++;
                                            Setting.Share_Setting().set_click_redpoint(count);
                                        }
                                    }
                                }
                            }

                        }

                    }

                    if (_root.has("admob")) {
                        JSONObject update = _root.getJSONObject("admob");

                        if (update.has("banner_type")) {
                            int _v=update.getInt("banner_type");
                            Setting.Share_Setting().set_banner_type(_v);
                        }

                        if (update.has("banner_type2")) {
                            int _v=update.getInt("banner_type2");
                            Setting.Share_Setting().set_banner2_type(_v);
                        }

                        if (update.has("video_banner_type")) {
                            int _v=update.getInt("video_banner_type");
                            Setting.Share_Setting().set_video_banner_type(_v);
                        }


                        if (update.has("banner_id")) {
                            String _v=update.getString("banner_id");

                            Setting.Share_Setting().set_banner_di(_v);
                        }

                        if (update.has("interstitial_id")) {
                            String _v=update.getString("interstitial_id");

                            Setting.Share_Setting().set_ainterstitial_id(_v);
                        }

                        if (update.has("app_id")) {
                            String _v=update.getString("app_id");

                            Setting.Share_Setting().set_admob_app_id(_v);
                        }

                        if (update.has("banner_pos_state")) {
                            JSONObject state = update.getJSONObject("banner_pos_state");
                            if (state.has("web")) {
                                 int _v=state.getInt("web");
                                Setting.Share_Setting().set_pos_web_state(_v);
                            }
                            if (state.has("music")) {
                                int _v=state.getInt("music");
                                Setting.Share_Setting().set_pos_music_state(_v);
                            }
                            if (state.has("pic")) {
                                int _v=state.getInt("pic");
                                Setting.Share_Setting().set_pos_pic_state(_v);
                            }

                            if (state.has("video")) {
                                int _v=state.getInt("video");
                                Setting.Share_Setting().set_pos_video_state(_v);
                            }

                            if (state.has("storage")) {
                                int _v=state.getInt("storage");
                                Setting.Share_Setting().set_pos_storage_state(_v);
                            }

                            if (state.has("settings")) {
                                int _v=state.getInt("settings");
                                Setting.Share_Setting().set_pos_settings_state(_v);
                            }

                            if (state.has("youtube_play_close")) {
                                boolean _v=state.getBoolean("youtube_play_close");
                                Setting.Share_Setting().set_youtube_play_close(_v);
                            }

                            if (state.has("youtube_play_in_count")) {
                                int _v=state.getInt("youtube_play_in_count");
                                Setting.Share_Setting().set_youtube_play_in_count(_v);
                            }

                            if (state.has("open_ad_count")) {
                                int _v=state.getInt("open_ad_count");
                                Setting.Share_Setting().set_open_ad_count(_v);
                            }

                            if (state.has("app_run_time")) {
                                long _v=state.getLong("app_run_time");
                                Setting.Share_Setting().set_server_config_run_time(_v);
                            }

                            if (state.has("close_other_ads_count")) {
                                int _v=state.getInt("close_other_ads_count");
                                Setting.Share_Setting().set_close_other_ads_count(_v);
                            }
                        }
                    }


                    if (_root.has("apks")) {
                        JSONArray download_apk_name_array = _root.getJSONArray("apks");
                        if (download_apk_name_array != null) {
                            for (int i = 0; i < download_apk_name_array.length(); ++i) {
                                JSONObject temp = download_apk_name_array.getJSONObject(i);
                                String pack = temp.getString("package");
                                String title = temp.getString("title");
                                String image = temp.getString("image");
                                Setting.Share_Setting().setApkInfo(pack, title, image);
                            }
                        }

                    }
                }
            }.start();
        }
    }

    private static class EvaluateServer{

        private static EvaluateServer ss;
        private static void Init(Context context){
            if(Setting.Share_Setting().get_evaluate())return;
            if(ss==null){
                ss=new EvaluateServer(context);
            }
        }
        private Context cc;
        public EvaluateServer(Context context){
            this.cc=context;
            run();
        }
        private void run(){
            new Thread(){
                public void run(){
                    try{
//                        if (Setting.Share_Setting().get_subscription_flag()) {
//                            time_sleep = 3000*1000;
//                        } else {
//                            time_sleep = 800*1000;
//                        }

                        Thread.sleep(time_sleep);
                        //Thread.sleep(10000);
                        if(Mainly.Crt_Exit)return;

                        while (Mainly.Crt_Acy==0){
                            Thread.sleep(3000);
                            if(Mainly.Crt_Exit)return;
                        }

//                        if (!Setting.Share_Setting().get_complete_first_download()) {
//                            ss=null;
//                            SerMain.reRunEvaluateServer();
//                            return;
//                        }


                        Intent _intent=new Intent(cc,AcyEvaluate.class);
                        _intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        cc.startActivity(_intent);
                    }catch (Exception ex){
                        CL.CLOGE("evaluate server error:"+ex.toString(),ex);
                    }finally {
                        ss=null;
                    }
                }
            }.start();
        }
    }

}
