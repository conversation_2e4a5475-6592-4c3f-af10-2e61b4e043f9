package amazon.browser.lionpro.primary;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.os.IBinder;
import android.os.PowerManager;
import androidx.annotation.Nullable;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.FileManager;
import amazon.browser.lionpro.downloader.Server;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;

import lion.CL;
import lion.CLCallback;
import lion.CLFileSystem;
import lion.CLTools;
import lion.web.CLHS;
import lion.web.CLRequest;
import lion.web.CLResponse;

/**
 * Created by leron on 2016/8/31.
 */
public class SerWifiShare extends Service{

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }


    @Override
    public void onCreate() {
        super.onCreate();
        this.cc=this.getApplicationContext();
        if(server==null) {
            PowerManager pm = (PowerManager) this.getSystemService(Context.POWER_SERVICE);
            wake_lock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "clws");
            wake_lock.acquire();

            server=new WebServer();
            CL.CLOGI("Wifi share Server was create!");
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        wake_lock.release();
        Destory_Web_Server();
        server=null;
        CL.CLOGI("Wifi share Server was destory!");
    }

    private String get_string(int id){
        return cc.getResources().getString(id);
    }

    private Context cc;
    private PowerManager.WakeLock wake_lock;
    private static WebServer server;
    public static void Start_Web_Server(String pwd, CLCallback.CB_TF cber){
        if(server!=null){
            server.start_server(pwd,cber);
        }
    }
    public static void Destory_Web_Server(){
        if(server!=null){
            server.destory_server();
        }
    }


    private class WebServer implements CLHS.Eventer{

        private String access_pwd=null;
        private CLCallback.CB_TF cber=null;
        private CLHS wserver;
        private Set<String> sessions;

        private FileManager file_mgr;
        private File dir_common,dir_thumb;
        private ArrayList<File> files_picture,files_gif;
        private ArrayList<Data.StructDLItem> files_video,files_music,files_doc,files_apk,files_other;

        private WebServer(){
            sessions=new HashSet<>();
        }

        public void start_server(final String pwd, final CLCallback.CB_TF cb){
            wserver=new CLHS(this);
            new Thread(){
                public void run(){
                    try {
                        File _dir=new File(cc.getFilesDir(),"common");
                        if(!_dir.exists())_dir.mkdir();
                        AssetManager _mgr=cc.getAssets();
                        String[] _fs=_mgr.list("common");
                        for(int i=0;i<_fs.length;++i){
                            File _target=new File(_dir,_fs[i]);
                            if(!_target.exists()){
                                InputStream _is=_mgr.open("common/"+_fs[i]);
                                CLFileSystem.Copy_File(_is,_target);
                            }
                        }
                        dir_common=_dir;
                        dir_thumb=Global.Dir_thum;
                        //init files
                        file_mgr= Server.Share_FileManager();
                        ArrayList<Struct.StoreDir> _ds= file_mgr.get_datas();
                        for(int i=0;i<_ds.size();++i){
                            Struct.StoreDir _d=_ds.get(i);
                            if(_d.type == 3){
                                files_picture=_d.files;
                            }else if(_d.type==1){
                                files_video=_d.dls;
                            }else if(_d.type == 4){
                                files_gif=_d.files;
                            }else if(_d.type==2){
                                files_music=_d.dls;
                            }else if(_d.type == 5){
                                files_doc=_d.dls;
                            }else if(_d.type == 6){
                                files_apk=_d.dls;
                            }else if(_d.type == 7){
                                files_other=_d.dls;
                            }
                        }

                        access_pwd=pwd;
                        cber=cb;
                        wserver.go();
                    }catch (Exception ex){
                        CL.CLOGE("copy file error:"+ex.toString(),ex);
                        cber.on_callback_fail(-1,"init fail");
                    }
                }
            }.start();
        }
        public void destory_server(){
            if(wserver!=null)wserver.stop();
            sessions.clear();
        }

        private void put_menu_json(JSONObject root)throws Exception{
            root.put("tip_video",get_string(R.string.store_video))
                    .put("tip_music",get_string(R.string.store_music))
                    .put("tip_picture",get_string(R.string.store_image))
                    .put("tip_gif",get_string(R.string.store_gif))
                    .put("tip_doc",get_string(R.string.store_doc))
                    .put("tip_apk",get_string(R.string.store_apk))
                    .put("tip_other",get_string(R.string.store_other));
        }


        @Override
        public void on_server_start() {
            CL.CLOGI("web server was start");
            if(cber!=null)cber.on_callback_success();
        }
        @Override
        public void on_server_exit() {
            CL.CLOGI("web server was exit");
        }

        @Override
        public void on_handle(CLRequest req, CLResponse rsp) throws Exception{
//            CL.CLOGI("^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^-----------------------------------");
//            CL.CLOGI("request uri:"+req.uri);
//            CL.CLOGI("request uri_path:"+req.uri_path);
//            CL.CLOGI("requset uri_file_name:"+req.uri_file_name);
//            CL.CLOGI("request method:"+req.method);
//            for(Map.Entry<String,String> item:req.header.entrySet()){
//                CL.CLOGI("header key:"+item.getKey()+" value:"+item.getValue());
//            }
//            for(Map.Entry<String,String> item:req.args.entrySet()){
//                CL.CLOGI("arg key:"+item.getKey()+" value:"+item.getValue());
//            }
//            CL.CLOGI("^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^-----------------------------------");

            if(req.uri_path.equals("/")) {
                if(req.uri_file_name!=null && req.uri_file_name.equals("favicon.ico")){
                    File _f=new File(dir_common,"favicon.png");
                    if(_f.exists()){
                        rsp.set_data(_f);
                        rsp.do_response();
                    }
                    return;
                }
                String _user_agent = req.header.get("User-Agent");
                if (_user_agent == null || _user_agent.toLowerCase().contains("mobile")) {
                    rsp.do_redirect("/common/pc.html");
                } else {
                    rsp.do_redirect("/common/pc.html");
                }
            }else if(req.uri_path.equals("/common")){
                if(req.uri_file_name!=null){
                    File _f=new File(dir_common,req.uri_file_name);
                    if(_f.exists()){
                        rsp.set_data(_f);
                        rsp.do_response();
                    }
                }
            }else if(req.uri_path.equals("/ajax")){
                if(req.args.containsKey("command")) {
                    String _cmd=req.args.get("command");
                    CL.CLOGI("++++++++++ on ajax request ++++++++++:"+_cmd);
                    if(_cmd.equals("need_login")){
                        String _token=req.args.get("token");
                        CL.CLOGI("need login:"+_token);
                        for (String _item : sessions) {
                            CL.CLOGI("session:"+_item);
                        }
                        if(_token!=null && sessions.contains(_token)){
                            CL.CLOGI("need login false");
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            _root.put("need_login",false);
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }
                        else if(access_pwd==null){
                            CL.CLOGI("need login false");
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            _root.put("need_login",false);
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }
                        else {
                            CL.CLOGI("need login true");
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            _root.put("need_login",true).put("tip_title",get_string(R.string.tip_web_enter_pwd)).put("tip_ok",get_string(R.string.yes));
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }
                    }else if(_cmd.equals("login")){
                        String _pwd=req.args.get("password");
                        String _token=req.args.get("token");
                        CL.CLOGI("login token:"+_token+" pwd:"+_pwd+" server_pwd:"+access_pwd);
                        if(access_pwd!=null) {
                            if(_token!=null && sessions.contains(_token)){
                                rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                                rsp.add_header("Access-Control-Allow-Origin", "*");
                                JSONObject _root = new JSONObject();
                                _root.put("status", true);
                                _root.put("token",_token);
                                put_menu_json(_root);
                                rsp.set_data(_root.toString().getBytes("utf8"));
                                rsp.do_response();
                                CL.CLOGI("login 1");
                            }else if (_pwd != null && _pwd.equals(access_pwd)) {
                                rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                                rsp.add_header("Access-Control-Allow-Origin", "*");
                                JSONObject _root = new JSONObject();
                                _root.put("status", true);
                                String _tn=CLTools.Get_MD5(System.currentTimeMillis()+"::"+(new Random(System.currentTimeMillis()).nextLong()));
                                sessions.add(_tn);
                                _root.put("token", _tn);
                                put_menu_json(_root);
                                rsp.set_data(_root.toString().getBytes("utf8"));
                                rsp.do_response();
                                CL.CLOGI("login 2");
                            }else{
                                rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                                rsp.add_header("Access-Control-Allow-Origin", "*");
                                JSONObject _root = new JSONObject();
                                _root.put("status", false);
                                rsp.set_data(_root.toString().getBytes("utf8"));
                                rsp.do_response();
                                CL.CLOGI("login 3");
                            }
                        }else{
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root = new JSONObject();
                            _root.put("status", true);
                            String _tn=CLTools.Get_MD5(System.currentTimeMillis()+"::"+(new Random(System.currentTimeMillis()).nextLong()));
                            sessions.add(_tn);
                            _root.put("token", _tn);
                            put_menu_json(_root);
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                            CL.CLOGI("login 22");
                        }
                    }
                    else if(_cmd.equals("verification")){
                        String _token=req.args.get("token");
                        rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                        rsp.add_header("Access-Control-Allow-Origin", "*");
                        JSONObject _root=new JSONObject();
                        if(_token==null || !sessions.contains(_token)){
                            _root.put("need_login",true);
                        }else{
                            _root.put("need_login",false);
                        }
                        rsp.set_data(_root.toString().getBytes("utf8"));
                        rsp.do_response();
                    }
                    else if(_cmd.equals("data")){
                        String _category=req.args.get("category");
                        CL.CLOGI("->->->->->->->->:"+_category);
                        if(_category==null)return;
                        if(_category.equals("video")) {
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_video.size();++i){
                                Data.StructDLItem _d=files_video.get(i);
                                JSONObject _item=new JSONObject();
                                _item.put("name",_d.name!=null?_d.name:_d.title);
                                _item.put("thumb","/video?category=thumb&path="+_d.path.replace('/','_'));
                                _item.put("src_path","/video?category=play&path="+_d.path);
                                _item.put("download_url","/download?path="+_d.path+"&name="+_item.getString("name")+"."+Data.Get_Type_Suffix(_d.type_minor));
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }else if(_category.equals("music")){
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_music.size();++i){
                                Data.StructDLItem _d=files_music.get(i);
                                JSONObject _item=new JSONObject();
                                _item.put("name",_d.name!=null?_d.name:_d.title);
                                _item.put("src_path","/music?path="+_d.path);
                                _item.put("download_url","/download?path="+_d.path+"&name="+_item.getString("name")+"."+Data.Get_Type_Suffix(_d.type_minor));
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }else if(_category.equals("picture")){
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_picture.size();++i){
                                JSONObject _item=new JSONObject();
                                _item.put("src_path","/picture/"+files_picture.get(i).getName());
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }else if(_category.equals("gif")){
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_gif.size();++i){
                                JSONObject _item=new JSONObject();
                                _item.put("src_path","/gif/"+files_gif.get(i).getName());
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }else if(_category.equals("doc")){
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_doc.size();++i){
                                Data.StructDLItem _d=files_doc.get(i);
                                JSONObject _item=new JSONObject();
                                _item.put("name",_d.name!=null?_d.name:_d.title);
                                _item.put("download_url","/download?path="+_d.path+"&name="+_item.getString("name")+"."+_d.suffix);
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }else if(_category.equals("apk")){
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_apk.size();++i){
                                Data.StructDLItem _d=files_apk.get(i);
                                JSONObject _item=new JSONObject();
                                _item.put("name",_d.name!=null?_d.name:_d.title);
                                _item.put("thumb","/apk?category=thumb&path="+_d.path.replace('/','_'));
                                _item.put("download_url","/download?path="+_d.path+"&name="+_item.getString("name")+".apk");
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }else if(_category.equals("other")){
                            rsp.set_status_code(CLResponse.STATUS_CODE.Code_200);
                            rsp.add_header("Access-Control-Allow-Origin", "*");
                            JSONObject _root=new JSONObject();
                            JSONArray _as=new JSONArray();
                            _root.put("datas",_as);
                            for(int i=0;i<files_other.size();++i){
                                Data.StructDLItem _d=files_other.get(i);
                                JSONObject _item=new JSONObject();
                                _item.put("name",_d.name!=null?_d.name:_d.title);
                                _item.put("download_url","/download?path="+_d.path+"&name="+_item.getString("name")+"."+_d.suffix);
                                _as.put(_item);
                            }
                            rsp.set_data(_root.toString().getBytes("utf8"));
                            rsp.do_response();
                        }
                    }
                }
            }
            else if(req.uri_path.equals("/picture")){
                if(req.uri_file_name!=null){
                    boolean _find=false;
                    File _dir=file_mgr.get_major_dir(Data.Type_Picture);
                    if(_dir!=null){
                        File _f=new File(_dir,req.uri_file_name);
                        if(_f.exists()) {
                            rsp.set_content_type("image/*");
                            rsp.set_data(_f);
                            rsp.do_response();
                            _find=true;
                        }
                    }
                    if(!_find) {
                        _dir = file_mgr.get_minor_dir(Data.Type_Picture);
                        if (_dir != null) {
                            File _f = new File(_dir, req.uri_file_name);
                            if (_f.exists()) {
                                rsp.set_content_type("image/*");
                                rsp.set_data(_f);
                                rsp.do_response();
                            }
                        }
                    }
                }
            }
            else if(req.uri_path.equals("/gif")){
                if(req.uri_file_name!=null){
                    boolean _find=false;
                    File _dir=file_mgr.get_major_dir(Data.Type_Image_GIF);
                    if(_dir!=null){
                        File _f=new File(_dir,req.uri_file_name);
                        if(_f.exists()) {
                            rsp.set_content_type("image/*");
                            rsp.set_data(_f);
                            rsp.do_response();
                            _find=true;
                        }
                    }
                    if(!_find) {
                        _dir = file_mgr.get_minor_dir(Data.Type_Image_GIF);
                        if (_dir != null) {
                            File _f = new File(_dir, req.uri_file_name);
                            if (_f.exists()) {
                                rsp.set_content_type("image/*");
                                rsp.set_data(_f);
                                rsp.do_response();
                            }
                        }
                    }
                }
            }
            else if(req.uri_path.equals("/video")){
                String _category=req.args.get("category");
                String _path=req.args.get("path");
                if(_category!=null){
                    if(_category.equals("thumb")){
                        CL.CLOGI("video thumb:"+_path);
                        File _f=new File(dir_thumb,_path);
                        if(_f.exists()) {
                            rsp.set_content_type("image/*");
                            rsp.set_data(_f);
                            rsp.do_response();
                        }
                    }else if(_category.equals("play")){
                        CL.CLOGI("video play:"+_path);
                        File _f=new File(_path);
                        if(_f.exists()){
                            if(req.request_range==null) {
                                rsp.set_content_type("video/mp4");
                                rsp.set_data(_f);
                                rsp.do_response();
                            }else{
                                if(req.request_range.range_end>_f.length()-1)req.request_range.range_end=_f.length()-1;
                                if(req.request_range.range_end>=req.request_range.range_start) {
                                    long _content_length = req.request_range.range_end - req.request_range.range_start + 1;
                                    rsp.set_status_code(CLResponse.STATUS_CODE.Code_206);
                                    rsp.set_content_length(String.valueOf(_content_length));
                                    rsp.set_content_range(req.request_range.range_start,req.request_range.range_end,_f.length());
                                    rsp.set_content_type("video/mp4");
                                    rsp.do_response();
                                    InputStream _is=new FileInputStream(_f);
                                    OutputStream _os=rsp.get_output_stream();
                                    byte[] _buff=new byte[1024*64];
                                    long _send_count=0;
                                    int _c;
                                    _is.skip(req.request_range.range_start);
                                    while ((_c=_is.read(_buff))!=-1){
                                        _send_count+=_c;
                                        if(_send_count<_content_length)_os.write(_buff,0,_c);
                                        else {
                                            _os.write(_buff,0,_c-(int)(_send_count-_content_length));
                                            break;
                                        }
                                    }
                                    _is.close();
                                }
                            }
                        }
                    }
                }
            }
            else if(req.uri_path.equals("/music")){
                String _path=req.args.get("path");
                if(_path!=null){
                    CL.CLOGI("music play:"+_path);
                    File _f=new File(_path);
                    if(_f.exists()){
                        if(req.request_range==null) {
                            rsp.set_content_type("audio/*");
                            rsp.set_data(_f);
                            rsp.do_response();
                        }else{
                            if(req.request_range.range_end>_f.length()-1)req.request_range.range_end=_f.length()-1;
                            if(req.request_range.range_end>=req.request_range.range_start) {
                                long _content_length = req.request_range.range_end - req.request_range.range_start + 1;
                                rsp.set_status_code(CLResponse.STATUS_CODE.Code_206);
                                rsp.set_content_length(String.valueOf(_content_length));
                                rsp.set_content_range(req.request_range.range_start,req.request_range.range_end,_f.length());
                                rsp.set_content_type("audio/*");
                                rsp.do_response();
                                InputStream _is=new FileInputStream(_f);
                                OutputStream _os=rsp.get_output_stream();
                                byte[] _buff=new byte[1024*64];
                                long _send_count=0;
                                int _c;
                                _is.skip(req.request_range.range_start);
                                while ((_c=_is.read(_buff))!=-1){
                                    _send_count+=_c;
                                    if(_send_count<_content_length)_os.write(_buff,0,_c);
                                    else {
                                        _os.write(_buff,0,_c-(int)(_send_count-_content_length));
                                        break;
                                    }
                                }
                                _is.close();
                            }
                        }
                    }
                }
            }
            else if(req.uri_path.equals("/apk")){
                String _category=req.args.get("category");
                String _path=req.args.get("path");
                if(_category!=null && _category.equals("thumb") && _path!=null){
                    CL.CLOGI("apk thumb:"+_path);
                    File _f=new File(dir_thumb,_path);
                    if(_f.exists()) {
                        rsp.set_content_type("image/*");
                        rsp.set_data(_f);
                        rsp.do_response();
                    }
                }
            }
            else if(req.uri_path.equals("/download")){
                String _path=req.args.get("path");
                String _download_name=req.args.get("name");
                CL.CLOGI("download : "+_path);
                CL.CLOGI("downlaod : "+_download_name);

                File _file=new File(_path);
                if(_file.exists()) {
                    rsp.add_header("Content-Disposition", "attachment; filename=\""+_download_name+"\"");
                    rsp.set_content_type("*/*");
                    rsp.set_data(_file);
                    rsp.do_response();
                }
            }
        }
    }

}
