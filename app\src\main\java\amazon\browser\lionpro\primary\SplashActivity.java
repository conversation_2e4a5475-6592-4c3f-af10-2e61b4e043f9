package amazon.browser.lionpro.primary;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.Window;

import androidx.annotation.Nullable;

import amazon.browser.video.downloader.R;

public class SplashActivity extends Activity {
    private static final int SPLASH_LENGTH = 2000 ;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.splashlayout);
        new Handler().postDelayed(new Runnable() {
            public void run() {
                Intent intent = new Intent(SplashActivity.this, AcyMain.class);
                startActivity(intent);
                //关闭splashActivity，避免按返回键返回此界面
                SplashActivity.this.finish();
            }
        }, SPLASH_LENGTH);

    }
}
