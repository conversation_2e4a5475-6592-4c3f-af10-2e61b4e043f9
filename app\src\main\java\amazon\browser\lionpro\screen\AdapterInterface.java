package amazon.browser.lionpro.screen;

import android.content.Context;
import android.view.View;

import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;


class AdapterInterface {

    public interface AdapterParamGet<T> {
        Context GetContext();
        T GetData();
        OnHolderItemClickListener GetHolderItemClickListerer();
        CommonDownloader.Eventer GetDlEventer();
    }

    public interface OnHolderItemClickListener {
        void onItemClick(View view, Data.StructDLItem item, int position);
    }

}
