package amazon.browser.lionpro.screen;


import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.views.KProgressHUD.KProgressHUD;
import amazon.browser.lionpro.views.Leida;
import amazon.browser.lionpro.views.RecycleViewDivider;
import amazon.browser.lionpro.views.WrapRecyclerView;
import com.google.android.gms.ads.OnUserEarnedRewardListener;
import com.google.android.gms.ads.rewarded.RewardItem;
import com.kongzue.dialogx.dialogs.BottomMenu;
import com.kongzue.dialogx.dialogs.PopMenu;
import com.kongzue.dialogx.interfaces.OnIconChangeCallBack;
import com.kongzue.dialogx.interfaces.OnMenuItemClickListener;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Iterator;
import java.util.concurrent.CopyOnWriteArrayList;

import gp.BillingActivity;
import lion.CL;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLToast;
import lion.CLTools;

public class CacheList extends LinearLayout implements  CLCallback.CB_TFO<Integer>, OnUserEarnedRewardListener {

    private Activity cc;
    private Leida lei_da;
    private CLCallback.CB_Activity cber_acy;
    private CopyOnWriteArrayList<CopyOnWriteArrayList<Data.StructDLItem>> mData;
    private Handler wait_handler = new Handler();
    private Handler handler;
    private int list_type;
    private ShowCacheAdapter mViewPaper2Adapter;
    private TextView leftTitle, rightTitle;
    private boolean bEarned;
    private String ident;
    private KProgressHUD hud;
    private LinearLayout dl_content;
    private LinearLayout winlist_content;
    private WrapRecyclerView win_list = null;
    private AdapterForWinItem win_adapter;
    private CopyOnWriteArrayList<Struct.StructWebsite> datas_winlist;
    private Struct.StructWebsite current_item;
    private TextView title;
    private Runnable runnable1 = new Runnable() {
        @Override
        public void run() {
                wait_handler.removeCallbacks(runnable1);
                hideWait();
        }
    };


    public void hideWaitTask() {
        if (wait_handler != null) {
            wait_handler.postDelayed(runnable1,6000);
        }
    }

    public void removeWaitTask() {
        if (wait_handler != null) {
            wait_handler.removeCallbacks(runnable1);
        }
    }

    public void showWait() {
        if (hud == null) {
            hud = KProgressHUD.create(cc)
                    .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE);
            hud.setCancellable(false);
        }

        if (hud != null) {
            hud.show();
        }
    }

    public void hideWait() {
        CL.CLOGI("Eddy hideWait");
        if (hud != null) {
            hud.dismiss();
        }
    }

    public CacheList(Activity context, Leida leida, CLCallback.CB_Activity cber_activity) {
        super(context);
        this.cc = context;
        this.lei_da = leida;
        this.cber_acy = cber_activity;
        handler=new Handler(Looper.getMainLooper());
        mData = new CopyOnWriteArrayList<>();
        mData.add(new CopyOnWriteArrayList<>());
        mData.add(new CopyOnWriteArrayList<>());
        initView();
        Global.setRewardCB(this);
    }

    public void initView() {
        this.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));
        dl_content=new LinearLayout(cc);
        dl_content.setOrientation(LinearLayout.VERTICAL);
        dl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));
        //this.addView(dl_content);

        View view = LayoutInflater.from(cc).inflate(R.layout.dlviewpaperlayout, null);
        CommonBackButton backArrow = view.findViewById(R.id.backArrow);
        backArrow.setCB(new CLCallback.CB() {
            @Override
            public void on_callback() {
                cber_acy.on_close();
            }
        });
        dl_content.addView(view);
        this.addView(dl_content);

        leftTitle = view.findViewById(R.id.left_title);
        rightTitle = view.findViewById(R.id.right_title);
        ViewPager2 viewpager2 = view.findViewById(R.id.viewpager2);
        viewpager2.setFocusableInTouchMode(false);
        mViewPaper2Adapter = new ShowCacheAdapter(cc, clickListener, listener_downloader, mData);
        viewpager2.setAdapter(mViewPaper2Adapter);
        viewpager2.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels);
            }

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                if (position == 0) {
                    leftTitle.setTextColor(cc.getResources().getColor(R.color.dl_title_color));
                    rightTitle.setTextColor(cc.getResources().getColor(R.color.font_txt_color));
                } else {
                    leftTitle.setTextColor(cc.getResources().getColor(R.color.font_txt_color));
                    rightTitle.setTextColor(cc.getResources().getColor(R.color.dl_title_color));
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
            }
        });


        leftTitle.setOnClickListener((v)-> {
            viewpager2.setCurrentItem(0);
        });

        rightTitle.setOnClickListener((v)-> {
            viewpager2.setCurrentItem(1);
        });

        addWinList();

        Server.Set_Listener(listener_server);
    }

    private void addWinList() {
        winlist_content = new LinearLayout(cc);
        winlist_content.setVisibility(View.GONE);
        winlist_content.setOrientation(LinearLayout.VERTICAL);
        winlist_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));
        this.addView(winlist_content);

        FrameLayout  _fl_header = new FrameLayout(cc);
        _fl_header.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(36)));
        winlist_content.addView(_fl_header);

        title = CLController.Get_TextView(cc, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER),
                cc.getResources().getString(R.string.multiwin_open_win),0xffa1a1a1,16,null);
        _fl_header.addView(title);
        _fl_header.addView(CLController.Get_TextView_Divider(cc,CL.Get_FLLP(CL.MP,1,Gravity.BOTTOM),0xff2f2f2f));

        win_list = new WrapRecyclerView(cc);
        win_list.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        win_list.setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
        win_list.addItemDecoration(new RecycleViewDivider(LinearLayoutManager.HORIZONTAL, new ColorDrawable(0xff2f2f2f), 1));
        win_list.setLayoutManager(new LinearLayoutManager(cc));
        win_adapter = new AdapterForWinItem();
        //  win_adapter=new WrapRecyclerAdapter(win_item_adapter);

        win_list.setAdapter(win_adapter);
        datas_winlist = new CopyOnWriteArrayList<Struct.StructWebsite>();

        updateTitle();
        winlist_content.addView(win_list);
        LinearLayout.LayoutParams params = CL.Get_LLLP(CL.MP, CL.DIP2PX_INT(36));
        TextView _bottom_textview= CLController.Get_TextView_With_Gravity(cc,params,
                Gravity.CENTER,cc.getResources().getString(R.string.multiwin_new_win),cc.getResources().getColor(R.color.app_primary_color),16,listener_new_win);

        _bottom_textview.setOnClickListener(listener_new_win);
        winlist_content.addView(_bottom_textview);
    }

    private Server.Eventer listener_server=new Server.Eventer() {
        @Override
        public void on_update_data(final CopyOnWriteArrayList<Data.StructDLItem> datas) {
            handler.post(() -> {
                if (datas == null)
                    return;

                synchronized (datas) {
                    CopyOnWriteArrayList<Data.StructDLItem> waitData = mData.get(0);
                    waitData.clear();
                    CopyOnWriteArrayList<Data.StructDLItem> dlData = mData.get(1);
                    dlData.clear();
                    for (Data.StructDLItem item : datas) {
                        if (item.show_type == 1) {
                            waitData.add(item);
                        } else if (item.show_type == 3 && !item.downloaded) {
                            dlData.add(0, item);
                            item.dler.set_listener(listener_downloader);
                        }
                    }
                    lei_da.update_count(waitData.size());
                    mViewPaper2Adapter.notifyDataSetChanged();
                }

            });
        }


        @Override
        public void on_update_downloading_data() {
            handler.post(() -> {
                if (mViewPaper2Adapter != null)
                    mViewPaper2Adapter.notifyDataSetChanged();
            });
        }
    };

//    public void change_state(int type) {
//        list_type = type;
//    }

    public void change_state(int type) {
        list_type = type;
        if (list_type == 1) {
            winlist_content.setVisibility(View.VISIBLE);
            dl_content.setVisibility(View.GONE);
        } else {
            winlist_content.setVisibility(View.GONE);
            dl_content.setVisibility(View.VISIBLE);
        }
    }

    public int get_state() {
        return list_type;
    }

    @Override
    public void onUserEarnedReward(@NonNull RewardItem rewardItem) {
        bEarned = true;
        if (Global.mycb != null) {
            Global.mycb.on_callback_success(Global.AD_EARNED, ident);
        }
    }

    public int getIndexformData(String _ident) {
        int index = -1;

        Iterator<Data.StructDLItem> iterator = mData.get(0).iterator();
        while (iterator.hasNext()) {
            Data.StructDLItem item1 = iterator.next();
            if (item1.ident.compareTo(_ident) == 0) {
                ++index;
                break;
            }
            ++index;
        }
        return index;
    }

    @Override
    public boolean on_callback_success(Integer value, String msg) {
        if (value == Global.AD_FULL_SCREEN || value == Global.AD_REWARD_INITERTITIAL_FULL_SCREEN) {
            removeWaitTask();
            hideWait();
        } else if (value == Global.AD_LOADED) {

        } else if (value == Global.AD_EARNED) {
            int index = -1;
                //因为list在不断刷新，所以需要动态得到数据
                if ((index = getIndexformData(msg)) != -1) {
                    Data.StructDLItem data = mData.get(0).get(index);
                    data.thread_num = 4;
                    clickDownloadBtn(data);
                    CLToast.Show(cc, this.getResources().getString(R.string.str_starting_downloading), true);
                }

        }
        mViewPaper2Adapter.notifyDataSetChanged();
        return true;
    }

    @Override
    public void on_callback_fail(int code, String msg) {
        hideWait();
        removeWaitTask();
        if (code == Global.AD_FULL_SCREEN_DISMISSED) {
            hideWaitTask();
            showWait();

            Global.rewardedAd = null;
            Global.rewardLoading = true;
            Global.LoadReward(cc);
        } else if (code == Global.AD_REWARD_INITERTITIAL_FULL_SCREEN_DISMISSED) {
            hideWaitTask();
            showWait();

            Global.rewardedInterstitialAd = null;
            Global.rewardedInterstitialLoading = true;
            Global.LoadRewardInterstitial(cc);
        } else if (code == Global.AD_LOADED_FAILED) {

        }
    }

    private void clickDownloadBtn(final Data.StructDLItem item) {
        if (Setting.Share_Setting().get_only_wifi()) {
            ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            if (networkInfo == null) {
                CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_network_error)).show();
                return;
            }
            int nType = networkInfo.getType();
            if (nType != ConnectivityManager.TYPE_WIFI) {
                CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                    @Override
                    public void on_callback_success() {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Conversion_To_Downloader(item);
                                item.dler.set_listener(listener_downloader);
                            }
                        });
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {
                    }
                }).show();
                return;
            }
        }



        handler.post(new Runnable() {
            @Override
            public void run() {

                //CopyOnWriteArrayList<Data.StructDLItem> dling_data = mData.get(1);
                Server.Conversion_To_Downloader(item);
                if (item.dler != null) {
                    item.dler.set_listener(listener_downloader);
                }
                else
                    CLToast.Show(cc, cc.getResources().getString(R.string.tip_operation_fail), false);
            }
        });
    }


    private CLDialog dialog_thread;
    private View dialog_content_thread;
    private Drawable get_bg(){
        RoundRectShape _shape=new RoundRectShape(new float[]{12,12,12,12,12,12,12,12}, null, null);
        ShapeDrawable _drawable=new ShapeDrawable(_shape);
        _drawable.getPaint().setColor(0xffffffff);
        _drawable.getPaint().setStyle(Paint.Style.FILL);
        int _pad=CL.DIP2PX_INT(6);
        _drawable.setPadding(0, _pad, 0, _pad);
        return _drawable;
    }

    private TextView get_button(Context cc,String name,final OnClickListener listener){
        TextView _btn=CLController.Get_Button(cc, CL.Get_LP(CL.MP, CL.WC), name, cc.getResources().getColor(R.color.colorPrimary3),
                16,
                CL.Get_StateList_Drawable(new ColorDrawable(0xffffffff), new ColorDrawable(0xffe0e0e0)),
                listener);
        _btn.setPadding(0,CL.DIP2PX_INT(10),0,CL.DIP2PX_INT(10));
        _btn.setGravity(Gravity.CENTER);
        return _btn;
    }

    private void show_thread_download_dialog(final CLCallback.CBO<Integer> cber){
        if(dialog_content_thread ==null){
            LinearLayout _ll= CLController.Get_LinearLayout(cc,CL.Get_LP_WW(),LinearLayout.VERTICAL,null);
            _ll.setPadding(CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),0);
            _ll.setClickable(true);

            LinearLayout _ll_btns=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.VERTICAL,null);
            _ll_btns.setBackground(get_bg());
            _ll_btns.setGravity(Gravity.CENTER);
            _ll.addView(_ll_btns);


            int flag = -1;
            String str_speed = "";
            int free_number = Setting.Share_Setting().get_free_number();
            str_speed = cc.getResources().getString(R.string.str_speed_download);
            if (Setting.Share_Setting().get_subscription_flag()) {
                // str_speed = str_speed + "(VIP)";
                flag = 2;
            } else if (free_number > 0) {
                str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_try_free_download) + String.valueOf(free_number) + ")";
                flag = 1;
            } else if (Global.rewardedAd != null) {
                str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                flag = 3;
            } else if (Global.rewardedInterstitialAd != null) {
                str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                flag = 4;
            } else if (Global.ad_interstitial != null) {
                str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                flag = 5;
            }  else {
                str_speed = str_speed + "(VIP)";
                flag = 6;
            }


            int finalFlag = flag;
            _ll_btns.addView(get_button(cc, str_speed, new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_thread.dismiss();
                    if(cber!=null)cber.on_callback(finalFlag);
                }
            }));

            if (!Setting.Share_Setting().get_subscription_flag()) {
                _ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
                int finalFlag2 = 10;
                _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.str_normal_download), new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog_thread.dismiss();
                        if (cber != null) cber.on_callback(finalFlag2);
                    }
                }));
            }
            LinearLayout _ll_cancel=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12))
                    ,LinearLayout.VERTICAL,null);
            _ll_cancel.setBackground(get_bg());
            _ll_cancel.setGravity(Gravity.CENTER);
            _ll_cancel.setPadding(CL.DIP2PX_INT(6), 0, CL.DIP2PX_INT(6), 0);
            _ll.addView(_ll_cancel);
            TextView _btn_cancel=get_button(cc, cc.getResources().getString(R.string.cancel), new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_thread.dismiss();
                    dialog_content_thread = null;
                }
            });
            _btn_cancel.setTextColor(cc.getResources().getColor(R.color.colorPrimary3));
            _btn_cancel.setPadding(0, 0, 0, 0);
            _ll_cancel.addView(_btn_cancel);
            dialog_content_thread =_ll;
            dialog_thread =CLDialog.Get_Dialog_Animation_From_Bottom(cc, dialog_content_thread);
        }
        dialog_thread.show();
    }

    private void showAdBottomMenu(final Data.StructDLItem item) {
        new Thread() {
            @Override
            public void run() {
                int flag = -1;
                String str_speed = "";
                int free_number = Setting.Share_Setting().get_free_number();
                str_speed = cc.getResources().getString(R.string.str_speed_download);
                if (Setting.Share_Setting().get_subscription_flag()) {
                    flag = 2;
                } else if (free_number > 0) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_try_free_download) + String.valueOf(free_number) + ")";
                    flag = 1;
                } else if (Global.rewardedAd != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 3;
                } else if (Global.rewardedInterstitialAd != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 4;
                } else if (Global.ad_interstitial != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 5;
                }  else {
                    str_speed = str_speed + "(VIP)";
                    flag = 6;
                }

                String menu[] = null;
                if (Setting.Share_Setting().get_subscription_flag()) {
                    menu = new String[] {str_speed};
                } else {
                    menu = new String[] {str_speed, cc.getResources().getString(R.string.str_normal_download)};
                }
                final int final_flg = flag;
                BottomMenu.build()
                        .setMenuList(menu)
                        .setOnMenuItemClickListener(new OnMenuItemClickListener<BottomMenu>() {
                            @Override
                            public boolean onClick(BottomMenu dialog, CharSequence text, int index) {
                                if (index == 0) {
                                    int value = final_flg;
                                    if (value == 1) {
                                        //免费下载次数
                                        int free_number = Setting.Share_Setting().get_free_number();
                                        value = free_number - 1;
                                        Setting.Share_Setting().set_free_number(value);
                                        if (value == 0) {
                                            Setting.Share_Setting().set_free_complete(true);
                                        }
                                        //Setting.Share_Setting().set_thread_number(4);
                                        item.thread_num = 4;
                                        clickDownloadBtn(item);
                                    } else if (value == 2) {
                                        //已订阅
                                        item.thread_num = 4;
                                        clickDownloadBtn(item);
                                    } else if (value == 3) {
                                        //广告准备好
                                        CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                                            @Override
                                            public void on_callback_success() {
                                                  hideWaitTask();
                                                  showWait();
                                                  ident = item.ident;
                                                Global.rewardedAd.show(cc, CacheList.this);
                                            }
                                            @Override
                                            public void on_callback_fail(int code, String msg) {

                                            }
                                        }).show();


                                    } else if (value == 4) {
                                        //广告准备好

                                        CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                                            @Override
                                            public void on_callback_success() {
                                                 hideWaitTask();
                                                 showWait();
                                                 ident = item.ident;
                                                Global.rewardedInterstitialAd.show(cc, CacheList.this);
                                            }
                                            @Override
                                            public void on_callback_fail(int code, String msg) {

                                            }
                                        }).show();
                                    } else if (value == 5) {
                                        //普通下载
                                        Global.showInterstitial_donow(cc, new CLCallback.CB() {
                                            @Override
                                            public void on_callback() {

                                                item.thread_num = 4;
                                                clickDownloadBtn(item);
                                            }
                                        });
                                    } else if (value == 6) {
                                        //打开订阅界面
                                        cc.startActivityForResult(new Intent(cc, BillingActivity.class), 2000);
                                    } else if (value == 10) {
                                        //普通下载

                                        clickDownloadBtn(item);
                                    }

                                } else if (index == 1) {
                                    clickDownloadBtn(item);
                                }
                                return false;
                            }
                        }).show();
            }
        }.start();
    /*
        new Thread() {
            @Override
            public void run() {
                super.run();
                int flag = -1;
                String str_speed = "";
                int free_number = Setting.Share_Setting().get_free_number();
                str_speed = cc.getResources().getString(R.string.str_speed_download);
                if (Setting.Share_Setting().get_subscription_flag()) {
                    flag = 2;
                } else if (free_number > 0) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_try_free_download) + String.valueOf(free_number) + ")";
                    flag = 1;
                } else if (Global.rewardedAd != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 3;
                } else if (Global.rewardedInterstitialAd != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 4;
                } else if (Global.ad_interstitial != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 5;
                }  else {
                    str_speed = str_speed + "(VIP)";
                    flag = 6;
                }

                String menu[] = null;
                if (Setting.Share_Setting().get_subscription_flag()) {
                    menu = new String[] {str_speed};
                } else {
                    menu = new String[] {str_speed, cc.getResources().getString(R.string.str_normal_download)};
                }
                final int final_flg = flag;
                BottomMenu.build()
                        .setMenuList(menu)
                        .setOnMenuItemClickListener(new OnMenuItemClickListener<BottomMenu>() {
                            @Override
                            public boolean onClick(BottomMenu dialog, CharSequence text, int index) {
                                if (index == 0) {
                                    int value = final_flg;
                                    if (value == 1) {
                                        //免费下载次数
                                        int free_number = Setting.Share_Setting().get_free_number();
                                        value = free_number - 1;
                                        Setting.Share_Setting().set_free_number(value);
                                        if (value == 0) {
                                            Setting.Share_Setting().set_free_complete(true);
                                        }
                                        //Setting.Share_Setting().set_thread_number(4);
                                        item.thread_num = 4;
                                        clickDownloadBtn(item);
                                    } else if (value == 2) {
                                        //已订阅

                                        clickDownloadBtn(item);
                                    } else if (value == 3) {
                                        //广告准备好
                                        CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                                            @Override
                                            public void on_callback_success() {
                                                //  hideWaitTask();
                                                //  showWait();
                                                //  ident = data.ident;
                                                Global.rewardedAd.show(cc, CacheList.this);
                                            }
                                            @Override
                                            public void on_callback_fail(int code, String msg) {

                                            }
                                        }).show();


                                    } else if (value == 4) {
                                        //广告准备好

                                        CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                                            @Override
                                            public void on_callback_success() {
                                                // hideWaitTask();
                                                //  showWait();
                                                // ident = data.ident;
                                                Global.rewardedInterstitialAd.show(cc, CacheList.this);
                                            }
                                            @Override
                                            public void on_callback_fail(int code, String msg) {

                                            }
                                        }).show();
                                    } else if (value == 5) {
                                        //普通下载
                                        Global.showInterstitial_donow(cc, new CLCallback.CB() {
                                            @Override
                                            public void on_callback() {

                                                item.thread_num = 4;
                                                clickDownloadBtn(item);
                                            }
                                        });
                                    } else if (value == 6) {
                                        //打开订阅界面
                                        cc.startActivityForResult(new Intent(cc, BillingActivity.class), 2000);
                                    } else if (value == 10) {
                                        //普通下载

                                        clickDownloadBtn(item);
                                    }

                                } else if (index == 1) {
                                }
                                return false;
                            }
                        }).show();
            }
        }.start();
        */
    }

    private void showBottomMenu(final Data.StructDLItem item) {
        show_thread_download_dialog(new CLCallback.CBO<Integer>(){
            @Override
            public void on_callback(Integer value) {
                dialog_content_thread = null;
                if (value == 1) {
                    //免费下载次数
                    int free_number = Setting.Share_Setting().get_free_number();
                    value = free_number - 1;
                    Setting.Share_Setting().set_free_number(value);
                    if (value == 0) {
                        Setting.Share_Setting().set_free_complete(true);
                    }
                    //Setting.Share_Setting().set_thread_number(4);
                    item.thread_num = 4;
                    clickDownloadBtn(item);
                } else if (value == 2) {
                    //已订阅
                    item.thread_num = 4;
                    clickDownloadBtn(item);
                } else if (value == 3) {
                    //广告准备好
                    CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                        @Override
                        public void on_callback_success() {
                            hideWaitTask();
                            showWait();
                            ident = item.ident;
                            Global.rewardedAd.show(cc, CacheList.this);
                        }
                        @Override
                        public void on_callback_fail(int code, String msg) {

                        }
                    }).show();


                } else if (value == 4) {
                    //广告准备好

                    CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                        @Override
                        public void on_callback_success() {
                            hideWaitTask();
                            showWait();
                            ident = item.ident;
                            Global.rewardedInterstitialAd.show(cc, CacheList.this);
                        }
                        @Override
                        public void on_callback_fail(int code, String msg) {

                        }
                    }).show();
                } else if (value == 5) {
                    //普通下载
                    Global.showInterstitial_donow(cc, new CLCallback.CB() {
                        @Override
                        public void on_callback() {

                            item.thread_num = 4;
                            clickDownloadBtn(item);
                        }
                    });
                } else if (value == 6) {
                    //打开订阅界面
                    cc.startActivityForResult(new Intent(cc, BillingActivity.class), 2000);
                } else if (value == 10) {
                    //普通下载

                    clickDownloadBtn(item);
                }
            }
        });
    }

    private CommonDownloader.Eventer listener_downloader = new CommonDownloader.Eventer() {
        @Override
        public void on_state_change(Data.StructDLItem item, int state) {
            if (handler != null && mViewPaper2Adapter != null) {
                handler.post(()-> {

                    if (state == CommonDownloader.Eventer.State_Complete) {
                        item.downloaded = true;
                        if (!Setting.Share_Setting().get_complete_first_download()) {
                            Setting.Share_Setting().set_complete_first_download(true);
                        }
                        if (item.type_major == Data.Type_Video || item.type_major == Data.Type_Music) {
                            if (item.duration == null) {
                                try {
                                    MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                                    retriever.setDataSource(item.path);
                                    String _ddd = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                                    item.duration = CLTools.Get_Media_Duration(Integer.parseInt(_ddd));
                                    CL.CLOGI("duration:" + _ddd);
                                    File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                    if (!_thumb.exists()) {
                                        Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path, MediaStore.Video.Thumbnails.MINI_KIND);
                                        if (bitmap_thumb != null) {
                                            bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                        }
                                    }
                                } catch (Exception ex) {
                                }
                            }
                        } else if (item.type_major == Data.Type_M3U8) {
                            try {
                                File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                if (!_thumb.exists()) {
                                    Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path + "/1", MediaStore.Video.Thumbnails.MINI_KIND);
                                    if (bitmap_thumb != null) {
                                        bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                    }
                                }
                            } catch (Exception ex) {
                                CL.CLOGE("m3u8 thum error:" + ex.toString(), ex);
                            }
                        } else if (item.type_major == Data.Type_APK) {
                            //生成
                            //扫描apk
                           // Context cc = mAdapter.GetContext();
                            try {
                                File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                if (!_thumb.exists()) {
                                    PackageManager _pm = cc.getPackageManager();
                                    PackageInfo _file_info = _pm.getPackageArchiveInfo(item.path, 0);
                                    if (_file_info != null) {
                                        ApplicationInfo _app_info = _file_info.applicationInfo;
                                        _app_info.sourceDir = item.path;
                                        _app_info.publicSourceDir = item.path;
                                        item.name = _app_info.loadLabel(_pm).toString();
                                        if (item.name != null)
                                            Server.Update_Download_Name(item, item.name);
                                        Drawable _dwe = _app_info.loadIcon(_pm);
                                        int w = _dwe.getIntrinsicWidth();
                                        int h = _dwe.getIntrinsicHeight();
                                        Bitmap.Config config = _dwe.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
                                        Bitmap bitmap = Bitmap.createBitmap(w, h, config);
                                        Canvas canvas = new Canvas(bitmap);
                                        _dwe.setBounds(0, 0, w, h);
                                        _dwe.draw(canvas);
                                        bitmap.compress(Bitmap.CompressFormat.PNG, 100, new FileOutputStream(_thumb));
                                    }
                                }
                            } catch (Exception ex) {
                            }
                        }
                        Server.Update_Download(item);
                        Server.Share_FileManager().notify_download_complete(item);


                        Iterator<Data.StructDLItem> iterator = mData.get(1).iterator();
                        while (iterator.hasNext()) {
                            Data.StructDLItem item1 = iterator.next();
                            if (item1.equals(item) && item.downloaded) {
                                mData.get(1).remove(item1);
                                break;
                            }
                        }
                    }

                    //mViewPaper2Adapter.notifyDataSetChanged();
                    if (item.getDelagate() != null) {
                        item.getDelagate().setData(item.getHolder(), item, 0);
                    }
                });

            }
        }

        @Override
        public void on_load_ratio(Data.StructDLItem item, long dl_size, float ratio) {
            if (handler != null && mViewPaper2Adapter != null) {
                handler.post(()-> {
                    //mViewPaper2Adapter.notifyDataSetChanged();
                    if (item.getDelagate() != null) {
                        item.getDelagate().setData(item.getHolder(), item, 0);
                    }
                });
            }
        }

        @Override
        public void on_error(Data.StructDLItem item, int code) {
            CLToast.Show(cc,"error:"+code+"  "+item.title,false);
        }

        @Override
        public void on_delete(Data.StructDLItem item, boolean success) {
            if (success && item.show_type == 3) {

                if (handler != null && mViewPaper2Adapter != null) {
                    handler.post(()-> {
                        Server.Delete_Download(item);
                        mViewPaper2Adapter.notifyDataSetChanged();
                    });
                }
            }
        }
    };


//    public interface OnHolderItemClickListener {
//        void onItemClick(View view, Data.StructDLItem item, int position);
//    }

    AdapterInterface.OnHolderItemClickListener clickListener = new AdapterInterface.OnHolderItemClickListener() {
        @Override
        public void onItemClick(View view, Data.StructDLItem item, int flag) {
            if (item.show_type == 3) {
                if (CL.Do_Once()) {
                    if (flag == 1) {
                       // View popView = (View) view.getTag();
                        PopMenu.show(new String[]{cc.getString(R.string.open), cc.getString(R.string.delete)})
//                            .setOverlayBaseView(true)
                                .setOnIconChangeCallBack(new OnIconChangeCallBack<PopMenu>(true) {
                                    @Override
                                    public int getIcon(PopMenu dialog, int index, String menuText) {
                                        switch (index) {
                                            case 0:
                                                return R.mipmap.play_normal;
                                            case 1:
                                                return R.mipmap.delete_normal;
                                            default:
                                                return 0;
                                        }
                                    }
                                }).setOnMenuItemClickListener(new OnMenuItemClickListener<PopMenu>() {
                            @Override
                            public boolean onClick(PopMenu dialog, CharSequence text, int index) {

                                if (index == 0) {
                                    handler.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            try {
                                                CLTools.playFullSrceenMedia(cc, item.url);
                                            } catch (Exception ex) {
                                                CL.CLOGE("open error:" + ex.toString(), ex);
                                            }
                                        }
                                    });
                                } else {
                                    CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_del_video), new CLCallback.CB_TF() {
                                        @Override
                                        public void on_callback_success() {
                                            if (item.dler.getCrt_status() == CommonDownloader.Eventer.State_Start) {
                                                if (handler != null) {
                                                    handler.post(()->{
                                                        CLToast.Show(cc, cc.getResources().getString(R.string.str_please_pause_then_delete), false);
                                                    });

                                                }
                                            } else {
                                                item.dler.delete();
                                            }
                                        }

                                        @Override
                                        public void on_callback_fail(int code, String msg) {
                                        }
                                    }).show();
                                }

                                return false;
                            }
                        });
                    } else {
                        if (item.dler != null) {
                            if (item.dler.get_status() == CommonDownloader.Eventer.State_Stop) {
                                if (Setting.Share_Setting().get_only_wifi()) {
                                    ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                                    NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                                    if (networkInfo == null) {
                                        CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_network_error)).show();
                                        return;
                                    }
                                    int nType = networkInfo.getType();
                                    if (nType != ConnectivityManager.TYPE_WIFI) {
                                        CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                                            @Override
                                            public void on_callback_success() {
                                                handler.post(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        if (item != null && item.dler != null)
                                                            item.dler.go();
                                                    }
                                                });
                                            }

                                            @Override
                                            public void on_callback_fail(int code, String msg) {
                                            }
                                        }).show();
                                        return;
                                    }
                                }
                                item.dler.go();
                            } else if (item.dler.get_status() == CommonDownloader.Eventer.State_Start) {
                                item.dler.stop();
                            }
                        }
                    }
                }
            } else if (item.show_type == 1) {
                if (CL.Do_Once()) {
                    if (flag == 1) {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                //Server.Remove_Confirm_Downloader(data);
                                try {
                                    CLTools.playFullSrceenMedia(cc, item.url);
                                } catch (Exception ex) {
                                    CL.CLOGE("open error:" + ex.toString(), ex);
                                }
                            }
                        });
                    } else if (flag == 2) {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Remove_Confirm_Downloader(item);
                            }
                        });
                    } else {
                        if (item.type_major == Data.Type_M3U8) {
                            //showBottomMenu(item);
                            showBottomMenu(item);
                          //  showAdBottomMenu(item);
                        //    clickDownloadBtn(item);
                        } else {
                            clickDownloadBtn(item);
                        }
                    }
                }
            }
        }
    };

    private void updateTitle() {
        String str_value = new String();
        str_value = String.format(cc.getResources().getString(R.string.multiwin_open_win), ""+datas_winlist.size());
        title.setText(str_value);
    }

    private class AdapterForWinItem extends RecyclerView.Adapter<HolderWinItem> {
        @NonNull
        @Override
        public HolderWinItem onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            CWebWinItem cv = new CWebWinItem(cc);
            return new HolderWinItem(cv);
        }

        @Override
        public void onBindViewHolder(@NonNull HolderWinItem holder, int position) {
            Struct.StructWebsite _data = datas_winlist.get(position);
            holder.setData(_data);
        }

        @Override
        public int getItemCount() {
            return datas_winlist.size();
        }

    }

    private class HolderWinItem extends RecyclerView.ViewHolder {
        public HolderWinItem(@NonNull View itemView) {
            super(itemView);
        }
        public void setData(Struct.StructWebsite d) {
            CWebWinItem item = (CWebWinItem)this.itemView;
            item.set_baisc_data(d, 0);
        }
    }

    private class CWebWinItem extends LinearLayout {
        private ImageView iw_icon, iw_del_icon;
        private TextView iw_title, iw_vivider ;
        private Struct.StructWebsite data;

        private View.OnClickListener listener_this=new OnClickListener() {
            @Override
            public void onClick(View v) {

                if (data.selected)
                    return;

                current_item.selected = false;
                int old_index = datas_winlist.indexOf(current_item);
                data.selected = true;
                current_item = data;
//                int select_index = datas_winlist.indexOf(current_item);
                int index = datas_winlist.indexOf(data);
//                int size = datas_winlist.size();
                win_adapter.notifyDataSetChanged();
                CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_change, Integer.valueOf(index), Integer.valueOf(old_index));
            }
        };

        private View.OnClickListener listener_del_btn=new OnClickListener() {
            @Override
            public void onClick(View v) {
                int select_index = datas_winlist.indexOf(current_item)+1;
                int index = datas_winlist.indexOf(data)+1;
                int size = datas_winlist.size();

                CL.CLOGI("vevEddy  index2 = "+ index +" select_index = "+select_index + " size " + size);

                if (size > 1) {
                    //如果最后一个，选中前面的

                    if (index == datas_winlist.size()) {
                        datas_winlist.remove(data);
                        if (select_index == index) {
                            Struct.StructWebsite site = datas_winlist.get(datas_winlist.size() - 1);
                            site.selected = true;
                        }

                    } else {
                        datas_winlist.remove(data);
                        if (select_index == index) {
                            Struct.StructWebsite site = datas_winlist.get(index-1);
                            site.selected = true;
                        }
                    }

                    win_adapter.notifyDataSetChanged();
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_del, index-1, select_index-1);

                } else if (size == 1) {

                    CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_del, Integer.valueOf(9999));
                }


                updateTitle();
            }
        };
        public CWebWinItem(Context context) {
            super(context);
            this.setLayoutParams(CL.Get_LP(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

            LinearLayout sub_parent=new LinearLayout(cc);
            sub_parent.setOrientation(LinearLayout.HORIZONTAL);
            sub_parent.setGravity(Gravity.CENTER_VERTICAL);
            sub_parent.setClickable(true);
            sub_parent.setOnClickListener(listener_this);

            sub_parent.setLayoutParams(CL.Get_LLLP(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, 1.0f));
            this.addView(sub_parent);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)sub_parent.getLayoutParams();
            layoutParams.setMargins(0, CL.DIP2PX_INT(4), 0, CL.DIP2PX_INT(4));

            iw_icon=new ImageView(context);
            iw_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),CL.DIP2PX_INT(4),CL.DIP2PX_INT(8),CL.DIP2PX_INT(4)));
            sub_parent.addView(iw_icon);

            iw_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC, 1.0f, 0,0,CL.DIP2PX_INT(2),0),"",0xffaaaaaa,12,null);
            iw_title.setSingleLine();
            iw_title.getPaint().setFakeBoldText(true);
            sub_parent.addView(iw_title);


            iw_del_icon=new ImageView(context);
            iw_del_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0));
            Rect bounds = new Rect();

            iw_del_icon.getHitRect(bounds);

            bounds.top -= 20;
            bounds.bottom += 20;
            bounds.left -= 20;
            bounds.right += 20;
            this.setTouchDelegate(new TouchDelegate(bounds, iw_del_icon));
            sub_parent.addView(iw_del_icon);

            iw_del_icon.setClickable(true);
            iw_del_icon.setOnClickListener(listener_del_btn);

            iw_vivider = CLController.Get_TextView_Divider(cc, CL.Get_LLLP(8,CL.MP,0,0,0,0),0xff007aff);
            iw_vivider.setGravity(Gravity.RIGHT);
            this.addView(iw_vivider);

        }

        public void set_baisc_data(Struct.StructWebsite d, int position){
            this.data=d;

            if (data.selected) {
                current_item = data;
                setBackgroundColor(0xff313131);
                iw_vivider.setVisibility(View.VISIBLE);
            } else {
                setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
                iw_vivider.setVisibility(View.INVISIBLE);
            }

            if (data.bitmap_icon != null) {
                iw_icon.setImageBitmap(data.bitmap_icon);
            } else {
                Drawable def_icon=cc.getResources().getDrawable(R.mipmap.address_web);
                iw_icon.setImageDrawable(def_icon);
            }

//            if (data.title != null && !data.title.isEmpty()) {
//                iw_title.setText(data.title + " + " + (position+1));
//            }


            if (data.title != null && !data.title.isEmpty()) {
                iw_title.setText(data.title);
            } else {
                if (data.url != null && !data.url.isEmpty()) {
                    iw_title.setText(data.url);
                }
            }


            if (iw_del_icon.getDrawable() == null) {
                Drawable def_icon=cc.getResources().getDrawable(R.mipmap.del);
                iw_del_icon.setImageDrawable(def_icon);
            }
        }
    }

    View.OnClickListener listener_new_win = new OnClickListener() {
        @Override
        public void onClick(View v) {
            String _url= Setting.Share_Setting().get_main_page();
            Struct.StructWebsite structWebsite = new Struct.StructWebsite();
            structWebsite.url = _url;

            int index = datas_winlist.indexOf(current_item);

            if (index < datas_winlist.size()-1) {

                datas_winlist.add(index+1, structWebsite);
            } else {
                datas_winlist.add(structWebsite);
                //  win_list.setSelection(win_list.getBottom());
                win_list.getLayoutManager().scrollToPosition(win_list.getBottom());
            }

            structWebsite.selected = true;
            if (current_item != null)
                current_item.selected = false;
            current_item = structWebsite;
            updateTitle();
            win_adapter.notifyDataSetChanged();
            CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_add, Integer.valueOf(index));
            // CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video,Global.Action_win_update_num,datas_winlist.size());
        }
    };
}
