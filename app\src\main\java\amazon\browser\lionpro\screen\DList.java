package amazon.browser.lionpro.screen;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.provider.MediaStore;
import android.view.Gravity;
import android.view.TouchDelegate;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MusicManager;
import amazon.browser.lionpro.downloader.Server;
//import com.browser.lionpro.primary.Global;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.primary.LookVideo;
import amazon.browser.lionpro.util.RewardCreator;
import amazon.browser.lionpro.views.DialogDLMusic;
import amazon.browser.lionpro.views.DialogDLOther;
import amazon.browser.lionpro.views.DialogDLVideo;
import amazon.browser.lionpro.views.KProgressHUD.KProgressHUD;
import amazon.browser.lionpro.views.Leida;
import amazon.browser.lionpro.views.ProgressBar;
import amazon.browser.lionpro.views.RecycleViewDivider;
import amazon.browser.lionpro.views.WrapRecyclerAdapter;
import amazon.browser.lionpro.views.WrapRecyclerView;

import com.google.android.gms.ads.OnUserEarnedRewardListener;
import com.google.android.gms.ads.rewarded.RewardItem;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import gp.BillingActivity;
import lion.CL;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLToast;
import lion.CLTools;
import lion.widget.CLWebkit;

import static android.text.TextUtils.TruncateAt.END;
import static amazon.browser.lionpro.primary.Global.mycb;
import static amazon.browser.lionpro.util.RewardCreator.AD_EARNED;

/**
 * Created by leron on 2016/7/1.
 */
public class DList extends LinearLayout implements  CLCallback.CB_TFO<Integer>, OnUserEarnedRewardListener {


    private Activity cc;
    private Handler handler;
    private Struct.StructWebsite current_item;
    private TextView title;
    private Leida lei_da;
    private ImageView btn_ad, btn_clear;
    //private CustomListView lv_list;
    //private CustomListView win_list;
    private WrapRecyclerView lv_list;
    private WrapRecyclerView win_list;
    private TextView tv_header,tv_footer;
    //private AdapterForItem adapter;
    private WrapRecyclerAdapter adapter;
    private AdapterForWinItem win_adapter;//win_item_adapter;
    // private WrapRecyclerAdapter win_adapter;
    private ArrayList<Data.StructDLItem> datas_server = new ArrayList<Data.StructDLItem>();
    private ArrayList<Data.StructDLItem> datas_temp_server = new ArrayList<Data.StructDLItem>();
    private ArrayList<Struct.StructWebsite> datas_winlist;
    private DialogDLVideo dialog_video;
    private DialogDLMusic dialog_music;
    private DialogDLOther dialog_other;
    private boolean discard=false;
    private int list_type = 0;
    LinearLayout dl_content;
    LinearLayout winlist_content;
   // ConcurrentHashMap<String, RewardCreator> listReward = new ConcurrentHashMap<>();
    ConcurrentHashMap<String, RewardCreator> listTempReward = new ConcurrentHashMap<>();
    private KProgressHUD hud;
    private String ident;
    private Data.StructDLItem cur_click_data;
    private boolean bEarned;
    private Handler wait_handler = new Handler();
    private Runnable runnable1 = new Runnable() {
        @Override
        public void run() {
            CL.CLOGI("Eddy show ad timeout");
            synchronized (datas_server) {
                wait_handler.removeCallbacks(runnable1);
                hideWait();
//                if (!Global.rewardLoading) {
//                    Global.rewardLoading = false;
//                    cur_click_data.thread_num = 4;
//                    if (cur_click_data != null)
//                        clickDownloadBtn(cur_click_data);
//                }
//
//                if (!Global.rewardedInterstitialLoading) {
//                    Global.rewardedInterstitialLoading = false;
//                    cur_click_data.thread_num = 4;
//                    if (cur_click_data != null)
//                        clickDownloadBtn(cur_click_data);
//                }
          //      CLToast.Show(cc, cc.getResources().getString(R.string.str_starting_downloading), true);
            }
        }
    };


    public void hideWaitTask() {
        if (wait_handler != null) {
            wait_handler.postDelayed(runnable1,6000);
        }
    }

    public void removeWaitTask() {
        if (wait_handler != null) {
            wait_handler.removeCallbacks(runnable1);
        }
    }

    @Override
    public void onUserEarnedReward(@NonNull RewardItem rewardItem) {
        bEarned = true;
        if (mycb != null) {
            mycb.on_callback_success(AD_EARNED, ident);
        }
    }

    public DList(Activity context, Leida leida) {
        super(context);
        this.cc=context;
        handler=new Handler();
        this.lei_da=leida;
        this.setFocusable(true);
        this.setFocusableInTouchMode(true);
//        RoundRectShape _shape=new RoundRectShape(new float[]{32,32,0,0,0,0,0,0},null,null);
//        ShapeDrawable _dwe_round=new ShapeDrawable(_shape);
//        _dwe_round.getPaint().setStyle(Paint.Style.FILL);
//        _dwe_round.getPaint().setColor(0xff2c2c2c);
//        this.setBackground(_dwe_round);
        this.setBackgroundColor(0xff2c2c2c);
        this.setClickable(true);
        this.setOrientation(LinearLayout.VERTICAL);
        this.setPadding(CL.DIP2PX_INT(2),0,0,0);
        init();
        Global.setRewardCB(this);
     //   Global.LoadReward(cc,this);
    }


    public void clickDownloadBtn(final Data.StructDLItem data) {
        if (Setting.Share_Setting().get_only_wifi()) {
            ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            if (networkInfo == null) {
                CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_network_error)).show();
                return;
            }
            int nType = networkInfo.getType();
            if (nType != ConnectivityManager.TYPE_WIFI) {
                CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                    @Override
                    public void on_callback_success() {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Conversion_To_Downloader(data);
                                data.dler.set_listener(listener_downloader);
                            }
                        });
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {
                    }
                }).show();
                return;
            }
        }
        handler.post(new Runnable() {
            @Override
            public void run() {
                Server.Conversion_To_Downloader(data);
                if (data.dler != null)
                    data.dler.set_listener(listener_downloader);
                else
                    CLToast.Show(cc, cc.getResources().getString(R.string.tip_operation_fail), false);
            }
        });
    }

    public int getIndexformData(String _ident) {
        Data.StructDLItem data = null;
        int index = -1;
        Iterator it = datas_server.iterator();
        while (it.hasNext()) {
            data = (Data.StructDLItem)it.next();
            if (data.ident == _ident) {
                ++index;
                break;
            }
            ++index;
        }
        return index;
    }

    @Override
    public boolean on_callback_success(Integer value, String msg) {
        if (value == RewardCreator.AD_FULL_SCREEN || value == RewardCreator.AD_REWARD_INITERTITIAL_FULL_SCREEN) {
            removeWaitTask();
            hideWait();
        } else if (value == RewardCreator.AD_LOADED) {

        } else if (value == AD_EARNED) {
            int index = -1;
            synchronized (datas_server) {
                //因为list在不断刷新，所以需要动态得到数据
                if ((index = getIndexformData(msg)) != -1) {
                    Data.StructDLItem data = datas_server.get(index);
                    data.thread_num = 4;
                    clickDownloadBtn(data);
                    CLToast.Show(cc, this.getResources().getString(R.string.str_starting_downloading), true);
                }
            }

        }
        adapter.notifyDataSetChanged();
        return true;
    }

    @Override
    public void on_callback_fail(int code, String msg) {
        hideWait();
        removeWaitTask();
        if (code == RewardCreator.AD_FULL_SCREEN_DISMISSED) {
            hideWaitTask();
            showWait();
          //  RewardCreator reward = listReward.get(msg);
         //   if (reward != null)
         //       reward.ReLoadReward(cc, DList.this);

            Global.rewardedAd = null;
            Global.rewardLoading = true;
            Global.LoadReward(cc);
        } else if (code == RewardCreator.AD_REWARD_INITERTITIAL_FULL_SCREEN_DISMISSED) {
            hideWaitTask();
            showWait();

            Global.rewardedInterstitialAd = null;
            Global.rewardedInterstitialLoading = true;
            Global.LoadRewardInterstitial(cc);
        } else if (code == RewardCreator.AD_LOADED_FAILED) {

        }

    }

    public void updateUI() {
        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            if(Setting.Share_Setting().get_subscription_flag()) {
                btn_ad.setVisibility(View.INVISIBLE);
            } else {
                btn_ad.setVisibility(View.VISIBLE);
            }
        } else {
            btn_ad.setVisibility(View.INVISIBLE);
        }
    }

    private void init(){

        dl_content=new LinearLayout(cc);
        //dl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP,0,0,CL.DIP2PX_INT(12), CL.DIP2PX_INT(12)));
        dl_content.setOrientation(LinearLayout.VERTICAL);


        dl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));
        this.addView(dl_content);
        FrameLayout _fl_header=new FrameLayout(cc);
        _fl_header.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(36)));
        dl_content.addView(_fl_header);



        _fl_header.addView(CLController.Get_TextView(cc, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER),
                cc.getResources().getString(R.string.dl_list),0xffa1a1a1,16,null));
        _fl_header.addView(CLController.Get_TextView_Divider(cc,CL.Get_FLLP(CL.MP,1,Gravity.BOTTOM),0xff2f2f2f));
        btn_clear=CLController.Get_ImageView(cc,CL.Get_FLLP(CL.DIP2PX_INT(60),CL.MP,Gravity.RIGHT),null,listener_clear);
        btn_clear.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_clear_normal,R.mipmap.icon_clear_click));
        btn_clear.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        _fl_header.addView(btn_clear);


/*
        btn_ad = CLController.Get_ImageView(cc, CL.Get_FLLP(CL.DIP2PX_INT(60), CL.MP, Gravity.LEFT), null, listener_ad);
        btn_ad.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.money, R.mipmap.money_press));
        btn_ad.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        _fl_header.addView(btn_ad);

        //if(!Setting.Share_Setting().get_outline_switch_ad() && !Global.Switch_AD) {

        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            if(Setting.Share_Setting().get_subscription_flag()) {
                btn_ad.setVisibility(View.INVISIBLE);
            }
        } else {
            btn_ad.setVisibility(View.INVISIBLE);
        }


*/

        tv_header=CLController.Get_TextView(cc,new AbsListView.LayoutParams(CL.MP,CL.WC),cc.getResources().getString(R.string.tip_no_confirm_dl),0xff505050,15,null);
        tv_header.setGravity(Gravity.CENTER);
        tv_header.setPadding(0,CL.DIP2PX_INT(9),0,CL.DIP2PX_INT(9));
        tv_footer=CLController.Get_TextView(cc,new AbsListView.LayoutParams(CL.MP,CL.WC),cc.getResources().getString(R.string.tip_no_dl),0xff505050,15,null);
        tv_footer.setGravity(Gravity.CENTER);
        tv_footer.setPadding(0,CL.DIP2PX_INT(9),0,CL.DIP2PX_INT(9));

        lv_list=new WrapRecyclerView(cc);
        lv_list.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        lv_list.setBackgroundColor(0xff222222);
       // lv_list.setCacheColorHint(Color.TRANSPARENT);
       // lv_list.setDivider(new ColorDrawable(0xff2f2f2f));
       // lv_list.setDividerHeight(1);
       // lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        lv_list.addItemDecoration(new RecycleViewDivider(LinearLayoutManager.HORIZONTAL, new ColorDrawable(0xff2f2f2f), 1));
        lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
      //  lv_list.setAdapter(null);
        lv_list.addHeaderView(tv_header);
        lv_list.addFooterView(tv_footer);

        AdapterForItem wrapRecyclerAdapter =new AdapterForItem();
       // lv_list.setAdapter(null);
        //WrapRecyclerAdapter
        lv_list.setLayoutManager(new LinearLayoutManager(cc));
        // 添加头部和底部 需要 包裹Adapter，才能添加头部和底部
        adapter = new WrapRecyclerAdapter(wrapRecyclerAdapter);
        lv_list.setAdapter(adapter);
        lv_list.removeHeaderView(tv_header);
        lv_list.removeFooterView(tv_footer);
        dl_content.addView(lv_list);

        Server.Set_Listener(listener_server);

        dialog_video=new DialogDLVideo(cc);
        dialog_music=new DialogDLMusic(cc);
        dialog_other=new DialogDLOther(cc);


        winlist_content=new LinearLayout(cc);
        winlist_content.setVisibility(View.GONE);
        winlist_content.setOrientation(LinearLayout.VERTICAL);
        winlist_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));
        this.addView(winlist_content);

        _fl_header=new FrameLayout(cc);
        _fl_header.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(36)));
        winlist_content.addView(_fl_header);
        title = CLController.Get_TextView(cc, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER),
                cc.getResources().getString(R.string.multiwin_open_win),0xffa1a1a1,16,null);
        _fl_header.addView(title);
        _fl_header.addView(CLController.Get_TextView_Divider(cc,CL.Get_FLLP(CL.MP,1,Gravity.BOTTOM),0xff2f2f2f));

        win_list=new WrapRecyclerView(cc);
        win_list.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        win_list.setBackgroundColor(0xff222222);
     //   win_list.setCacheColorHint(Color.TRANSPARENT);
     //   win_list.setDivider(new ColorDrawable(0xff2f2f2f));
     //   win_list.setDividerHeight(1);
     //   win_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
    //    win_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
     //   win_list.addItemDecoration(new RecycleViewDivider(cc, LinearLayoutManager.HORIZONTAL));
        win_list.addItemDecoration(new RecycleViewDivider(LinearLayoutManager.HORIZONTAL, new ColorDrawable(0xff2f2f2f), 1));
        win_list.setLayoutManager(new LinearLayoutManager(cc));
        win_adapter = new AdapterForWinItem();
      //  win_adapter=new WrapRecyclerAdapter(win_item_adapter);

        win_list.setAdapter(win_adapter);
        datas_winlist = new ArrayList<>(10);

        updateTitle();
        winlist_content.addView(win_list);
        LinearLayout.LayoutParams params = CL.Get_LLLP(CL.MP, CL.DIP2PX_INT(36));
        TextView _bottom_textview= CLController.Get_TextView_With_Gravity(cc,params,
                Gravity.CENTER,cc.getResources().getString(R.string.multiwin_new_win),cc.getResources().getColor(R.color.app_primary_color),16,listener_new_win);

        _bottom_textview.setOnClickListener(listener_new_win);
        winlist_content.addView(_bottom_textview);

    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
//        if(Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
//            btn_ad.setVisibility(View.VISIBLE);
//        }
//        if(!Setting.Share_Setting().get_subscription_flag()) {
//            btn_ad.setVisibility(View.VISIBLE);
//        }

        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            if(Setting.Share_Setting().get_subscription_flag()) {
                if (btn_ad!= null)
                btn_ad.setVisibility(View.INVISIBLE);
            } else {
                if (btn_ad!= null)
                btn_ad.setVisibility(View.VISIBLE);
            }
        } else {
            if (btn_ad!= null)
            btn_ad.setVisibility(View.INVISIBLE);
        }

        super.onLayout(changed, l, t, r, b);
    }

    void updateData(ArrayList<CLWebkit> list) {

        int index = -1;
        if (current_item!=null) {
            index = datas_winlist.indexOf(current_item);
        }

        Iterator iter = list.iterator();
        ArrayList<Struct.StructWebsite> tmp = new ArrayList<Struct.StructWebsite>();
        int i = 0;
        while (iter.hasNext()) {
            CLWebkit webkit = (CLWebkit)iter.next();
            Struct.StructWebsite value = new Struct.StructWebsite();
            String url = webkit.getUrl();
            String title = webkit.getTitle();
            String orign_url = webkit.getOriginalUrl();
            value.url = (url!=null?url:(orign_url!=null?orign_url:""));
            value.title = (title!=null?title:"");
            if (i++ == index) {
                value.selected = true;
            }
            tmp.add(value);
        }
        datas_winlist.clear();
        datas_winlist.addAll(tmp);
        if (index == -1) {
            datas_winlist.get(datas_winlist.size()-1).selected = true;
        }
        win_adapter.notifyDataSetChanged();
        updateTitle();
    }

    View.OnClickListener listener_new_win = new OnClickListener() {
        @Override
        public void onClick(View v) {
            String _url= Setting.Share_Setting().get_main_page();
            Struct.StructWebsite structWebsite = new Struct.StructWebsite();
            structWebsite.url = _url;

            int index = datas_winlist.indexOf(current_item);

            if (index < datas_winlist.size()-1) {

                datas_winlist.add(index+1, structWebsite);
            } else {
                datas_winlist.add(structWebsite);
              //  win_list.setSelection(win_list.getBottom());
                win_list.getLayoutManager().scrollToPosition(win_list.getBottom());
            }

            structWebsite.selected = true;
            if (current_item != null)
            current_item.selected = false;
            current_item = structWebsite;
            updateTitle();
            win_adapter.notifyDataSetChanged();
            CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_add, Integer.valueOf(index));
           // CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video,Global.Action_win_update_num,datas_winlist.size());
        }
    };

    public void change_state(int type) {
        list_type = type;
        if (list_type == 1) {
            winlist_content.setVisibility(View.VISIBLE);
            dl_content.setVisibility(View.GONE);
        } else {
            winlist_content.setVisibility(View.GONE);
            dl_content.setVisibility(View.VISIBLE);
        }
    }

    public int get_state() {
        return list_type;
    }


    private View.OnClickListener listener_ad=new OnClickListener() {
        @Override
        public void onClick(View v) {
//            Global.Show_Interstitial_IM(cc, new CLCallback.CB() {
//                @Override
//                public void on_callback() {
//                }
//            });
        }
    };

    private View.OnClickListener listener_clear=new OnClickListener() {
        @Override
        public void onClick(View v) {
            CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_clear_completed), new CLCallback.CB_TF() {
                @Override
                public void on_callback_success() {
                    Server.clear_completed();
                }
                @Override
                public void on_callback_fail(int code, String msg) {}
            }).show();
        }
    };

    private CLBus.CBEventer listener_update_name=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action== Global.Action_rename){
                if(datas_server==null)return;
                String _ident=(String)msgs[0];
                String _name=(String)msgs[1];
                CL.CLOGI("rename ident:"+_ident+"  name:"+_name);
                for(int i=0;i<datas_server.size();++i){
                    Data.StructDLItem _item=datas_server.get(i);

                    if(_item.show_type==3 && _item.ident_md5.equals(_ident)){
                        _item.name=_name;
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                lv_list.requestLayout();
                                adapter.notifyDataSetChanged();
                            }
                        });
                        break;
                    }
                }
            }
        }
    };


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        discard=false;
        CLBus.Share_Instance().register(Global.Group_update_info,listener_update_name,Global.Action_rename);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        discard=true;
        CLBus.Share_Instance().unregister(Global.Group_update_info,listener_update_name);
    }

    private Server.Eventer listener_server=new Server.Eventer() {
        @Override
        public void on_update_data(CopyOnWriteArrayList<Data.StructDLItem> datas) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    if (datas == null)
                        return;
                    synchronized (datas) {
                        if (datas_server != null)
                            datas_server.clear();
                        else
                            datas_server = new ArrayList<Data.StructDLItem>();
                        datas_server.addAll(datas);
                        // datas_server = datas;
/*
                        ArrayList<Data.StructDLItem> tmplist = new ArrayList<>();
                        ArrayList<Data.StructDLItem> tmpDatalist = new ArrayList<>();
                        tmplist.addAll(datas_server);
                        tmpDatalist.addAll(datas);
                        datas_temp_server.clear();
                        listTempReward.clear();
                        Iterator it = tmpDatalist.iterator();

                        while (it.hasNext()) {
                            Data.StructDLItem updateData = (Data.StructDLItem) it.next();
                            if (updateData.show_type == 2) {
                                datas_temp_server.add(updateData);
                                continue;
                            }
                            Iterator it2 = tmplist.iterator();
                            Boolean bfind = false;
                            while (it2.hasNext()) {
                                Data.StructDLItem visualData = (Data.StructDLItem) it2.next();
                                if (visualData.show_type == 2) {
                                    continue;
                                }

                                //如果新更新的数据里和以前数据一样,复用之前的reward ad
                                if (updateData.ident.compareTo(visualData.ident) == 0) {
                                    bfind = true;
                                    datas_temp_server.add(updateData);
                                    listTempReward.put(updateData.ident, listReward.get(updateData.ident));
                                    break;
                                }
                            }
                            //如果没有发现说明需要创建新的广告，这是新的数据
                            if (!bfind) {
                                RewardCreator creator = new RewardCreator();
                                creator.LoadReward(cc, DList.this);
                                datas_temp_server.add(updateData);
                                listTempReward.put(updateData.ident, creator);
                            }
                        }
                        datas_server.clear();
                        listReward.clear();
                        datas_server.addAll(datas_temp_server);
                        listReward.putAll(listTempReward);
                        */
                        int _count_wait = 0;
                        boolean _has_wait = false;
                        boolean _has_dl = false;
                        for (int i = 0; i < datas_server.size(); ++i) {
                            Data.StructDLItem _item = datas_server.get(i);
                            if (_item.show_type == 1) {
                                ++_count_wait;
                                if (!_has_wait) _has_wait = true;
                                //_item.LoadReward(cc, DList.this);
                            } else if (_item.show_type == 3) {
                                if (!_has_dl) _has_dl = true;
                            }
                            if (_item.dler != null && (_item.dler.get_listener() == null || _item.dler.get_listener() != listener_downloader)) {
                                _item.dler.set_listener(listener_downloader);
                            }
                        }
                        if (!_has_wait) {
                            if (lv_list.getHeaderViewsCount() == 0) {
                                lv_list.addHeaderView(tv_header);
                            }
                        } else lv_list.removeHeaderView(tv_header);
                        if (!_has_dl) {
                            if (lv_list.getFooterViewsCount() == 0) {
                                lv_list.addFooterView(tv_footer);
                            }
                            btn_clear.setVisibility(View.GONE);
                        } else {
                            btn_clear.setVisibility(View.VISIBLE);
                            lv_list.removeFooterView(tv_footer);
                        }

                        lei_da.update_count(_count_wait);
                        lv_list.requestLayout();
                        adapter.notifyDataSetChanged();
                    }
                }
            });
        }

        @Override
        public void on_update_downloading_data() {

        }
    };

    private CommonDownloader.Eventer listener_downloader=new CommonDownloader.Eventer() {
        @Override
        public void on_state_change(final Data.StructDLItem item, final int state) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    for(int i=0;!discard && i<lv_list.getChildCount();++i){
                        View _v=lv_list.getChildAt(i);
                        if(_v instanceof CTMItemDL){
                            CTMItemDL _view=(CTMItemDL)_v;
                            if(_view.data!=null && _view.data == item){
                                if(state==CommonDownloader.Eventer.State_Complete){
                                    item.downloaded=true;
                                    if (!Setting.Share_Setting().get_complete_first_download()) {
                                        Setting.Share_Setting().set_complete_first_download(true);
                                    }
                                    if(item.type_major == Data.Type_Video || item.type_major == Data.Type_Music){
                                        if(item.duration==null) {
                                            try {
                                                MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                                                retriever.setDataSource(item.path);
                                                String _ddd = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                                                item.duration = CLTools.Get_Media_Duration(Integer.parseInt(_ddd));
                                                CL.CLOGI("duration:" + _ddd);
                                                File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                                if (!_thumb.exists()) {
                                                    Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path, MediaStore.Video.Thumbnails.MINI_KIND);
                                                    if (bitmap_thumb != null) {
                                                        bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                                    }
                                                }
                                            } catch (Exception ex) {
                                            }
                                        }
                                    }else if(item.type_major==Data.Type_M3U8){
                                        try {
                                            File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                            if (!_thumb.exists()) {
                                                //Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path+"/m_1.ts", MediaStore.Video.Thumbnails.MINI_KIND);
                                                Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path+"/1", MediaStore.Video.Thumbnails.MINI_KIND);
                                                if (bitmap_thumb != null) {
                                                    bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                                }
                                            }
                                        } catch (Exception ex) {
                                            CL.CLOGE("m3u8 thum error:"+ex.toString(),ex);
                                        }
                                    }
                                    else if(item.type_major == Data.Type_APK){
                                        //生成
                                        //扫描apk
                                        try {
                                            File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                            if (!_thumb.exists()) {
                                                PackageManager _pm = cc.getPackageManager();
                                                PackageInfo _file_info = _pm.getPackageArchiveInfo(item.path, 0);
                                                if (_file_info != null) {
                                                    ApplicationInfo _app_info = _file_info.applicationInfo;
                                                    _app_info.sourceDir = item.path;
                                                    _app_info.publicSourceDir = item.path;
                                                    item.name=_app_info.loadLabel(_pm).toString();
                                                    if(item.name!=null)Server.Update_Download_Name(item,item.name);
                                                    Drawable _dwe = _app_info.loadIcon(_pm);
                                                    int w = _dwe.getIntrinsicWidth();
                                                    int h = _dwe.getIntrinsicHeight();
                                                    Bitmap.Config config = _dwe.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
                                                    Bitmap bitmap = Bitmap.createBitmap(w, h, config);
                                                    Canvas canvas = new Canvas(bitmap);
                                                    _dwe.setBounds(0, 0, w, h);
                                                    _dwe.draw(canvas);
                                                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, new FileOutputStream(_thumb));
                                                }
                                            }
                                        }catch (Exception ex){}
                                    }
                                    Server.Update_Download(item);
                                    Server.Share_FileManager().notify_download_complete(item);
                                } else if (state==CommonDownloader.Eventer.State_packet) {
                                    if (item.type_major == Data.Type_M3U8) {

                                    }
                                }
                                _view.set_baisc_data(item);
                            }
                        }
                    }
                }
            });
        }
        @Override
        public void on_load_ratio(final Data.StructDLItem item, long dl_size, float ratio) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    for(int i=0;!discard && i<lv_list.getChildCount();++i){
                        View _v=lv_list.getChildAt(i);
                        if(_v instanceof CTMItemDL){
                            CTMItemDL _view=(CTMItemDL)_v;
                            if(_view.data!=null && _view.data == item){
                                _view.set_baisc_data(item);
                            }
                        }
                    }
                }
            });
        }

        @Override
        public void on_delete(final Data.StructDLItem item, final boolean success) {
            if(success){
                new Thread() {
                    @Override
                    public void run() {
                        super.run();
                        Server.Delete_Download(item);
                    }
                }.start();

                handler.post(new Runnable() {
                    @Override
                    public void run() {
                      //  datas_server.remove(item);

                      //  lv_list.requestLayout();
                      //  adapter.notifyDataSetChanged();
                        if(item.type_major==Data.Type_Video || item.type_major==Data.Type_M3U8) {
                            if (dialog_video != null && dialog_video.isShowing() && dialog_video.get_current_data() == item) {
                                dialog_video.dismiss();
                            }
                        }else if(item.type_major==Data.Type_Music){
                            if (dialog_music != null && dialog_music.isShowing() && dialog_music.get_current_data() == item) {
                                dialog_music.dismiss();
                            }
                        }else{
                            if (dialog_other != null && dialog_other.isShowing() && dialog_other.get_current_data() == item) {
                                dialog_other.dismiss();
                            }
                        }
                    }
                });
            }
        }

        @Override
        public void on_error(Data.StructDLItem item, int code) {
            CLToast.Show(cc,"error:"+code+"  "+item.title,false);
        }
    };

    private void updateTitle() {
        String str_value = new String();
        str_value = String.format(cc.getResources().getString(R.string.multiwin_open_win), ""+datas_winlist.size());
        title.setText(str_value);
    }


    private class AdapterForWinItem extends RecyclerView.Adapter<HolderWinItem> {
        @NonNull
        @Override
        public HolderWinItem onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            CWebWinItem cv = new CWebWinItem(cc);
            return new HolderWinItem(cv);
        }

        @Override
        public void onBindViewHolder(@NonNull HolderWinItem holder, int position) {
            Struct.StructWebsite _data = datas_winlist.get(position);
            holder.setData(_data);
        }

        @Override
        public int getItemCount() {
            return datas_winlist.size();
        }

    }

    private class HolderWinItem extends RecyclerView.ViewHolder {
        public HolderWinItem(@NonNull View itemView) {
            super(itemView);
        }
        public void setData(Struct.StructWebsite d) {
            CWebWinItem item = (CWebWinItem)this.itemView;
            item.set_baisc_data(d, 0);
        }
    }

    private class CWebWinItem extends LinearLayout {
        private ImageView iw_icon, iw_del_icon;
        private TextView iw_title, iw_vivider ;
        private Struct.StructWebsite data;

        private View.OnClickListener listener_this=new OnClickListener() {
            @Override
            public void onClick(View v) {

                if (data.selected)
                    return;

                current_item.selected = false;
                int old_index = datas_winlist.indexOf(current_item);
                data.selected = true;
                current_item = data;
//                int select_index = datas_winlist.indexOf(current_item);
                int index = datas_winlist.indexOf(data);
//                int size = datas_winlist.size();
                win_adapter.notifyDataSetChanged();
                CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_change, Integer.valueOf(index), Integer.valueOf(old_index));
            }
        };

        private View.OnClickListener listener_del_btn=new OnClickListener() {
            @Override
            public void onClick(View v) {
                int select_index = datas_winlist.indexOf(current_item)+1;
                int index = datas_winlist.indexOf(data)+1;
                int size = datas_winlist.size();

                CL.CLOGI("vevEddy  index2 = "+ index +" select_index = "+select_index + " size " + size);

                if (size > 1) {
                    //如果最后一个，选中前面的

                    if (index == datas_winlist.size()) {
                        datas_winlist.remove(data);
                        if (select_index == index) {
                            Struct.StructWebsite site = datas_winlist.get(datas_winlist.size() - 1);
                            site.selected = true;
                        }

                    } else {
                        datas_winlist.remove(data);
                        if (select_index == index) {
                            Struct.StructWebsite site = datas_winlist.get(index-1);
                            site.selected = true;
                        }
                    }

                    win_adapter.notifyDataSetChanged();
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_del, index-1, select_index-1);

                } else if (size == 1) {

                    CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_del, Integer.valueOf(9999));
                }


                updateTitle();
            }
        };
        public CWebWinItem(Context context) {
            super(context);
            this.setLayoutParams(CL.Get_LP(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));

            LinearLayout sub_parent=new LinearLayout(cc);
            sub_parent.setOrientation(LinearLayout.HORIZONTAL);
            sub_parent.setGravity(Gravity.CENTER_VERTICAL);
            sub_parent.setClickable(true);
            sub_parent.setOnClickListener(listener_this);

            sub_parent.setLayoutParams(CL.Get_LLLP(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, 1.0f));
            this.addView(sub_parent);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)sub_parent.getLayoutParams();
            layoutParams.setMargins(0, CL.DIP2PX_INT(4), 0, CL.DIP2PX_INT(4));

            iw_icon=new ImageView(context);
            iw_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),CL.DIP2PX_INT(4),CL.DIP2PX_INT(8),CL.DIP2PX_INT(4)));
            sub_parent.addView(iw_icon);

            iw_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC, 1.0f, 0,0,CL.DIP2PX_INT(2),0),"",0xffaaaaaa,12,null);
            iw_title.setSingleLine();
            iw_title.getPaint().setFakeBoldText(true);
            sub_parent.addView(iw_title);


            iw_del_icon=new ImageView(context);
            iw_del_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0));
            Rect bounds = new Rect();

            iw_del_icon.getHitRect(bounds);

            bounds.top -= 20;
            bounds.bottom += 20;
            bounds.left -= 20;
            bounds.right += 20;
            this.setTouchDelegate(new TouchDelegate(bounds, iw_del_icon));
            sub_parent.addView(iw_del_icon);

            iw_del_icon.setClickable(true);
            iw_del_icon.setOnClickListener(listener_del_btn);

            iw_vivider = CLController.Get_TextView_Divider(cc, CL.Get_LLLP(8,CL.MP,0,0,0,0),0xff007aff);
            iw_vivider.setGravity(Gravity.RIGHT);
            this.addView(iw_vivider);

        }

        public void set_baisc_data(Struct.StructWebsite d, int position){
            this.data=d;

            if (data.selected) {
                current_item = data;
                setBackgroundColor(0xff313131);
                iw_vivider.setVisibility(View.VISIBLE);
            } else {
                setBackgroundColor(0xff222222);
                iw_vivider.setVisibility(View.INVISIBLE);
            }

            if (data.bitmap_icon != null) {
                iw_icon.setImageBitmap(data.bitmap_icon);
            } else {
                Drawable def_icon=cc.getResources().getDrawable(R.mipmap.address_web);
                iw_icon.setImageDrawable(def_icon);
            }

//            if (data.title != null && !data.title.isEmpty()) {
//                iw_title.setText(data.title + " + " + (position+1));
//            }


            if (data.title != null && !data.title.isEmpty()) {
                iw_title.setText(data.title);
            } else {
                if (data.url != null && !data.url.isEmpty()) {
                    iw_title.setText(data.url);
                }
            }


            if (iw_del_icon.getDrawable() == null) {
                Drawable def_icon=cc.getResources().getDrawable(R.mipmap.del);
                iw_del_icon.setImageDrawable(def_icon);
            }
        }
    }



    private class AdapterForItem extends RecyclerView.Adapter<RecyclerView.ViewHolder>  {

        @NonNull
        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            if (viewType == 1) {
                CTMItemWait cv = new CTMItemWait(cc);
                return new HolderWaitItem(cv);
            }else if(viewType == 2){
                TextView cv = CLController.Get_TextView_Divider(cc,new AbsListView.LayoutParams(CL.MP,CL.DIP2PX_INT(6)),0xff2c2c2c);
                return new HolderDivider(cv);
            }else if(viewType == 3) {
                CTMItemDL cv = new CTMItemDL(cc);
                return new HolderItemDl(cv);
            }else {
                CTMItemWait cv = new CTMItemWait(cc);
                return new HolderWaitItem(cv);
            }
          //  return null;
        }

        @Override
        public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position) {
            int cur_pos = position;
            if (datas_server == null) {
                return;
            }
            Data.StructDLItem  _data = datas_server.get(position);
            if (_data.show_type == 1) {
              //  holder.setData(_data);
                _data.index = position;
                ((HolderWaitItem)holder).setData(_data);
            } else if(_data.show_type == 2) {
                ((HolderDivider)holder).itemView.setVisibility(View.VISIBLE);
            } else if(_data.show_type == 3) {
                ((HolderItemDl)holder).setData(_data);
            }

        }

        @Override
        public int getItemCount() {
            if (datas_server == null) {
                return 0;
            }
            return datas_server.size();
        }

        @Override
        public int getItemViewType(int position) {
            if (datas_server == null) {
                return super.getItemViewType(position);
            }
            Data.StructDLItem  _data = datas_server.get(position);
            return _data.show_type;
          //  return super.getItemViewType(position);
        }
    }

    private class HolderWaitItem extends RecyclerView.ViewHolder {
        public HolderWaitItem(@NonNull View itemView) {
            super(itemView);
        }
        public void setData(Data.StructDLItem d) {
            CTMItemWait item = (CTMItemWait)this.itemView;
            item.set_baisc_data(d);
        }
    }

    private class CTMItemWait extends LinearLayout {

        private Data.StructDLItem data;
        private ImageView iv_icon;
        private TextView tv_title,tv_length, tv_resloution;
        private ImageView btn_del,btn_download, btn_play, btn_copy_url;

        public void copy(Context cc, String content)
        {
// 得到剪贴板管理器
            ClipboardManager cmb = (ClipboardManager)cc.getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData mClipData = ClipData.newPlainText("Label", content);
            cmb.setPrimaryClip(mClipData);
        }



        private View.OnClickListener listener_btn_copy_url=new OnClickListener() {
            @Override
            public void onClick(View v) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        //Server.Remove_Confirm_Downloader(data);
                        copy(cc, data.url);
                        CLToast.Show(cc,cc.getResources().getString(R.string.copy_url_to_clipboard),true);
                    }
                });
            }
        };

        private View.OnClickListener listener_btn_confirm_play=new OnClickListener() {
            @Override
            public void onClick(View v) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        //Server.Remove_Confirm_Downloader(data);
                        try {
                            CLTools.playFullSrceenMedia(cc, data.url);
                        }catch (Exception ex){
                            CL.CLOGE("open error:"+ex.toString(),ex);
                        }
                    }
                });
            }
        };

        private View.OnClickListener listener_btn_confirm_del=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (CL.Do_Once()) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            //if (listReward != null && data != null && data.ident != null)
                            //    listReward.remove(data.ident);
                            Server.Remove_Confirm_Downloader(data);
                        }
                    });
                }
            }
        };
        /*
        private View.OnClickListener listener_btn_confirm_download=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (CL.Do_Once()) {
                    Integer index = (Integer) v.getTag();
                    CL.CLOGI("Eddy index1 =" + index);
                    if (index != null && index != -1) {
                        final RewardCreator reward = listReward.get(data.ident);
                        final String ident = data.ident;
                        if (reward.rewardedAd != null && !Setting.Share_Setting().get_subscription_flag()) {

                            CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                                @Override
                                public void on_callback_success() {
                                    synchronized (datas_server) {
                                        Iterator it = datas_server.iterator();
                                        int index = -1;
                                        while(it.hasNext()) {
                                            Data.StructDLItem item = (Data.StructDLItem)it.next();
                                            if (item.ident == ident) {
                                                ++index;
                                                cur_click_data = item;
                                                break;
                                            }
                                            ++index;
                                        }
                                        CL.CLOGI("Eddy index2 =" + index);
                                        if (reward.rewardedAd != null && index != -1) {
                                            hideWaitTask();
                                            showWait();
                                            reward.setIndex(index);
                                            reward.setIdent(ident);
                                            reward.rewardedAd.show(cc, reward);
                                        } else {
                                            clickDownloadBtn();
                                        }
                                    }
                                }
                                @Override
                                public void on_callback_fail(int code, String msg) {

                                }
                            }).show();


                        } else if (Global.ad_interstitial != null) {
                           Global.showInterstitial_donow(cc, new CLCallback.CB() {
                                @Override
                                public void on_callback() {
                                    clickDownloadBtn();
                                    CLToast.Show(cc, cc.getResources().getString(R.string.str_starting_downloading), true);
                                }
                            });
                        } else {
                            clickDownloadBtn();
                        }
                    } else {
                        clickDownloadBtn();
                    }
                }
            }
        };
*/
        private View.OnClickListener listener_btn_confirm_download = new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (data != null && data.type_major == Data.Type_M3U8) {
                    showBottomMenu();
                } else {
                    clickDownloadBtn();
                }
            }
        };

        private void clickDownloadBtn() {
            if (Setting.Share_Setting().get_only_wifi()) {
                ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                if (networkInfo == null) {
                    CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_network_error)).show();
                    return;
                }
                int nType = networkInfo.getType();
                if (nType != ConnectivityManager.TYPE_WIFI) {
                    CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                        @Override
                        public void on_callback_success() {
                            handler.post(new Runnable() {
                                @Override
                                public void run() {
                                    if (data != null && data.dler != null) {
                                        Server.Conversion_To_Downloader(data);
                                        data.dler.set_listener(listener_downloader);
                                    }
                                }
                            });
                        }

                        @Override
                        public void on_callback_fail(int code, String msg) {
                        }
                    }).show();
                    return;
                }
            }
            handler.post(new Runnable() {
                @Override
                public void run() {
                    if (data == null) {
                        return;
                    }
                    synchronized (data) {
                        Server.Conversion_To_Downloader(data);
                        if (data != null && data.dler != null)
                            data.dler.set_listener(listener_downloader);
                        else
                            CLToast.Show(cc, cc.getResources().getString(R.string.tip_operation_fail), false);
                    }
                }
            });
        }

        private void showBottomMenu() {
            show_thread_download_dialog(new CLCallback.CBO<Integer>(){
                @Override
                public void on_callback(Integer value) {
                    dialog_content_thread = null;
                    if (value == 1) {
                        //免费下载次数
                        int free_number = Setting.Share_Setting().get_free_number();
                        value = free_number - 1;
                        Setting.Share_Setting().set_free_number(value);
                        if (value == 0) {
                            Setting.Share_Setting().set_free_complete(true);
                        }
                        //Setting.Share_Setting().set_thread_number(4);
                        data.thread_num = 4;
                        clickDownloadBtn();
                    } else if (value == 2) {
                        //已订阅

                        clickDownloadBtn();
                    } else if (value == 3) {
                        //广告准备好
                        CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
                            @Override
                            public void on_callback_success() {
                                hideWaitTask();
                                showWait();
                                ident = data.ident;
                                Global.rewardedAd.show(cc, DList.this);
                            }
                            @Override
                            public void on_callback_fail(int code, String msg) {

                            }
                        }).show();

                    } else if (value == 4) {
                        //广告准备好
                        hideWaitTask();
                        showWait();
                        ident = data.ident;
                        Global.rewardedInterstitialAd.show(cc, DList.this);
//                        CLDialog.Get_Confirm_Dialog(Global.Acy_Main, Global.Acy_Main.getResources().getString(R.string.str_reward_tip_title),Global.Acy_Main.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
//                            @Override
//                            public void on_callback_success() {
//                                hideWaitTask();
//                                showWait();
//                                ident = data.ident;
//                                Global.rewardedInterstitialAd.show(cc, DList.this);
//                            }
//                            @Override
//                            public void on_callback_fail(int code, String msg) {
//
//                            }
//                        }).show();
                    } else if (value == 5) {
                        Global.showInterstitial_donow(cc, new CLCallback.CB() {
                            @Override
                            public void on_callback() {

                                data.thread_num = 4;
                                clickDownloadBtn();
                            }
                        });
                    } else if (value == 6) {
                        //打开订阅界面
                        cc.startActivityForResult(new Intent(cc, BillingActivity.class), 2000);
                    } else if (value == 10) {
                        //普通下载

                        clickDownloadBtn();
                    }
                }
            });
        }

        private CLDialog dialog_thread;
        private View dialog_content_thread;
        private void show_thread_download_dialog(final CLCallback.CBO<Integer> cber){
            cur_click_data = null;
            if(dialog_content_thread ==null){
                LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP_WW(),LinearLayout.VERTICAL,null);
                _ll.setPadding(CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),0);
                _ll.setClickable(true);

                LinearLayout _ll_btns=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.VERTICAL,null);
                _ll_btns.setBackground(get_bg());
                _ll_btns.setGravity(Gravity.CENTER);
                _ll.addView(_ll_btns);


                int flag = -1;
                String str_speed = "";
                int free_number = Setting.Share_Setting().get_free_number();
                str_speed = cc.getResources().getString(R.string.str_speed_download);
                if (Setting.Share_Setting().get_subscription_flag()) {
                    // str_speed = str_speed + "(VIP)";
                    flag = 2;
                } else if (free_number > 0) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_try_free_download) + String.valueOf(free_number) + ")";
                    flag = 1;
                } else if (Global.rewardedAd != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 3;
                } else if (Global.rewardedInterstitialAd != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 4;
                } else if (Global.ad_interstitial != null) {
                    str_speed = str_speed + "(" + cc.getResources().getString(R.string.str_have_ad) + ")";
                    flag = 5;
                } else {
                    str_speed = str_speed + "(VIP)";
                    flag = 6;
                }

                cur_click_data = data;
                int finalFlag = flag;
                _ll_btns.addView(get_button(cc, str_speed, v -> {
                    dialog_thread.dismiss();
                    if(cber!=null)cber.on_callback(finalFlag);
                }));
                if (!Setting.Share_Setting().get_subscription_flag()) {
                    _ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
                    int finalFlag2 = 10;
                    _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.str_normal_download), new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog_thread.dismiss();
                            if (cber != null) cber.on_callback(finalFlag2);
                        }
                    }));
                }

                LinearLayout _ll_cancel=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12))
                        ,LinearLayout.VERTICAL,null);
                _ll_cancel.setBackground(get_bg());
                _ll_cancel.setGravity(Gravity.CENTER);
                _ll_cancel.setPadding(CL.DIP2PX_INT(6), 0, CL.DIP2PX_INT(6), 0);
                _ll.addView(_ll_cancel);
                TextView _btn_cancel=get_button(cc, cc.getResources().getString(R.string.cancel), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog_thread.dismiss();
                        dialog_content_thread = null;
                    }
                });
                _btn_cancel.setTextColor(cc.getResources().getColor(R.color.colorPrimary3));
                _btn_cancel.setPadding(0, 0, 0, 0);
                _ll_cancel.addView(_btn_cancel);
                dialog_content_thread =_ll;
                dialog_thread =CLDialog.Get_Dialog_Animation_From_Bottom(cc, dialog_content_thread);
            }
            dialog_thread.show();
        }

        private Drawable get_bg(){
            RoundRectShape _shape=new RoundRectShape(new float[]{12,12,12,12,12,12,12,12}, null, null);
            ShapeDrawable _drawable=new ShapeDrawable(_shape);
            _drawable.getPaint().setColor(0xffffffff);
            _drawable.getPaint().setStyle(Paint.Style.FILL);
            int _pad=CL.DIP2PX_INT(6);
            _drawable.setPadding(0, _pad, 0, _pad);
            return _drawable;
        }

        private TextView get_button(Context cc,String name,final View.OnClickListener listener){
            TextView _btn=CLController.Get_Button(cc, CL.Get_LP(CL.MP, CL.WC), name, cc.getResources().getColor(R.color.colorPrimary3),
                    16,
                    CL.Get_StateList_Drawable(new ColorDrawable(0xffffffff), new ColorDrawable(0xffe0e0e0)),
                    listener);
            _btn.setPadding(0,CL.DIP2PX_INT(10),0,CL.DIP2PX_INT(10));
            _btn.setGravity(Gravity.CENTER);
            return _btn;
        }

        public CTMItemWait(Context context) {
            super(context);
            this.setLayoutParams(CL.Get_LP(ViewGroup.LayoutParams.MATCH_PARENT, CL.DIP2PX_INT(56)));
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);

            iv_icon=new ImageView(context);
            iv_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0));
            this.addView(iv_icon);

            tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC,0,0,CL.DIP2PX_INT(2),0),"",0xffaaaaaa,12,null);
            tv_title.getPaint().setFakeBoldText(true);
            this.addView(tv_title);

            LinearLayout video_info_layout = CLController.Get_LinearLayout(context,CL.Get_LLLP(CL.MP, CL.WC,1.0f),LinearLayout.VERTICAL,null);
            this.addView(video_info_layout);

            tv_length=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC, CL.WC,1.0f),"",0xffaaaaaa,14,null);
            tv_length.setSingleLine();
            tv_length.setEllipsize(END);
            video_info_layout.addView(tv_length);

            tv_resloution=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP, CL.MP,1.0f),"",0xffaaaaaa,14,null);
            // tv_resloution.setSingleLine();
            tv_resloution.setLines(2);

            video_info_layout.addView(tv_resloution);
            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));
/*

            btn_play=new ImageView(context);
            btn_play.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(28),CL.DIP2PX_INT(28)));
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams)btn_play.getLayoutParams();
            params.setMargins(18, 0, 18, 0);

            btn_play.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_play_normal,R.mipmap.icon_play_press));
            btn_play.setScaleType(ImageView.ScaleType.FIT_CENTER);
            btn_play.setClickable(true);
            btn_play.setOnClickListener(listener_btn_confirm_play);
            */

            int size = 40;
/*
            btn_copy_url=new ImageView(context);
            btn_copy_url.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(size),CL.DIP2PX_INT(size)));
            btn_copy_url.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.copy_normal,R.mipmap.copy_press));
            btn_copy_url.setScaleType(ImageView.ScaleType.CENTER);
            btn_copy_url.setClickable(true);
            btn_copy_url.setOnClickListener(listener_btn_copy_url);
            this.addView(btn_copy_url);

            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));
*/
            btn_play=new ImageView(context);
            btn_play.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(size),CL.DIP2PX_INT(size)));
            btn_play.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.play_normal,R.mipmap.play_press));
            btn_play.setScaleType(ImageView.ScaleType.CENTER);
            btn_play.setClickable(true);
            btn_play.setOnClickListener(listener_btn_confirm_play);
            this.addView(btn_play);

          //  this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));

            btn_del=new ImageView(context);
            btn_del.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(size),CL.DIP2PX_INT(size)));
            btn_del.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.delete_normal,R.mipmap.delete_press));
            btn_del.setScaleType(ImageView.ScaleType.CENTER);
            btn_del.setClickable(true);
            btn_del.setOnClickListener(listener_btn_confirm_del);
            this.addView(btn_del);

          //  this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));



            btn_download=new ImageView(context);
            btn_download.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(size),CL.DIP2PX_INT(size)));
            btn_download.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.download_normal,R.mipmap.download_press));
            btn_download.setScaleType(ImageView.ScaleType.CENTER);
            btn_download.setClickable(true);
            btn_download.setOnClickListener(listener_btn_confirm_download);
            this.addView(btn_download);
            //LoadReward(cc, this);
          //  this.LoadReward(cc, this);
//            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),CL.DIP2PX_INT(2),CL.DIP2PX_INT(6)),0xff2f2f2f));
        }
        public void set_baisc_data(Data.StructDLItem d){
            this.data = d;
            if(d.type_major==Data.Type_APK){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_apk_s);
                tv_title.setText("APK");
                btn_play.setVisibility(View.GONE);
            }
            else if(d.type_major==Data.Type_Music){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_music_s);
                if(d.type_minor==Data.Type_Music_MP3)tv_title.setText("MP3");
                else if(d.type_minor==Data.Type_Music_OGG)tv_title.setText("OGG");
                else if(d.type_minor==Data.Type_Music_FLAC)tv_title.setText("FLAC");
                else if(d.type_minor==Data.Type_Music_WAV)tv_title.setText("WAV");
                else if(d.type_minor==Data.Type_Music_M4A)tv_title.setText("M4A");
                else tv_title.setText("???");
                btn_play.setVisibility(View.VISIBLE);
            }
            else if(d.type_major==Data.Type_Video){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                if(d.type_minor==Data.Type_Video_MP4)tv_title.setText("MP4");
                else if(d.type_minor==Data.Type_Video_3GP)tv_title.setText("3GP");
                else if(d.type_minor==Data.Type_Video_MKV)tv_title.setText("MKV");
                else if(d.type_minor==Data.Type_Video_WEBM)tv_title.setText("WEBM");
                else tv_title.setText("???");
                btn_play.setVisibility(View.VISIBLE);
            }
            else if(d.type_major==Data.Type_M3U8){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                tv_title.setText("M3U8");
                btn_play.setVisibility(View.VISIBLE);
            }
            else if(d.type_major==Data.Type_Doc){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_doc_s);
                CL.CLOGI("set suffix:"+d.suffix);
                tv_title.setText(d.suffix);
                btn_play.setVisibility(View.GONE);
            }
            else {
                iv_icon.setBackgroundResource(R.mipmap.res_icon_other_s);
                tv_title.setText(d.suffix);
                btn_play.setVisibility(View.GONE);
            }
            //btn_download.setTag(false);
            btn_download.setTag(-1);
            tv_resloution.setText("");
          //  RewardCreator reward = listReward.get(data.ident);
            if(d.type_major!=Data.Type_M3U8) {
                //tv_length.setText(CLTools.Get_Capacity_Format(d.length));


                String name = "";
                if (d.type_major == Data.Type_Video_MERGE) {
                    name = cc.getString(R.string.str_try_merge);
                    tv_length.setText(name);
                } else {
                    name = (data.name == null ? data.title : data.name);
                    tv_length.setText(CLTools.Get_Capacity_Format(d.length) + "-" + name);
                }



                tv_resloution.setText("");
                /*
                int r = Global.rand.nextInt(10)+1;
                if (r < Setting.Share_Setting().get_frequency_show()
                        && Setting.Share_Setting().get_complete_first_download()
                        && (Global.rewardedAd != null || Global.ad_interstitial != null)
                        && !Setting.Share_Setting().get_subscription_flag()
                        && data.length > 1024 * 1024 * 15) {
                    // if (r < 3) {
                    btn_download.setTag(data.index);
                  //  tv_resloution.setText(cc.getResources().getString(R.string.str_have_ad));
                }

                 */
            } else {
                //tv_length.setText(data.name==null?data.title:data.name);
                tv_length.setText(data.name == null ? data.title : data.name);

                if (data.quality != null && data.quality.length() > 0) {
                    String[] _quality = data.quality.split("=");
                    if (_quality != null ) {//&& data.rewardedAd != null
                        if (data.hasAd && Setting.Share_Setting().get_complete_first_download()
                                && (Global.rewardedAd != null || Global.ad_interstitial != null)
                                && !Setting.Share_Setting().get_subscription_flag()) {
                                tv_resloution.setText(_quality[1]);
//                            tv_resloution.setText(_quality[1] + "\n" + cc.getResources().getString(R.string.str_quality_ad));
//                            tv_resloution.setLines(2);
                            btn_download.setTag(data.index);
                        } else {
                            /*
                            int r = Global.rand.nextInt(10)+1;
                            if (r < Setting.Share_Setting().get_frequency_show()
                                    && Setting.Share_Setting().get_complete_first_download()
                                    && (Global.rewardedAd != null || Global.ad_interstitial != null)
                                    && !Setting.Share_Setting().get_subscription_flag()) {
                                // if (r < 3) {
                                btn_download.setTag(data.index);
                              //  tv_resloution.setText(cc.getResources().getString(R.string.str_have_ad));
                            } else {
                                tv_resloution.setText(_quality[1]);
                                tv_resloution.setLines(1);
                            }
*/
                            if (_quality != null) {
                                tv_resloution.setText(_quality[1]);
                                tv_resloution.setLines(1);
                            }
                        }
                    }
                } else {
                    int r = Global.rand.nextInt(10)+1;
                    if (r < Setting.Share_Setting().get_frequency_show()
                            && Setting.Share_Setting().get_complete_first_download()
                            && (Global.rewardedAd != null || Global.ad_interstitial != null)
                            && !Setting.Share_Setting().get_subscription_flag()
                            && data.length > 1024 * 1024 * 15) {
                        // if (r < 3) {
                        btn_download.setTag(data.index);
                      //  tv_resloution.setText(cc.getResources().getString(R.string.str_have_ad));
                    }
                }
            }
        }
    }


    private class HolderDivider extends  RecyclerView.ViewHolder {
        public HolderDivider(@NonNull View itemView) {
            super(itemView);
        }
    }

    private class HolderItemDl extends  RecyclerView.ViewHolder {
        public HolderItemDl(@NonNull View itemView) {
            super(itemView);
        }

        public void setData(Data.StructDLItem d) {
            CTMItemDL item = (CTMItemDL)this.itemView;
            item.set_baisc_data(d);
        }
    }

    private class CTMItemDL extends LinearLayout{

        private Data.StructDLItem data;
        private ImageView iv_icon;
        private TextView tv_title,tv_speed,tv_dl_size,tv_perview, tv_duration;
        private ImageView iv_btn, iv_play;
        private ProgressBar progressBar;
        private TextView tv_div1, tv_div2;


        private View.OnClickListener listener_this=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(data.type_major==Data.Type_Video){
                    if(!data.downloaded) {
                        dialog_video.show();
                        dialog_video.update_downloader(data);
                    }else{
                        //open
                        if(CL.Do_Once()){
//                            Intent _intent=new Intent();
//                            _intent.setClass(cc, LookVideo.class);
//                            _intent.putExtra("name",data.name==null?data.title:data.name);
//                            _intent.putExtra("path",data.path);
//                            cc.startActivity(_intent);
                            CLTools.playFullSrceenMedia(cc, data.path);
                        }
                    }
                }else if(data.type_major==Data.Type_M3U8){
                    if(!data.downloaded) {
                        dialog_video.show();
                        dialog_video.update_downloader(data);
                    }else{
                        //open
                        if(CL.Do_Once()){
                            Intent _intent=new Intent();
                            _intent.setClass(cc, LookVideo.class);
                            _intent.putExtra("m3u8",true);
                            _intent.putExtra("name",data.name==null?data.title:data.name);
                            _intent.putExtra("path","http://127.0.0.1:8200/m3u8/"+data.ident_md5+"/plist");
                            cc.startActivity(_intent);
                        }
                    }
                }else if(data.type_major==Data.Type_Music){
                    if(!data.downloaded) {
                        dialog_music.show();
                        dialog_music.update_downloader(data);
                    }else{
                        MusicManager _mm=Server.Share_Music();
                        if(_mm!=null){
                            _mm.play_music(data);
                        }
                    }
                }else if(data.type_major==Data.Type_APK){
                    if(!data.downloaded) {
                        dialog_other.show();
                        dialog_other.update_downloader(data);
                    }else {
                        if(!new File(data.path).exists()){
                            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                            //Server.Remove_Confirm_Downloader(data);
                            Server.Delete_Download(data);
                            return;
                        }
//                        Intent _intent = new Intent(Intent.ACTION_VIEW);
//                        _intent.setDataAndType(Uri.parse("file://" + data.path), "application/vnd.android.package-archive");
//                        _intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                        cc.startActivity(_intent);


                        CLTools.install(cc, data.path);


                    }
                }else{
                    if(!data.downloaded) {
                        dialog_other.show();
                        dialog_other.update_downloader(data);
                    }else{
                        if(!new File(data.path).exists()){
                            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                            return;
                        }
//                        try {
//                            Intent intent = new Intent();
//                            intent.addCategory(Intent.CATEGORY_DEFAULT);
//                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                            intent.setAction(Intent.ACTION_VIEW);
//                            String _type= MimeTypeMap.getSingleton().getMimeTypeFromExtension(data.suffix);
//                            intent.setDataAndType(Uri.fromFile(new File(data.path)), _type);
//                            cc.startActivity(Intent.createChooser(intent,cc.getResources().getString(R.string.open)));
//                        }catch (Exception ex){
//                            CL.CLOGE("open error:"+ex.toString(),ex);
//                        }
                        CLTools.playFullSrceenMedia(cc, data.url);
                    }
                }
            }


//            public static void installApkFile(Context context, String filePath) {
//                Intent intent = new Intent(Intent.ACTION_VIEW);
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//                    intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
//                    Uri contentUri = FileProvider.getUriForFile(context, "com.yuneec.android.saleelfin.fileprovider", new File(filePath));
//                    intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
//                } else {
//                    intent.setDataAndType(Uri.fromFile(new File(filePath)), "application/vnd.android.package-archive");
//                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                }
//                context.startActivity(intent);
//            }

        };

        private View.OnClickListener listener_btn=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(data.dler!=null){
                    if(data.dler.get_status()==CommonDownloader.Eventer.State_Stop){
                        if(Setting.Share_Setting().get_only_wifi()){
                            ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                            if (networkInfo == null) {
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_network_error)).show();
                                return ;
                            }
                            int nType = networkInfo.getType();
                            if (nType != ConnectivityManager.TYPE_WIFI) {
                                CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                                    @Override
                                    public void on_callback_success() {
                                        handler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (data != null && data.dler != null)
                                                    data.dler.go();
                                            }
                                        });
                                    }
                                    @Override
                                    public void on_callback_fail(int code, String msg) {}
                                }).show();
                                return;
                            }
                        }
                        data.dler.go();
                    }
                    else if(data.dler.get_status()==CommonDownloader.Eventer.State_Start)data.dler.stop();
                }
            }
        };

        public CTMItemDL(Context context) {
            super(context);
            this.setOrientation(LinearLayout.VERTICAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);
            this.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0xffffbb33)));
            this.setOnClickListener(listener_this);

            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(CL.MP, CL.WC);

            this.setLayoutParams(layoutParams);
            LinearLayout up_linear = new LinearLayout(context);
            up_linear.setOrientation(LinearLayout.HORIZONTAL);
            up_linear.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP,0,CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8)));
            // up_linear.setBackgroundColor(Color.BLUE);
            this.addView(up_linear);

            iv_icon=new ImageView(context);
            LinearLayout.LayoutParams params = CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0);
            params.gravity = Gravity.CENTER;
            iv_icon.setLayoutParams(params);
            up_linear.addView(iv_icon);

            params = CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC,0,0,CL.DIP2PX_INT(2),0);
            params.gravity = Gravity.CENTER;
            tv_title=CLController.Get_TextView(cc,params,"",Color.WHITE,12,null);
            tv_title.getPaint().setFakeBoldText(true);
            up_linear.addView(tv_title);

            LinearLayout _ll_info=new LinearLayout(context);
            _ll_info.setOrientation(LinearLayout.VERTICAL);
            _ll_info.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f,0,0,0,0));
            up_linear.addView(_ll_info);

            tv_speed=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP, CL.MP,1.0f),"0B/S",Color.WHITE,14,null);
            tv_speed.setSingleLine();
            tv_speed.setEllipsize(END);
            _ll_info.addView(tv_speed);
            LinearLayout _ll_size=new LinearLayout(context);
            _ll_size.setOrientation(LinearLayout.HORIZONTAL);
            _ll_size.setLayoutParams(CL.Get_LLLP(CL.MP, CL.MP,1.0f,0,CL.DIP2PX_INT(8),0,0));
            _ll_info.addView(_ll_size);
            tv_dl_size=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP, CL.WC, 1.0f),"",Color.WHITE,14,null);
            _ll_size.addView(tv_dl_size);
            tv_duration=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP, CL.WC,1.0f,0,0,0,0),"",Color.WHITE,14,null);
            _ll_size.addView(tv_duration);

            tv_perview=CLController.Get_TextView(cc, CL.Get_LLLP(CL.DIP2PX_INT(60),CL.MP),"",0xffaaaaaa,14,null);
            tv_perview.setGravity(Gravity.CENTER_VERTICAL);
            tv_perview.setVisibility(View.GONE);
            up_linear.addView(tv_perview);
            tv_div1=CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f);
            up_linear.addView(tv_div1);

            iv_play=new ImageView(context);
            params = CL.Get_LLLP(CL.DIP2PX_INT(60),CL.DIP2PX_INT(40));
            params.gravity = Gravity.CENTER;
            iv_play.setLayoutParams(params);
            iv_play.setScaleType(ImageView.ScaleType.CENTER);
            iv_play.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.play_normal,R.mipmap.play_press));
            iv_play.setClickable(true);
            iv_play.setOnClickListener(listener_btn_confirm_play);
            up_linear.addView(iv_play);

            tv_div2=CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f);
            up_linear.addView(tv_div2);

            iv_btn=new ImageView(context);
            params = CL.Get_LLLP(CL.DIP2PX_INT(60),CL.DIP2PX_INT(40));
            params.gravity = Gravity.CENTER;
            iv_btn.setLayoutParams(params);
            iv_btn.setScaleType(ImageView.ScaleType.CENTER);
            iv_btn.setClickable(true);
            iv_btn.setOnClickListener(listener_btn);
            up_linear.addView(iv_btn);

            progressBar=new ProgressBar(context);
            progressBar.setId(View.generateViewId());
            progressBar.setLayoutParams(CL.Get_LLLP(CL.MP,5,1.0f));
            //progressBar.setProgress(50);

            //  progressBar.setProgressTintList(ColorStateList.valueOf(Color.GREEN));
            //  progressBar.setMax(100);

            this.addView(progressBar);
        }

        private View.OnClickListener listener_btn_confirm_play=new OnClickListener() {
            @Override
            public void onClick(View v) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            CLTools.playFullSrceenMedia(cc, data.url);
                        }catch (Exception ex){
                            CL.CLOGE("open error:"+ex.toString(),ex);
                        }
                    }
                });
            }
        };

        public void set_baisc_data(Data.StructDLItem d){
            this.data=d;
            if(data.type_major==Data.Type_APK){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_apk_s);
                tv_title.setText("APK");
                iv_play.setVisibility(View.GONE);
            }
            else if(data.type_major==Data.Type_Music){
                if (data.downloaded) {
                    iv_play.setVisibility(View.GONE);
                    tv_div1.setVisibility(View.GONE);
                    tv_div2.setVisibility(View.GONE);
                    this.data.pos = 0;
                    this.data.max = 0;
                } else {
                    iv_play.setVisibility(View.VISIBLE);
                    tv_div1.setVisibility(View.VISIBLE);
                    tv_div2.setVisibility(View.VISIBLE);
                }
                iv_icon.setBackgroundResource(R.mipmap.res_icon_music_s);
                if(d.type_minor==Data.Type_Music_MP3)tv_title.setText("MP3");
                else if(d.type_minor==Data.Type_Music_OGG)tv_title.setText("OGG");
                else if(d.type_minor==Data.Type_Music_FLAC)tv_title.setText("FLAC");
                else if(d.type_minor==Data.Type_Music_WAV)tv_title.setText("WAV");
                else if(d.type_minor==Data.Type_Music_M4A)tv_title.setText("M4A");
                else tv_title.setText("???");
            //    iv_play.setVisibility(View.VISIBLE);
            }
            else if(data.type_major==Data.Type_Video){
                if (data.downloaded) {
                    iv_play.setVisibility(View.GONE);
                    tv_div1.setVisibility(View.GONE);
                    tv_div2.setVisibility(View.GONE);
                    this.data.pos = 0;
                    this.data.max = 0;
                } else {
                    iv_play.setVisibility(View.VISIBLE);
                    tv_div1.setVisibility(View.VISIBLE);
                    tv_div2.setVisibility(View.VISIBLE);
                }
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                if(d.type_minor==Data.Type_Video_MP4)tv_title.setText("MP4");
                else if(d.type_minor==Data.Type_Video_3GP)tv_title.setText("3GP");
                else if(d.type_minor==Data.Type_Video_MKV)tv_title.setText("MKV");
                else if(d.type_minor==Data.Type_Video_WEBM)tv_title.setText("WEBM");
                else tv_title.setText("???");
              //  iv_play.setVisibility(View.VISIBLE);
            }
            else if(d.type_major==Data.Type_M3U8){
                if (data.downloaded) {
                    iv_play.setVisibility(View.GONE);
                    tv_div1.setVisibility(View.GONE);
                    tv_div2.setVisibility(View.GONE);
                    this.data.pos = 0;
                    this.data.max = 0;
                } else {
                    iv_play.setVisibility(View.VISIBLE);
                    tv_div1.setVisibility(View.VISIBLE);
                    tv_div2.setVisibility(View.VISIBLE);
                }
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                tv_title.setText("M3U8");
                iv_play.setVisibility(View.VISIBLE);
            }
            else if(data.type_major==Data.Type_Doc){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_doc_s);
                tv_title.setText(d.suffix);
                iv_play.setVisibility(View.GONE);
            }
            else {
                iv_icon.setBackgroundResource(R.mipmap.res_icon_other_s);
                tv_title.setText(d.suffix);
                iv_play.setVisibility(View.GONE);
            }

            if(this.data.dler!=null){
                if(this.data.type_major==Data.Type_M3U8){
                    tv_dl_size.setText(CLTools.Get_Capacity_Format(this.data.dler.get_total_size()));
                    if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Start)
                        tv_speed.setText(CLTools.Get_Capacity_Format(this.data.dler.get_speed()) + "/S");
                    else tv_speed.setText(this.data.name!=null?this.data.name:this.data.title);

                    if (progressBar != null && this.data.max != 0) {
                        long v = (long)(this.data.pos * 100)/this.data.max;
                        progressBar.setVisibility(VISIBLE);
                        progressBar.setProgress((int)v);
                    } else {
                        progressBar.setVisibility(GONE);
                    }
                }else {
                    tv_dl_size.setText(CLTools.Get_Capacity_Format(this.data.dler.get_total_size()) + "/" + CLTools.Get_Capacity_Format(this.data.length));
                    if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Start)
                        tv_speed.setText(CLTools.Get_Capacity_Format(this.data.dler.get_speed()) + "/S");
                    else tv_speed.setText(this.data.name!=null?this.data.name:this.data.title);

                    if (progressBar != null && this.data.max != 0) {
                        long v = (long)(this.data.pos * 100)/this.data.max;
                        progressBar.setVisibility(VISIBLE);
                        progressBar.setProgress((int)v);
                    } else {
                        progressBar.setVisibility(GONE);
                    }
                }
            }else {
                tv_dl_size.setText("");
                tv_speed.setText("");
            }
            tv_duration.setVisibility(View.GONE);
            iv_btn.setVisibility(View.VISIBLE);
            if(this.data.downloaded){
                tv_dl_size.setVisibility(View.VISIBLE);
                tv_dl_size.setText(CLTools.Get_Capacity_Format(this.data.length));
                if(this.data.name!=null)tv_speed.setText(this.data.name);
                else tv_speed.setText(this.data.title);
                iv_btn.setVisibility(View.GONE);
                tv_duration.setVisibility(View.VISIBLE);
                if(this.data.duration!=null)tv_duration.setText(this.data.duration);
                else tv_duration.setText(null);
                tv_div2.setVisibility(View.GONE);
                progressBar.setVisibility(GONE);
            }else {
                if (this.data.dler != null) {
                    if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Start) {
                        iv_btn.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.pause_normal,R.mipmap.pause_press));
                    } else if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Complete) {
                        iv_btn.setVisibility(View.GONE);
                        iv_play.setVisibility(View.GONE);
                        progressBar.setVisibility(View.GONE);
                    } else if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Stop) {
                        iv_btn.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.download_normal,R.mipmap.download_press));
                    }

                    if (this.data.pos == 100) {
                        iv_play.setVisibility(View.GONE);
                        iv_btn.setVisibility(View.GONE);
                        tv_dl_size.setVisibility(View.GONE);
                        tv_duration.setVisibility(VISIBLE);
                        tv_duration.setText(cc.getResources().getString(R.string.str_start_packet));
                    }
                }
                tv_div2.setVisibility(View.VISIBLE);
            }
        }
    }

    public void showWait() {
        CL.CLOGI("Eddy showWait");
        if (hud == null) {
            hud = KProgressHUD.create(cc)
                    .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE);
            hud.setCancellable(false);
        }

        if (hud != null) {
            hud.show();
        }
    }

    public void hideWait() {
        CL.CLOGI("Eddy hideWait");
        if (hud != null) {
            hud.dismiss();
        }
    }
}
