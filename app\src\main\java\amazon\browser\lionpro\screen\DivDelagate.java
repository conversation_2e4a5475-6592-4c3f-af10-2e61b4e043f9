package amazon.browser.lionpro.screen;


import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ItemViewDelegate;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ViewHolder;

import java.util.List;


public class DivDelagate implements ItemViewDelegate<Data.StructDLItem> {
    AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> myAdapter;
    public DivDelagate(AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> adapter) {
        myAdapter = adapter;
    }

    @Override
    public int getItemViewLayoutId() {
        return R.layout.divlayout;
    }

    @Override
    public boolean isForViewType(Data.StructDLItem item, int position) {
        if (item.show_type == 2)
            return true;
        else
            return false;
    }

    @Override
    public void convert(ViewHolder holder, Data.StructDLItem structDLItem, int position) {
    }
}
