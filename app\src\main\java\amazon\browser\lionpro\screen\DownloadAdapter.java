package amazon.browser.lionpro.screen;

import android.content.Context;

import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.MultiItemTypeAdapter;

import java.util.List;


public class DownloadAdapter extends MultiItemTypeAdapter<Data.StructDLItem> implements AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> {
    private Context cc;
    private AdapterInterface.OnHolderItemClickListener listener;
    private CommonDownloader.Eventer mEventer;
    public DownloadAdapter(Context context, AdapterInterface.OnHolderItemClickListener itemClickListener, CommonDownloader.Eventer eventer, List<Data.StructDLItem> datas)
    {
        super(context, datas);
        cc = context;
        listener = itemClickListener;
        mEventer = eventer;
        addItemViewDelegate(new ReadyDLDelagate(this));
        addItemViewDelegate(new DownloadingDLDelagate(this));
        addItemViewDelegate(new DivDelagate(this));
    }

    @Override
    public Context GetContext() {
        return cc;
    }

    @Override
    public List<Data.StructDLItem> GetData() {
        return mDatas;
    }

    @Override
    public  AdapterInterface.OnHolderItemClickListener GetHolderItemClickListerer() {
        return listener;
    }

    @Override
    public CommonDownloader.Eventer GetDlEventer() {
        return mEventer;
    }
}
