package amazon.browser.lionpro.screen;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Environment;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.util.MenuBus;

import java.io.File;
import java.util.List;

import gp.BillingActivity;
import gp.BillingPurchaseDetails;
import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLFileChooser;
import lion.CLFileSystem;
import lion.CLInputer;
import lion.CLToast;
import lion.widget.CLFlipper;
import lion.widget.IosCheckBox;

/**
 * Created by leron on 2016/8/5.
 */
public class DownloadSettings extends LinearLayout implements CLFlipper.EventListener{

    private CLActivity cc;
    private CLFlipper flipper;
    private CLCallback.CB_Activity cber_activity;
    private View rpMenu;
    private TextView tv_min, tv_max;
    private LinearLayout min_layout, max_layout;
    private ImageView _iv_vip;
    private LinearLayout bottom;
    private MenuBus menuBus = new MenuBus() {
        @Override
        public void onActionSettingActivity(boolean show) {
            if (Setting.Share_Setting().get_redpoint() == 1)
                rpMenu.setVisibility(show ? VISIBLE : GONE);
        }
    };

    public DownloadSettings(CLActivity context, CLFlipper f, CLCallback.CB_Activity cber) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_activity=cber;

        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.str_cache_config).toString(),0xffd0d0d0,18,null));

      //  this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));
        init_UI();
    }

    private OnClickListener listener_ad=new OnClickListener() {
        @Override
        public void onClick(View v) {
//            Global.Show_Interstitial_IM(cc, new CLCallback.CB() {
//                @Override
//                public void on_callback() {
//                }
//            });
        }
    };

    private void init_UI(){
        ScrollView _sv=new ScrollView(cc);
        _sv.setLayoutParams(CL.Get_LLLP(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, 1.0f));
        this.addView(_sv);

        LinearLayout _ll=new LinearLayout(cc);
        _ll.setOrientation(LinearLayout.VERTICAL);
        _ll.setClickable(true);
        _sv.addView(_ll);
        _ll.addView(get_only_wifi());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));

        _ll.addView(get_m3u8_merge());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));

        _ll.addView(get_Thread());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
      //  _ll.addView(get_downloadMode());
//        _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));

        _ll.addView(get_export_folder());
    //    _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff323232));
        downlaod_mode = Setting.Share_Setting().get_download_path_mode();

        if (Global.IsAndroid10()) {
            downlaod_mode = false;
        }
        _ll.addView(get_filter_title());

        _ll.addView(get_download_mini_size());
       // _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff323232));

        _ll.addView(get_download_max_size());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff323232));


        bottom =new LinearLayout(cc);
        bottom.setOrientation(LinearLayout.VERTICAL);

        bottom.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC));
        this.addView(bottom);

        int pos = Setting.Share_Setting().get_ads_pos();
        int banner_type = Setting.Share_Setting().get_banner_type();
        Global.Get_banner(cc, bottom, pos==1?0:1, banner_type, null);
        initViewModel();
    }

    private void initViewModel() {
        if (BillingActivity.viewModel != null) {
            BillingActivity.viewModel.mProductEvent.observe(cc, new Observer<List<BillingPurchaseDetails>>() {
                @Override
                public void onChanged(List<BillingPurchaseDetails> list) {
                    Setting.Share_Setting().set_subscription_flag(false);
                }
            });
            BillingActivity.viewModel.mHaveSubscribeEvent.observe(cc, new Observer<String>() {
                @Override
                public void onChanged(String list) {
                    Setting.Share_Setting().set_subscription_flag(true);
                    if (bottom != null && _iv_vip != null) {
                        bottom.setVisibility(View.GONE);
                        _iv_vip.setVisibility(View.GONE);
                    }
                }
            });
        }
    }

    private LinearLayout get_download_mini_size(){
        LinearLayout _ll =CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        min_layout = _ll;
        if (Setting.Share_Setting().get_filter_switch()) {
            min_layout.setVisibility(View.VISIBLE);
        } else {
            min_layout.setVisibility(View.GONE);
        }
        _ll.setBackgroundColor(0xff22282c);
        _ll.setClickable(true);
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                long min_size = Setting.Share_Setting().get_filter_mini_size();

                CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                    @Override
                    public boolean on_callback_success(String obj, String msg) {

                        if (obj != null) {
                            long size = Long.valueOf(obj);
                            if (size < 0 || size > 9223372036854775807L) {
                                CLToast.Show(cc, cc.getString(R.string.str_max_number_tip),true);
                                return false;
                            }
                            Setting.Share_Setting().set_filter_mini_size(Long.valueOf(obj));
                            tv_min.setText(obj);
                            return true;
                        }
                        return false;
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {

                    }
                }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                        .show(cc.getString(R.string.str_filter_min_size), String.valueOf(min_size));
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getString(R.string.str_filter_min_size),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);


        long min_size = Setting.Share_Setting().get_filter_mini_size();
        TextView _tv=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                String.valueOf(min_size),Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(_tv);
        tv_min = _tv;

        TextView tx_size = CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                cc.getString(R.string.str_byte),Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(tx_size);
        return _ll;
    }

    private LinearLayout get_download_max_size(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        max_layout = _ll;
        if (Setting.Share_Setting().get_filter_switch()) {
            max_layout.setVisibility(View.VISIBLE);
        } else {
            max_layout.setVisibility(View.GONE);
        }
        _ll.setBackgroundColor(0xff22282c);
        _ll.setClickable(true);
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                long max_size = Setting.Share_Setting().get_filter_max_size();
                CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                    @Override
                    public boolean on_callback_success(String obj, String msg) {
                        if (obj != null) {
                            long size = Long.valueOf(obj);


                            if (size > 9223372036854775807L) {
                                CLToast.Show(cc, cc.getString(R.string.str_max_number_tip),true);
                                return false;
                            }

                            if (size <= 0) {
                                Setting.Share_Setting().set_filter_max_size(-1);
                                tv_max.setText("∞");
                            } else {
                                Setting.Share_Setting().set_filter_max_size(Long.valueOf(obj));
                                tv_max.setText(obj);
                            }

                        }
                        return true;
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {

                    }
                }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                        .show(cc.getString(R.string.str_filter_max_size),  max_size == -1 ? "∞" : String.valueOf(max_size));
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getString(R.string.str_filter_max_size),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        long max_size = Setting.Share_Setting().get_filter_max_size();
        TextView _tv=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                max_size == -1 ? "∞" : String.valueOf(max_size),Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(_tv);
        tv_max = _tv;
        TextView tx_size = CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                cc.getString(R.string.str_byte),Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(tx_size);
        return _ll;
    }

    private LinearLayout get_only_wifi(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),cc.getResources().getString(R.string.only_wifi),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        _checker.set_check(Setting.Share_Setting().get_only_wifi());
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                Setting.Share_Setting().set_only_wifi(check);
            }
        });
        _ll.addView(_checker);
        return _ll;
    }

    private TextView tv_export_dir;
    private TextView tv_export_dir_title;
    private boolean downlaod_mode = false;
    private LinearLayout get_export_folder(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.HORIZONTAL,null);
        _ll.setClickable(true);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));

        LinearLayout _ll_header=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f),LinearLayout.VERTICAL,null);
        _ll.addView(_ll_header);

//        tv_export_dir_title = CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),CL.DIP2PX_INT(10),0,0),
//                cc.getResources().getString(R.string.export_folder),Color.WHITE,14,null);
        tv_export_dir_title = CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),CL.DIP2PX_INT(10),0,0),
                cc.getResources().getString(R.string.str_new_download_name),Color.WHITE,14,null);

        _ll_header.addView(tv_export_dir_title);

        String _dir=Setting.Share_Setting().get_export_dir();
        tv_export_dir=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(3),0,CL.DIP2PX_INT(10)),_dir,
                Color.WHITE,12,null);
        _ll_header.addView(tv_export_dir);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _iv_arrow.setVisibility(View.INVISIBLE);
        _ll.addView(_iv_arrow);

        if (Global.Dir_Root_1 != null) {
            tv_export_dir.setText(Global.Dir_Root_1.getAbsolutePath());
        }
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                //权限处理
                if (!downlaod_mode) {
                    try {
                        CLDialog.Get_Alert_Scroll_Dialog(cc, cc.getResources().getString(R.string.my_download_tip), 10000, 1000, null).show();
                    } catch (Exception e) {

                        e.printStackTrace();
                    }
                    return;
                }
                if (ContextCompat.checkSelfPermission(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        != PackageManager.PERMISSION_GRANTED) {
                    if (ActivityCompat.shouldShowRequestPermissionRationale(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                        cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                            @Override
                            public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                                if (grant_results[0] == PackageManager.PERMISSION_GRANTED) {
                                    show_file_chooser();
                                } else {
                                    CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_open_storage)).show();
                                }
                            }
                        });
                    } else {
                        cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                            @Override
                            public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                                if (grant_results[0] == PackageManager.PERMISSION_GRANTED) {
                                    show_file_chooser();
                                } else {
                                    CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_open_storage)).show();
                                }
                            }
                        });
                    }
                } else {
                    CL.CLOGI("write external storage is granted");
                    show_file_chooser();
                }
            }
        });
        return _ll;
    }


    private void show_file_chooser(){
        String[] _paths=null;
        if(!CLFileSystem.Whether_Availability()){
            CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.not_found_SD)).show();
            return;
        }
        _paths=new String[2];
        _paths[0]=new File(Setting.Share_Setting().get_export_dir()).getAbsolutePath();
        _paths[1]=Environment.getExternalStorageDirectory().getAbsolutePath();
        if(_paths==null)return;
        CLFileChooser _chooser=new CLFileChooser(cc, true, _paths, new CLCallback.CBO<String>() {
            @Override
            public void on_callback(String obj) {
                File _f=new File(obj);
                if(_f.exists()){
                    Setting.Share_Setting().set_export_dir(_f.getAbsolutePath());
                    tv_export_dir.setText(_f.getAbsolutePath());
                    Global.reInitStore(Global.Acy_Main.getApplicationContext());
                    Server.reInit(Global.Acy_Main.getApplicationContext());
                }
            }
        });
        _chooser.set_resource(R.style.pop_from_bottom, R.mipmap.icon_folder,R.mipmap.icon_folder_add_normal,R.mipmap.icon_folder_add_click);
        _chooser.show();
    }

    private LinearLayout get_m3u8_merge(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),cc.getString(R.string.str_merge_m3u8),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        final IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        boolean m3u8_merge_mode = Setting.Share_Setting().get_m3u8_merge_mode();
        _checker.set_check(m3u8_merge_mode);
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                //CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui,Global.Action_fullscreen, Boolean.valueOf(check));

                if (!Setting.Share_Setting().get_m3u8_merge_mode_tip()) {
                    CLDialog.Get_Alert_Scroll_Dialog(cc, cc.getString(R.string.str_merge_m3u8_tip), 10000, 1000, null).show();
                    Setting.Share_Setting().set_m3u8_merge_mode_tip(true);
                    box.set_check(!check);
                } else {
                    Setting.Share_Setting().set_m3u8_merge_mode(check);
                }
            }
        });
        _ll.addView(_checker);
        return _ll;
    }

    private LinearLayout get_Thread(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setClickable(true);

        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.str_thread_num),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        int _s= Setting.Share_Setting().get_thread_number();
        String thread_number = String.valueOf(_s);

        final TextView _tv=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                thread_number,Color.WHITE,16,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(_tv);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(30),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);


        _iv_vip=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.iconfont_premium,R.mipmap.iconfont_premium),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_vip);

        if (Setting.Share_Setting().get_subscription_flag()) {
            _iv_vip.setVisibility(View.GONE);
        }

        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                show_thread_UA(new CLCallback.CB(){
                    @Override
                    public void on_callback() {

                        int _ss = Setting.Share_Setting().get_thread_number();
                        _tv.setText(String.valueOf(_ss));

                    }
                });
            }
        });
        return _ll;
    }

    private CLDialog dialog_UA;
    private View dialog_content_UA;
    private void show_thread_UA(final CLCallback.CB cber){
        if (!Setting.Share_Setting().get_subscription_flag()) {
            cc.startActivityForResult(new Intent(cc, BillingActivity.class), 10004);
            return;
        }
        if(dialog_content_UA ==null){
            LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP_WW(),LinearLayout.VERTICAL,null);
            _ll.setPadding(CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),0);
            _ll.setClickable(true);

            LinearLayout _ll_btns=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.VERTICAL,null);
            _ll_btns.setBackground(get_bg());
            _ll_btns.setGravity(Gravity.CENTER);
            _ll.addView(_ll_btns);

            for (int i=0; i<8; i++) {
                if (i !=0) {
                    _ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
                }

                TextView tv = get_button(cc, String.valueOf(i+1), new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog_UA.dismiss();
                        Integer value = (Integer) v.getTag();
                        Setting.Share_Setting().set_thread_number(value);
                        if(cber!=null)cber.on_callback();
                    }
                });
                tv.setTag(new Integer(i+1));
                _ll_btns.addView(tv);
            }

            LinearLayout _ll_cancel=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12))
                    ,LinearLayout.VERTICAL,null);
            _ll_cancel.setBackground(get_bg());
            _ll_cancel.setGravity(Gravity.CENTER);
            _ll_cancel.setPadding(CL.DIP2PX_INT(6), 0, CL.DIP2PX_INT(6), 0);
            _ll.addView(_ll_cancel);
            TextView _btn_cancel=get_button(cc, cc.getResources().getString(R.string.cancel), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_UA.dismiss();
                }
            });
            _btn_cancel.setTextColor(cc.getResources().getColor(R.color.colorPrimary3));
            _btn_cancel.setPadding(0, 0, 0, 0);
            _ll_cancel.addView(_btn_cancel);
            dialog_content_UA =_ll;
            dialog_UA =CLDialog.Get_Dialog_Animation_From_Bottom(cc, dialog_content_UA);
        }
        dialog_UA.show();
    }

    private Drawable get_bg(){
        RoundRectShape _shape=new RoundRectShape(new float[]{12,12,12,12,12,12,12,12}, null, null);
        ShapeDrawable _drawable=new ShapeDrawable(_shape);
        _drawable.getPaint().setColor(0xffffffff);
        _drawable.getPaint().setStyle(Paint.Style.FILL);
        int _pad=CL.DIP2PX_INT(6);
        _drawable.setPadding(0, _pad, 0, _pad);
        return _drawable;
    }

    private TextView get_button(Context cc, String name, final View.OnClickListener listener){
        TextView _btn=CLController.Get_Button(cc, CL.Get_LP(CL.MP, CL.WC), name, cc.getResources().getColor(R.color.colorPrimary3),
                16,
                CL.Get_StateList_Drawable(new ColorDrawable(0xffffffff), new ColorDrawable(0xffe0e0e0)),
                listener);
        _btn.setPadding(0,CL.DIP2PX_INT(10),0,CL.DIP2PX_INT(10));
        _btn.setGravity(Gravity.CENTER);
        return _btn;
    }

    private LinearLayout get_downloadMode(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),cc.getString(R.string.str_direct_download_tip),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        final IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        boolean download_path_mode = Setting.Share_Setting().get_download_path_mode();
        _checker.set_check(download_path_mode);
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                //CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui,Global.Action_fullscreen, Boolean.valueOf(check));

                if (!Setting.Share_Setting().get_download_path_mode_tip()) {
                    CLDialog.Get_Alert_Scroll_Dialog(cc, cc.getString(R.string.str_new_download_path_tip),
                            10000, 1000, null).show();
                    Setting.Share_Setting().set_download_path_mode_tip(true);
                    box.set_check(!check);
                } else {
                    downlaod_mode = check;
                    Setting.Share_Setting().set_download_path_mode(downlaod_mode);
                    Global.reInitStore(Global.Acy_Main.getApplicationContext());
                    Server.reInit(Global.Acy_Main.getApplicationContext());
                    if (downlaod_mode) {
                        tv_export_dir_title.setText(cc.getResources().getString(R.string.str_new_download_name));
                    } else {
                       // tv_export_dir_title.setText(cc.getResources().getString(R.string.export_folder));
                        tv_export_dir_title.setText(cc.getResources().getString(R.string.str_new_download_name));
                    }

                }
            }
        });
        _ll.addView(_checker);
        return _ll;
    }

    private LinearLayout get_filter_title(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setClickable(true);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
//        _ll.setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                flipper.go_next(new About(cc,flipper));
//            }
//        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getString(R.string.str_sniffer_filter),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        _checker.set_check(Setting.Share_Setting().get_filter_switch());
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                Setting.Share_Setting().set_filter_switch(check);
                if (max_layout != null && min_layout != null) {
                    if (check) {
                        min_layout.setVisibility(View.VISIBLE);
                        max_layout.setVisibility(View.VISIBLE);
                    } else {
                        min_layout.setVisibility(View.GONE);
                        max_layout.setVisibility(View.GONE);
                    }
                }
            }
        });
        _ll.addView(_checker);
        return _ll;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        menuBus.register(cc);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        menuBus.unregister();
    }

    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }
}
