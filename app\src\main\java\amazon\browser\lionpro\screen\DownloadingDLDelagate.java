package amazon.browser.lionpro.screen;


import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.view.View;
import android.widget.ImageView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ItemViewDelegate;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ViewHolder;
import amazon.browser.lionpro.views.ProgressBar;

import java.io.File;
import java.io.FileOutputStream;
import java.util.List;

import lion.CL;
import lion.CLTools;

public class DownloadingDLDelagate implements ItemViewDelegate<Data.StructDLItem> {
    AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> mAdapter;
    private Handler handler = new Handler(Looper.getMainLooper());
    public DownloadingDLDelagate(AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> adapter) {
        mAdapter = adapter;
    }

    @Override
    public int getItemViewLayoutId() {
        return R.layout.downloadinglayout;
    }

    @Override
    public boolean isForViewType(Data.StructDLItem item, int position) {
        if (item.show_type == 3 && !item.downloaded)
            return true;
        else
            return false;
    }

    @Override
    public void convert(ViewHolder holder, Data.StructDLItem item, int position) {
        item.setHolder(holder);
        item.setDownloadingDLDelagate(this);
        setData(holder, item, position);
        //updateDownloadStatus(holder, item, position);
        bindEvent(holder, item, position);
    }

    public void setData(ViewHolder holder, Data.StructDLItem item, int position) {
        if (item.type_major == Data.Type_APK) {
            holder.setImageResource(R.id.v_icon, R.mipmap.res_icon_apk_s);
            holder.setText(R.id.tv_name, item.title);
            holder.setText(R.id.tv_format, "APK");
            updateSpeed(holder, item);

        } else if (item.type_major == Data.Type_Music) {
            holder.setImageResource(R.id.v_icon, R.mipmap.res_icon_music_s);
            holder.setText(R.id.tv_name, item.title);
            int type_minor = item.type_minor;
            if (type_minor == Data.Type_Music_MP3) holder.setText(R.id.tv_format, "MP3");
            else if(type_minor == Data.Type_Music_OGG) holder.setText(R.id.tv_format, "OGG");
            else if(type_minor == Data.Type_Music_FLAC) holder.setText(R.id.tv_format, "FLAC");
            else if(type_minor == Data.Type_Music_WAV) holder.setText(R.id.tv_format, "WAV");
            else if(type_minor == Data.Type_Music_M4A) holder.setText(R.id.tv_format, "M4A");
            else holder.setText(R.id.tv_format, "???");
            updateSpeed(holder, item);
        } else if (item.type_major == Data.Type_Video) {
            holder.setImageResource(R.id.v_icon, R.mipmap.res_icon_video_s);
            holder.setText(R.id.tv_name, item.title);
            int type_minor = item.type_minor;
            if (type_minor == Data.Type_Video_MP4) holder.setText(R.id.tv_format, "MP4");
            else if(type_minor == Data.Type_Video_3GP) holder.setText(R.id.tv_format, "3GP");
            else if(type_minor == Data.Type_Video_MKV) holder.setText(R.id.tv_format, "MKV");
            else if(type_minor == Data.Type_Video_WEBM)holder.setText(R.id.tv_format, "WEBM");
            else holder.setText(R.id.tv_format, "???");
            updateSpeed(holder, item);
        } else if (item.type_major == Data.Type_M3U8) {
            holder.setImageResource(R.id.v_icon, R.mipmap.res_icon_video_s);
            holder.setText(R.id.tv_name, item.title);
            holder.setText(R.id.tv_format, "M3U8");

            if (item.dler != null) {
                holder.setText(R.id.tv_length, CLTools.Get_Capacity_Format(item.dler.get_total_size()));
                if (item.dler.get_status() == CommonDownloader.Eventer.State_Start) {
                    holder.setText(R.id.tv_speed, CLTools.Get_Capacity_Format(item.dler.get_speed()) + "/S");
                } else {
                    holder.setText(R.id.tv_speed, "???/S");
                }
            }
        } else if (item.type_major == Data.Type_Doc) {
            holder.setImageResource(R.id.v_icon, R.mipmap.res_icon_doc_s);
            holder.setText(R.id.tv_name, item.title);
            holder.setText(R.id.tv_format, item.suffix);
            updateSpeed(holder, item);
        } else {
            holder.setImageResource(R.id.v_icon, R.mipmap.res_icon_other_s);
            holder.setText(R.id.tv_name, item.title);
            holder.setText(R.id.tv_format, item.suffix);
            updateSpeed(holder, item);
        }

        ProgressBar progressBar = holder.getView(R.id.v_progress);
        if (progressBar != null && item.max != 0) {
            long v = (long)(item.pos * 100)/item.max;
            progressBar.setProgress((int)v);
        }
        updateDownloadStatus(holder, item, position);
    }

    private  void updateSpeed(ViewHolder holder, Data.StructDLItem item) {
        if (item.dler != null) {
            holder.setText(R.id.tv_length, CLTools.Get_Capacity_Format(item.dler.get_total_size()) + "/" + CLTools.Get_Capacity_Format(item.length));
            if (item.dler.get_status() == CommonDownloader.Eventer.State_Start) {
                holder.setText(R.id.tv_speed, CLTools.Get_Capacity_Format(item.dler.get_speed()) + "/S");
            } else {
                holder.setText(R.id.tv_speed, "???/S");
            }
        }
    }

    private void bindEvent(ViewHolder holder, Data.StructDLItem item, int position) {
        ImageView img_more = holder.getView(R.id.btn_more);
        img_more.setTag(item);
        img_more.setOnClickListener((v)-> {
            if (mAdapter != null) {
                handler.post(()-> {
                    Data.StructDLItem tmp_item = (Data.StructDLItem) img_more.getTag();
                    //img_more.setTag(holder.getView(R.id.tv_name));
                    //updateDownloadStatus(holder, tmp_item, position);
                    mAdapter.GetHolderItemClickListerer().onItemClick(v, tmp_item, 1);
                });
            }
        });
        ImageView img_download = holder.getView(R.id.btn_download);
        img_download.setTag(item);
        img_download.setOnClickListener((v)->{
            if (mAdapter != null && handler != null) {
                handler.post(()-> {
                    Data.StructDLItem tmp_item = (Data.StructDLItem)img_download.getTag();
                    //updateDownloadStatus(holder, tmp_item, position);
                    mAdapter.GetHolderItemClickListerer().onItemClick(v, tmp_item, 2);
                });
            }
        });
    }

    private void updateDownloadStatus(ViewHolder holder, Data.StructDLItem item, int position) {
        if (item.dler != null) {
            if (item.dler.get_status() == CommonDownloader.Eventer.State_Start) {
                ImageView img = (ImageView) holder.getView(R.id.btn_download);
                img.setImageDrawable(CL.Get_StateList_Drawable(mAdapter.GetContext(), R.mipmap.dl_pause_normal,R.mipmap.dl_pause_press));
            } else if (item.dler.get_status() == CommonDownloader.Eventer.State_Complete) {
                ProgressBar progressBar = holder.getView(R.id.v_progress);
                progressBar.setVisibility(View.GONE);
            } else if (item.dler.get_status() == CommonDownloader.Eventer.State_Stop) {
                ImageView img = (ImageView) holder.getView(R.id.btn_download);
                //img.setImageResource(R.mipmap.dl_download_normal);
                img.setImageDrawable(CL.Get_StateList_Drawable(mAdapter.GetContext(), R.mipmap.dl_download_normal,R.mipmap.dl_download_press));
            }
        }
    }


    private CommonDownloader.Eventer listener_downloader=new CommonDownloader.Eventer() {
        @Override
        public void on_state_change(Data.StructDLItem item, int state) {
            handler.post(()-> {
                if (state == CommonDownloader.Eventer.State_Complete) {
                    item.downloaded = true;
                    if (!Setting.Share_Setting().get_complete_first_download()) {
                        Setting.Share_Setting().set_complete_first_download(true);
                    }
                    if (item.type_major == Data.Type_Video || item.type_major == Data.Type_Music) {
                        if (item.duration == null) {
                            try {
                                MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                                retriever.setDataSource(item.path);
                                String _ddd = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                                item.duration = CLTools.Get_Media_Duration(Integer.parseInt(_ddd));
                                CL.CLOGI("duration:" + _ddd);
                                File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                if (!_thumb.exists()) {
                                    Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path, MediaStore.Video.Thumbnails.MINI_KIND);
                                    if (bitmap_thumb != null) {
                                        bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                    }
                                }
                            } catch (Exception ex) {
                            }
                        }
                    } else if (item.type_major == Data.Type_M3U8) {
                        try {
                            File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                            if (!_thumb.exists()) {
                                Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path + "/1", MediaStore.Video.Thumbnails.MINI_KIND);
                                if (bitmap_thumb != null) {
                                    bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                }
                            }
                        } catch (Exception ex) {
                            CL.CLOGE("m3u8 thum error:" + ex.toString(), ex);
                        }
                    } else if (item.type_major == Data.Type_APK) {
                        //生成
                        //扫描apk
                        Context cc = mAdapter.GetContext();
                        try {
                            File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                            if (!_thumb.exists()) {
                                PackageManager _pm = cc.getPackageManager();
                                PackageInfo _file_info = _pm.getPackageArchiveInfo(item.path, 0);
                                if (_file_info != null) {
                                    ApplicationInfo _app_info = _file_info.applicationInfo;
                                    _app_info.sourceDir = item.path;
                                    _app_info.publicSourceDir = item.path;
                                    item.name = _app_info.loadLabel(_pm).toString();
                                    if (item.name != null)
                                        Server.Update_Download_Name(item, item.name);
                                    Drawable _dwe = _app_info.loadIcon(_pm);
                                    int w = _dwe.getIntrinsicWidth();
                                    int h = _dwe.getIntrinsicHeight();
                                    Bitmap.Config config = _dwe.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
                                    Bitmap bitmap = Bitmap.createBitmap(w, h, config);
                                    Canvas canvas = new Canvas(bitmap);
                                    _dwe.setBounds(0, 0, w, h);
                                    _dwe.draw(canvas);
                                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, new FileOutputStream(_thumb));
                                }
                            }
                        } catch (Exception ex) {
                        }
                    }
                    Server.Update_Download(item);
                    Server.Share_FileManager().notify_download_complete(item);
                }


                if (mAdapter != null) {
                    mAdapter.GetDlEventer().on_state_change(item, state);
                }
            });
        }

        @Override
        public void on_load_ratio(Data.StructDLItem item, long dl_size, float ratio) {
            if (handler != null) {
                handler.post(()->{
                    ViewHolder holder = item.getHolder();
                    setData(holder, item, 0);
                    if (mAdapter != null) {
                        mAdapter.GetDlEventer().on_load_ratio(item, dl_size, ratio);
                    }

                });
            }
        }

        @Override
        public void on_error(Data.StructDLItem item, int code) {
            if (mAdapter != null) {
                mAdapter.GetDlEventer().on_error(item, code);
            }
        }

        @Override
        public void on_delete(Data.StructDLItem item, boolean success) {
            if (mAdapter != null) {
                mAdapter.GetDlEventer().on_delete(item, success);
            }
        }
    };
}
