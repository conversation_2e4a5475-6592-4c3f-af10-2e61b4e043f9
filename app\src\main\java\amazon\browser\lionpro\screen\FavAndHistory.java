package amazon.browser.lionpro.screen;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Bundle;
import android.os.Handler;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.views.Favorites;
import amazon.browser.lionpro.views.History;
import amazon.browser.lionpro.views.ViewTaber;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLHelper;

/**
 * Created by leron on 2016/4/15.
 */
public class FavAndHistory extends Dialog{

    private Activity cc;
    private FAH ctm_view;
    public FavAndHistory(Activity context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);
        this.cc=context;
        ctm_view=new FAH(this.getContext());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int _sh=CL.Set_Translucent_StatusBar(this.getWindow());
        ctm_view.setPadding(0,_sh,0,0);
        this.setContentView(ctm_view);
    }

    @Override
    public void onBackPressed() {
        dismiss();
    }

    @Override
    public void dismiss() {
        if(is_dismissing)return;
        is_dismissing=true;
        ctm_view.go_dismiss();
    }

    private boolean is_dismissing=false;

    private Animation.AnimationListener listener_anim_hide=new Animation.AnimationListener() {
        @Override
        public void onAnimationStart(Animation animation) {
        }
        @Override
        public void onAnimationEnd(Animation animation) {
            FavAndHistory.super.dismiss();
        }
        @Override
        public void onAnimationRepeat(Animation animation) {

        }
    };

    private class FAH extends LinearLayout{


        private ViewTaber taber;
        private FrameLayout fl_view;
        private TranslateAnimation anim_show,anim_hide;
        private ViewPager pager;
        private Favorites view_fav;
        private History view_history;

        private ViewTaber.Eventer listener_taber=new ViewTaber.Eventer() {
            @Override
            public void on_click_left() {
                pager.arrowScroll(View.FOCUS_LEFT);
            }
            @Override
            public void on_click_right() {
                pager.arrowScroll(View.FOCUS_RIGHT);
            }
        };


        public FAH(Context context) {
            super(context);
            this.setClickable(true);
            this.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
            this.setMotionEventSplittingEnabled(false);
            this.setOrientation(LinearLayout.VERTICAL);
            this.setGravity(Gravity.CENTER_HORIZONTAL);
            this.setBackgroundColor(context.getResources().getColor(R.color.bg_main));

            taber=new ViewTaber(context,listener_taber);
            taber.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(63)));
            this.addView(taber);

            fl_view=new FrameLayout(context);
            fl_view.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
            this.addView(fl_view);
            this.addView(CLController.Get_TextView_Divider(context,CL.Get_LLLP(CL.MP,1),0xff313131));
            FrameLayout _fl_arrow=new FrameLayout(context);
            _fl_arrow.setLayoutParams(CL.Get_LP(CL.WC,CL.DIP2PX_INT(40)));
            _fl_arrow.addView(CLController.Get_ImageView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                    context.getResources().getDrawable(R.mipmap.down_arrow),null));
            this.addView(_fl_arrow);

            view_fav=new Favorites(context);
            view_history=new History(context);
            pager=new ViewPager(context);
            pager.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL));
            pager.addOnPageChangeListener(listener_viewpager);
            pager.setAdapter(new AdapterForPageview());
            fl_view.addView(pager);

            anim_show=new TranslateAnimation(Animation.RELATIVE_TO_SELF,0,Animation.RELATIVE_TO_SELF,0,
                    Animation.RELATIVE_TO_SELF,1.0f,Animation.RELATIVE_TO_SELF,0);
            anim_show.setDuration(200);

            anim_hide=new TranslateAnimation(Animation.RELATIVE_TO_SELF,0,Animation.RELATIVE_TO_SELF,0,
                    Animation.RELATIVE_TO_SELF,0.0f,Animation.RELATIVE_TO_SELF,1.0f);
            anim_hide.setDuration(200);
            anim_hide.setAnimationListener(listener_anim_hide);
        }

        private void go_show(){
            this.startAnimation(anim_show);
        }

        private void go_dismiss(){
            this.startAnimation(anim_hide);
        }



        private ViewPager.OnPageChangeListener listener_viewpager=new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }
            @Override
            public void onPageSelected(int position) {
                taber.set_selected_index(position+1);
            }
            @Override
            public void onPageScrollStateChanged(int state) {

            }
        };
        private class AdapterForPageview extends PagerAdapter{
            @Override
            public int getCount() {
                return 2;
            }
            @Override
            public boolean isViewFromObject(View view, Object object) {
                return view==object;
            }

            @Override
            public Object instantiateItem(ViewGroup container, int position) {
                View _v=position==0?view_fav:view_history;
                container.addView(_v);
                return _v;
            }
        }



    }

    @Override
    public void show() {
        super.show();
        is_dismissing=false;
        ctm_view.go_show();

        if(!Setting.Share_Setting().get_tip(Setting.Type_fav_history)){

            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                            cc.getResources().getString(R.string.tip_can_check), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_fav_history,true);
                        }
                    }).show();
                }
            },500);
        }
    }

}
