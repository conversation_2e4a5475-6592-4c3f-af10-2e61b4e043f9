package amazon.browser.lionpro.screen;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.net.Uri;
import android.os.Build;

import androidx.drawerlayout.widget.DrawerLayout;

import android.os.Handler;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import amazon.browser.Interfaces.IWinOperator;
import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Affairs;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MultiResolution;
import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.downloader.extend.WebViewCallBack;
import amazon.browser.lionpro.primary.AcyMain;
import amazon.browser.lionpro.primary.AcySetting;
import amazon.browser.lionpro.primary.AcyStorage;
import amazon.browser.lionpro.primary.AcyWifiShare;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.primary.Mainly;
import amazon.browser.lionpro.primary.MoreApps;
import amazon.browser.lionpro.toys.DrawerArrowDrawable;
import amazon.browser.lionpro.toys.Tools;
import amazon.browser.lionpro.util.MenuBus;
import amazon.browser.lionpro.views.DialogStoragePicture;
import amazon.browser.lionpro.views.DialogWebsiteSniffer;
import amazon.browser.lionpro.views.Favorites;
import amazon.browser.lionpro.views.FloatButton;
import amazon.browser.lionpro.views.Leida;
import amazon.browser.lionpro.views.MusicView;
import amazon.browser.lionpro.views.MusicViewDialog;
import amazon.browser.lionpro.views.WebAddressStatusBar;
import amazon.browser.lionpro.views.WebToolBar;
import com.google.android.gms.ads.AdView;
import com.nineoldandroids.view.ViewHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.regex.Pattern;

import gp.BillingActivity;
//import gp.PremiumActivity;
import lion.CL;
import lion.CLBitmapLoader;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLFileSystem;
import lion.widget.CLWebkit;
import lion.widget.WebPageScrollView;

/**
 * Created by leron on 2016/4/6.
 */
public class Home extends DrawerLayout{

    private int mBarHeight;
    private String list_Header_Params = "";
    private boolean isFinish;

    public Home(AcyMain context) {
        super(context);
        this.cc=context;
        s_home = this;
        this.mDrawerLayout = this;
        iwinoper = new WinOperator();
        list_weber = new ArrayList<CLWebkit>(10);
        Server.Set_Sniff_Listener(listener_sniffer);
        init();

        //update_UA();
    }
    private MenuBus menuBus = new MenuBus() {
        @Override
        public void onActionMainMenuIcon(boolean show) {
            if (Setting.Share_Setting().get_redpoint() == 1)
                rpMenu.setVisibility(show ? VISIBLE : GONE);
        }
    };

    private AcyMain cc;
    private WebPageScrollView ll_main;
    private LinearLayout ll_header;
    private WebAddressStatusBar addresser;
    private ProgressBar progressbar;
    private FrameLayout fl_webview;
    private CLWebkit weber;
    private HomeGrid home_grid;
    private ArrayList<CLWebkit> list_weber;
    private MusicView music;
    private MusicViewDialog dialog_music;
    private WebToolBar toolbar;
    private MainMenu layer_left;
    private DList layer_right;
 //   private CacheList layer_right;
    private ImageView btn_sniffer;
    private TextView update_tip_view;
    private ImageView btn_screen_change;
    private DialogStoragePicture dialog_storage_picture;
    private DialogWebsiteSniffer dialog_sniffer;
    private View rpMenu;

    private Leida leida;
    private ImageView iv_back;
    private DrawerArrowDrawable dwr_back;
    private float offset;
    private boolean flipped;
    private float offset_left,offset_right;
    private boolean offset_qipa;
    private AdView m_AdView;
    private View m_divid_t,m_divid_b;
    private WinOperator iwinoper;
    private DrawerLayout mDrawerLayout;

    public void updateAdsStatus() {
        if (home_grid != null) {
            home_grid.updateAdsStatus();
        }
        if (Setting.Share_Setting().get_subscription_flag() && m_AdView != null) {
            m_AdView.setVisibility(View.GONE);
        }
    }

    public WebToolBar getToolbar() {
        return toolbar;
    }

    private DrawerLayout.DrawerListener listener_drawerlayout=new DrawerListener() {
        @Override
        public void onDrawerSlide(View drawerView, float slideOffset) {

            if (drawerView==layer_left) {
                offset_left = slideOffset;

            }
            else if(drawerView== layer_right) {
                offset_right=slideOffset;
                View mContent = mDrawerLayout.getChildAt(0);

                if (layer_right.get_state() == 1) {
                    ViewHelper.setTranslationX(mContent,-drawerView.getMeasuredWidth() * slideOffset);
                }
            }

            if(offset_left>0 && offset_right>0){
                offset_qipa =true;
                return;
            }
            if(offset_qipa){
                if((offset_left==1 && offset_right>0) || (offset_left>0 && offset_right==1)){
                    slideOffset=1;
                    offset_qipa =false;
                }else if(offset_left==0 && offset_right==0){
                    slideOffset=0;
                    offset_qipa =false;
                }else if(offset_left==0 && offset_right>0){
                    slideOffset=offset_right;
                }else if(offset_right==0 && offset_left>0){
                    slideOffset=offset_left;
                }
                else return;
            }

            offset = slideOffset;
            if (offset >= 0.995) {
                flipped = true;
                dwr_back.setFlip(flipped);
                ((MainMenu)layer_left).loadAds();
            } else if (offset <= 0.005) {
                flipped = false;
                dwr_back.setFlip(flipped);
            }
            dwr_back.setParameter(offset);
        }
        @Override
        public void onDrawerOpened(View drawerView) {
            if (drawerView instanceof DList) {
               // ((DList)drawerView).updateUI();
            }
        }
        @Override
        public void onDrawerClosed(View drawerView) {
            weber.requestFocus();
        }
        @Override
        public void onDrawerStateChanged(int newState) {

        }
    };
    private View.OnClickListener listener_menu=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(!isDrawerOpen(layer_left)){
                openDrawer(layer_left);
            }
        }
    };

    private View.OnClickListener listener_leida=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(!isDrawerOpen(layer_right)){
                layer_right.change_state(0);
                openDrawer(layer_right);
            }
        }
    };

    private CLCallback.CB_Activity cber_acy=new CLCallback.CB_Activity() {
        @Override
        public void on_close() {
            closeDrawer(layer_right);
        }
    };

    private WebAddressStatusBar.EventListener listener_address=new WebAddressStatusBar.EventListener() {
        @Override
        public void on_go_url(String url) {
         //   weber.setVisibility(View.VISIBLE);
            fl_webview.setVisibility(View.VISIBLE);
            home_grid.setVisibility(View.GONE);
            updateAdView();
            weber.loadUrl(url);
            weber.requestFocus();
            CL.CLOGI("on go url:"+url);
        }
        @Override
        public void on_go_search(String key) {
            int _sc= Setting.Share_Setting().get_searcher();
            if(_sc==101)weber.loadUrl(Global.Google_Searcher.replace("%s",key));
            else if(_sc==102)weber.loadUrl(Global.Baidu_Searcher.replace("%s",key));
            weber.requestFocus();
        }
        @Override
        public void on_refresh() {
            if (weber.getUrl() != null)
                weber.reload();
            else {
                if (addresser.get_address() != null)
                    weber.loadUrl(addresser.get_address());
            }
            weber.requestFocus();
        }
        @Override
        public void on_stop_load() {
            weber.stopLoading();
            weber.requestFocus();
        }
    
        @Override
        public void on_change_text() {
            addresser.set_address("");
            addresser.set_loading_state(WebAddressStatusBar.NOTHING);
        }
    };
    private WebToolBar.EventListener listener_toolbar=new WebToolBar.EventListener() {
        @Override
        public void on_click_back() {
            if(weber.canGoBack()){
                fl_webview.setVisibility(View.VISIBLE);
                home_grid.setVisibility(View.GONE);
                weber.goBack();
                toolbar.set_can_back(weber.canGoBack());
                toolbar.set_can_forward(weber.canGoForward());
                String url = weber.getUrl();
                updateyoutbue_ad(url);
            }
        }
        @Override
        public void on_click_forward() {
            if(weber.canGoForward()){
                fl_webview.setVisibility(View.VISIBLE);
                home_grid.setVisibility(View.GONE);
                weber.goForward();
                toolbar.set_can_back(weber.canGoBack());
                toolbar.set_can_forward(weber.canGoForward());
                String url = weber.getUrl();
                updateyoutbue_ad(url);
            }
        }
        @Override
        public void on_click_fav() {
            layer_fav_and_history.show();
        }
        @Override
        public void on_long_click_fav() {
            if(star_data==null){
                Favorites.Show_Add_Dialog(weber.getTitle(), weber.getUrl(), new CLCallback.CB_TFO<Struct.StructWebsite>() {
                    @Override
                    public boolean on_callback_success(Struct.StructWebsite obj, String msg) {
                        String _crt_url=weber.getUrl();
                        if(_crt_url==null || _crt_url.isEmpty())return true;
                        if(obj.url != null && obj.url.equals(_crt_url)){
                            star_data=obj;
                            toolbar.set_fav_state(true);
                        }
                        return true;
                    }
                    @Override
                    public void on_callback_fail(int code, String msg) {
                    }
                });
            }else{
                CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_del_bookmark), new CLCallback.CB_TF() {
                    @Override
                    public void on_callback_success() {
                        if(star_data!=null) {
                            typer_fav.delete_favorite(cc,star_data.ID);
                            CLBus.Share_Instance().send_msg_immediate(Global.Group_main_menu,Global.Action_main_menu);
                        }
                        star_data=null;
                        toolbar.set_fav_state(false);
                    }
                    @Override
                    public void on_callback_fail(int code, String msg) {}
                }).show();
            }
        }
        @Override
        public void on_click_storage() {
            if(CL.Do_Once()){
                Intent _intent=new Intent(cc, AcyStorage.class);
                cc.startActivity(_intent);
            }
        }

        @Override
        public void on_click_open_home() {
            layer_left.open_home();
        }

        @Override
        public void on_click_open_new_win() {
            if(CL.Do_Once()){
                if(!isDrawerOpen(layer_right)){
                    layer_right.change_state(1);
                    openDrawer(layer_right);
                }
            }
        }

    };

    private Bitmap bitmap_website_icon;
    private boolean load_finish=false;
    private String star_page_url = null;
    private boolean is_hava_website_icon=false;
    private FavAndHistory layer_fav_and_history;
    private Affairs.TypeHistory typer_history=new Affairs.TypeHistory();
    private Affairs.TypeFavorite typer_fav=new Affairs.TypeFavorite();
    private Struct.StructWebsite star_data;
    private volatile String title;
    public static Home s_home;


    public void updateyoutbue_ad(String url) {
        if (m_AdView == null)
            return;
        int count = Setting.Share_Setting().get_app_run_count();
       // long run_time = Setting.Share_Setting().get_app_run_time();
        if (url != null && Setting.Share_Setting().get_youtube_play_close() && count < Setting.Share_Setting().get_youtube_play_in_count()) {
            if (url.toLowerCase().contains("youtube.com")) {
               // CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui, Global.Action_close_ad, 1);
                if (m_AdView.getVisibility() != View.INVISIBLE && !Setting.Share_Setting().get_subscription_flag()) {
                    m_AdView.setVisibility(View.INVISIBLE);
                }
            }
            else {
               // CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui, Global.Action_close_ad, 0);
                if (m_AdView.getVisibility() != View.VISIBLE && !Setting.Share_Setting().get_subscription_flag()) {
                    m_AdView.setVisibility(View.VISIBLE);
                }
            }
        } else {
            if (m_AdView.getVisibility() != View.VISIBLE && !Setting.Share_Setting().get_subscription_flag()) {
                m_AdView.setVisibility(View.VISIBLE);
            }
        }
    }

    public boolean is_have_life(CLWebkit webview) {
        int index = list_weber.indexOf(webview);
        return (index>=0?true:false);
    }


    //value //1 onpagestart
    private void BackgroundWebViewData(CLWebkit webview, int value, Object... msgs) {
        CL.CLOGI("Eddy BackgroundWebViewData webview = " + webview +
        " value = " + value);
        if (value == 1) {
            String url = (String) msgs[0];
            CL.CLOGI("Eddy BackgroundWebViewData url1 = " + url);

            star_data=typer_fav.get_favorite_star(cc,url.toLowerCase());
            webview.set_Star(star_data==null?false:true);
            webview.set_finishloaded(false);
            //addresser.set_address(url);
            //webview.set_star_page_url(url);
            update_multi_win_list();
        } else if (value == 2) {
            String url = (String) msgs[0];
            CL.CLOGI("Eddy BackgroundWebViewData url2 = " + url);
            webview.set_finishloaded(true);
            webview.setProgress(100);
            //addresser.set_address(url);
            title = webview.getTitle();
            String _file_name = Tools.Get_Url_Host(url);
            if (_file_name != null && title != null) {
                _file_name = new File(Global.Dir_thum, _file_name).getAbsolutePath();
                typer_history.add_history(cc, title, url, _file_name);
            }

            if (Build.VERSION.SDK_INT < 19) {

                Server.Sniff_Url(new ResSniffer.SniffData(Global.JS_Find, list_Header_Params, (WebViewCallBack) webview));
            }
            update_multi_win_list();
        } else if (value == 3) {
            Integer pos = (Integer) msgs[0];
            CL.CLOGI("Eddy BackgroundWebViewData pos = " + pos);
            webview.setProgress(pos);
        } else if (value == 4) {
            webview.set_Title(title);
        } else if (value == 5) {
            Bitmap icon = (Bitmap) msgs[0];
            CL.CLOGI("Eddy BackgroundWebViewData icon = " + icon);

            if(icon==null || icon.isRecycled()){
                webview.set_website_icon(false);
                webview.set_bitmap(null);
            }
            else{
                webview.set_website_icon(true);
                webview.set_bitmap(icon);
                //sometimes favicon.ico获取不到,那就在这里存储
                if(CLFileSystem.Whether_Availability() && Global.Dir_thum.exists()) {
                    String _file_name = Tools.Get_Url_Host(webview.getUrl());
                    if (_file_name != null ) {
                        File _path=new File(Global.Dir_thum,_file_name);
                        if(_path!=null && !_path.exists()) {
                            CLBitmapLoader.Compress_And_Store(bitmap_website_icon, CLBitmapLoader.MB, _path.getAbsolutePath());
                        }
                    }
                }
            }
        } else if (value == 6) {
            View v1 = (View)msgs[0];

            CL.CLOGI("Eddy BackgroundWebViewData v1 = " + v1);
            WebChromeClient.CustomViewCallback callback = (WebChromeClient.CustomViewCallback)msgs[1];
            webview.setCallback(callback);
            webview.set_view(v1);

            //CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video,Global.Action_show_video,view);
        } else if (value == 7) {
            webview.setHave_hide_customview(true);
        }
    }
	
	
	private WindowManager mWindowManager = null;
    private View mNightView = null;
    private WindowManager.LayoutParams mNightViewParam;
    private boolean mIsAddedView;



    private CLWebkit.EventLinstener listener_weber=new CLWebkit.EventLinstener() {
        @Override
        public void on_page_start(WebView view, String url, Bitmap favicon) {
            isFinish = false;
            updateyoutbue_ad(url);

            if (weber != view) {
                BackgroundWebViewData((CLWebkit)view, 1, url);
                return;
            }
            //load_finish=false;
            //((CLWebkit)view).set_star_page_url(url);
            is_hava_website_icon=false;
            addresser.set_address(url);
            addresser.set_loading_state(WebAddressStatusBar.LOADING);
            toolbar.set_can_back(weber.canGoBack());
            toolbar.set_can_forward(weber.canGoForward());
            star_data=typer_fav.get_favorite_star(cc,url.toLowerCase());
            toolbar.set_fav_state(star_data==null?false:true);
            //Server.arse_done = false;

//            if (Build.VERSION.SDK_INT < 19) {
//                if (Server.parse_done) {
//                    return;
//                } else {
//                    Server.Sniff_Url(new ResSniffer.SniffData(Global.JS_Find, (WebViewCallBack) view));
//
//                }
//            }
        }
        @Override
        public void on_page_finish(WebView view, String url) {
            //if(!load_finish) {

                if (weber != view) {
                 //   CL.CLOGI("Eddy on_page_finish url1 = " + url);
                    BackgroundWebViewData((CLWebkit)view, 2, url);
                    return;
                } else {

                    //load_finish = true;
                    String org = view.getOriginalUrl();
                    //String star_page_url = ((CLWebkit)view).get_star_page_url();
                    String cur_url = view.getUrl();
                 //   CL.CLOGI("Eddy on_page_finish org = " + org);
                    //CL.CLOGI("Eddy on_page_finish star_page_url = " + star_page_url);
                   // CL.CLOGI("Eddy on_page_finish cur_url = " + cur_url);
                  //  CL.CLOGI("Eddy on_page_finish url = " + url);
                    if (cur_url != null && cur_url.compareTo(url) !=0) {

                  //      CL.CLOGI("Eddy oooooooooooooooooooooooooooooooooooooooooo");
                        //view.loadUrl(url);
                        addresser.set_address(url);
                    } else {
                  //      CL.CLOGI("Eddy xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
                        addresser.set_address(url);
                    }

                    addresser.set_address(url);
                    addresser.set_loading_state(WebAddressStatusBar.STOP);
                    addresser.set_progress(100);


                    toolbar.set_can_back(weber.canGoBack());
                    toolbar.set_can_forward(weber.canGoForward());

                    star_data=typer_fav.get_favorite_star(cc,url.toLowerCase());
                    toolbar.set_fav_state(star_data==null?false:true);


                    if (progressbar.getVisibility() == View.VISIBLE) {
                        progressbar.setVisibility(View.GONE);
                    }
                    ((CLWebkit)view).set_finishloaded(true);
                    if (!is_hava_website_icon) {
                        addresser.update_website_icon(null);
                    }
                    //add history
                    title = view.getTitle();
                    String _file_name = Tools.Get_Url_Host(url);
                    if (_file_name != null && title != null) {
                        _file_name = new File(Global.Dir_thum, _file_name).getAbsolutePath();
                        typer_history.add_history(cc, title, url, _file_name);
                    }

                    if (!isFinish) {
                        isFinish = true;
                        snifferFromJs(url);
                    }
                }

        }

        public void snifferFromJs(String url) {
            Pattern p = Pattern.compile(".*://xhamster.*\\.com/.*");
            if (p.matcher(url).find()) {
                final String webFinishParseJavascript = "javascript:function grab() {" +
                        "var result = [];" +
                        "var links = document.getElementsByTagName('link');" +
                        "if (links != null) {" +
                        "for (var i = 0; i < links.length; i++) {" +
                        "var link = links[i];" +
                        "if (link.rel == \"preload\" && (link.as == \"fetch\" || link.as == \"video\" || link.as == \"audio\")) {" +
                        "result.push(link.href);" +
                        "}" +
                        "}" +
                        "}" +
                        "return result" +
                        "}grab();";
                weber.evaluateJavascript(webFinishParseJavascript, new ValueCallback<String>() {
                    @Override
                    public void onReceiveValue(String value) {

                        if (!TextUtils.isEmpty(value) && value.startsWith("[\"") && value.endsWith("\"]") && value.length() > 2) {
                            String vs = value.substring(2, value.length() - 2);
                            String[] vss = vs.split("\",\"");

                            for (int i = 0; i < vss.length; ++i) {
                                String vss_url = vss[i];
                                request(weber.getTitle(), vss_url, list_Header_Params);
                            }
                        }
                    }
                });
            }

        }



        public void request(String title, String url, String headerParams) {
            CL.CLOGI("on intercept1:"+url);

            list_Header_Params = headerParams;
            Server.Sniff_Url(new ResSniffer.SniffData(url, title, headerParams));

        }


        @Override
        public boolean on_progress(WebView view, int progress) {

            //updateAdView();
            if (weber != view) {
                BackgroundWebViewData((CLWebkit)view, 3, progress);
                return true;
            }
           // CL.CLOGI("Eddy on_progress progress = " + progress);
            //if(!load_finish)
            addresser.set_progress(progress);

            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) ((LinearLayout)addresser.getParent()).getLayoutParams();
          //  Log.i("sdfdfdf", "progress = " + progress);
            if (lp.topMargin < 0) {
                progressbar.setVisibility(View.VISIBLE);
                  if (progress == 100) {

                        progressbar.setProgress(100);
                        postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                progressbar.setVisibility(GONE);
                            }
                        }, 500);
                    } else {
                        if (progressbar.getVisibility() == GONE)
                            progressbar.setVisibility(VISIBLE);
                        progressbar.setProgress(progress);
                    }

            } else {
                progressbar.setVisibility(View.GONE);

                if (progress == 100) {
              //      progressbar.setVisibility(GONE);
                    progressbar.setProgress(0);
                } else {

                    progressbar.setProgress(progress);
                }
            }


            return true;
//            String url = addresser.get_address();
//            String org = view.getUrl();
//
//            if (org!=null && org.compareTo(url)!=0) {
//                addresser.set_address(org);
//                star_data=typer_fav.get_favorite_star(cc,org.toLowerCase());
//                toolbar.set_fav_state(star_data==null?false:true);
//            }


        }


        @Override
        public void on_received_title(WebView view, String t) {
            CL.CLOGI(">>>>>>>>>>>>>:"+t);
            title=t;
            if (weber != view) {
                BackgroundWebViewData((CLWebkit)view, 4, title);
                CL.CLOGI("Eddy update_multi_win_list");
                update_multi_win_list();
                return;
            }
           // CL.CLOGI("Eddy on_received_title t = " + t +  " url = " + view.getUrl());
            addresser.set_address(view.getUrl());
            ((CLWebkit)view).set_Title(title);
            update_multi_win_list();
        }
        @Override
        public void on_received_icon(WebView view, Bitmap icon) {
            String url = view.getUrl();
            if (url != null && !url.isEmpty())
             updateyoutbue_ad(url);
            if (weber != view) {
                BackgroundWebViewData((CLWebkit)view, 5, icon);
                update_multi_win_list();
                return;
            }

            is_hava_website_icon=true;
            if(bitmap_website_icon!=null && !bitmap_website_icon.isRecycled()){
                bitmap_website_icon.recycle();
                bitmap_website_icon=null;
            }
            if(icon==null || icon.isRecycled()){
                addresser.update_website_icon(null);
            }
            else{
                bitmap_website_icon= icon;
                addresser.update_website_icon(bitmap_website_icon);
                //sometimes favicon.ico获取不到,那就在这里存储
                if(CLFileSystem.Whether_Availability() && Global.Dir_thum.exists()) {
                    String _file_name = Tools.Get_Url_Host(view.getUrl());
                    if (_file_name != null ) {
                        File _path=new File(Global.Dir_thum,_file_name);
                        if(_path!=null && !_path.exists()) {
                            CLBitmapLoader.Compress_And_Store(bitmap_website_icon, CLBitmapLoader.MB, _path.getAbsolutePath());
                        }
                    }
                }
            }
        }
        @Override
        public void on_show_customview(CLWebkit webview, View view, WebChromeClient.CustomViewCallback callback) {

            if (weber != webview) {
                CL.CLOGI("Eddy on_show_customview1");
                BackgroundWebViewData((CLWebkit)webview, 6, view, callback);
                return;
            }

            CL.CLOGI("Eddy on_show_customview2");
            if(cb_video!=null){
                CL.CLOGI("Eddy on_show_customview3");
                cb_video.onCustomViewHidden();
                cb_video=null;
                return;
            }
            cb_video=callback;
            CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video,Global.Action_show_video,view);
        }
        @Override
        public void on_hide_customview(CLWebkit webview) {
            if (weber != webview) {
           //     CL.CLOGI("Eddy on_hide_customview 1");
                BackgroundWebViewData((CLWebkit)webview, 7);
                return;
            }

         //   CL.CLOGI("Eddy on_hide_customview 2");
            CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video, Global.Action_hide_video, new CLCallback.CB() {
                @Override
                public void on_callback() {
                  //  CL.CLOGI("Eddy on_hide_customview 3");
                    if (cb_video != null) {
                        CL.CLOGI("Eddy on_hide_customview 4");
                        cb_video.onCustomViewHidden();
                        cb_video = null;
                    }
                }
            });
        }

        @Override
        public void on_webkit_download(String type, String url, String name,int length, String params) {
            if(type.equals("apk")){
                Server.Add_Confirm_Downloader(Data.Type_APK,"apk",name,url,length, params);
            }else if(type.equals("txt") || type.equals("doc") || type.equals("docx") || type.equals("pdf")
                    || type.equals("xls") || type.equals("xlsx") || type.equals("ppt")
                    || type.equals("pptx") || type.equals("pps") || type.equals("xlt")
                    || type.equals("xlts") || type.equals("dot") || type.equals("dotx")
                    ){
                Server.Add_Confirm_Downloader(Data.Type_Doc,type,name,url,length,params);
            }else{
                Server.Add_Confirm_Downloader(Data.Type_Other,type,name,url,length,params);
            }
        }

        @Override
        public void on_long_click_image(String url) {
         //   CL.CLOGI("tip download image:"+url);
            if(dialog_storage_picture==null)dialog_storage_picture=new DialogStoragePicture(cc);
            if(dialog_storage_picture.isShowing())return;
            dialog_storage_picture.show();
            dialog_storage_picture.update_picture(url);
        }

        @Override
        public void on_intercept(String title,String url, String headerParams) {
            CL.CLOGI("on intercept:"+url);
            Server.Sniff_Url(new ResSniffer.SniffData(url, title, headerParams));
        }

        @Override
        public void on_intercept(WebView view, String url, String headerParams) {
            CL.CLOGI("on intercept1:"+url);


            String title = ((CLWebkit) view).Title();
            Server.Sniff_Url(new ResSniffer.SniffData(url, title, headerParams));

        }


        private void upWebViewUIState(WebView webview) {
            //load_finish = true;
            addresser.set_loading_state(WebAddressStatusBar.STOP);
            addresser.set_progress(100);
            toolbar.set_can_back(weber.canGoBack());
            toolbar.set_can_forward(weber.canGoForward());
            if (!is_hava_website_icon) {
                addresser.update_website_icon(null);
            }
            //add history
            title = webview.getTitle();
            String url =  webview.getUrl();
            if (url != null) {
                String _file_name = Tools.Get_Url_Host(url);
                if (_file_name != null && title != null) {
                    _file_name = new File(Global.Dir_thum, _file_name).getAbsolutePath();
                    typer_history.add_history(cc, title, url, _file_name);
                }
            }
        }
        //        @Override
//        public void on_sniffer(final String type, final String url,final int length) {
//            handler.post(new Runnable() {
//                @Override
//                public void run() {
//                    if(type.startsWith("video/")){
//                        String _type=MimeTypeMap.getSingleton().getExtensionFromMimeType(type);
////                        int p_one = url.lastIndexOf('/');
////                        int p_two = url.indexOf("?");
////                        String _filename = p_one > 0 ? url.substring(p_one + 1,(p_two>0?p_two:url.length())) : "noname"+System.currentTimeMillis();
//                        CL.CLOGI("TYPE:"+_type);
//                        CL.CLOGI("length:"+length);
//                        CL.CLOGI("title:"+weber.getTitle());
//                        CL.CLOGI("url:"+url);
//                        Server.Add_Confirm_Downloader(Data.Type_Video,Data.Get_Type_minor(_type),weber.getTitle(),url,length);
//                    }else if(type.startsWith("audio/")){
//
//                    }else if(type.equals("m3u8")){
//                        CL.CLOGI("!!! m3u8:"+url);
//                    }
//                }
//            });
//        }
    };

    private ResSniffer.SniffEventer listener_sniffer=new ResSniffer.SniffEventer() {
        @Override
        public void on_sniffer(String type, String url, long length, String title, MultiResolution resolution) {
            String _t=title;

            _t = Tools.ReplaceBadFilename(_t);

            if (!Tools.CheckFileName(_t)) {
                _t = Tools.FixFileName(_t);
            }

            String _type=type.substring(type.lastIndexOf('/')+1);

            if(type.startsWith("video/")){

                Server.Add_Confirm_Downloader(Data.Type_Video,Data.Get_Type_minor(_type),_t,url,length, list_Header_Params, null);
            }else if(type.startsWith("audio/")){
                Server.Add_Confirm_Downloader(Data.Type_Music,Data.Get_Type_minor(_type),_t,url,length, list_Header_Params, null);
            }else if(type.equals("m3u8")){
                String quality = null;
                if (resolution.resolution != null && resolution.resolution.length() > 0)
                    quality = resolution.resolution;
                else if (resolution.quality != null && resolution.quality.length() > 0)
                    quality = resolution.quality;
                Server.Add_Confirm_Downloader(_t,url,list_Header_Params,quality, resolution.hasAd);
            }

        }

        @Override
        public void on_sniffer(String type, String url, long length, String title) {
            String _t=title;

            _t = Tools.ReplaceBadFilename(_t);

            if (!Tools.CheckFileName(_t)) {
                _t = Tools.FixFileName(_t);
            }

          //  CL.CLOGI("TYPE:"+type);
            String _type=type.substring(type.lastIndexOf('/')+1);
          //  CL.CLOGI("type:"+_type);
          //  CL.CLOGI("length:"+length);
          //  CL.CLOGI("title:"+_t);
          //  CL.CLOGI("url:"+url);
            if(type.startsWith("video/")){

                Server.Add_Confirm_Downloader(Data.Type_Video,Data.Get_Type_minor(_type),_t,url,length, list_Header_Params, null);
            }else if(type.startsWith("audio/")){
                Server.Add_Confirm_Downloader(Data.Type_Music,Data.Get_Type_minor(_type),_t,url,length, list_Header_Params, null);
            }else if(type.equals("m3u8")){
                Server.Add_Confirm_Downloader(_t,url,list_Header_Params, null, false);
            }
//            else{
//                if(type.equals("apk")){
//                    Server.Add_Confirm_Downloader(Data.Type_APK,Data.Get_Type_minor(_type),title,url,length);
//                }else if(type.equals("doc") || type.equals("docx") || type.equals("pdf")
//                        || type.equals("xls") || type.equals("xlsx") || type.equals("ppt")
//                        || type.equals("pptx") || type.equals("pps") || type.equals("xlt")
//                        || type.equals("xlts") || type.equals("dot") || type.equals("dotx")
//                        ){
//                    Server.Add_Confirm_Downloader(Data.Type_Doc,type,title,url,length);
//                }else{
//                    Server.Add_Confirm_Downloader(Data.Type_Other,type,title,url,length);
//                }
//            }
        }

        @Override
        public void on_sniffer_parse(ResSniffer.SniffDataWebsite data) {
            crt_sniffer=data;
            post(new Runnable() {
                @Override
                public void run() {
                    if(btn_sniffer.getVisibility()==View.GONE)btn_sniffer.setVisibility(View.VISIBLE);
                }
            });
        }
    };

    private MainMenu.Eventer listener_left=new MainMenu.Eventer() {

        @Override
        public void on_main_page() {
            if (CL.Do_Once()) {
                if (isDrawerOpen(layer_left)) {
                    closeDrawer(layer_left);
                }
                weber.stopLoading();
                weber.loadUrl("about:blank");
                //weber.onPause();
                //weber.destroy();
                //fl_webview.removeAllViews();
                addresser.set_address("");
                addresser.set_loading_state(WebAddressStatusBar.STOP);
                addresser.set_progress(0);
                addresser.update_website_icon(null);

                //weber=new CLWebkit(cc,listener_weber);
                //weber.setLayoutParams(CL.Get_LP_MM());
                //fl_webview.addView(weber);
                //CL.Set_Fouces_When_Touch(weber);
                //weber.requestFocus();
                //  if(Global.Main_Page_Url!=null)weber.loadUrl(Global.Main_Page_Url);

                //   weber.setVisibility(View.INVISIBLE);
                fl_webview.setVisibility(View.GONE);
                home_grid.setVisibility(View.VISIBLE);
                if (m_AdView != null) {
                    m_AdView.setVisibility(View.GONE);
                }
            }
        }
        @Override
        public void on_music_player() {
            if (CL.Do_Once()) {
                if (isDrawerOpen(layer_left)) {
                    closeDrawer(layer_left);
                }
                dialog_music.show();
            }
        }

        @Override
        public void on_wifi_share() {
            if (isDrawerOpen(layer_left)) {
                closeDrawer(layer_left);
            }
            if (CL.Do_Once()) {
                Intent _intent = new Intent(cc, AcyWifiShare.class);
                cc.startActivity(_intent);
            }
        }

        @Override
        public void on_my_download() {
            if (isDrawerOpen(layer_left)) {
                closeDrawer(layer_left);
            }
            if (CL.Do_Once()) {
                Intent _intent = new Intent(cc, AcyStorage.class);
                cc.startActivity(_intent);
            }
        }

        @Override
        public void on_setting() {
            if (isDrawerOpen(layer_left)) {
                closeDrawer(layer_left);
            }
            if (CL.Do_Once()) {
                Intent _intent = new Intent(cc, AcySetting.class);
                cc.startActivity(_intent);
            }
        }

        @Override
        public void on_share() {
            if (isDrawerOpen(layer_left)) {
                closeDrawer(layer_left);
            }
            if (CL.Do_Once()) {
                Intent _intent = new Intent();
                _intent.setAction(Intent.ACTION_SEND);
                _intent.putExtra(Intent.EXTRA_TEXT, "Sharing a good application -> https://play.google.com/store/apps/details?id=" + cc.getPackageName());
                _intent.setType("text/plain");
                cc.startActivity(Intent.createChooser(_intent, cc.getResources().getString(R.string.select_client)));
            }
        }

        @Override
        public void on_feedback() {
            if (isDrawerOpen(layer_left)) {
                closeDrawer(layer_left);
            }
            if (CL.Do_Once()) {
                Intent _intent = new Intent();
                _intent.setAction(Intent.ACTION_SENDTO);
                _intent.setData(Uri.parse("mailto:<EMAIL>"));
                _intent.putExtra(Intent.EXTRA_SUBJECT, cc.getResources().getString(R.string.feedback));
                _intent.putExtra(Intent.EXTRA_TEXT, "");
             //   cc.startActivityForResult();
                cc.startActivity(Intent.createChooser(_intent, cc.getResources().getString(R.string.select_client)));
            }
        }

        @Override
        public void open_ad(int value) {
            if (CL.Do_Once()) {
                if (value == 1) {
                    cc.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=com.blacklion.browser")));
                } else if (value == 2) {

                    cc.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + Setting.Share_Setting().get_update_packagename())));
                }
            }
        }

        @Override
        public void open_url(String url) {
            if (CL.Do_Once()) {
                if (isDrawerOpen(layer_left)) {
                    closeDrawer(layer_left);
                }
                iwinoper.open_url(url);
            }
        }

        @Override
        public void remove_ad() {
            if (CL.Do_Once()) {
                cc.startActivityForResult(new Intent(cc, BillingActivity.class), 10004);
            }
        }

        @Override
        public void close_view(View v) {
            closeDrawer(v);
        }

        @Override
        public void open_more_app(ArrayList<Setting.ApkInfo> apks) {
            if (CL.Do_Once()) {
                Intent _intent = new Intent(cc, MoreApps.class);
                _intent.putExtra("list", apks);
                _intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                cc.startActivity(_intent);
                close_view(layer_left);
            }
        }
    };

    private ResSniffer.SniffDataWebsite crt_sniffer;
    private View.OnClickListener listener_btn_sniffer=new OnClickListener() {
        @Override
        public void onClick(View view) {
            if(dialog_sniffer==null)dialog_sniffer=new DialogWebsiteSniffer(cc);
            dialog_sniffer.update_data(crt_sniffer, new CLCallback.CB_TF() {
                @Override
                public void on_callback_success() {

                }
                @Override
                public void on_callback_fail(int code, String msg) {
                    if(msg!=null && msg.equals(crt_sniffer.title)){
                        crt_sniffer=null;
                        btn_sniffer.setVisibility(View.GONE);
                    }
                }
            });
            dialog_sniffer.show();
        }
    };


    /**
     * 设置夜间模式
     */
    private void changeToNight() {
        if (mIsAddedView == true)
            return;
        mNightViewParam = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.TYPE_APPLICATION,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSPARENT);
        mWindowManager = cc.getWindowManager();
        mNightView = new View(cc);
        mNightView.setBackgroundResource(R.color.night_float_color);
        mWindowManager.addView(mNightView, mNightViewParam);
        mIsAddedView = true;
        Setting.Share_Setting().set_night_mode(true);
    }

    /**
     * 设置白天模式
     */
    public void changeToDay(){

        if (mIsAddedView && mNightView!=null) {
            mWindowManager.removeViewImmediate(mNightView);
            mWindowManager = null;
            mNightView = null;
            mIsAddedView=false;
            Setting.Share_Setting().set_night_mode(false);
        }
    }


    //网页视频播放
    private WebChromeClient.CustomViewCallback cb_video;


    private void init(){
        this.setMotionEventSplittingEnabled(false);
        this.addDrawerListener(listener_drawerlayout);
        toolbar=new WebToolBar(cc,listener_toolbar);
        mBarHeight = getContext().getResources().getDimensionPixelSize(R.dimen.top_bar_height);
        ll_main=new WebPageScrollView(cc, this);
        ll_main.setLayoutParams(CL.Get_LP_MM());
        ll_main.setOrientation(LinearLayout.VERTICAL);
        ll_main.setBackgroundColor(Color.WHITE);
        this.addView(ll_main);

        ll_header=new LinearLayout(cc);
        ll_header.setLayoutParams(CL.Get_LLLP(CL.MP, CL.DIP2PX_INT(36)));
        ll_header.setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
        ll_main.addView(ll_header);

        FrameLayout _fl_back=new FrameLayout(cc);
        _fl_back.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(45), CL.MP));
        _fl_back.setClickable(true);
        _fl_back.setOnClickListener(listener_menu);
        dwr_back=new DrawerArrowDrawable(cc.getResources());
        dwr_back.setStrokeColor(cc.getResources().getColor(R.color.light_gray));
        iv_back= new ImageView(cc);
        iv_back.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        iv_back.setImageDrawable(dwr_back);
        _fl_back.addView(iv_back);

        rpMenu = new View(cc);
        rpMenu.setId(R.id.view_red_point);
        int wh = (int)CL.DIP2PX(8);
        rpMenu.setLayoutParams(CL.Get_FLLP(wh, wh, Gravity.CENTER,
                (int)CL.DIP2PX(12), -(int)CL.DIP2PX(12), 0, 0));
        rpMenu.setBackgroundResource(R.drawable.red_point);
        rpMenu.setVisibility(View.INVISIBLE);
        _fl_back.addView(rpMenu);
        ll_header.addView(_fl_back);

        addresser=new WebAddressStatusBar(cc,listener_address);
        addresser.setLayoutParams(CL.Get_LLLP(CL.WC,CL.MP,1.0f,CL.DIP2PX_INT(4),CL.DIP2PX_INT(4),0,CL.DIP2PX_INT(4)));
        ll_header.addView(addresser);

        leida=new Leida(cc);
        leida.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(50),CL.MP));
        leida.setClickable(true);
        leida.setOnClickListener(listener_leida);
        ll_header.addView(leida);

        layer_left=new MainMenu(cc,listener_left);
        DrawerLayout.LayoutParams _lp_left=new DrawerLayout.LayoutParams(CL.DIP2PX_INT(260),CL.MP, Gravity.LEFT);
        _lp_left.setMargins(0, CL.DIP2PX_INT(36),0,0);
        layer_left.setLayoutParams(_lp_left);
        this.addView(layer_left);

        layer_right =new DList(cc,leida);
       //layer_right = new CacheList(cc, leida, cber_acy);
        this.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        DrawerLayout.LayoutParams _lp_right=new DrawerLayout.LayoutParams(CL.MP,CL.MP, Gravity.RIGHT);
//        _lp_right.setMargins(CL.DIP2PX_INT(6),0,0,0);
        layer_right.setLayoutParams(_lp_right);
        this.addView(layer_right);

        fl_webview=new FrameLayout(cc);
        fl_webview.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        ll_main.addView(fl_webview);

        weber=new CLWebkit(cc,listener_weber);
        CL.Set_Fouces_When_Touch(weber);
        weber.setLayoutParams(CL.Get_LP_MM());
        fl_webview.addView(weber);
        fl_webview.requestFocus();
        fl_webview.setVisibility(View.GONE);

        home_grid = new HomeGrid((AcyMain) cc,null,null);



        home_grid.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP,1.0f));

        ll_main.addView(home_grid);
        home_grid.setVisibility(View.VISIBLE);

//
//        if(Global.Main_Page_Url!=null)weber.loadUrl(Global.Main_Page_Url);
//        else addresser.set_address("");
        addresser.set_address("");

        btn_sniffer= CLController.Get_ImageView(cc, CL.Get_FLLP(CL.WC,CL.WC,Gravity.TOP|Gravity.RIGHT,0,CL.DIP2PX_INT(15),CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.tip_download_normal,R.mipmap.tip_download_click),listener_btn_sniffer);
        btn_sniffer.setVisibility(View.GONE);
        fl_webview.addView(btn_sniffer);


//        if (Setting.Share_Setting().get_update() != 0) {
//            int count = Setting.Share_Setting().get_app_run_count();
//
//            if (!Setting.Share_Setting().get_subscription_flag() && count > Setting.Share_Setting().get_update_count() && !Setting.Share_Setting().get_download_enable()) {
//                update_tip_view = CLController.Get_TextView(cc,CL.Get_FLLP(CL.MP,CL.WC,Gravity.CENTER),
//                        cc.getResources().getText(R.string.store_video).toString(),0xffd0d0d0,18,null);
//                update_tip_view.setGravity(Gravity.CENTER);
//                ShapeDrawable _dwe_bg=new ShapeDrawable();
//
//                _dwe_bg.getPaint().setColor(Color.BLACK);
//                _dwe_bg.getPaint().setStyle(Paint.Style.STROKE);
//                _dwe_bg.getPaint().setStrokeWidth(14);
//                _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
//
//                update_tip_view.setBackground(_dwe_bg);
//                fl_webview.addView(update_tip_view);
//            }
//        }



        progressbar = new ProgressBar(cc, null, android.R.attr.progressBarStyleHorizontal);
        progressbar.setLayoutParams(CL.Get_FLLP(CL.MP, 10, 0));

        Drawable drawable = cc.getResources().getDrawable(R.drawable.progress_bar_states);
        progressbar.setProgressDrawable(drawable);
        progressbar.setVisibility(View.GONE);
        fl_webview.addView(progressbar);

        //createfloatbutton(listener_btn_floatbutton);
        dialog_music=new MusicViewDialog(cc);
        music=new MusicView(cc,dialog_music);
        music.setLayoutParams(CL.Get_LP(CL.MP,CL.WC));
        music.setVisibility(View.GONE);
        //updateAdView();
        ll_main.addView(music);

//        m_AdView=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), new CLCallback.CB() {
//            @Override
//            public void on_callback() {
//                if (m_AdView != null && m_AdView.getVisibility() != View.GONE)
//                    m_AdView.setVisibility(View.GONE);
//            }
//        });
//        if(m_AdView!=null)ll_main.addView(m_AdView);



     //   toolbar=new WebToolBar(cc,listener_toolbar);
        toolbar.setLayoutParams(CL.Get_LLLP(CL.MP, CL.DIP2PX_INT(45)));
        toolbar.setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
        ll_main.addView(toolbar);


      //  updateAdView();

        layer_fav_and_history=new FavAndHistory(cc);

        list_weber = new ArrayList<CLWebkit>(10);
        list_weber.add(weber);
        update_multi_win_list();

        DisplayMetrics metric = new DisplayMetrics();
        cc.getWindowManager().getDefaultDisplay().getMetrics(metric);
        int windowsWight = metric.widthPixels;
        // int windowsHeight = metric.heightPixels;
        DrawerLayout.LayoutParams rightParams = (DrawerLayout.LayoutParams)layer_right.getLayoutParams();
        //leftParams.height = windowsHeight;
        rightParams.width = windowsWight;
        layer_right.setLayoutParams(rightParams);
    }

    public void updateAdView() {
        //Setting.Share_Setting().set_subscription_flag(true);
        int count = Setting.Share_Setting().get_app_run_count();
        if (Setting.Share_Setting().get_pos_web_state() == 0 || count < Setting.Share_Setting().get_pos_web_state())

            return;
        if (m_AdView == null) {
            long run_time = Setting.Share_Setting().get_app_run_time();
            //Eddy
            if (run_time < Setting.Share_Setting().get_server_config_run_time()) {
                return;
            }


            m_AdView=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), new CLCallback.CB() {
                @Override
                public void on_callback() {
//                    if (m_AdView != null && m_AdView.getVisibility() != View.GONE) {
//                        m_AdView.setVisibility(View.GONE);
//                        if (m_divid_t != null)
//                            m_divid_t.setVisibility(View.GONE);
//                        if (m_divid_b != null)
//                            m_divid_b.setVisibility(View.GONE);
//                    }
                }
            });
            if (m_AdView == null)
                return;

            if(!Setting.Share_Setting().get_subscription_flag()) {
                int index = ll_main.indexOfChild(toolbar);

                int pos = Setting.Share_Setting().get_ads_pos();

                if (pos == 1) {
                    //View divid = CLController.Get_TextView_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(10)), 0xff2c2c2c);
                    int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
                    m_divid_t= CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(2)), colors);
                    ll_main.addView(m_divid_t);
                    ll_main.addView(m_AdView);
                }
                else {
                    int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
                    m_divid_t = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(2)), colors);
                    ll_main.addView(m_divid_t, index);
                    ll_main.addView(m_AdView, index);

                    m_divid_b = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(2)), colors);
                    ll_main.addView(m_divid_b, index);
                }
            }
        }


        if (Setting.Share_Setting().get_subscription_flag()) {
            if (m_AdView.getVisibility() != View.GONE) {
                m_AdView.setVisibility(View.GONE);
                if (m_divid_t != null)
                    m_divid_t.setVisibility(View.GONE);
                if (m_divid_b != null)
                    m_divid_b.setVisibility(View.GONE);
            }
        } else {
            if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
                 m_AdView.setVisibility(View.VISIBLE);
                if (m_divid_t != null)
                    m_divid_t.setVisibility(View.VISIBLE);
                if (m_divid_b != null)
                    m_divid_b.setVisibility(View.VISIBLE);
            } else {
                 m_AdView.setVisibility(View.GONE);
                if (m_divid_t != null)
                    m_divid_t.setVisibility(View.GONE);
                if (m_divid_b != null)
                    m_divid_b.setVisibility(View.GONE);
            }
        }
    }

    WindowManager windowManager;
    private WindowManager.LayoutParams windowManagerParams;
    private void createfloatbutton(OnClickListener l) {
        mWindowManager = cc.getWindowManager();
        btn_screen_change = new FloatButton(cc.getApplicationContext(), mWindowManager);
        btn_screen_change.setOnClickListener(l);
        btn_screen_change.setClickable(true);
        //btn_screen_change.setImageResource(R.mipmap.r2);

        StateListDrawable _drawable=new StateListDrawable();
        _drawable.addState(new int[]{-android.R.attr.state_pressed}, cc.getResources().getDrawable(R.mipmap.abs));
        _drawable.addState(new int[]{android.R.attr.state_pressed}, cc.getResources().getDrawable(R.mipmap.abt));
        btn_screen_change.setBackground(_drawable);


//        windowManager = (WindowManager) cc.getApplicationContext().getSystemService(cc.WINDOW_SERVICE);
//
        windowManagerParams = ((Mainly) cc.getApplication()).getWindowParams();
        windowManagerParams.type = WindowManager.LayoutParams.TYPE_APPLICATION; // 设置window type
        windowManagerParams.format = PixelFormat.TRANSPARENT; // 设置图片格式，效果为背景透明
        windowManagerParams.flags =  WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
/*
* 注意，flag的值可以为：
* LayoutParams.FLAG_NOT_TOUCH_MODAL 不影响后面的事件
* LayoutParams.FLAG_NOT_FOCUSABLE 不可聚焦
* LayoutParams.FLAG_NOT_TOUCHABLE 不可触摸
*/
// 调整悬浮窗口至左上角，便于调整坐标
        //windowManagerParams.gravity = Gravity.RIGHT | Gravity.BOTTOM;
// 以屏幕左上角为原点，设置x、y初始值
        windowManagerParams.x = CL.DIP2PX_INT(15);
        windowManagerParams.y = CL.DIP2PX_INT(15) + CL.DIP2PX_INT(45);
// 设置悬浮窗口长宽数据
        windowManagerParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        windowManagerParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
// 显示myFloatView图像

        mWindowManager.addView(btn_screen_change, windowManagerParams);
    }

    private boolean bfull = false;
    private View.OnClickListener listener_btn_floatbutton=new OnClickListener() {
        @Override
        public void onClick(View view) {
            if (!bfull) {
                bfull = true;
                ll_header.setVisibility(View.GONE);
                toolbar.setVisibility(View.GONE);
                // btn_screen_change.setImageResource(R.mipmap.r1);

                StateListDrawable _drawable=new StateListDrawable();
                _drawable.addState(new int[]{-android.R.attr.state_pressed}, cc.getResources().getDrawable(R.mipmap.abu));
                _drawable.addState(new int[]{android.R.attr.state_pressed}, cc.getResources().getDrawable(R.mipmap.abv));
                btn_screen_change.setBackground(_drawable);
            } else {
                bfull = false;
                ll_header.setVisibility(View.VISIBLE);
                toolbar.setVisibility(View.VISIBLE);
                StateListDrawable _drawable=new StateListDrawable();
                _drawable.addState(new int[]{-android.R.attr.state_pressed}, cc.getResources().getDrawable(R.mipmap.abs));
                _drawable.addState(new int[]{android.R.attr.state_pressed}, cc.getResources().getDrawable(R.mipmap.abt));
                btn_screen_change.setBackground(_drawable);
            }
        }    };


    public void update_multi_win_list() {
        //layer_right.updateData(list_weber);
    }


    public void load_url(String url){
        listener_address.on_go_url(url);
    }

    public boolean can_go_back(){

        if(layer_left != null && isDrawerOpen(layer_left)){
            closeDrawer(layer_left);
            return true;
        }else if(layer_right != null && isDrawerOpen(layer_right)){
            closeDrawer(layer_right);
            return true;
        }else if(toolbar != null && weber != null && weber.canGoBack()){
            weber.goBack();

            toolbar.set_can_back(weber.canGoBack());
            toolbar.set_can_forward(weber.canGoForward());
            return true;
        }else return false;
    }

    public void on_activity_pause(){
        weber.onPause();
    }
    public void on_activity_resume(){
        weber.onResume();
        updateAdView();

        if (Setting.Share_Setting().get_night_mode()) {
            new Handler().post(new Runnable() {
                @Override
                public void run() {
                    changeToNight();
                }
            });
        }
    }
    public void on_activity_destory(){
        weber.stopLoading();
        weber.onPause();
        weber.destroy();
    }
    public void on_activity_orientation(boolean landscape){
        if(landscape){//横屏
            ll_header.setVisibility(View.VISIBLE);
            toolbar.setVisibility(View.VISIBLE);
            home_grid.activity_orientation(true);
            CL.Set_Full_Screen(cc,true);
            post(new Runnable() {
                @Override
                public void run() {
                    setDrawerLockMode(LOCK_MODE_LOCKED_CLOSED);
                }
            });
        }else{//竖屏
            ll_header.setVisibility(View.VISIBLE);
            toolbar.setVisibility(View.VISIBLE);
            home_grid.activity_orientation(false);
            CL.Set_Full_Screen(cc,false);
            setDrawerLockMode(LOCK_MODE_UNLOCKED);
        }
    }

    private CLBus.CBEventer listener_clbus=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action==Global.Action_favorite_add){
                Struct.StructWebsite _d=(Struct.StructWebsite)msgs[0];
                if(_d!=null && _d.url != null && _d.url.equals(weber.getUrl())){
                    star_data=_d;
                    toolbar.set_fav_state(true);
                }
            }else if(action==Global.Action_favorite_del){
                if(star_data!=null){
                    star_data=typer_fav.get_favorite_star(cc,weber.getUrl().toLowerCase());
                    if(star_data==null)toolbar.set_fav_state(false);
                    else toolbar.set_fav_state(true);
                }
            }
        }
    };
    private CLBus.CBEventer listener_open_url=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action==Global.Action_open_url_from_fav_history){
                layer_fav_and_history.dismiss();
                String _url=(String)msgs[0];
                //_url=_url.trim().toLowerCase();
                if (_url == null)
                    return;
                _url=_url.trim();
                if(_url.equals(""))return;
                String _fix_url=Tools.Was_Web_Site(_url);
                if(_fix_url!=null){
                    listener_address.on_go_url(_fix_url);
                }else{
                    listener_address.on_go_search(_url);
                }
            } else if (action == Global.Action_open_url_in_current_window) {
                home_grid.setVisibility(View.GONE);

                fl_webview.setVisibility(View.VISIBLE);
                String url = (String)msgs[0];

                weber.loadUrl(url);
                weber.requestFocus();
               // updateyoutbue_ad(url);
            }
        }
    };


    private CLBus.CBEventer listener_change_mainmenu_bookmark=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action==Global.Action_main_menu){
                if (layer_left != null)
                layer_left.reLoadMenu();
            }
        }
    };

    private CLBus.CBEventer listener_win=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            Integer v1 = 0;
            Integer v2 = 0;
            if (msgs.length > 0) {
                v1 = (Integer)msgs[0];
            }

            if (msgs.length > 1) {
                v2 = (Integer)msgs[1];
            }

            if (v1 == null || v2 == null)
                return;
            if(action==Global.Action_win_del){
                if (v1!=-1&&v2!=-1)
                    iwinoper.delWin(v1, v2);


            } else if (action == Global.Action_win_add) {
                if (v1 != -1)
                    iwinoper.createWin(v1);

            } else if (action == Global.Action_win_change) {
                    if (v1!=-1&&v2!=-1)                 {
                        iwinoper.changeNextWin(v1,v2);
                    }
            } else if (action == Global.Action_win_update_num) {

            }
        }
    };

    private CLBus.CBEventer listener_change_main_ui=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action==Global.Action_night_mode){
                boolean value = ((Boolean) msgs[0]).booleanValue();

                if (value) {
                    changeToNight();
                } else {
                    changeToDay();
                }
            } else if (action == Global.Action_close_ad) {
                Integer value = (Integer) msgs[0];
                if (m_AdView == null)
                    return;
                if (value.intValue() == 1) {
                    m_AdView.setVisibility(View.INVISIBLE);
                } else {
                    m_AdView.setVisibility(View.VISIBLE);
                }
            }
        }
    };

    private CLBus.CBEventer listener_change_fullscreen=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action==Global.Action_fullscreen){
                boolean value = ((Boolean) msgs[0]).booleanValue();

                if (value) {
                    Setting.Share_Setting().set_fullscreen_mode(true);
                } else {
                    Setting.Share_Setting().set_fullscreen_mode(false);
                }
            }
        }
    };

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        CLBus.Share_Instance().register(Global.Group_favorite, listener_clbus,
                Global.Action_favorite_add,Global.Action_favorite_del);
        CLBus.Share_Instance().register(Global.Group_open_url,listener_open_url,
                Global.Action_open_url_from_fav_history);

        CLBus.Share_Instance().register(Global.Group_open_url,listener_open_url,
                Global.Action_open_url_in_current_window);
        CLBus.Share_Instance().register(Global.Group_Change_UA,listener_change_UA,Global.Action_change_UA);
        CLBus.Share_Instance().register(Global.Group_main_menu,listener_change_mainmenu_bookmark,Global.Action_main_menu);
        CLBus.Share_Instance().register(Global.Group_win_event,listener_win,Global.Action_win_del);
        CLBus.Share_Instance().register(Global.Group_win_event,listener_win,Global.Action_win_add);
        CLBus.Share_Instance().register(Global.Group_win_event,listener_win,Global.Action_win_change);
       // CLBus.Share_Instance().register(Global.Group_win_event,listener_win,Global.Action_win_update_num);
	    CLBus.Share_Instance().register(Global.Group_main_ui,listener_change_main_ui,Global.Action_night_mode);
        CLBus.Share_Instance().register(Global.Group_main_ui,listener_change_fullscreen,Global.Action_fullscreen);

        CLBus.Share_Instance().register(Global.Group_main_ui,listener_change_fullscreen,Global.Action_fullscreen);

        CLBus.Share_Instance().register(Global.Group_main_ui,listener_change_main_ui,Global.Action_close_ad);
        CLBus.Share_Instance().register(Global.Group_main_ui,listener_change_main_ui,Global.Action_night_mode);
        menuBus.register(cc);

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        CLBus.Share_Instance().unregister(Global.Group_favorite,listener_clbus);
        CLBus.Share_Instance().unregister(Global.Group_open_url,listener_open_url);
        CLBus.Share_Instance().unregister(Global.Group_Change_UA,listener_change_UA);
        CLBus.Share_Instance().unregister(Global.Group_main_menu,listener_change_mainmenu_bookmark);
        CLBus.Share_Instance().unregister(Global.Group_win_event,listener_win);
		CLBus.Share_Instance().unregister(Global.Group_main_ui,listener_change_main_ui);
        menuBus.unregister();
    }


    private void update_UA(){
        int _ua=Setting.Share_Setting().get_UA();
        CL.CLOGI("UA:"+_ua);
        if(_ua==101)Global.Crt_UA=Global.UA_Android;
        else if(_ua==102)Global.Crt_UA=Global.UA_Chrome;
        else if(_ua==103)Global.Crt_UA=Global.UA_Iphone;
        weber.getSettings().setUserAgentString(Global.Crt_UA);
    }
    private CLBus.CBEventer listener_change_UA=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            update_UA();
        }
    };

    public class WinOperator implements IWinOperator {
        @Override
        public void createWin(int cur_pos) {
            if(isDrawerOpen(layer_right)){
                closeDrawer(layer_right);
            }
//            if (weber!=null) {
//                weber.stopLoading();
//                weber.onPause();
//                weber.destroy();
//            }
//            fl_webview.removeAllViews();


            String _url= Setting.Share_Setting().get_main_page();
            Struct.StructWebsite structWebsite = new Struct.StructWebsite();
            structWebsite.url = _url;

            int index = cur_pos;


            home_grid.setVisibility(View.GONE);
            fl_webview.setVisibility(View.VISIBLE);
            addresser.set_address("");
            addresser.set_loading_state(WebAddressStatusBar.STOP);
            addresser.set_progress(0);
            addresser.update_website_icon(null);

            weber=new CLWebkit(cc,listener_weber);
            weber.setLayoutParams(CL.Get_LP_MM());

            CL.CLOGI("vevEddy createWin fl_webview.getChildCount() = "+fl_webview.getChildCount()+" list_weber.size() = "+list_weber.size());
            int num = fl_webview.getChildCount();
            fl_webview.addView(weber, num-2);


            CL.Set_Fouces_When_Touch(weber);
            weber.requestFocus();

            if(Global.Main_Page_Url!=null)weber.loadUrl(Global.Main_Page_Url);
            list_weber.add(index+1, weber);
            toolbar.updateWinNum(list_weber.size());
        }

        @Override
        public void delWin(int pos, int cur_pos) {
            if(isDrawerOpen(layer_right)){
                closeDrawer(layer_right);
            }
            int select_index = cur_pos;
            int index = pos;
            int size = list_weber.size();


            if (size > 1 && (index >=0 &&index < size)) {
                //如果最后一个，选中前面的

                CLWebkit last_web = list_weber.get(index);
                last_web.reload ();
                last_web.removeAllViews();
                last_web.destroy();
                fl_webview.removeView(last_web);
                list_weber.remove(index);
                if (select_index == index) {//并且是被选中的
                    if (index >= list_weber.size())
                        index -= 1;

                    list_weber.get(index).setVisibility(View.VISIBLE);
                    weber = list_weber.get(index);
                }

            } else if (size == 1) {


            }
            toolbar.updateWinNum(list_weber.size());
        }

        @Override
        public void changeNextWin(int pos, int old_pos) {
            if(isDrawerOpen(layer_right)){
                closeDrawer(layer_right);
            }

            if (pos == old_pos) {
                return;
            }

            if (list_weber.indexOf(weber) !=old_pos) {
                CL.CLOGI("EddyHu weber = "+list_weber.indexOf(weber)+" | oldpos = " + old_pos);
                return;
            }
            fl_webview.setVisibility(View.VISIBLE);
            home_grid.setVisibility(View.GONE);
            weber.setVisibility(View.INVISIBLE);
            CLWebkit webview = list_weber.get(pos);

            if (webview !=null) {
                weber = webview;

                webview.setVisibility(View.VISIBLE);
                Bitmap icon = webview.get_bitmap();
                boolean have_icon = webview.is_hava_website_icon();
                boolean is_star = webview.is_isStar();
                int progress = webview.getProgress();
                WebChromeClient.CustomViewCallback callback = webview.getCallback();
                boolean is_hide_custorm_view = webview.isHave_hide_customview();
                String url = webview.getUrl();
                updateyoutbue_ad(url);
//                int count = Setting.Share_Setting().get_app_run_count();
//                if (url != null && Setting.Share_Setting().get_youtube_play_close() && count < Setting.Share_Setting().get_youtube_play_in_count()) {
//                    if (url.toLowerCase().contains("youtube.com"))
//                        CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui, Global.Action_close_ad, 1);
//                    else
//                        CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui, Global.Action_close_ad, 0);
//                }
                //String title = webview.getTitle();
                String old_ulr = addresser.get_address();
                if (webview.is_finishloaded()) {
                   // load_finish = true;
                    if (url != null)
                        addresser.set_address(url);
                    else
                        addresser.set_address(old_ulr);
                    addresser.set_loading_state(WebAddressStatusBar.STOP);
                    addresser.set_progress(0);
                    CL.CLOGI("EddyHu progress1 100");
//                    toolbar.set_can_back(weber.canGoBack());
//                    toolbar.set_can_forward(weber.canGoForward());
                } else {
                  //  load_finish = false;
                    if (url != null)
                        addresser.set_address(url);
                    else
                        addresser.set_address(url);
                    if (progress != 0) {
                        addresser.set_loading_state(WebAddressStatusBar.STOP);
                    }
                    addresser.set_progress(progress);
                    CL.CLOGI("EddyHu progress = "+progress);
                }

                toolbar.set_can_back(weber.canGoBack());
                toolbar.set_can_forward(weber.canGoForward());
                toolbar.set_fav_state(is_star);


                if (!have_icon) {
                    addresser.update_website_icon(null);
                } else {
                    addresser.update_website_icon(icon);
                }

                if (url != null)
                    star_data=typer_fav.get_favorite_star(cc,url.toLowerCase());
                else
                    star_data=null;

                toolbar.set_fav_state(star_data==null?false:true);
                //custom view
                if (callback != null) {
                    //如果当前页也有custom video 先关闭
                    if (cb_video != null) {
                        cb_video.onCustomViewHidden();
                        cb_video = null;
                    }
                    cb_video = callback;
                    if (is_hide_custorm_view) {
                        CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video, Global.Action_hide_video, new CLCallback.CB() {
                            @Override
                            public void on_callback() {
                                if (cb_video != null) {
                                    cb_video.onCustomViewHidden();
                                    cb_video = null;
                                }
                            }
                        });
                    } else {
//                        if(cb_video!=null){
//                            cb_video.onCustomViewHidden();
//                            cb_video=null;
//                            return;
//                        }
                        View view = webview.get_view();
                        CLBus.Share_Instance().send_msg_immediate(Global.Group_web_video,Global.Action_show_video,view);
                    }
                }

            } else {
                CL.CLOGI("EddyHu weber is null");
            }

        }


        @Override
        public void open_url(String url) {
            weber.stopLoading();
            addresser.set_address(url);
            addresser.set_loading_state(WebAddressStatusBar.STOP);
            addresser.set_progress(0);
            addresser.update_website_icon(null);

            weber.loadUrl(url);
            update_multi_win_list();
        }
    }
}
