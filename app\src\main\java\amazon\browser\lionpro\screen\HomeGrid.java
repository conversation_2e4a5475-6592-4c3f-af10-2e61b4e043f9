package amazon.browser.lionpro.screen;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.ScaleAnimation;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.ad.GoogleAdmob;
import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Affairs;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.primary.AcyMain;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.views.CustomGridView;
import amazon.browser.lionpro.views.Deleter;
import com.google.android.gms.ads.AdView;
//import com.google.android.gms.ads.formats.NativeAdView;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;

import lion.CL;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLVThumLoader;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/7/29.
 */
public class HomeGrid extends LinearLayout implements CLFlipper.EventListener, GoogleAdmob.NativeEventer {

    private AcyMain cc;
    private CLFlipper flipper;
    private CLCallback.CB cber_update;
    private FrameLayout fl_content;
    private Deleter btn_del;
    private ScaleAnimation anim_scale;
    private CustomGridView gv_list;
    private AdapterForfavorites adapter;
    private ArrayList<Struct.StructWebsite> datas=new ArrayList<>();
    private LinearLayout flSubContent;
    private FrameLayout fl_ad;
 //   private NativeAdView native_adview;
//    private Struct.StoreDir data;


    private LinearLayout dialog_content;

    private boolean discard=false;
    private CLVThumLoader loader;
    private int grid_column=5;
    private int grid_width_height=0;
    private boolean editor=false;
    private int del_number=0;
    private Affairs.TypeFavorite typer;
   // private ArrayList<Struct.StructWebsite> iListfavorite = new ArrayList<Struct.StructWebsite>(10);
//    public class favorites_datas {
//        String name;
//        String image;
//        String url;
//    }

    private CLBus.CBEventer listener_clbus=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            updateGrid();
        }
    };

    public HomeGrid(AcyMain context, CLFlipper f, CLCallback.CB cber_update) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_update=cber_update;
        init();
        CLBus.Share_Instance().register(Global.Group_main_menu, listener_clbus,
                Global.Action_main_menu);
    }

    public void updateAdsStatus() {
        if (fl_ad != null && Setting.Share_Setting().get_subscription_flag()) {
            fl_ad.setVisibility(View.GONE);
        }
    }

    public void update_data(ArrayList<Struct.StructWebsite> d){
        this.datas=d;
        if(this.datas==null)return;

        adapter.notifyDataSetChanged();
    }


    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }

//    @Override
//    protected void onLayout(boolean changed, int l, int t, int r, int b) {
//        super.onLayout(changed,l,t,r,b);
//        if(changed){
//            if(this.getWidth()>this.getHeight()){
//                grid_column=5;
//                gv_list.setNumColumns(grid_column);
//            }else {
//                grid_column=2;
//                gv_list.setNumColumns(grid_column);
//            }
//            grid_width_height=(this.getWidth()-CL.DIP2PX_INT(4)*(grid_column+1))/grid_column;
//            grid_width_height=grid_width_height*70/100;
//        }
//    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();

    }

    private void clear_resource(){
        if(datas!=null){
            for(int i=0;i<datas.size();++i){
                Struct.StructWebsite _tmp=datas.get(i);
                recycle_item(_tmp);
            }
            datas.clear();
            CL.CLOGI("P_memory:"+memory);
        }
    }
    private void recycle_item(Struct.StructWebsite item){
//        if(item!=null && item.bitmap_thumb!=null && !item.bitmap_thumb.isRecycled()){
//            memory-=item.bitmap_thumb.getByteCount();
//            item.bitmap_thumb.recycle();
//            item.bitmap_thumb=null;
//        }
    }

    private OnClickListener listener_ad=new OnClickListener() {
        @Override
        public void onClick(View v) {
//            Global.Show_Interstitial_IM(cc, new CLCallback.CB() {
//                @Override
//                public void on_callback() {
//                }
//            });
        }
    };


    private static String getJson(Context mContext, String fileName) {
        // TODO Auto-generated method stub
        StringBuilder sb = new StringBuilder();
        AssetManager am = mContext.getAssets();
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(
                    am.open(fileName)));
            String next = "";
            while (null != (next = br.readLine())) {
                sb.append(next);
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            sb.delete(0, sb.length());
        }
        return sb.toString().trim();
    }

    public void activity_orientation(boolean landscape) {
        if (landscape) {
            fl_ad.setVisibility(View.GONE);
        } else {
            fl_ad.setVisibility(View.VISIBLE);
        }
    }

    public void updateGrid() {
        datas.clear();
        final String BOOKMARKS = "bookmarks";
        typer=new Affairs.TypeFavorite();

        String str_json = getJson(cc, Global.json_bookmarks);
        str_json = str_json.replaceAll("\r", "");
        str_json = str_json.replaceAll("	", "");
        try {
            JSONObject json = new JSONObject(str_json);
            JSONArray bookmarksArray = json.getJSONArray(BOOKMARKS);


            for (int i = 0; i < bookmarksArray.length(); i++) {
                JSONObject pageJson = bookmarksArray.getJSONObject(i);
                try {
                    String name = pageJson.getString("name");
                    String image = pageJson.getString("image");
                    String url = pageJson.getString("url");

                    // favorites_datas data = new favorites_datas();
                    Struct.StructWebsite data = new Struct.StructWebsite();
                    data.title = name;
                    data.icon_file_name = image;
                    data.url = url;
                    this.datas.add(data);
                } catch (Exception e) {
                    // TODO: handle exception
                }
            }
        } catch (JSONException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        ArrayList<Struct.StructWebsite> list_favorites = typer.get_favorites(this.getContext());
        datas.addAll(list_favorites);
        adapter.notifyDataSetChanged();
    }
    private void initadapter() {
        final String BOOKMARKS = "bookmarks";
        typer=new Affairs.TypeFavorite();

        String str_json = getJson(cc, Global.json_bookmarks);
        str_json = str_json.replaceAll("\r", "");
        str_json = str_json.replaceAll("	", "");
        try {
            JSONObject json = new JSONObject(str_json);
            JSONArray bookmarksArray = json.getJSONArray(BOOKMARKS);


            for (int i = 0; i < bookmarksArray.length(); i++) {
                JSONObject pageJson = bookmarksArray.getJSONObject(i);
                try {
                    String name = pageJson.getString("name");
                    String image = pageJson.getString("image");
                    String url = pageJson.getString("url");

                   // favorites_datas data = new favorites_datas();
                    Struct.StructWebsite data = new Struct.StructWebsite();
                    data.title = name;
                    data.icon_file_name = image;
                    data.url = url;
                    this.datas.add(data);
                } catch (Exception e) {
                    // TODO: handle exception
                }
            }
        } catch (JSONException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        ArrayList<Struct.StructWebsite> list_favorites = typer.get_favorites(this.getContext());
        datas.addAll(list_favorites);
        adapter.notifyDataSetChanged();
    }
    private void init(){
        this.setOrientation(LinearLayout.VERTICAL);

        fl_content=new FrameLayout(cc);
        fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));


        this.addView(fl_content);

        flSubContent = new LinearLayout(cc);
        flSubContent.setOrientation(LinearLayout.VERTICAL);
        //  flSearcher.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP));
        flSubContent.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP, 0, 0, 0, 0, 0));
        flSubContent.setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
        fl_content.addView(flSubContent);

        gv_list=new CustomGridView(cc);

        gv_list.setMotionEventSplittingEnabled(false);
        //gv_list.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP,Gravity.FILL));
        gv_list.setLayoutParams(CL.Get_LLLP(CL.MP, CL.MP,1.0f, 0, CL.DIP2PX_INT(10),0,CL.DIP2PX_INT(8)));
        gv_list.setCacheColorHint(Color.TRANSPARENT);
        //gv_list.setEmptyView(_empty);
        gv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        int _space=CL.DIP2PX_INT(4);
        gv_list.setHorizontalSpacing(_space);
        gv_list.setVerticalSpacing(_space);
        gv_list.setPadding(_space,_space,_space,_space);
        adapter=new AdapterForfavorites();
        gv_list.setAdapter(adapter);
        gv_list.setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
        gv_list.setNumColumns(grid_column);
        flSubContent.addView(gv_list);
        initadapter();

        fl_ad = new FrameLayout(cc);
        fl_ad.setBackgroundColor(cc.getResources().getColor(R.color.bg_main_title));
       // fl_ad.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP, 1.0f, 0, (int)CL.DIP2PX(10), 0, 0));
        LinearLayout.LayoutParams params = CL.Get_LLLP(CL.MP, CL.MP, 1.0f, (int)CL.DIP2PX(20), (int)CL.DIP2PX(10), (int)CL.DIP2PX(20),(int)CL.DIP2PX(10));
        fl_ad.setLayoutParams(params);
        flSubContent.addView(fl_ad);

        Global.Get_Native_Ad(cc, fl_ad, this);

       // addAdView(this);

    }
    public static AdView m_AdView;
    private void addAdView(ViewGroup parent) {
        m_AdView=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), new CLCallback.CB() {
            @Override
            public void on_callback() {
                if (m_AdView != null && m_AdView.getVisibility() != View.GONE)
                    m_AdView.setVisibility(View.GONE);
            }
        });
        if (m_AdView != null)
            parent.addView(m_AdView);
    }
    private final int Max_Memory=(int)(8*1024*1024* CL.Density);
    private int memory=0;
    private CLVThumLoader.LoaderListener listener_loader=new CLVThumLoader.LoaderListener() {
        @Override
        public void on_load_complete(final String opath, String tpath, final Bitmap bm,final Object tag) {

        }

        @Override
        public void on_load_fail(String opath, Object tag) {

        }
    };

    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {

        }
        @Override
        public void on_cancel_click() {
//            editor=false;
//            del_number=0;
//            btn_del.deformation(false);
//            btn_del.setVisibility(View.GONE);
//            btn_export.setVisibility(View.GONE);
//            for(int i=0;i<datas.size();++i){
//                datas.get(i).check = false;
//            }
//            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {

        }
    };

    @Override
    public void onAddedCallback(View v) {
        CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui, Global.Action_close_ad, 0);
    }

    private View view_last_long_click;

    private class AdapterForfavorites extends BaseAdapter {

        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new FavoriteItem(cc);
            FavoriteItem _v=(FavoriteItem)cv;
            //_v.set_basic_data(datas.get(position));
            try {
                _v.set_basic_data(datas.get(position), position);
            } catch (IndexOutOfBoundsException e) {
                if (datas.size() > 0)
                    _v.set_basic_data(datas.get(0), position);
                else
                    return null;
            }

           // ViewUtils.setGridViewItemWith(cv, parent, 50, 10, 10, true);
            return cv;
        }
    }

    private OnClickListener listener_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
          //  CLBus.Share_Instance().send_msg_immediate(Global.Group_win_event,Global.Action_win_add, Integer.valueOf(index));
            Integer index = (Integer) v.getTag();
            CLBus.Share_Instance().send_msg_immediate(Global.Group_open_url,Global.Action_open_url_in_current_window,datas.get(index).url);
        }
    };
    private OnLongClickListener listener_click_long=new OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            return true;
        }
    };


    private class FavoriteItem extends LinearLayout {

        private Struct.StructWebsite data;
        private ImageView iv_image;
        private String url;
        private TextView tv_name;

        public FavoriteItem(Context context) {
            super(context);
            this.setClickable(true);
            this.setLongClickable(true);
            this.setLayoutParams(new AbsListView.LayoutParams(CL.MP,CL.MP));
            this.setOnClickListener(listener_click);
            this.setOrientation(LinearLayout.VERTICAL);
          //  this.setOnLongClickListener(listener_click_long);

            iv_image=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(36)),null,null);
            this.addView(iv_image);


            tv_name=CLController.Get_TextView(cc, CL.Get_LLLP(CL.MP,CL.WC,Gravity.TOP|Gravity.LEFT),"",Color.WHITE,12,null);
            tv_name.setMaxLines(1);
           // tv_name.setEllipsize(TextUtils.TruncateAt.END);
            tv_name.setGravity(Gravity.CENTER);
           // tv_name.setBackgroundColor(0xaa000000);
            tv_name.setPadding(CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3));
            this.addView(tv_name);
        }
        public void set_basic_data(Struct.StructWebsite d, int postion){
            this.data=d;

//            AbsListView.LayoutParams _lp=(AbsListView.LayoutParams) this.getLayoutParams();
//            _lp.width=CL.MP;
//            _lp.height=grid_width_height;
//            this.setLayoutParams(_lp);

            try {
                InputStream is = null;
                if (data.icon_file_name.endsWith(".png")) {
                    is = cc.getAssets().open(data.icon_file_name);
                } else {
                    data.icon_file_name =  Global.path_website;
                    is = cc.getAssets().open(data.icon_file_name);
                }
                Drawable drawable = Drawable.createFromStream(is, null);
                iv_image.setImageDrawable(drawable);
                iv_image.setScaleType(ImageView.ScaleType.FIT_CENTER);
                tv_name.setText(data.title);
                this.setTag(Integer.valueOf(postion));
            } catch (Exception e) {

            }
        }
    }
}
