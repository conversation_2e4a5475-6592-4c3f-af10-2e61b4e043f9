package amazon.browser.lionpro.screen;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.appcompat.view.ContextThemeWrapper;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Affairs;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
//import com.browser.lionpro.primary.AcyRemoveAd;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.util.MenuBus;
import com.bumptech.glide.Glide;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;

//import gp.PremiumActivity;
import lion.CL;
import lion.CLBus;
import lion.CLController;

import static amazon.browser.lionpro.datas.Setting.Share_Setting;


/**
 * Created by leron on 2016/7/1.
 */
public class MainMenu extends LinearLayout{



    public interface Eventer{
        void on_main_page();
        void on_music_player();
        void on_wifi_share();
        void on_my_download();
        void on_share();
        void on_feedback();
        void on_setting();
        void open_url(String url);
        void open_ad(int value);
        void close_view(View v);
        void open_more_app(ArrayList<Setting.ApkInfo> apks);
        void remove_ad();
    }




    private Context cc;
    private Eventer listener;
    private LinearLayout btn_music_list;
    private LinearLayout btn_wifi_share;
    private LinearLayout btn_main_page;
    private LinearLayout btn_my_download;
    private LinearLayout btn_setting;
    private LinearLayout btn_ad;
    private LinearLayout btn_more_app;
    private RelativeLayout btn_sub_more_app;
    private LinearLayout btn_share;
    private LinearLayout btn_feedback;
    private LinearLayout _ll_bookmark;
    private LinearLayout _ll_main;
    private RelativeLayout btn_browser;
    private RelativeLayout btn_downloader;
    private Affairs.TypeFavorite typer;
    private View rpMenu;


    private MenuBus menuBus = new MenuBus() {
        @Override
        public void onActionMainMenuSetting(boolean show) {
            if (Share_Setting().get_redpoint() == 1)
                rpMenu.setVisibility(show ? VISIBLE : GONE);
        }
    };

    public MainMenu(Context context,Eventer listen) {
        super(context);
        this.cc=context;
        this.listener=listen;
        this.setFocusable(true);
        this.setFocusableInTouchMode(true);
        this.setBackgroundColor(0xff202020);
        this.setPadding(0,CL.DIP2PX_INT(6),0,0);
        this.setClickable(true);

        typer=new Affairs.TypeFavorite();
        ScrollView _sv=new ScrollView(context);
        _sv.setLayoutParams(CL.Get_LP_MM());
        this.addView(_sv);

        _ll_main=CLController.Get_LinearLayout(context,CL.Get_LP_MM(),LinearLayout.VERTICAL,null);
        _sv.addView(_ll_main);


        btn_main_page =get_btn(R.mipmap.icon_home_normal,R.mipmap.icon_home_click,R.string.main_page);
        _ll_main.addView(btn_main_page);

        btn_music_list=get_btn(R.mipmap.icon_music_normal,R.mipmap.icon_music_click,R.string.music_player_list);
        _ll_main.addView(btn_music_list);

        btn_wifi_share=get_btn(R.mipmap.icon_wifi_normal,R.mipmap.icon_wifi_click,R.string.wifi_share);
        _ll_main.addView(btn_wifi_share);

        btn_my_download=get_btn(R.mipmap.icon_mydownload_normal,R.mipmap.icon_mydownload_click,R.string.store_title);
        _ll_main.addView(btn_my_download);

        btn_share=get_btn(R.mipmap.icon_share_normal,R.mipmap.icon_share_click,R.string.share);
        _ll_main.addView(btn_share);

        btn_feedback=get_btn(R.mipmap.icon_feedback_normal,R.mipmap.icon_feedback_click,R.string.feedback);
        _ll_main.addView(btn_feedback);

        btn_setting=get_btn(R.mipmap.icon_setting_normal,R.mipmap.icon_setting_click,R.string.settings);
        _ll_main.addView(btn_setting);


        btn_ad = get_btn(R.mipmap.icon_ad_normal, R.mipmap.icon_ad_click, R.string.remove_ad);
        _ll_main.addView(btn_ad);

        btn_more_app= new LinearLayout(cc);
        btn_more_app.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        btn_more_app.setLayoutParams(lp);
        _ll_main.addView(btn_more_app);



//
//        btn_downloader = get_ad_btn(R.mipmap.bl2, R.mipmap.bl2, R.string.str_browser_bl2);
//        _ll_main.addView(btn_downloader);

        rpMenu = new View(cc);
        rpMenu.setId(R.id.view_red_point);
        int wh = (int)CL.DIP2PX(8);
        rpMenu.setLayoutParams(CL.Get_FLLP(wh, wh, Gravity.LEFT,
                (int)CL.DIP2PX(12), -(int)CL.DIP2PX(12), 0, 0));
        rpMenu.setBackgroundResource(R.drawable.red_point);
        rpMenu.setVisibility(View.INVISIBLE);
        btn_setting.addView(rpMenu);

        View divid = CLController.Get_TextView_Divider(cc,new AbsListView.LayoutParams(CL.MP,CL.DIP2PX_INT(6)),0xff2c2c2c);
        _ll_main.addView(divid);

        _ll_bookmark=CLController.Get_LinearLayout(cc,CL.Get_LP_MM(),LinearLayout.VERTICAL,null);
        _ll_main.addView(_ll_bookmark);
        addMarkBook(_ll_bookmark);


       // CLBus.Share_Instance().register(Global.Group_favorite, listener_clbus,
        //        Global.Action_main_favroite_refresh);
    }
/*
    public void loadAds() {
        long run_time = 1000;//Share_Setting().get_app_run_time();
        if (run_time > Share_Setting().get_server_config_run_time()) {
            int id = 100;
            int parent_id = 1000;
            if (btn_ad != null) {
                btn_ad.removeAllViews();
            }
            ArrayList<Setting.ApkInfo> apks = Setting.Share_Setting().getApks();
            if (apks != null) {
                for (Setting.ApkInfo info : apks) {
                    String pack = info.getPack_name();
                    String title = info.getTitle();
                    String image_path = info.getImage();
                    RelativeLayout rl = get_ad_btn(++id, ++id, title, image_path);
                    rl.setId(++parent_id);
                    rl.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            cc.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + pack)));
                        }
                    });
                    btn_ad.addView(rl);

                    View div = new View(cc);
                    RelativeLayout.LayoutParams lpdiv = new RelativeLayout.LayoutParams(CL.MP, CL.DIP2PX_INT(1));
                    lpdiv.addRule(RelativeLayout.BELOW, parent_id);
                    btn_ad.addView(div, lpdiv);
                }
            }
        }
    }
*/

    public void loadAds() {
        int count = Setting.Share_Setting().get_app_run_count();
        long run_time = Setting.Share_Setting().get_app_run_time();
        if (count > Setting.Share_Setting().get_update_count()
                && Setting.Share_Setting().get_complete_first_download()
                && run_time > (Setting.Share_Setting().get_server_config_run_time())) {
                ArrayList<Setting.ApkInfo> apks = Setting.Share_Setting().getApks();
                if (apks != null && apks.size() > 0) {
                    btn_more_app.removeAllViews();
                    btn_sub_more_app = get_ad_btn(R.mipmap.icon_moreapps_normal, R.mipmap.icon_moreapps_click, R.string.str_more_apps);
                    //_ll_main.addView(btn_ads);
                    btn_sub_more_app.setTag(apks);
                    btn_more_app.addView(btn_sub_more_app);
                }
        }
    }


    private CLBus.CBEventer listener_clbus=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
           // updateGrid();
            reLoadMenu();
        }
    };


    public void reLoadMenu() {
        _ll_bookmark.removeAllViews();
        addMarkBook(_ll_bookmark);
    }

    private void addMarkBook(ViewGroup parent) {

        final String BOOKMARKS = "bookmarks";


        String str_json = getJson(cc, Global.json_bookmarks);
        str_json = str_json.replaceAll("\r", "");
        str_json = str_json.replaceAll("	", "");
        try {
            JSONObject json = new JSONObject(str_json);
            JSONArray bookmarksArray = json.getJSONArray(BOOKMARKS);


            for (int i = 0; i < bookmarksArray.length(); i++) {
                JSONObject pageJson = bookmarksArray.getJSONObject(i);
                try {
                    String name = pageJson.getString("name");
                    String image = pageJson.getString("image");
                    String url = pageJson.getString("url");

                    View view = get_bookmark_btn(name, image, url);
                    parent.addView(view);
                   // View divid = CLController.Get_TextView_Divider(cc,new AbsListView.LayoutParams(CL.MP,CL.DIP2PX_INT(1)),0xff2c2c2c);
                   // parent.addView(divid);
                } catch (Exception e) {
                    // TODO: handle exception
                }
            }
        } catch (JSONException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        ArrayList<Struct.StructWebsite> datas = typer.get_favorites(this.getContext());
        if (datas != null && !datas.isEmpty()) {
            for (Struct.StructWebsite site : datas) {
                String name = site.title;
                String image = Global.path_website;
                String url = site.url;
                View view = get_bookmark_btn(name, image, url);
                parent.addView(view);
                View divid = CLController.Get_TextView_Divider(cc,new AbsListView.LayoutParams(CL.MP,CL.DIP2PX_INT(1)),0xff2c2c2c);
                parent.addView(divid);
            }
        }
    }

    public void open_home() {
        if (listener != null)
            listener.on_main_page();
    }

    public static String getJson(Context mContext, String fileName) {
        // TODO Auto-generated method stub
        StringBuilder sb = new StringBuilder();
        AssetManager am = mContext.getAssets();
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(
                    am.open(fileName)));
            String next = "";
            while (null != (next = br.readLine())) {
                sb.append(next);
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            sb.delete(0, sb.length());
        }
        return sb.toString().trim();
    }

    private View.OnClickListener listener_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
                if (v == btn_main_page) {
                    if (listener != null) listener.on_main_page();
                } else if (v == btn_music_list) {
                    if (listener != null) listener.on_music_player();
                } else if (v == btn_wifi_share) {
                    if (listener != null) listener.on_wifi_share();
                } else if (v == btn_my_download) {
                    if (listener != null) listener.on_my_download();
                } else if (v == btn_setting) {
                    if (listener != null) listener.on_setting();
                } else if (v == btn_share) {
                    if (listener != null) listener.on_share();
                } else if (v == btn_feedback) {
                    if (listener != null) listener.on_feedback();
                } else if (v == btn_browser) {
                    if (listener != null) listener.open_ad(1);
                } else if (v == btn_downloader) {
                    if (listener != null) listener.open_ad(2);
                } else if (v == btn_sub_more_app) {
                    ArrayList<Setting.ApkInfo> apks = (ArrayList<Setting.ApkInfo>) btn_sub_more_app.getTag();
                    if (listener != null) listener.open_more_app(apks);
                } else if (v == btn_ad) {
                    if (listener != null) listener.remove_ad();

                }

        }
    };


    private LinearLayout get_btn(final int rid_normal,final int rid_click,int text_id){
        LinearLayout _ll= CLController.Get_LinearLayout(cc,
                CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(40)),LinearLayout.HORIZONTAL,listener_click);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        if(rid_normal!=-1){
            ImageView _iv_icon=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.DIP2PX_INT(28),CL.DIP2PX_INT(28),CL.DIP2PX_INT(18),0,0,0),
                    null,null);
            _iv_icon.setImageDrawable(CL.Get_StateList_Drawable(cc,rid_normal,rid_click));
            _iv_icon.setTouchDelegate(_ll.getTouchDelegate());
            _iv_icon.setScaleType(ImageView.ScaleType.FIT_CENTER);
            _ll.addView(_iv_icon);
        }
        if(text_id!=-1){
            _ll.addView(CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(22),0,0,0),
                    cc.getResources().getString(text_id),Color.WHITE,15,null));
        }
        return _ll;
    }


    private RelativeLayout get_ad_btn(final int rid_normal, final int rid_click, int text_id) {
        RelativeLayout _rl= CLController.GetRelativeLayout(cc,
                CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(40)),LinearLayout.HORIZONTAL,listener_click);
        //_rl.setGravity(Gravity.CENTER_VERTICAL);

        TextView txt = new TextView(new ContextThemeWrapper(cc,R.style.AppTheme_AdAttribution));
        _rl.addView(txt);
        if(rid_normal!=-1){
            RelativeLayout.LayoutParams rlp = CL.Get_RLLP(CL.DIP2PX_INT(28),CL.DIP2PX_INT(28),CL.DIP2PX_INT(18),0,0,0);
            //rlp.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
            rlp.addRule(RelativeLayout.CENTER_VERTICAL, RelativeLayout.TRUE);
            ImageView _iv_icon=CLController.Get_ImageView(cc, rlp, null, null);
            _iv_icon.setId(200);
            _iv_icon.setImageDrawable(CL.Get_StateList_Drawable(cc,rid_normal,rid_click));
            _iv_icon.setTouchDelegate(_rl.getTouchDelegate());
            _iv_icon.setScaleType(ImageView.ScaleType.FIT_CENTER);
            _rl.addView(_iv_icon);
        }
        if(text_id!=-1){
            RelativeLayout.LayoutParams rlp = CL.Get_RLLP(CL.WC,CL.WC,CL.DIP2PX_INT(22),0,0,0);
            rlp.addRule(RelativeLayout.RIGHT_OF, 200);
            rlp.addRule(RelativeLayout.CENTER_VERTICAL);
            _rl.addView(CLController.Get_TextView(cc, rlp, cc.getResources().getString(text_id),Color.WHITE,15,null));
        }
        return _rl;
    }

    private RelativeLayout get_ad_btn(int id, int id2, String text, String path) {
        RelativeLayout rl = new RelativeLayout(cc);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CL.DIP2PX_INT(48));
        rl.setLayoutParams(lp);

        ImageView iv = new ImageView(cc);
        RelativeLayout.LayoutParams rlp_iv = new RelativeLayout.LayoutParams(CL.DIP2PX_INT(48), CL.DIP2PX_INT(48));
        rlp_iv.addRule(RelativeLayout.ALIGN_PARENT_START);
        rlp_iv.addRule(RelativeLayout.CENTER_IN_PARENT);
        rl.setPadding(CL.DIP2PX_INT(4),CL.DIP2PX_INT(4),CL.DIP2PX_INT(4),CL.DIP2PX_INT(4));
        rl.addView(iv, rlp_iv);
        iv.setId(id);
        iv.setTag(null);
        Glide.with(cc).load(path).into(iv);

        TextView txt = new TextView(new ContextThemeWrapper(cc,R.style.AppTheme_AdAttribution));

        rl.addView(txt);

        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.WRAP_CONTENT);
        rlp.addRule(RelativeLayout.CENTER_IN_PARENT);
        rlp.addRule(RelativeLayout.RIGHT_OF, id);
        txt = new TextView(cc);
        txt.setText(text);
        txt.setId(id2);
        txt.setGravity(Gravity.CENTER);
        txt.setPadding(0, CL.DIP2PX_INT(0), 0,CL.DIP2PX_INT(0));
        rl.addView(txt, rlp);

//        View div = new View(cc);
//        RelativeLayout.LayoutParams lpdiv = new RelativeLayout.LayoutParams(CL.MP, CL.DipToPix(cc, 1));
//        lpdiv.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
//        rl.addView(div,lpdiv);
        return rl;
    }


    private View.OnClickListener listener_markbook=new OnClickListener() {
        @Override
        public void onClick(View v) {


            String url = (String)v.getTag();
            if (url!= null && !url.isEmpty())
                if(listener!=null)listener.open_url(url);

        }
    };

    private LinearLayout get_bookmark_btn(final String name, final String image_name , String url){
        LinearLayout _ll= CLController.Get_LinearLayout(cc,
                CL.Get_LLLP(CL.MP,CL.MP),LinearLayout.HORIZONTAL,listener_markbook);
        _ll.setGravity(Gravity.CENTER_VERTICAL);

        AssetManager am = cc.getAssets();
        try {


            if(image_name!=null){

                InputStream in = am.open(image_name);

                Bitmap bm = BitmapFactory.decodeStream(in);

                ImageView _iv_icon=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.DIP2PX_INT(28),CL.DIP2PX_INT(28),CL.DIP2PX_INT(28),0,0,0),
                        null,null);
                //_iv_icon.setImageDrawable(CL.Get_StateList_Drawable(cc,rid_normal,rid_click));
                LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)_iv_icon.getLayoutParams();
                layoutParams.setMargins(CL.DIP2PX_INT(10),CL.DIP2PX_INT(5), 0, CL.DIP2PX_INT(5));
                _iv_icon.setImageBitmap(bm);
                _iv_icon.setTouchDelegate(_ll.getTouchDelegate());
                _iv_icon.setScaleType(ImageView.ScaleType.FIT_CENTER);
                _ll.addView(_iv_icon);
                _iv_icon.setTag(url);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        if(url!=null){
            TextView txt = CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(10),0,0,0),
                    name,Color.WHITE,15,null);
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)txt.getLayoutParams();
            layoutParams.setMargins(0,0, CL.DIP2PX_INT(10), 0);
            txt.setLayoutParams(layoutParams);
            txt.setSingleLine();
            txt.setTouchDelegate(_ll.getTouchDelegate());
            _ll.addView(txt);
            txt.setTag(url);
        }
        _ll.setTag(url);

        return _ll;
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        menuBus.register(cc);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        menuBus.unregister();
    }
}
