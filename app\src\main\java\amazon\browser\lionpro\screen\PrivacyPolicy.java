package amazon.browser.lionpro.screen;

import android.graphics.Color;
import android.view.Gravity;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ScrollView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.toys.CommonBackButton;

import java.io.BufferedReader;
import java.io.InputStreamReader;

import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/8/17.
 */
public class PrivacyPolicy extends LinearLayout implements CLFlipper.EventListener{

    private CLActivity cc;
    private CLFlipper flipper;


    public PrivacyPolicy(CLActivity context, CLFlipper f) {
        super(context);
        this.cc=context;
        this.flipper=f;

        this.setOrientation(LinearLayout.VERTICAL);


        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.Privacy_Policy).toString(),0xffd0d0d0,18,null));
       // this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));

        //FrameLayout _fl_content=new FrameLayout(cc);
        //_fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));

        ScrollView _fl_content=new ScrollView(context);
        _fl_content.setLayoutParams(CL.Get_LP_MM());
        _fl_content.setFillViewport(true);
        this.addView(_fl_content);

        LinearLayout _ll_content=CLController.Get_LinearLayout(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER,0,0,0,0),LinearLayout.VERTICAL,null);
        _ll_content.setGravity(Gravity.CENTER);
        String privacy_txt = getPrivacy("common/Privacy.txt");
        _fl_content.addView(CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.MP,0,0,0,0), privacy_txt,Color.WHITE,18,null));
    }

    private String getPrivacy(String fileName) {
        try {
            InputStreamReader inputReader = new InputStreamReader(getResources().getAssets().open(fileName) );
            BufferedReader bufReader = new BufferedReader(inputReader);
            String line="";
            String Result="";
            while((line = bufReader.readLine()) != null)
                Result += line;
            return Result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }
}
