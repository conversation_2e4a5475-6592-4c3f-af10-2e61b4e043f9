package amazon.browser.lionpro.screen;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.FileManager;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.views.CustomGridView;
import amazon.browser.lionpro.views.DiskCapacity;
import amazon.browser.lionpro.views.MusicViewDialog;

import com.google.android.gms.ads.AdView;

import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.CLFileSystem;
import lion.CLToast;
import lion.CLTools;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/6/21.
 */
public class RStorage extends LinearLayout{


    private CLActivity cc;
    private CLFlipper flipper;
    private FrameLayout fl_header;
    private CustomGridView gv_list;
    private DiskCapacity disker;

    private MusicViewDialog dialog_music;
    private AdapterForRes adapter;
    private ArrayList<Struct.StoreDir> datas;
    private CLCallback.CB_Activity cber_acy;

    private ResVideo layer_video;
    private ResMusic layer_music;
    private ResPicture layer_picture;
    private ResGif layer_gif;
    private ResAPK layer_apk;
    private ResOther layer_doc,layer_other;
    private AdView m_AdView;
    private View.OnClickListener listener_music_player=new OnClickListener() {
        @Override
        public void onClick(View v) {
            dialog_music.show();
        }
    };

    private FileManager.Eventer listener_filemanager=new FileManager.Eventer() {
        @Override
        public void on_data_update() {
            flipper.handler.post(new Runnable() {
                @Override
                public void run() {
                    FileManager fm= Server.Share_FileManager();
                    if(fm!=null){
                        datas=fm.get_datas();
                        adapter.notifyDataSetChanged();
                        CL.CLOGI("rstorage data size:"+datas.size());
                    }
                }
            });
        }
        @Override
        public void on_error(int code) {
            CLToast.Show(cc,"error:"+code,true);
        }
    };

    private CLCallback.CB cber_update=new CLCallback.CB() {
        @Override
        public void on_callback() {
            adapter.notifyDataSetChanged();
        }
    };


    public RStorage(CLActivity context, CLFlipper flipper, CLCallback.CB_Activity cber_activity) {
        super(context);
        this.cc=context;
        this.flipper=flipper;
        this.cber_acy=cber_activity;
        this.setOrientation(LinearLayout.VERTICAL);

        fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                cber_acy.on_close();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.store_title).toString(),0xffd0d0d0,18,null));
      //  this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));

        ImageView iv_icon= CLController.Get_ImageView(cc,
                CL.Get_FLLP(CL.DIP2PX_INT(60),CL.DIP2PX_INT(36),Gravity.RIGHT|Gravity.CENTER_VERTICAL,0, CL.DIP2PX_INT(2),0,0),
                null,listener_music_player);
        iv_icon.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.icon_music_click,R.mipmap.icon_music_normal));
        iv_icon.setScaleType(ImageView.ScaleType.FIT_CENTER);
        fl_header.addView(iv_icon);


        gv_list=new CustomGridView(cc);
        gv_list.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC, 1.0f));
        gv_list.setCacheColorHint(Color.TRANSPARENT);
        gv_list.setVerticalSpacing(0);
        gv_list.setHorizontalSpacing(0);
        gv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        adapter=new AdapterForRes();
        gv_list.setAdapter(adapter);
        this.addView(gv_list);




        LinearLayout _ll=new LinearLayout(cc);
        _ll.setLayoutParams(CL.Get_LLLP(CL.MP, CL.DIP2PX_INT(36)));
        _ll.setOrientation(LinearLayout.HORIZONTAL);
        _ll.setBackgroundColor(0xff444444);
        _ll.setPadding(CL.DIP2PX_INT(4), CL.DIP2PX_INT(4), CL.DIP2PX_INT(4), CL.DIP2PX_INT(4));
        _ll.setGravity(Gravity.CENTER);
        this.addView(_ll);

        addAdView(this);

        disker=new DiskCapacity(cc);
        disker.setLayoutParams(CL.Get_LLLP(CL.WC, CL.MP, 1.0f));
        _ll.addView(disker);

        update_sd_category();
        dialog_music=new MusicViewDialog(cc);

        FileManager.Set_Listener(listener_filemanager);
        FileManager fm=Server.Share_FileManager();
        if(fm!=null){
            datas=fm.get_datas();
        }

    }

    private void addAdView(ViewGroup parent) {
        int count = Setting.Share_Setting().get_app_run_count();
        if (Setting.Share_Setting().get_pos_storage_state() != 0 && count >= Setting.Share_Setting().get_pos_storage_state()) {
            int pos = Setting.Share_Setting().get_nine_ads_pos();
            int banner_type = Setting.Share_Setting().get_banner2_type();
            Global.Get_banner(cc, parent, pos, banner_type, null);
        }
        /*
        AdSize adsize;
        int banner_type = Setting.Share_Setting().get_banner_type();
        if (banner_type == 0)
            adsize=AdSize.BANNER;
        else if (banner_type == 1)
            adsize=AdSize.FULL_BANNER;
        else if (banner_type == 2)
            adsize=AdSize.LARGE_BANNER;
        else if (banner_type == 3)
            adsize=AdSize.LEADERBOARD;
        else if (banner_type == 4)
            adsize=AdSize.MEDIUM_RECTANGLE;
        else if (banner_type == 5)
            adsize=AdSize.WIDE_SKYSCRAPER;
        else if (banner_type == 6)
            adsize=AdSize.SMART_BANNER;
        else
            adsize=AdSize.BANNER;
        //View _v1=Global.Get_Banner(cc, adsize, null);

        m_AdView=Global.Get_Banner(cc, adsize, new CLCallback.CB() {
            @Override
            public void on_callback() {
//                if (m_AdView != null && m_AdView.getVisibility() != View.GONE)
//                    m_AdView.setVisibility(View.GONE);
            }
        });
        if (m_AdView != null)
            parent.addView(m_AdView);
            */
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
     //   Global.Show_Interstitial(cc,null);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    //统计SD卡容量
    private void update_sd_category(){
        long[] _v= CLFileSystem.Get_Used_Totalspace(cc);
        disker.set_data(_v[0],_v[1]);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed,l,t,r,b);
        if(changed){
            if(this.getWidth()>this.getHeight()){
                gv_list.setNumColumns(4);
            }else gv_list.setNumColumns(3);
        }
    }

    private class AdapterForRes extends BaseAdapter{


        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new CTMItem(cc);
            CTMItem _v=(CTMItem)cv;
            _v.set_data(datas.get(position));
            return cv;
        }
    }

    private Paint p_normal,p_click;
    private ColorDrawable dwe_normal=new ColorDrawable(){
        @Override
        public void draw(Canvas canvas) {
            if(p_normal==null){
                p_normal=new Paint(Paint.ANTI_ALIAS_FLAG);
                p_normal.setStyle(Paint.Style.STROKE);
                p_normal.setStrokeWidth(1.0f);
                p_normal.setColor(0xff626262);
            }
           // Rect _rect=canvas.getClipBounds();
//xxx                canvas.drawLine(0,_rect.height()-1,_rect.width(),_rect.height()-1,p_normal);
           // canvas.drawRect(_rect.left,_rect.top,_rect.right,_rect.bottom,p_normal);

        }
    };
    private ColorDrawable dwe_click=new ColorDrawable(){
        @Override
        public void draw(Canvas canvas) {
            if(p_normal==null){
                p_normal=new Paint(Paint.ANTI_ALIAS_FLAG);
                p_normal.setStyle(Paint.Style.STROKE);
                p_normal.setStrokeWidth(2.0f);
                p_normal.setColor(0xff626262);
            }
            canvas.drawColor(0xff777777);
            Rect _rect=canvas.getClipBounds();
            canvas.drawRect(_rect.left,_rect.top,_rect.right,_rect.bottom,p_normal);
        }
    };
    private View.OnClickListener listener_click_item=new OnClickListener() {
        @Override
        public void onClick(final View v) {
            if(v instanceof CTMItem){
                Struct.StoreDir _d=((CTMItem) v).data;
                if(_d.type==1){//video
                    if(layer_video==null)layer_video=new ResVideo(cc,flipper,cber_update);
                    layer_video.update_data(_d);
                    flipper.go_next(layer_video);
                }else if(_d.type==2){//music
                    if(layer_music==null)layer_music=new ResMusic(cc,flipper,cber_update);
                    layer_music.update_data(_d);
                    flipper.go_next(layer_music);
                }else if(_d.type==3){//picture
                    if(layer_picture==null)layer_picture=new ResPicture(cc,flipper,cber_update);
                    layer_picture.update_data(_d);
                    flipper.go_next(layer_picture);
                }else if(_d.type==4){//gif
                    if(layer_gif==null)layer_gif=new ResGif(cc,flipper,cber_update);
                    layer_gif.update_data(_d);
                    flipper.go_next(layer_gif);
                }else if(_d.type==5){//doc
                    if(layer_doc==null)layer_doc=new ResOther(cc,flipper,
                            cc.getResources().getString(R.string.store_doc),cber_update);
                    layer_doc.update_data(_d);
                    flipper.go_next(layer_doc);
                }else if(_d.type==6){//apk
                    if(layer_apk==null)layer_apk=new ResAPK(cc,flipper,cber_update);
                    layer_apk.update_data(_d);
                    flipper.go_next(layer_apk);
                }else if(_d.type==7){//other
                    if(layer_other==null)layer_other=new ResOther(cc,flipper,
                            cc.getResources().getString(R.string.store_other),cber_update);
                    layer_other.update_data(_d);
                    flipper.go_next(layer_other);
                }
            }



            /*Global.Show_Interstitial(cc, new CLCallback.CB() {
                @Override
                public void on_callback() {
                    if(v instanceof CTMItem){
                        Struct.StoreDir _d=((CTMItem) v).data;
                        if(_d.type==1){//video
                            if(layer_video==null)layer_video=new ResVideo(cc,flipper,cber_update);
                            layer_video.update_data(_d);
                            flipper.go_next(layer_video);
                        }else if(_d.type==2){//music
                            if(layer_music==null)layer_music=new ResMusic(cc,flipper,cber_update);
                            layer_music.update_data(_d);
                            flipper.go_next(layer_music);
                        }else if(_d.type==3){//picture
                            if(layer_picture==null)layer_picture=new ResPicture(cc,flipper,cber_update);
                            layer_picture.update_data(_d);
                            flipper.go_next(layer_picture);
                        }else if(_d.type==4){//gif
                            if(layer_gif==null)layer_gif=new ResGif(cc,flipper,cber_update);
                            layer_gif.update_data(_d);
                            flipper.go_next(layer_gif);
                        }else if(_d.type==5){//doc
                            if(layer_doc==null)layer_doc=new ResOther(cc,flipper,
                                    cc.getResources().getString(R.string.store_doc),cber_update);
                            layer_doc.update_data(_d);
                            flipper.go_next(layer_doc);
                        }else if(_d.type==6){//apk
                            if(layer_apk==null)layer_apk=new ResAPK(cc,flipper,cber_update);
                            layer_apk.update_data(_d);
                            flipper.go_next(layer_apk);
                        }else if(_d.type==7){//other
                            if(layer_other==null)layer_other=new ResOther(cc,flipper,
                                    cc.getResources().getString(R.string.store_other),cber_update);
                            layer_other.update_data(_d);
                            flipper.go_next(layer_other);
                        }
                    }
                }
            });*/
        }
    };
    private class CTMItem extends FrameLayout{

        private Struct.StoreDir data;
        private ImageView iv_icon;
        private TextView tv_title;
        private TextView tv_length;

        public CTMItem(Context context) {
            super(context);
            this.setBackground(CL.Get_StateList_Drawable(dwe_normal,dwe_click));
            this.setClickable(true);
            this.setOnClickListener(listener_click_item);

            LinearLayout _ll=new LinearLayout(context);
            _ll.setOrientation(LinearLayout.VERTICAL);
            _ll.setGravity(Gravity.CENTER_HORIZONTAL);
            _ll.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL,0,CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8)));

            this.addView(_ll);

            iv_icon=CLController.Get_ImageView(cc,CL.Get_LP_WW(),null,null);
            _ll.addView(iv_icon);
            tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(5),0,CL.DIP2PX_INT(2)),"",0xfff1f1f1,14,null);
            tv_title.setSingleLine();
            _ll.addView(tv_title);
            tv_length=CLController.Get_TextView(cc,CL.Get_LP_WW(),"",0xfff1f1f1,12,null);
            _ll.addView(tv_length);
        }
        public void set_data(Struct.StoreDir d){
            this.data=d;
            if(this.data.type==1)iv_icon.setBackgroundResource(R.mipmap.res_icon_video);
            else if(this.data.type==2)iv_icon.setBackgroundResource(R.mipmap.res_icon_music);
            else if(this.data.type==3)iv_icon.setBackgroundResource(R.mipmap.res_icon_picture);
            else if(this.data.type==4)iv_icon.setBackgroundResource(R.mipmap.res_icon_gif);
            else if(this.data.type==5)iv_icon.setBackgroundResource(R.mipmap.res_icon_doc);
            else if(this.data.type==6)iv_icon.setBackgroundResource(R.mipmap.res_icon_apk);
            else if(this.data.type==7)iv_icon.setBackgroundResource(R.mipmap.res_icon_other);
            else if(this.data.type==100)iv_icon.setBackgroundResource(R.mipmap.res_icon_video);
            tv_title.setText(this.data.name);
            tv_length.setText(this.data.files_size+" ("+ CLTools.Get_Capacity_Format(this.data.length)+")");
        }
    }


}
