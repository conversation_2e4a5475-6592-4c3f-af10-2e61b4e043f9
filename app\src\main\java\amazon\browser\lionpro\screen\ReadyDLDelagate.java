package amazon.browser.lionpro.screen;


import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.ImageView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ItemViewDelegate;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.base.ViewHolder;

import java.util.List;

import lion.CLTools;

public class ReadyDLDelagate implements ItemViewDelegate<Data.StructDLItem> {
    private Handler handler = new Handler(Looper.getMainLooper());
   // CacheList.OnHolderItemClickListener listener;
   AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> mAdapter;
    public ReadyDLDelagate(AdapterInterface.AdapterParamGet<List<Data.StructDLItem>> adapter) {
        mAdapter = adapter;
    }


    @Override
    public int getItemViewLayoutId()
    {
        return R.layout.readdllayout;
    }

    @Override
    public boolean isForViewType(Data.StructDLItem item, int position) {
        if (item.show_type == 1)
            return true;
        else
            return false;
    }

    @Override
    public void convert(ViewHolder holder, Data.StructDLItem item, int position) {
        Context cc = holder.getConvertView().getContext();
        if (item.type_major == Data.Type_APK) {
            holder.setImageResource(R.id.img, R.mipmap.res_icon_apk_s);
            holder.setText(R.id.tv_format, "APK");
            holder.setText(R.id.tv_name, item.title);
            holder.setVisible(R.id.play, false);

            if (item.length > 0) {
                String tmp = cc.getString(R.string.file_size) + ":" + CLTools.Get_Capacity_Format(item.length);
                holder.setText(R.id.video_size, tmp);
            }
        } else if (item.type_major == Data.Type_Music) {
            holder.setImageResource(R.id.img, R.mipmap.res_icon_music_s);
            holder.setVisible(R.id.play, true);
            int type_minor = item.type_minor;
            if (type_minor == Data.Type_Music_MP3) holder.setText(R.id.tv_format, "MP3");
            else if(type_minor == Data.Type_Music_OGG) holder.setText(R.id.tv_format, "OGG");
            else if(type_minor == Data.Type_Music_FLAC) holder.setText(R.id.tv_format, "FLAC");
            else if(type_minor == Data.Type_Music_WAV) holder.setText(R.id.tv_format, "WAV");
            else if(type_minor == Data.Type_Music_M4A) holder.setText(R.id.tv_format, "M4A");
            else holder.setText(R.id.tv_format, "???");

            if (item.length > 0) {
                String tmp = cc.getString(R.string.file_size) + ":" + CLTools.Get_Capacity_Format(item.length);
                holder.setText(R.id.video_size, tmp);
            }
        } else if (item.type_major == Data.Type_Video) {
            holder.setImageResource(R.id.img, R.mipmap.res_icon_video_s);
            holder.setVisible(R.id.play, true);
            holder.setText(R.id.tv_name, item.title);
            int type_minor = item.type_minor;
            if (type_minor == Data.Type_Video_MP4) holder.setText(R.id.tv_format, "MP4");
            else if(type_minor == Data.Type_Video_3GP) holder.setText(R.id.tv_format, "3GP");
            else if(type_minor == Data.Type_Video_MKV) holder.setText(R.id.tv_format, "MKV");
            else if(type_minor == Data.Type_Video_WEBM)holder.setText(R.id.tv_format, "WEBM");
            else holder.setText(R.id.tv_format, "???");

            if (item.length > 0) {
                String tmp = cc.getString(R.string.file_size) + ":" + CLTools.Get_Capacity_Format(item.length);
                holder.setText(R.id.video_size, tmp);
            }
        } else if(item.type_major==Data.Type_M3U8) {
            holder.setImageResource(R.id.img, R.mipmap.res_icon_video_s);
            holder.setVisible(R.id.play, true);
            holder.setText(R.id.tv_name, item.title);
            String tmp = "";
            if (item.quality != null) {
                if (item.quality.toUpperCase().contains("X")) {
                    tmp = cc.getString(R.string.str_starting_resolution) + ":" + item.quality;
                } else {
                    tmp = cc.getString(R.string.str_starting_bit_rate) + ":" + item.quality;
                }
            }
            holder.setText(R.id.video_size, tmp);
            holder.setText(R.id.tv_format, "M3U8");
        } else if(item.type_major==Data.Type_Doc) {
            holder.setImageResource(R.id.img, R.mipmap.res_icon_doc_s);
            holder.setText(R.id.tv_name, item.title);
            holder.setVisible(R.id.play, false);
            holder.setText(R.id.tv_format, item.suffix);

            if (item.length > 0) {
                String tmp = cc.getString(R.string.file_size) + ":" + CLTools.Get_Capacity_Format(item.length);
                holder.setText(R.id.video_size, tmp);
            }
        } else {
            holder.setImageResource(R.id.img, R.mipmap.res_icon_other_s);
            holder.setText(R.id.tv_name, item.title);
            holder.setVisible(R.id.play, false);
            holder.setText(R.id.tv_format, item.suffix);

            if (item.length > 0) {
                String tmp = cc.getString(R.string.file_size) + ":" + CLTools.Get_Capacity_Format(item.length);
                holder.setText(R.id.video_size, tmp);
            }
        }
        bindEvent(holder, item, position);
    }

    private void bindEvent(ViewHolder holder, Data.StructDLItem item, int position) {
        ImageView img_play = holder.getView(R.id.play);
        img_play.setTag(item);
        img_play.setOnClickListener((v)->{
            if (mAdapter != null) {
                Data.StructDLItem tmp_item = (Data.StructDLItem)img_play.getTag();
                mAdapter.GetHolderItemClickListerer().onItemClick(v, tmp_item, 1);
            }
        });
        ImageView img_delete = holder.getView(R.id.delete);
        img_delete.setTag(item);
        img_delete.setOnClickListener((v)->{
            if (mAdapter != null) {
                Data.StructDLItem tmp_item = (Data.StructDLItem)img_delete.getTag();
                mAdapter.GetHolderItemClickListerer().onItemClick(v, tmp_item, 2);
            }
        });

        ImageView img_download = holder.getView(R.id.download);
        img_download.setTag(item);
        img_download.setOnClickListener((v)->{
            if (mAdapter != null) {
                Data.StructDLItem tmp_item = (Data.StructDLItem)img_download.getTag();
                mAdapter.GetHolderItemClickListerer().onItemClick(v, tmp_item, 3);
            }
        });
    }

//    private void updateDownloadStatus(ViewHolder holder, Data.StructDLItem item, int position) {
//        if (item.dler != null) {
//            if (item.dler.get_status() == CommonDownloader.Eventer.State_Start) {
//                ImageView img = (ImageView) holder.getView(R.id.btn_download);
//                img.setImageDrawable(CL.Get_StateList_Drawable(mAdapter.GetContext(), R.mipmap.dl_pause_normal,R.mipmap.dl_pause_press));
//            } else if (item.dler.get_status() == CommonDownloader.Eventer.State_Complete) {
//                ProgressBar progressBar = holder.getView(R.id.v_progress);
//                progressBar.setVisibility(View.GONE);
//            } else if (item.dler.get_status() == CommonDownloader.Eventer.State_Stop) {
//                ImageView img = (ImageView) holder.getView(R.id.btn_download);
//                //img.setImageResource(R.mipmap.dl_download_normal);
//                img.setImageDrawable(CL.Get_StateList_Drawable(mAdapter.GetContext(), R.mipmap.dl_download_normal,R.mipmap.dl_download_press));
//            }
//        }
//    }
}
