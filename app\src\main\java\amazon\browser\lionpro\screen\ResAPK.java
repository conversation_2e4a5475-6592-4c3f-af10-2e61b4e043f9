package amazon.browser.lionpro.screen;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;

import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.views.CustomGridView;
import amazon.browser.lionpro.views.Deleter;
import amazon.browser.lionpro.views.DialogExport;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLToast;
import lion.CLTools;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/7/31.
 */
public class ResAPK extends LinearLayout implements CLFlipper.EventListener{


    private CLActivity cc;
    private CLFlipper flipper;
    private CLCallback.CB cber_update;
    private FrameLayout fl_content;
    private Deleter btn_del;
    private ScaleAnimation anim_scale;
    private ImageView empty;
    private CustomGridView gv_list;
    private CLController.Waiter waiter;
    private ApkFileLoader threader_init;
    private AdapterForAPK adapter;
    private ArrayList<StructAPK> datas=new ArrayList<>();
    private Struct.StoreDir data;

    private CLDialog dialog_force;

    private boolean discard=false;
    private CLApkThumLoader loader;
    private int grid_column=3;
    private int grid_width_height=0;
    private boolean editor=false;
    private int del_number=0;


    private BroadcastReceiver receiver_apk=new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if(datas==null)return;
            if (intent.getAction().equals(Intent.ACTION_PACKAGE_ADDED)) {
                String _pn = intent.getDataString().substring(8);
                for(int i=0;i<datas.size();++i){
                    StructAPK _apk=datas.get(i);
                    if(_apk.package_name==null || _apk.package_name.isEmpty())continue;
                    if(_apk.package_name.equals(_pn)){
                        try {
                            PackageInfo _info = cc.getPackageManager().getPackageInfo(_apk.package_name, 0);
                            if (_info != null && _info.versionCode >= _apk.code_version) {
                                _apk.installed = true;
                            }
                        }catch (Exception ex){}
                    }
                }
                adapter.notifyDataSetChanged();
            }
            else if (intent.getAction().equals(Intent.ACTION_PACKAGE_REMOVED)) {
                String _pn = intent.getDataString().substring(8);
                for(int i=0;i<datas.size();++i){
                    StructAPK _apk=datas.get(i);
                    if(_apk.package_name==null || _apk.package_name.isEmpty())continue;
                    if(_apk.package_name.equals(_pn)){
                        _apk.installed=false;
                    }
                }
                adapter.notifyDataSetChanged();
            }
        }
    };




    public ResAPK(CLActivity context,CLFlipper f,CLCallback.CB cber_update) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_update=cber_update;
        init();
    }
    public void update_data(Struct.StoreDir d){
        this.data=d;
        if(this.data==null && this.data.dls==null)return;
    }


    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }

    private ImageView btn_export;
    private View.OnClickListener listener_export=new OnClickListener() {
        @Override
        public void onClick(View v) {
            //权限处理
            if(ContextCompat.checkSelfPermission(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                if(ActivityCompat.shouldShowRequestPermissionRationale(cc,Manifest.permission.WRITE_EXTERNAL_STORAGE)){
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }else{
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }
            }else {
                show_dialog_export();
            }
        }
    };
    private void show_dialog_export(){

        ArrayList<DialogExport.ExportData> _ds=new ArrayList<>();
        for(int i=0;i<datas.size();++i){
            StructAPK _tmp=datas.get(i);
            if(!_tmp.check)continue;
            File _o_f=new File(_tmp.path);
            if(_o_f.exists()){
                DialogExport.ExportData _d=new DialogExport.ExportData();
                _d.o_path=_tmp.path;
                _d.name=_tmp.name+".apk";
                _ds.add(_d);
            }
        }
        if(_ds.size()==0){
            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists),true);
            return;
        }
        final DialogExport _dialog_export=new DialogExport(cc, _ds, new CLCallback.CB() {
            @Override
            public void on_callback() {
                editor=false;
                del_number=0;
                btn_del.set_number(0);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
                adapter.notifyDataSetChanged();
                for(int i=0;i<datas.size();++i){
                    datas.get(i).check=false;
                }
            }
        });
        _dialog_export.show();
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed,l,t,r,b);
        if(changed){
            if(this.getWidth()>this.getHeight()){
                grid_column=7;
                gv_list.setNumColumns(grid_column);
            }else {
                grid_column=4;
                gv_list.setNumColumns(grid_column);
            }
            grid_width_height=(this.getWidth()-CL.DIP2PX_INT(4)*(grid_column+1))/grid_column;
            grid_width_height=grid_width_height*90/100;
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        IntentFilter _filter=new IntentFilter();
        _filter.addAction(Intent.ACTION_PACKAGE_ADDED);
        _filter.addAction(Intent.ACTION_PACKAGE_REMOVED);
        _filter.addDataScheme("package");
        cc.registerReceiver(receiver_apk,_filter);
        threader_init=new ApkFileLoader();
        threader_init.start();
        loader=new CLApkThumLoader(CL.DIP2PX_INT(118));
        discard=false;

        if(datas==null || datas.size()==0)return;
        /*
        if(!Setting.Share_Setting().get_tip(Setting.Type_item)){
            flipper.handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                            cc.getResources().getString(R.string.tip_can_check), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_item,true);
                        }
                    }).show();
                }
            },500);
        }
        */
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cc.unregisterReceiver(receiver_apk);
        threader_init.run=false;
        loader.clear_task();
        loader.go_exit();
        discard=true;
        clear_resource();
        for(int i = 0; i< gv_list.getChildCount(); ++i){
            View _v= gv_list.getChildAt(i);
            if(_v instanceof VideoAPK) {
                VideoAPK _vv = (VideoAPK) _v;
                _vv.iv_thumb.setImageBitmap(null);
            }
        }
        editor=false;
        del_number=0;
        btn_del.set_number(0);
        btn_del.deformation_direct(false);
        btn_del.setVisibility(View.GONE);
        if (btn_export != null)
            btn_export.setVisibility(View.GONE);
        adapter.notifyDataSetChanged();
    }

    private void clear_resource(){
        if(datas!=null){
            for(int i=0;i<datas.size();++i){
                StructAPK _tmp=datas.get(i);
                recycle_item(_tmp);
            }
            datas.clear();
        }
    }
    private void recycle_item(StructAPK item){
        if(item!=null && item.bitmap_thumb!=null && !item.bitmap_thumb.isRecycled()){
            memory-=item.bitmap_thumb.getByteCount();
            item.bitmap_thumb.recycle();
            item.bitmap_thumb=null;
        }
    }


    private void init(){
        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.store_apk).toString(),0xffd0d0d0,18,null));
    //    this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));

        fl_content=new FrameLayout(cc);
        fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        this.addView(fl_content);

        empty=new ImageView(cc);
        empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        empty.setBackgroundResource(R.mipmap.no_data);
        empty.setVisibility(View.GONE);
        fl_content.addView(empty);

        gv_list=new CustomGridView(cc);
        gv_list.setMotionEventSplittingEnabled(false);
        gv_list.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP,Gravity.FILL));
        gv_list.setCacheColorHint(Color.TRANSPARENT);
        gv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        int _space=CL.DIP2PX_INT(4);
        gv_list.setHorizontalSpacing(_space);
        gv_list.setVerticalSpacing(_space);
        gv_list.setPadding(_space,_space,_space,_space);
        adapter=new AdapterForAPK();
        gv_list.setAdapter(adapter);
        fl_content.addView(gv_list);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);

        btn_del=new Deleter(cc,listener_del);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        fl_content.addView(btn_del);

        btn_del.measure(0,0);
        int _h=btn_del.getMeasuredHeight();

        if (Global.IsAndroid10()) {
            btn_export = new ImageView(cc);
            btn_export.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.icon_export_normal, R.mipmap.icon_export_click));
            btn_export.setLayoutParams(CL.Get_FLLP(CL.WC, CL.WC, Gravity.BOTTOM | Gravity.RIGHT, 0, 0, CL.DIP2PX_INT(22), CL.DIP2PX_INT(36) + _h));
            btn_export.setClickable(true);
            btn_export.setOnClickListener(listener_export);
            btn_export.setVisibility(View.GONE);
            fl_content.addView(btn_export);
        }

        waiter=new CLController.Waiter(cc,Color.WHITE);
        waiter.setLayoutParams(CL.Get_FLLP(CL.DIP2PX_INT(38),CL.DIP2PX_INT(38),Gravity.CENTER));
        fl_content.addView(waiter);
/*
        int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
        View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);
        this.addView(divid);
        View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
        if(_v!=null)this.addView(_v);
*/
        dialog_force=CLDialog.Get_Force_Wait(cc);
    }

    private final int Max_Memory=(int)(8*1024*1024* CL.Density);
    private int memory=0;
    private LoaderListener listener_loader=new LoaderListener() {
        @Override
        public void on_load_complete(final String opath, String tpath,final Bitmap bm,final Object tag) {
            if(discard)return;
            flipper.handler.post(new Runnable() {
                @Override
                public void run() {
                    boolean _catch=false;
                    for(int i = 0; i< gv_list.getChildCount(); ++i){
                        View _v= gv_list.getChildAt(i);
                        if(_v instanceof VideoAPK) {
                            VideoAPK _vv = (VideoAPK) _v;
                            if (_vv.data == tag && _vv.data.path.equals(opath) && _vv.data.bitmap_thumb==null) {
                                _vv.data.bitmap_thumb=bm;
                                _vv.iv_thumb.setImageBitmap(_vv.data.bitmap_thumb);
                                memory+=bm.getByteCount();
                                CL.CLOGI("P_memory:"+memory);
                                _catch=true;
                                break;
                            }
                        }
                    }
                    if(memory>Max_Memory){
                        int _sp= gv_list.getFirstVisiblePosition();
                        int _lp= gv_list.getLastVisiblePosition();
                        for(int i=datas.size()-1;i>0;--i){
                            if(i<_sp || i>_lp){
                                StructAPK _tmp=datas.get(i);
                                recycle_item(_tmp);
                                if(memory<Max_Memory)break;
                            }
                        }
                    }
                    if(!_catch)bm.recycle();
                }
            });
        }

        @Override
        public void on_load_fail(String opath, Object tag) {

        }
    };

    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand){
                btn_del.deformation(false);
                if (btn_export != null)
                    btn_export.setVisibility(View.VISIBLE);
            }
            else {
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
            }
        }
        @Override
        public void on_cancel_click() {
            editor=false;
            del_number=0;
            btn_del.setVisibility(View.GONE);
            btn_del.deformation(false);
            for(int i=0;i<datas.size();++i){
                datas.get(i).check = false;
            }
            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            final CLDialog _waiter=CLDialog.Get_Force_Wait(cc);
            _waiter.show();
            new Thread(){
                @Override
                public void run() {
                    try {
                        for(int i=0;i<datas.size();++i){
                            StructAPK _item=datas.get(i);
                            if(!_item.check)continue;
                            File _f=new File(_item.path);
                            long _flength=_f.length();
                            if(_f.exists() && _f.delete()){
                                _f=new File(Global.Dir_thum,_item.name);
                                if(_f.exists() && _f.delete()){
                                    CL.CLOGI("delete thum success");
                                }
                            }
                            --data.files_size;
                            data.length-=_flength;
                            recycle_item(_item);
                            datas.remove(_item);
                            data.dls.remove(_item.item);
                            Server.Delete_Download(_item.item);
                            --i;
                        }
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Force_Update_DL();
                                if(cber_update!=null)cber_update.on_callback();
                            }
                        });

                    }catch (Exception ex){
                        CL.CLOGI("delete error:"+ex.toString());
                    }finally {
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                if(cber_update!=null)cber_update.on_callback();
                                _waiter.dismiss();
                                editor=false;
                                del_number=0;
                                btn_del.setVisibility(View.GONE);
                                btn_del.deformation(false);
                                adapter.notifyDataSetChanged();
                            }
                        });
                    }
                }
            }.start();
        }
    };

    private class StructAPK {
        public Data.StructDLItem item;

        public String package_name;
        public int code_version;

        public String name;
        public String path;
        public boolean installed;

        public Bitmap bitmap_thumb;
        public boolean check=false;

        public StructAPK(String name, String path, Data.StructDLItem item){
            this.name=name;
            this.path=path;
            this.item=item;
        }
    }
    private class AdapterForAPK extends BaseAdapter {

        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new VideoAPK(cc);
            VideoAPK _v=(VideoAPK)cv;
            _v.set_basic_data(datas.get(position));
            return cv;
        }
    }

    private View.OnClickListener listener_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(editor){
                if(v instanceof VideoAPK){
                    VideoAPK _vv=(VideoAPK)v;
                    _vv.data.check=!_vv.data.check;
                    if(_vv.data.check) {
                        _vv.iv_check.setImageDrawable(cc.getResources().getDrawable(R.mipmap.comm_select_2));
                        ++del_number;
                    }
                    else {
                        _vv.iv_check.setImageDrawable(cc.getResources().getDrawable(R.mipmap.comm_select_1));
                        --del_number;
                        if(del_number==0){
                            editor=false;
                            btn_del.deformation(false);
                            btn_del.setVisibility(View.GONE);
                            if (btn_export != null)
                                btn_export.setVisibility(View.GONE);
                            for(int i=0;i<data.dls.size();++i){
                                data.dls.get(i).selected=false;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                    btn_del.set_number(del_number);
                }
            }else{
                //open
                if(v instanceof VideoAPK){
                    VideoAPK _vv=(VideoAPK)v;
                    if(_vv.data.installed)return;
                    if(CL.Do_Once()){
//                        Intent _intent=new Intent(Intent.ACTION_VIEW);
//                        _intent.setDataAndType(Uri.parse("file://"+_vv.data.path),"application/vnd.android.package-archive");
//                        _intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                        cc.startActivity(_intent);
                        CLTools.install(cc, _vv.data.path);
                        dialog_force.show();
                        flipper.handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                dialog_force.dismiss();
                            }
                        },2000);
                    }
                }
            }
        }
    };
    private View.OnLongClickListener listener_click_long=new OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            editor=!editor;
            if(editor) {
                btn_del.setVisibility(View.VISIBLE);
                btn_del.startAnimation(anim_scale);
                if (btn_export != null) {
                    btn_export.setVisibility(View.VISIBLE);
                    btn_export.startAnimation(anim_scale);
                }
                btn_del.set_number(del_number);
                listener_click.onClick(v);
            }else{
                del_number=0;
                btn_del.deformation(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
                for(int i=0;i<datas.size();++i){
                    datas.get(i).check = false;
                }
            }
            adapter.notifyDataSetChanged();
            return true;
        }
    };


    private class VideoAPK extends FrameLayout {

        private StructAPK data;
        private ImageView iv_check;
        private ImageView iv_thumb;
        private TextView tv_name, tv_install;

        public VideoAPK(Context context) {
            super(context);
            this.setClickable(true);
            this.setLongClickable(true);
            this.setLayoutParams(new AbsListView.LayoutParams(CL.MP,CL.WC));
            this.setBackgroundColor(0xff262626);
            this.setOnClickListener(listener_click);
            this.setOnLongClickListener(listener_click_long);

            LinearLayout _ll_content=CLController.Get_LinearLayout(cc,CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL),
                    LinearLayout.VERTICAL,null);
            _ll_content.setOrientation(LinearLayout.VERTICAL);
            _ll_content.setGravity(Gravity.CENTER_HORIZONTAL);
            this.addView(_ll_content);

            iv_thumb=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC),null,null);
            iv_thumb.setScaleType(ImageView.ScaleType.CENTER_CROP);
            _ll_content.addView(iv_thumb);


            tv_name=CLController.Get_TextView(cc, CL.Get_LLLP(CL.MP,CL.WC,0,0,0,0),"",Color.WHITE,12,null);
            tv_name.setSingleLine();
            tv_name.setEllipsize(TextUtils.TruncateAt.END);
//            tv_name.setBackgroundColor(0x88000000);
            tv_name.setGravity(Gravity.CENTER);
            tv_name.setPadding(CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3));
            _ll_content.addView(tv_name);

            tv_install =CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,0,CL.DIP2PX_INT(4)),"", Color.GREEN,12,null);
            _ll_content.addView(tv_install);

            iv_check=new ImageView(context);
            iv_check.setLayoutParams(CL.Get_FLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(2),CL.DIP2PX_INT(2)));
            iv_check.setBackgroundColor(0xff222222);
            this.addView(iv_check);
        }
        public void set_basic_data(StructAPK d){
            this.data=d;

            LinearLayout.LayoutParams _lp=(LinearLayout.LayoutParams)iv_thumb.getLayoutParams();
            _lp.width=grid_width_height;
            _lp.height=grid_width_height;
            iv_thumb.setLayoutParams(_lp);


            if(editor){
                iv_check.setVisibility(View.VISIBLE);
                if(this.data.check)iv_check.setImageDrawable(cc.getResources().getDrawable(R.mipmap.comm_select_2));
                else iv_check.setImageDrawable(cc.getResources().getDrawable(R.mipmap.comm_select_1));
            }
            else iv_check.setVisibility(View.GONE);

            tv_name.setText(this.data.name);
            if(this.data.installed) tv_install.setText(cc.getResources().getString(R.string.installed));
            else tv_install.setText(cc.getResources().getString(R.string.install));

            iv_thumb.setImageBitmap(null);
            iv_thumb.setBackground(cc.getResources().getDrawable(R.mipmap.def_apk));
            if(this.data.bitmap_thumb!=null){
                iv_thumb.setImageBitmap(this.data.bitmap_thumb);
            }else{
                if(this.data.path!=null && this.data.name!=null && Global.Dir_thum!=null){
                    loader.go_add_item(this.data.path, Global.Dir_thum.getAbsolutePath(),this.data,listener_loader);
                }
            }
        }
    }


    private interface LoaderListener{
        void on_load_complete(String opath, String tpath, Bitmap bm, Object tag);
        void on_load_fail(String opath, Object tag);
    }
    private class CLApkThumLoader {
        private int grid_width=CL.DIP2PX_INT(220);
        public CLApkThumLoader(){
            worker=new ThumLoader();
            run=true;
            worker.start();
        }
        public CLApkThumLoader(int wh){
            grid_width=wh;
            if(grid_width<80)grid_width=80;
            worker=new ThumLoader();
            run=true;
            worker.start();
        }

        public void go_add_item(String apk_p, String store_dir,Object tag,LoaderListener cber){
            if(apk_p==null || store_dir==null || tag==null || cber==null)return;
            File _store_dir=new File(store_dir);
            if(!_store_dir.exists() || !_store_dir.isDirectory())return;
            synchronized (CLApkThumLoader.this){
                if(crt_data!=null){
                    if(crt_data.apk_path.equals(apk_p)
                            && crt_data.store_dir_path.equals(store_dir)
                            && crt_data.listener==cber
                            && crt_data.tag==tag){
                        return;
                    }
                }
                for(int i=0;i<datas.size();++i){
                    StructThum _tmp=datas.get(i);
                    if(_tmp.apk_path.equals(apk_p)
                            && _tmp.store_dir_path.equals(store_dir)
                            && _tmp.listener==cber
                            && _tmp.tag==tag){
                        return;
                    }
                }
                datas.add(new StructThum(apk_p,_store_dir,tag,cber));
                CLApkThumLoader.this.notifyAll();
            }
        }

        public void clear_task(){
            synchronized (CLApkThumLoader.this){
                datas.clear();
            }
        }

        public void go_exit(){
            run=false;
            synchronized (CLApkThumLoader.this){
                CLApkThumLoader.this.notifyAll();
                if(worker!=null)worker.interrupt();
            }
        }

        private class StructThum{
            private String apk_path;
            private String name;
            private String store_dir_path;
            private File store_dir;
            private Object tag;
            private Bitmap bitmap_thum;
            private LoaderListener listener;
            public StructThum(String apk_p,File s_d,Object tag,LoaderListener cber){
                this.apk_path=apk_p;
                store_dir=s_d;
                store_dir_path=store_dir.getAbsolutePath();
                this.tag=tag;
                this.listener=cber;
                name=apk_p.replaceAll(File.separator,"_");
            }

        }

        private ThumLoader worker;
        private boolean run;
        private ArrayList<StructThum> datas=new ArrayList<>();
        private StructThum crt_data=null;
        private class ThumLoader extends Thread{
            @Override
            public void run() {
                while (run){
                    try{
                        crt_data=null;
                        synchronized (CLApkThumLoader.this){
                            if(datas.size()>0)crt_data=datas.remove(0);
                            else {
                                CLApkThumLoader.this.wait();
                                continue;
                            }
                        }
                        File _t=new File(crt_data.store_dir,crt_data.name);
                        //存在
                        if(_t.exists()){
                            crt_data.bitmap_thum = BitmapFactory.decodeFile(_t.getAbsolutePath());
                            if (crt_data.bitmap_thum == null) {//图片不正常
                                _t.delete();
                                datas.add(crt_data);
                            }else{
                                crt_data.listener.on_load_complete(crt_data.apk_path,_t.getAbsolutePath(),crt_data.bitmap_thum,crt_data.tag);
                            }
                            continue;
                        }
                        //生成
                        //扫描apk
                        PackageManager _pm = cc.getPackageManager();
                        PackageInfo _file_info = _pm.getPackageArchiveInfo(crt_data.apk_path, 0);
                        if(_file_info!=null) {
                            ApplicationInfo _app_info=_file_info.applicationInfo;
                            _app_info.sourceDir=crt_data.apk_path;
                            _app_info.publicSourceDir=crt_data.apk_path;
                            if(crt_data.bitmap_thum==null) {
                                Drawable _dwe = _app_info.loadIcon(_pm);
                                int w = _dwe.getIntrinsicWidth();
                                int h = _dwe.getIntrinsicHeight();
                                Bitmap.Config config = _dwe.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
                                Bitmap bitmap = Bitmap.createBitmap(w, h, config);
                                Canvas canvas = new Canvas(bitmap);
                                _dwe.setBounds(0, 0, w, h);
                                _dwe.draw(canvas);
                                crt_data.bitmap_thum = bitmap;
                            }
                        }

                        //存储
                        if(crt_data.bitmap_thum!=null){//缩略图正常
                            crt_data.bitmap_thum.compress(Bitmap.CompressFormat.PNG, 100, new FileOutputStream(_t));
                            crt_data.listener.on_load_complete(crt_data.apk_path,_t.getAbsolutePath(),
                                    crt_data.bitmap_thum,crt_data.tag);
                        }else crt_data.listener.on_load_fail(crt_data.apk_path,crt_data.tag);
                    } catch (OutOfMemoryError e) {
                        return;
                    } catch (Exception ex){
                        if(ex instanceof InterruptedException)return;
                        CL.CLOGE("thum loader error:",ex);
                        if(crt_data!=null)crt_data.listener.on_load_fail(crt_data.apk_path,crt_data.tag);
                    }
                }
                CL.CLOGI("thum loader over");
            }
        }
    }

    private class ApkFileLoader extends Thread{

        private boolean run=true;

        @Override
        public void run() {

            final ArrayList<StructAPK> _datas=new ArrayList<>();
            for(int i=0;i<data.dls.size();++i){
                if(!run)return;
                Data.StructDLItem _tmp=data.dls.get(i);
                _datas.add(new StructAPK(_tmp.name!=null?_tmp.name:_tmp.title,_tmp.path, _tmp));
            }

            //扫描apk
            PackageManager _pm = cc.getPackageManager();
            for(int i=0;i<_datas.size();++i){
                if(!run)return;
                try {
                    StructAPK _apk = _datas.get(i);
                    PackageInfo _file_info = _pm.getPackageArchiveInfo(_apk.path, 0);
                    if (_file_info != null) {
                        ApplicationInfo _app_info = _file_info.applicationInfo;
                        _app_info.sourceDir=_apk.path;
                        _app_info.publicSourceDir=_apk.path;
                        _apk.package_name=_file_info.packageName;
                        _apk.code_version=_file_info.versionCode;
                        _apk.name = _app_info.loadLabel(_pm).toString();
                        //看看安装没有
                        PackageInfo _info = cc.getPackageManager().getPackageInfo(_file_info.packageName, 0);
                        if (_info != null && _info.versionCode >= _file_info.versionCode) {
                            _apk.installed = true;
                        }
                    }
                }catch (Exception ex){}
            }
            if(run){
                flipper.handler.post(new Runnable() {
                    @Override
                    public void run() {
                        gv_list.setEmptyView(empty);
                        datas=_datas;
                        adapter.notifyDataSetChanged();
                        fl_content.removeView(waiter);
                    }
                });
            }

        }
    }
}
