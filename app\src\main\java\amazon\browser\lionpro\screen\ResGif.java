package amazon.browser.lionpro.screen;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;

import amazon.browser.video.downloader.R;

import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.primary.LookPicture;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.views.CustomGridView;
import amazon.browser.lionpro.views.Deleter;
import amazon.browser.lionpro.views.DialogExport;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLThumLoader;
import lion.CLToast;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/7/26.
 */
public class ResGif extends LinearLayout implements CLFlipper.EventListener{

    private CLActivity cc;
    private CLFlipper flipper;
    private CLCallback.CB cber_update;
    private FrameLayout fl_content;
    private Deleter btn_del;
    private ScaleAnimation anim_scale;
    private CustomGridView gv_list;
    private AdapterForItem adapter;
    private Struct.StoreDir data;
    private ArrayList<StructPicture> datas=new ArrayList<>();
    private boolean discard=false;
    private CLThumLoader loader;
    private int grid_column=3;
    private int grid_width_height=0;
    private boolean editor=false;
    private int del_number=0;


    public ResGif(CLActivity context, CLFlipper f, CLCallback.CB cber_update) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_update=cber_update;
        init();
    }

    public void update_data(Struct.StoreDir d){
        this.data=d;
        if(this.data==null && this.data.files==null)return;
        for(int i=0;i<this.data.files.size();++i){
            File _tmp=this.data.files.get(i);
            datas.add(new StructPicture(_tmp.getName(),_tmp.getAbsolutePath()));
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }

    private ImageView btn_export;
    private View.OnClickListener listener_export=new OnClickListener() {
        @Override
        public void onClick(View v) {
            //权限处理
            if(ContextCompat.checkSelfPermission(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                if(ActivityCompat.shouldShowRequestPermissionRationale(cc,Manifest.permission.WRITE_EXTERNAL_STORAGE)){
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }else{
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }
            }else {
                CL.CLOGI("write external storage is granted");
                show_dialog_export();
            }
        }
    };
    private void show_dialog_export(){

        ArrayList<DialogExport.ExportData> _ds=new ArrayList<>();
        for(int i=0;i<datas.size();++i){
            StructPicture _tmp=datas.get(i);
            if(!_tmp.check)continue;
            File _o_f=new File(_tmp.path);
            if(_o_f.exists()){
                DialogExport.ExportData _d=new DialogExport.ExportData();
                _d.o_path=_tmp.path;
                String _name=_tmp.name;
                if(_name.endsWith(".1001")){//png
                    _name=_name.substring(0,_name.lastIndexOf("."))+".png";
                }else if(_name.endsWith(".1002")){//jpg
                    _name=_name.substring(0,_name.lastIndexOf("."))+".jpg";
                }else if(_name.endsWith(".1003")){//bmp
                    _name=_name.substring(0,_name.lastIndexOf("."))+".bmp";
                }else if(_name.endsWith(".1004")){//webp
                    _name=_name.substring(0,_name.lastIndexOf("."))+".webp";
                }else if(_name.endsWith(".1005")){//gif
                    _name=_name.substring(0,_name.lastIndexOf("."))+".gif";
                }
                _d.name=_name;
                _ds.add(_d);
            }
        }
        if(_ds.size()==0){
            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists),true);
            return;
        }
        final DialogExport _dialog_export=new DialogExport(cc, _ds, new CLCallback.CB() {
            @Override
            public void on_callback() {
                editor=false;
                del_number=0;
                btn_del.set_number(0);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
                adapter.notifyDataSetChanged();
                for(int i=0;i<datas.size();++i){
                    datas.get(i).check=false;
                }
            }
        });
        _dialog_export.show();
    }


    private void init(){
        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.store_gif).toString(),0xffd0d0d0,18,null));
      //  this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));

        fl_content=new FrameLayout(cc);
        fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        this.addView(fl_content);

        ImageView _empty=new ImageView(cc);
        _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        _empty.setBackgroundResource(R.mipmap.no_data);
        fl_content.addView(_empty);

        gv_list=new CustomGridView(cc);
        gv_list.setMotionEventSplittingEnabled(false);
        gv_list.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP,Gravity.FILL));
        gv_list.setCacheColorHint(Color.TRANSPARENT);
        gv_list.setEmptyView(_empty);
        gv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        int _space=CL.DIP2PX_INT(4);
        gv_list.setHorizontalSpacing(_space);
        gv_list.setVerticalSpacing(_space);
        gv_list.setPadding(_space,_space,_space,_space);
        adapter=new AdapterForItem();
        gv_list.setAdapter(adapter);
        fl_content.addView(gv_list);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);

        btn_del=new Deleter(cc,listener_del);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        fl_content.addView(btn_del);

        btn_del.measure(0,0);
        int _h=btn_del.getMeasuredHeight();

        if (Global.IsAndroid10()) {
            btn_export = new ImageView(cc);
            btn_export.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.icon_export_normal, R.mipmap.icon_export_click));
            btn_export.setLayoutParams(CL.Get_FLLP(CL.WC, CL.WC, Gravity.BOTTOM | Gravity.RIGHT, 0, 0, CL.DIP2PX_INT(22), CL.DIP2PX_INT(36) + _h));
            btn_export.setClickable(true);
            btn_export.setOnClickListener(listener_export);
            btn_export.setVisibility(View.GONE);
            fl_content.addView(btn_export);
        }

//        View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
//        if(_v!=null)this.addView(_v);


/*
        LinearLayout bottom =new LinearLayout(cc);
        bottom.setOrientation(LinearLayout.VERTICAL);
        bottom.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC));
        this.addView(bottom);

        int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
        View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);

        bottom.addView(divid);
        View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
        bottom.addView(_v);
        */
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed,l,t,r,b);
        if(changed){
            if(this.getWidth()>this.getHeight()){
                grid_column=4;
                gv_list.setNumColumns(grid_column);
            }else {
                grid_column=3;
                gv_list.setNumColumns(grid_column);
            }
            grid_width_height=(this.getWidth()-CL.DIP2PX_INT(4)*(grid_column+1))/grid_column;
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        loader=new CLThumLoader(CL.DIP2PX_INT(118),CL.DIP2PX_INT(118));
        discard=false;

        if(datas==null || datas.size()==0)return;
        /*
        if(!Setting.Share_Setting().get_tip(Setting.Type_item)){
            flipper.handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                            cc.getResources().getString(R.string.tip_can_check), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_item,true);
                        }
                    }).show();
                }
            },500);
        }

         */
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        loader.clear_task();
        loader.go_exit();
        discard=true;
        clear_resource();
        for(int i = 0; i< gv_list.getChildCount(); ++i){
            View _v= gv_list.getChildAt(i);
            if(_v instanceof ViewForPicture) {
                ViewForPicture _vv = (ViewForPicture) _v;
                _vv.iv_pic.setImageBitmap(null);
            }
        }
        editor=false;
        del_number=0;
        btn_del.set_number(0);
        btn_del.deformation_direct(false);
        btn_del.setVisibility(View.GONE);
        if (btn_export != null) {
            btn_export.setVisibility(View.GONE);
        }
        adapter.notifyDataSetChanged();
    }
    private void clear_resource(){
        if(datas!=null){
            for(int i=0;i<datas.size();++i){
                StructPicture _tmp=datas.get(i);
                recycle_item(_tmp);
            }
            datas.clear();
            CL.CLOGI("P_memory:"+memory);
        }
    }
    private void recycle_item(StructPicture item){
        if(item!=null && item.bitmap_thumb!=null && !item.bitmap_thumb.isRecycled()){
            memory-=item.bitmap_thumb.getByteCount();
            item.bitmap_thumb.recycle();
            item.bitmap_thumb=null;
        }
    }



    private final int Max_Memory=(int)(8*1024*1024* CL.Density);
    private int memory=0;
    private CLThumLoader.LoaderListener listener_loader=new CLThumLoader.LoaderListener() {
        @Override
        public void on_load_complete(final String opath, String tpath, final Bitmap bm,final Object tag) {
            if(discard)return;
            flipper.handler.post(new Runnable() {
                @Override
                public void run() {
                    boolean _catch=false;
                    for(int i = 0; i< gv_list.getChildCount(); ++i){
                        View _v= gv_list.getChildAt(i);
                        if(_v instanceof ViewForPicture) {
                            ViewForPicture _vv = (ViewForPicture) _v;
                            if (_vv.data == tag && _vv.data.path.equals(opath) && _vv.data.bitmap_thumb==null) {
                                _vv.data.bitmap_thumb=bm;
                                _vv.iv_pic.setImageBitmap(_vv.data.bitmap_thumb);
                                memory+=bm.getByteCount();
                                CL.CLOGI("P_memory:"+memory);
                                _catch=true;
                                break;
                            }
                        }
                    }
                    if(memory>Max_Memory){
                        int _sp= gv_list.getFirstVisiblePosition();
                        int _lp= gv_list.getLastVisiblePosition();
                        for(int i=datas.size()-1;i>0;--i){
                            if(i<_sp || i>_lp){
                                StructPicture _tmp=datas.get(i);
                                recycle_item(_tmp);
                                if(memory<Max_Memory)break;
                            }
                        }
                    }
                    if(!_catch)bm.recycle();
                }
            });
        }

        @Override
        public void on_load_fail(String opath, Object tag) {

        }
    };

    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand){
                btn_del.deformation(false);
                if (btn_export != null) {
                    btn_export.setVisibility(View.VISIBLE);
                }
            }
            else {
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
            }
        }
        @Override
        public void on_cancel_click() {
            editor=false;
            del_number=0;
            btn_del.setVisibility(View.GONE);
            btn_del.deformation(false);
            for(int i=0;i<datas.size();++i){
                datas.get(i).check = false;
            }
            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            final CLDialog _waiter=CLDialog.Get_Force_Wait(cc);
            _waiter.show();
            new Thread(){
                @Override
                public void run() {
                    try {
                        for(int i=0;i<datas.size();++i){
                            StructPicture _item=datas.get(i);
                            if(!_item.check)continue;
                            File _f=new File(_item.path);
                            long _flength=_f.length();
                            if(_f.exists() && _f.delete()){
                                CL.CLOGI("delete file success");
                                _f=new File(Global.Dir_thum,_item.name);
                                if(_f.exists() && _f.delete()){
                                    CL.CLOGI("delete thum success");
                                }
                            }
                            data.files.remove(i);
                            --data.files_size;
                            data.length-=_flength;
                            recycle_item(_item);
                            datas.remove(i);
                            --i;
                        }

                    }catch (Exception ex){
                        CL.CLOGI("delete error:"+ex.toString());
                    }finally {
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                if(cber_update!=null)cber_update.on_callback();
                                _waiter.dismiss();
                                editor=false;
                                del_number=0;
                                btn_del.setVisibility(View.GONE);
                                btn_del.deformation(false);
                                adapter.notifyDataSetChanged();
                            }
                        });
                    }
                }
            }.start();
        }
    };


    private class StructPicture{
        public String name;
        public String path;
        public Bitmap bitmap_thumb;
        public boolean check=false;

        public StructPicture(String name,String path){
            this.name=name;
            this.path=path;
        }
    }
    private class AdapterForItem extends BaseAdapter{

        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new ViewForPicture(cc);
            ViewForPicture _vv=(ViewForPicture)cv;
            _vv.set_basic_data(datas.get(position));
            return cv;
        }
    }

    private View.OnClickListener listener_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(editor) {
                if (v instanceof ViewForPicture) {
                    ViewForPicture _vv = (ViewForPicture) v;
                    _vv.data.check = !_vv.data.check;
                    if (_vv.data.check) {
                        _vv.iv_check.setBackgroundResource(R.mipmap.comm_select_2);
                        ++del_number;
                    } else {
                        _vv.iv_check.setBackgroundResource(R.mipmap.comm_select_1);
                        --del_number;
                        if (del_number == 0) {
                            editor = false;
                            btn_del.deformation(false);
                            btn_del.setVisibility(View.GONE);
                            if (btn_export != null)
                                btn_export.setVisibility(View.GONE);
                            for (int i = 0; i < datas.size(); ++i) {
                                datas.get(i).check = false;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                    btn_del.set_number(del_number);
                }
            }else{
                //open
                StructPicture _data=((ViewForPicture)v).data;
                if(_data.path==null || !new File(_data.path).exists()){
                    CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                    return;
                }
                if(CL.Do_Once()){
                    Intent _intent=new Intent();
                    _intent.setClass(cc, LookPicture.class);
                    _intent.putExtra("type","gif");
                    _intent.putExtra("index", datas.indexOf(_data));
                    String[] _names=new String[datas.size()];
                    for(int i=0;i<_names.length;++i){
                        StructPicture _item=datas.get(i);
                        _names[i]=_item.path;
                    }
                    _intent.putExtra("paths",_names);
                    cc.startActivity(_intent);
                }
            }
        }
    };
    private View.OnLongClickListener listener_click_long=new OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            editor=!editor;
            if(editor){
                btn_del.setVisibility(View.VISIBLE);
                btn_del.startAnimation(anim_scale);
                if (btn_export != null) {
                    btn_export.setVisibility(View.VISIBLE);
                    btn_export.startAnimation(anim_scale);
                }
                btn_del.set_number(del_number);
                listener_click.onClick(v);
            }else{
                del_number=0;
                btn_del.deformation(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
                for(int i=0;i<datas.size();++i){
                    datas.get(i).check = false;
                }
            }
            adapter.notifyDataSetChanged();
            return true;
        }
    };

    private class ViewForPicture extends FrameLayout{

        private StructPicture data;
        private ImageView iv_pic;
        private ImageView iv_check;


        public ViewForPicture(Context context) {
            super(context);
            this.setClickable(true);
            this.setLayoutParams(new AbsListView.LayoutParams(CL.MP,CL.WC));
            this.setLongClickable(true);
            this.setOnClickListener(listener_click);
            this.setOnLongClickListener(listener_click_long);

            iv_pic=CLController.Get_ImageView(cc,CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL),null,null);
            iv_pic.setBackgroundColor(Color.GRAY);
            iv_pic.setScaleType(ImageView.ScaleType.CENTER_CROP);
            this.addView(iv_pic);
            iv_check=CLController.Get_ImageView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.RIGHT|Gravity.BOTTOM,0,0,CL.DIP2PX_INT(3),CL.DIP2PX_INT(3)),
                    null,null);
            this.addView(iv_check);
        }

        public void set_basic_data(StructPicture d){
            this.data=d;
            AbsListView.LayoutParams _lp=(AbsListView.LayoutParams) this.getLayoutParams();
            _lp.width=CL.MP;
            _lp.height=grid_width_height;
            this.setLayoutParams(_lp);

            if(editor){
                iv_check.setVisibility(View.VISIBLE);
                if(this.data.check)iv_check.setBackgroundResource(R.mipmap.comm_select_2);
                else iv_check.setBackgroundResource(R.mipmap.comm_select_1);
            }else iv_check.setVisibility(View.GONE);


            iv_pic.setImageBitmap(null);
            if(this.data.bitmap_thumb!=null){
                iv_pic.setImageBitmap(this.data.bitmap_thumb);
            }else{
                if(this.data.path!=null && this.data.name!=null && Global.Dir_thum!=null){
                    loader.go_add_item(this.data.path, Global.Dir_thum.getAbsolutePath(),this.data,listener_loader);
                }
            }
        }

    }

}
