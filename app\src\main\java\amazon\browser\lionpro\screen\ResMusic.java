package amazon.browser.lionpro.screen;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Handler;
import android.os.Message;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MusicManager;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.views.Deleter;
import amazon.browser.lionpro.views.DialogExport;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLInputer;
import lion.CLToast;
import lion.CLTools;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/7/23.
 */
public class ResMusic extends LinearLayout implements CLFlipper.EventListener{

    public ResMusic(CLActivity context, CLFlipper f, CLCallback.CB cber_update) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_update=cber_update;
        init();
    }

    public void update_data(Struct.StoreDir d){
        this.data=d;
    }

    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }

    private ImageView btn_export;
    private View.OnClickListener listener_export=new OnClickListener() {
        @Override
        public void onClick(View v) {
            //权限处理
            if(ContextCompat.checkSelfPermission(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                if(ActivityCompat.shouldShowRequestPermissionRationale(cc,Manifest.permission.WRITE_EXTERNAL_STORAGE)){
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }else{
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }
            }else {
                CL.CLOGI("write external storage is granted");
                show_dialog_export();
            }
        }
    };
    private void show_dialog_export(){

        ArrayList<DialogExport.ExportData> _ds=new ArrayList<>();
        for(int i=0;i<data.dls.size();++i){
            Data.StructDLItem _tmp=data.dls.get(i);
            if(!_tmp.selected)continue;
            File _o_f=new File(_tmp.path);
            if(_o_f.exists()){
                DialogExport.ExportData _d=new DialogExport.ExportData();
                _d.o_path=_tmp.path;
                _d.name=_tmp.name!=null?_tmp.name:_tmp.title;
                _d.name=_d.name+"."+Data.Get_Type_Suffix(_tmp.type_minor);
                _ds.add(_d);
            }
        }
        if(_ds.size()==0){
            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists),true);
            return;
        }
        final DialogExport _dialog_export=new DialogExport(cc, _ds, new CLCallback.CB() {
            @Override
            public void on_callback() {
                editor=false;
                del_number=0;
                btn_del.set_number(0);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
                adapter.notifyDataSetChanged();
                for(int i=0;i<data.dls.size();++i){
                    data.dls.get(i).selected=false;
                }
            }
        });
        _dialog_export.show();
    }


    private void init(){
        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.store_music).toString(),0xffd0d0d0,18,null));
     //   this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));
        int pos = Setting.Share_Setting().get_ads_pos();

        if (pos == 0) {
            int count = Setting.Share_Setting().get_app_run_count();
            if (Setting.Share_Setting().get_pos_music_state() != 0 && count >= Setting.Share_Setting().get_pos_music_state()) {
               // int pos = Setting.Share_Setting().get_ads_pos();
                int banner_type = Setting.Share_Setting().get_banner_type();
                Global.Get_banner(cc, this, 1, banner_type, null);
            }
        }

//        View _v1=Global.Get_Banner(cc, AdSize.LARGE_BANNER, null);
//        if(_v1!=null)this.addView(_v1);

        FrameLayout _fl_content=new FrameLayout(cc);
        _fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        this.addView(_fl_content);

        ImageView _empty=new ImageView(cc);
        _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        _empty.setBackgroundResource(R.mipmap.no_data);
        _fl_content.addView(_empty);

        lv_list=new ListView(cc);
        lv_list.setLayoutParams(CL.Get_LP_MM());
        lv_list.setCacheColorHint(Color.TRANSPARENT);
        lv_list.setDivider(new ColorDrawable(0xff313131));
        lv_list.setDividerHeight(1);
        lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
        lv_list.setEmptyView(_empty);
        adapter=new AdapterForMusic();
        lv_list.setAdapter(adapter);
        _fl_content.addView(lv_list);

        btn_del=new Deleter(cc,listener_del);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        _fl_content.addView(btn_del);

        btn_del.measure(0,0);
        int _h=btn_del.getMeasuredHeight();

        if (Global.IsAndroid10()) {
            btn_export = new ImageView(cc);
            btn_export.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.icon_export_normal, R.mipmap.icon_export_click));
            btn_export.setLayoutParams(CL.Get_FLLP(CL.WC, CL.WC, Gravity.BOTTOM | Gravity.RIGHT, 0, 0, CL.DIP2PX_INT(22), CL.DIP2PX_INT(36) + _h));
            btn_export.setClickable(true);
            btn_export.setOnClickListener(listener_export);
            btn_export.setVisibility(View.GONE);
            _fl_content.addView(btn_export);
        }

        this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1),0xff313131));

        ll_player=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP,CL.WC),LinearLayout.HORIZONTAL,null);
        ll_player.setBackgroundColor(0xff15181b);
        ll_player.setGravity(Gravity.CENTER_VERTICAL);
        this.addView(ll_player);

        LinearLayout _ll_content=CLController.Get_LinearLayout(cc,
                CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8)),LinearLayout.VERTICAL,null);
        ll_player.addView(_ll_content);
        tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC),"",0xffa1a1a1,14,null);
        tv_title.setSingleLine();
        tv_title.setEllipsize(TextUtils.TruncateAt.END);
        tv_time=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC),"",0xffa1a1a1,12,null);
        _ll_content.addView(tv_title);
        _ll_content.addView(tv_time);

        btn_play=CLController.Get_ImageView(cc, CL.Get_LLLP(CL.WC, CL.WC,CL.DIP2PX_INT(16),0,CL.DIP2PX_INT(16),0),
                null, listener_btn_play_pause);
        ll_player.addView(btn_play);

        btn_next=CLController.Get_ImageView(cc, CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(22),0),
                cc.getResources().getDrawable(R.mipmap.icon_music_next),listener_btn_next);
        ll_player.addView(btn_next);


        dialog_content=CLController.Get_LinearLayout(cc,null,LinearLayout.VERTICAL,0xff1e1e1e,null);
        dialog_content.setClickable(true);
        TextView btn_rename=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(220),CL.DIP2PX_INT(50)),
                cc.getResources().getString(R.string.rename),Color.WHITE,15,listener_dialog_rename);
        btn_rename.setGravity(Gravity.LEFT|Gravity.CENTER_VERTICAL);
        btn_rename.setPadding(CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0);
        dialog_content.addView(btn_rename);
        dialog_content.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff313131));
        TextView btn_check=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(220),CL.DIP2PX_INT(50)),
                cc.getResources().getString(R.string.check),Color.WHITE,15,listener_dialog_check);
        btn_check.setGravity(Gravity.LEFT|Gravity.CENTER_VERTICAL);
        btn_check.setPadding(CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0);
        dialog_content.addView(btn_check);
        dialog_menu=CLDialog.Get_Dialog(cc,dialog_content);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);

//        View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
//        if(_v!=null)this.addView(_v);
/*
        int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
        View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);
        this.addView(divid);

        AdSize adsize;
        int banner_type = Setting.Share_Setting().get_banner_type();
        if (banner_type == 0)
            adsize=AdSize.BANNER;
        else if (banner_type == 1)
            adsize=AdSize.FULL_BANNER;
        else if (banner_type == 2)
            adsize=AdSize.LARGE_BANNER;
        else if (banner_type == 3)
            adsize=AdSize.LEADERBOARD;
        else if (banner_type == 4)
            adsize=AdSize.MEDIUM_RECTANGLE;
        else if (banner_type == 5)
            adsize=AdSize.WIDE_SKYSCRAPER;
        else if (banner_type == 6)
            adsize=AdSize.SMART_BANNER;
        else
            adsize=AdSize.BANNER;
        View _v1=Global.Get_Banner(cc, adsize, null);
        if(_v1!=null)this.addView(_v1);
        */
//        int count = Setting.Share_Setting().get_app_run_count();
//        if (Setting.Share_Setting().get_pos_music_state() != 0 && count >= Setting.Share_Setting().get_pos_music_state()) {
//            int pos = Setting.Share_Setting().get_ads_pos();
//            int banner_type = Setting.Share_Setting().get_banner_type();
//            Global.Get_banner(cc, this, pos, banner_type, null);
//        }

        if (pos == 1) {
            int count = Setting.Share_Setting().get_app_run_count();
            if (Setting.Share_Setting().get_pos_music_state() != 0 && count >= Setting.Share_Setting().get_pos_music_state()) {
                //int pos = Setting.Share_Setting().get_ads_pos();
                int banner_type = Setting.Share_Setting().get_banner_type();
                Global.Get_banner(cc, this, 0, banner_type, null);
            }
        }
    }


    private CLActivity cc;
    private CLFlipper flipper;
    private CLCallback.CB cber_update;
    private Struct.StoreDir data;
    private Deleter btn_del;
    private ListView lv_list;
    private LinearLayout ll_player;
    private TextView tv_title,tv_time;
    private ImageView btn_play,btn_next;
    private AdapterForMusic adapter;
    private ScaleAnimation anim_scale;
    private CLDialog dialog_menu;
    private LinearLayout dialog_content;
    private boolean editor=false;
    private int del_number=0;
    private boolean is_stop=false;


    private String music_time_size="00:00";
    private View.OnClickListener listener_btn_play_pause=new OnClickListener() {
        @Override
        public void onClick(View v) {
            MusicManager _mm= Server.Share_Music();
            if(_mm==null)return;
            _mm.play_pause();
        }
    };
    private View.OnClickListener listener_btn_next=new OnClickListener() {
        @Override
        public void onClick(View v) {
            MusicManager _mm=Server.Share_Music();
            if(_mm==null)return;
            if(is_stop)_mm.stop_music();
            else _mm.play_next();
        }
    };
    private Handler handler=new Handler(){
        @Override
        public void handleMessage(Message msg) {
            if(msg.what==1000){
                tv_time.setText(CLTools.Get_Time_00_00_String(Server.Share_Music().get_current_position())+" - "+music_time_size);
                handler.sendEmptyMessageDelayed(1000,1000);
            }
        }
    };
    private View.OnClickListener listener_dialog_rename=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(dialog_menu!=null && dialog_menu.isShowing())dialog_menu.dismiss();
            final Data.StructDLItem _item=((MusicItem)view_last_long_click).data;
            String _name=_item.name;
            if(_name==null || _name.isEmpty())_name=((MusicItem)view_last_long_click).data.title;
            final String __name=_name;
            CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                @Override
                public boolean on_callback_success(String obj, String msg) {
                    if(obj.equals(__name))return false;
                    Server.Update_Download_Name(_item,obj);
                    _item.name=obj;
                    ((MusicItem) view_last_long_click).tv_name.setText(obj);
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_update_info,Global.Action_rename,
                            _item.ident_md5,obj);
                    return false;
                }

                @Override
                public void on_callback_fail(int code, String msg) {

                }
            }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                    .show(cc.getResources().getString(R.string.rename),_name,null);
        }
    };
    private View view_last_long_click;
    private View.OnClickListener listener_dialog_check=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (dialog_menu != null && dialog_menu.isShowing()) dialog_menu.dismiss();
            editor = true;
            btn_del.setVisibility(View.VISIBLE);
            btn_del.startAnimation(anim_scale);
            if (btn_export != null) {
                btn_export.setVisibility(View.VISIBLE);
                btn_export.startAnimation(anim_scale);
            }
            listener_click_item.onClick(view_last_long_click);
            adapter.notifyDataSetChanged();
        }
    };


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        CL.CLOGI("on attach window");
        adapter.notifyDataSetChanged();
        MusicManager.Add_Listener(listener_music);
        listener_music.on_status_change(Server.Share_Music().get_current_state());
        listener_music.on_music_change(Server.Share_Music().get_current_music());

        if(data.dls==null || data.dls.size()==0)return;
        /*
        if(!Setting.Share_Setting().get_tip(Setting.Type_item)){
            flipper.handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                            cc.getResources().getString(R.string.tip_can_check), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_item,true);
                        }
                    }).show();
                }
            },500);
        }

         */
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        MusicManager.Reduce_Listener(listener_music);
        handler.removeMessages(1000);
        editor=false;
        del_number=0;
        btn_del.set_number(0);
        btn_del.deformation_direct(false);
        btn_del.setVisibility(View.GONE);
        if (btn_export != null)
            btn_export.setVisibility(View.GONE);
        adapter.notifyDataSetChanged();
        if(data!=null && data.dls!=null)
            for(int i=0;i<data.dls.size();++i){
                data.dls.get(i).selected=false;
            }
        CL.CLOGI("on detached window");
    }

    private MusicManager.Eventer listener_music=new MusicManager.Eventer() {
        @Override
        public void on_data_update() {
        }

        @Override
        public void on_music_change(Data.StructDLItem item) {
            adapter.notifyDataSetChanged();

            if(item==null){
                ll_player.setVisibility(View.GONE);
                return;
            }
            ll_player.setVisibility(View.VISIBLE);
            String _t=item.name;
            if(_t==null || _t.isEmpty())_t=item.title;
            tv_title.setText(_t);
            music_time_size=CLTools.Get_Time_00_00_String(Server.Share_Music().get_current_duration());
            tv_time.setText("00:00"+" - "+music_time_size);
            handler.removeMessages(1000);
            handler.sendEmptyMessage(1000);
        }

        @Override
        public void on_status_change(int state) {
            if(state==0 || state==1){
                handler.removeMessages(1000);
                btn_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_play));
                btn_next.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_stop));
                is_stop=true;
            }else if(state==2){
                handler.sendEmptyMessage(1000);
                btn_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_pause));
                btn_next.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_next));
                is_stop=false;
            }
        }
    };


    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand){
                btn_del.deformation(false);
                if (btn_export != null)
                    btn_export.setVisibility(View.VISIBLE);
            }
            else {
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
            }
        }
        @Override
        public void on_cancel_click() {
            editor=false;
            del_number=0;
            btn_del.setVisibility(View.GONE);
            btn_del.deformation(false);
            for(int i=0;i<data.dls.size();++i){
                data.dls.get(i).selected=false;
            }
            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            final CLDialog _waiter=CLDialog.Get_Force_Wait(cc);
            _waiter.show();
            new Thread(){
                @Override
                public void run() {
                    try {
                        for(int i=0;i<data.dls.size();++i){
                            Data.StructDLItem _item=data.dls.get(i);
                            if(!_item.selected)continue;
                            Data.StructDLItem _crt=Server.Share_Music().get_current_music();
                            if(_crt!=null && _crt.ident_md5.equals(_item.ident_md5)){
                                handler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        Server.Share_Music().stop_music();
                                    }
                                });
                                Thread.sleep(200);
                            }
                            File _f=new File(_item.path);
                            if(_f.exists() && _f.delete()){
                                CL.CLOGI("delete file success");
                            }
                            _f=new File(Global.Dir_thum,_item.ident_md5);
                            if(_f.exists() && _f.delete()){
                                CL.CLOGI("delete thum success");
                            }
                            Server.Delete_Download(_item);
                            data.dls.remove(_item);
                            --data.files_size;
                            data.length-=_item.length;
                            --i;
                        }
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Force_Update_DL();
                                if(cber_update!=null)cber_update.on_callback();
                                MusicManager _mm=Server.Share_Music();
                                if(_mm!=null)_mm.force_update();
                            }
                        });
                    }catch (Exception ex){
                        CL.CLOGI("delete error:"+ex.toString());
                    }finally {
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                _waiter.dismiss();
                                editor=false;
                                del_number=0;
                                btn_del.setVisibility(View.GONE);
                                btn_del.deformation(false);
                                adapter.notifyDataSetChanged();
                            }
                        });
                    }
                }
            }.start();
        }
    };
    private View.OnClickListener listener_click_item=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(editor){
                if(v instanceof MusicItem){
                    MusicItem _vv=(MusicItem)v;
                    _vv.data.selected=!_vv.data.selected;
                    if(_vv.data.selected) {
                        _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                        ++del_number;
                    }
                    else {
                        _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
                        --del_number;
                        if(del_number==0){
                            editor=false;
                            btn_del.deformation(false);
                            btn_del.setVisibility(View.GONE);
                            if (btn_export != null)
                                btn_export.setVisibility(View.GONE);
                            for(int i=0;i<data.dls.size();++i){
                                data.dls.get(i).selected=false;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                    btn_del.set_number(del_number);
                }
            }else{
                if(v instanceof MusicItem){
                    MusicItem _vv=(MusicItem)v;
                    Server.Share_Music().play_music(_vv.data);
                }
            }
        }
    };
    private View.OnLongClickListener listener_long_click_item=new View.OnLongClickListener() {
        @Override
        public boolean onLongClick(final View v) {
            if(editor){
                editor=false;
                del_number=0;
                btn_del.deformation(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
                for(int i=0;i<data.dls.size();++i){
                    data.dls.get(i).selected=false;
                }
                adapter.notifyDataSetChanged();
            }else{
                dialog_menu.show();
                view_last_long_click=v;
            }
            return true;
        }
    };


    private class AdapterForMusic extends BaseAdapter {

        @Override
        public int getCount() {
            if(data==null || data.dls==null)return 0;
            return data.dls.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new MusicItem(cc);
            MusicItem _v=(MusicItem)cv;
            _v.set_basic_data(data.dls.get(position));
            return cv;
        }
    }
    private class MusicItem extends LinearLayout {

        private Data.StructDLItem data;
        private ImageView btn_selected;
        private TextView tv_name,tv_type,tv_length,tv_duration;

        public MusicItem(Context context) {
            super(context);
            this.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0x993d6f59)));
            this.setPadding(CL.DIP2PX_INT(15),CL.DIP2PX_INT(6),CL.DIP2PX_INT(8),CL.DIP2PX_INT(6));
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);
            this.setLongClickable(true);
            this.setOnClickListener(listener_click_item);
            this.setOnLongClickListener(listener_long_click_item);


            btn_selected=new ImageView(context);
            btn_selected.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),0,0,CL.DIP2PX_INT(15),0));
            btn_selected.setVisibility(View.GONE);
            this.addView(btn_selected);

            LinearLayout _ll_content=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),LinearLayout.VERTICAL,null);
            this.addView(_ll_content);

            tv_name=CLController.Get_TextView(cc, CL.Get_LP_WW(),"",Color.WHITE,15,null);
            tv_name.setSingleLine();
            tv_name.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_name);

            LinearLayout _ll_detail=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP,CL.WC,0,CL.DIP2PX_INT(4),CL.DIP2PX_INT(8),0),LinearLayout.HORIZONTAL,null);
            _ll_content.addView(_ll_detail);

            tv_type=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(60),CL.WC),"", Color.WHITE,14,null);
            _ll_detail.addView(tv_type);

            tv_length=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f),"", Color.WHITE,14,null);
            _ll_detail.addView(tv_length);

            tv_duration=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),"", Color.WHITE,14,null);
            _ll_detail.addView(tv_duration);
        }
        public void set_basic_data(Data.StructDLItem d){
            this.data=d;

            if(editor){
                btn_selected.setVisibility(View.VISIBLE);
                if(this.data.selected)btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                else btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
            }
            else btn_selected.setVisibility(View.GONE);

            tv_name.setText(this.data.name==null?this.data.title:this.data.name);

            if(data.type_minor==Data.Type_Music_MP3)tv_type.setText("MP3");
            else if(data.type_minor==Data.Type_Music_OGG)tv_type.setText("OGG");
            else if(data.type_minor==Data.Type_Music_FLAC)tv_type.setText("FLAC");
            else if(data.type_minor==Data.Type_Music_WAV)tv_type.setText("WAV");
            else if(data.type_minor==Data.Type_Music_M4A)tv_type.setText("M4A");
            else tv_type.setText("???");

            tv_length.setText(CLTools.Get_Capacity_Format(this.data.length));
            tv_duration.setText(this.data.duration);
        }
    }
}
