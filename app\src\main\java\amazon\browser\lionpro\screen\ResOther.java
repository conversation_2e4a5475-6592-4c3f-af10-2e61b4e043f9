package amazon.browser.lionpro.screen;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;

import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.views.Deleter;
import amazon.browser.lionpro.views.DialogExport;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLInputer;
import lion.CLToast;
import lion.CLTools;
import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/8/1.
 */
public class ResOther extends LinearLayout implements CLFlipper.EventListener{

    public ResOther(CLActivity context, CLFlipper f, String title, CLCallback.CB cber_update) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.title=title;
        this.cber_update=cber_update;
        init();
    }

    public void update_data(Struct.StoreDir d){
        this.data=d;
//        if(this.data!=null){
//            for(int i=0;i<this.data.dls.size();++i){
//                Data.StructDLItem _tmp=this.data.dls.get(i);
//                StructFile _f=new StructFile();
//                _f.item=_tmp;
//                _f.name=_tmp.name!=null?_tmp.name:_tmp.title;
//                _f.type_major=_tmp.type_major;
//                _f.suffix=_tmp.suffix;
//                _f.length=_tmp.length;
//                _f.path=_tmp.path;
//                datas.add(_f);
//            }
//        }
    }

    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        flipper.go_previously(this);
    }


    private ImageView btn_export;
    private View.OnClickListener listener_export=new OnClickListener() {
        @Override
        public void onClick(View v) {
            //权限处理
            if(ContextCompat.checkSelfPermission(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    != PackageManager.PERMISSION_GRANTED){
                if(ActivityCompat.shouldShowRequestPermissionRationale(cc,Manifest.permission.WRITE_EXTERNAL_STORAGE)){
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }else{
                    cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                        @Override
                        public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                            if(grant_results[0]==PackageManager.PERMISSION_GRANTED){
                                show_dialog_export();
                            }else{
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_open_storage)).show();
                            }
                        }
                    });
                }
            }else {
                CL.CLOGI("write external storage is granted");
                show_dialog_export();
            }
        }
    };
    private void show_dialog_export(){

        ArrayList<DialogExport.ExportData> _ds=new ArrayList<>();
        for(int i=0;i<data.dls.size();++i){
            Data.StructDLItem _tmp=data.dls.get(i);
            if(!_tmp.selected)continue;
            File _o_f=new File(_tmp.path);
            if(_o_f.exists()){
                DialogExport.ExportData _d=new DialogExport.ExportData();
                _d.o_path=_tmp.path;
                _d.name=_tmp.name;
                _ds.add(_d);
            }
        }
        if(_ds.size()==0){
            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists),true);
            return;
        }
        final DialogExport _dialog_export=new DialogExport(cc, _ds, new CLCallback.CB() {
            @Override
            public void on_callback() {
                editor=false;
                del_number=0;
                btn_del.set_number(0);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
                adapter.notifyDataSetChanged();
                for(int i=0;i<data.dls.size();++i){
                    data.dls.get(i).selected=false;
                }
            }
        });
        _dialog_export.show();
    }


    private void init(){
        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),title,0xffd0d0d0,18,null));
     //   this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));


        FrameLayout _fl_content=new FrameLayout(cc);
        _fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        this.addView(_fl_content);

        ImageView _empty=new ImageView(cc);
        _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        _empty.setBackgroundResource(R.mipmap.no_data);
        _fl_content.addView(_empty);

        lv_list=new ListView(cc);
        lv_list.setLayoutParams(CL.Get_LP_MM());
        lv_list.setCacheColorHint(Color.TRANSPARENT);
        lv_list.setDivider(new ColorDrawable(0xff313131));
        lv_list.setDividerHeight(1);
        lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
        lv_list.setEmptyView(_empty);
        adapter=new AdapterForFile();
        lv_list.setAdapter(adapter);
        _fl_content.addView(lv_list);

        btn_del=new Deleter(cc,listener_del);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        _fl_content.addView(btn_del);

        btn_del.measure(0,0);
        int _h=btn_del.getMeasuredHeight();

        if (Global.IsAndroid10()) {
            btn_export = new ImageView(cc);
            btn_export.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.icon_export_normal, R.mipmap.icon_export_click));
            btn_export.setLayoutParams(CL.Get_FLLP(CL.WC, CL.WC, Gravity.BOTTOM | Gravity.RIGHT, 0, 0, CL.DIP2PX_INT(22), CL.DIP2PX_INT(36) + _h));
            btn_export.setClickable(true);
            btn_export.setOnClickListener(listener_export);
            btn_export.setVisibility(View.GONE);
            _fl_content.addView(btn_export);
        }

        dialog_content=CLController.Get_LinearLayout(cc,null,LinearLayout.VERTICAL,0xff1e1e1e,null);
        dialog_content.setClickable(true);
        TextView btn_rename=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(220),CL.DIP2PX_INT(50)),
                cc.getResources().getString(R.string.rename),Color.WHITE,15,listener_dialog_rename);
        btn_rename.setGravity(Gravity.LEFT|Gravity.CENTER_VERTICAL);
        btn_rename.setPadding(CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0);
        dialog_content.addView(btn_rename);
        dialog_content.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff313131));
        TextView btn_check=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(220),CL.DIP2PX_INT(50)),
                cc.getResources().getString(R.string.check),Color.WHITE,15,listener_dialog_check);
        btn_check.setGravity(Gravity.LEFT|Gravity.CENTER_VERTICAL);
        btn_check.setPadding(CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0);
        dialog_content.addView(btn_check);
        dialog_menu= CLDialog.Get_Dialog(cc,dialog_content);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);

//        int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
//        View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);
//        this.addView(divid);
//        View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
//        if(_v!=null)this.addView(_v);
    }


    private CLActivity cc;
    private CLFlipper flipper;
    private String title;
    private CLCallback.CB cber_update;
    private Struct.StoreDir data;
   // private ArrayList<StructFile> datas=new ArrayList<>();
    private Deleter btn_del;
    private ListView lv_list;
    private AdapterForFile adapter;
    private ScaleAnimation anim_scale;
    private CLDialog dialog_menu;
    private LinearLayout dialog_content;
    private boolean editor=false;
    private int del_number=0;


    private View.OnClickListener listener_dialog_rename=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(dialog_menu!=null && dialog_menu.isShowing())dialog_menu.dismiss();
            final Data.StructDLItem _item=((FileItem)view_last_long_click).data;
            CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                @Override
                public boolean on_callback_success(String obj, String msg) {
                    if(obj.equals(_item.name))return false;
                    Server.Update_Download_Name(_item,obj);
                    _item.name=obj;
                    ((FileItem) view_last_long_click).tv_name.setText(obj);
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_update_info,Global.Action_rename,
                            _item.ident_md5,obj);
                    return false;
                }
                @Override
                public void on_callback_fail(int code, String msg) {

                }
            }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                    .show(cc.getResources().getString(R.string.rename),_item.name,null);
        }
    };
    private View view_last_long_click;
    private View.OnClickListener listener_dialog_check=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(dialog_menu!=null && dialog_menu.isShowing())dialog_menu.dismiss();
            editor=true;
            btn_del.setVisibility(View.VISIBLE);
            btn_del.startAnimation(anim_scale);
            if (btn_export != null) {
                btn_export.setVisibility(View.VISIBLE);
                btn_export.startAnimation(anim_scale);
            }
            listener_click_item.onClick(view_last_long_click);
            adapter.notifyDataSetChanged();
        }
    };


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        adapter.notifyDataSetChanged();
        if(data.dls==null || data.dls.size()==0)return;
        /*
        if(!Setting.Share_Setting().get_tip(Setting.Type_item)){
            flipper.handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                            cc.getResources().getString(R.string.tip_can_check), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_item,true);
                        }
                    }).show();
                }
            },500);
        }

         */
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        editor=false;
        del_number=0;
        btn_del.set_number(0);
        btn_del.deformation_direct(false);
        btn_del.setVisibility(View.GONE);
        if (btn_export != null) {
            btn_export.setVisibility(View.GONE);
        }
       // datas.clear();
        adapter.notifyDataSetChanged();

        if(data!=null && data.dls!=null)
            for(int i=0;i<data.dls.size();++i){
                data.dls.get(i).selected=false;
            }
        CL.CLOGI("on detached window");
    }


    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand){
                btn_del.deformation(false);
                if (btn_export != null) {
                    btn_export.setVisibility(View.VISIBLE);
                }
            }
            else {
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
            }
        }
        @Override
        public void on_cancel_click() {
            editor=false;
            del_number=0;
            btn_del.setVisibility(View.GONE);
            btn_del.deformation(false);
            for(int i=0;i<data.dls.size();++i){
                data.dls.get(i).selected=false;
            }

            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            final CLDialog _waiter=CLDialog.Get_Force_Wait(cc);
            _waiter.show();
            new Thread(){
                @Override
                public void run() {
                    try {
                        for(int i=0;i<data.dls.size();++i){
                            Data.StructDLItem _item=data.dls.get(i);
                            if(!_item.selected)continue;
                            File _f=new File(_item.path);
                            if(_f.exists() && _f.delete()){
                                CL.CLOGI("delete file success");
                            }
                            _f=new File(Global.Dir_thum,_item.ident_md5);
                            if(_f.exists() && _f.delete()){
                                CL.CLOGI("delete thum success");
                            }
                            Server.Delete_Download(_item);
                            data.dls.remove(_item);
                            --data.files_size;
                            data.length-=_item.length;
                            --i;
                        }
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Force_Update_DL();
                                if(cber_update!=null)cber_update.on_callback();
                            }
                        });
                    }catch (Exception ex){
                        CL.CLOGI("delete error:"+ex.toString());
                    }finally {
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                _waiter.dismiss();
                                editor=false;
                                del_number=0;
                                btn_del.setVisibility(View.GONE);
                                btn_del.deformation(false);
                                adapter.notifyDataSetChanged();
                            }
                        });
                    }
                }
            }.start();
        }
    };
    private View.OnClickListener listener_click_item=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(editor){
                if(v instanceof FileItem){
                    FileItem _vv=(FileItem)v;
                    _vv.data.selected=!_vv.data.selected;
                    //_vv.data.check = true;
                    if(_vv.data.selected) {
                        _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                        ++del_number;
                    }
                    else {
                        _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
                        --del_number;
                        if(del_number==0){
                            editor=false;
                            btn_del.deformation(false);
                            btn_del.setVisibility(View.GONE);
                            if (btn_export != null) {
                                btn_export.setVisibility(View.GONE);
                            }
                            for(int i=0;i<data.dls.size();++i){
                                data.dls.get(i).selected=false;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                    btn_del.set_number(del_number);
                }
            }else{
                if(v instanceof FileItem){
                    FileItem _vv=(FileItem)v;
                    if(!new File(_vv.data.path).exists()){
                        CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                        return;
                    }
//                    try {
//                        Intent intent = new Intent();
//                        intent.addCategory(Intent.CATEGORY_DEFAULT);
//                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                        intent.setAction(Intent.ACTION_VIEW);
//                        String _type= MimeTypeMap.getSingleton().getMimeTypeFromExtension(_vv.data.suffix);
////                        CL.CLOGI("mime type:"+_type);
////                        CL.CLOGI("path:"+_vv.data.path);
//                        intent.setDataAndType(Uri.fromFile(new File(_vv.data.path)), _type);
//                        cc.startActivity(Intent.createChooser(intent,cc.getResources().getString(R.string.open)));
//                    }catch (Exception ex){
//                        CL.CLOGE("open error:"+ex.toString(),ex);
//                    }
                    CLTools.playFullSrceenMedia(cc, _vv.data.path);
                }
            }
        }
    };
    private View.OnLongClickListener listener_long_click_item=new View.OnLongClickListener() {
        @Override
        public boolean onLongClick(final View v) {
            if(editor){
                editor=false;
                del_number=0;
                btn_del.deformation(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null) {
                    btn_export.setVisibility(View.GONE);
                }
                for(int i=0;i<data.dls.size();++i){
                    data.dls.get(i).selected=false;
                }
                adapter.notifyDataSetChanged();
            }else{
                dialog_menu.show();
                view_last_long_click=v;
            }
            return true;
        }
    };


    private class StructFile {
        public Data.StructDLItem item;

        public int type_major;
        public String suffix;
        public long length;
        public String name;
        public String path;

        public boolean check=false;
    }

    private class AdapterForFile extends BaseAdapter {

        @Override
        public int getCount() {
            if(data==null || data.dls==null)return 0;
            return data.dls.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new FileItem(cc);
            FileItem _v=(FileItem)cv;
            _v.set_basic_data(data.dls.get(position));
            return cv;
        }
    }
    private class FileItem extends LinearLayout {

        private Data.StructDLItem data;
        private ImageView btn_selected;
        private ImageView iv_icon;
        private TextView tv_name,tv_type,tv_length;

        public FileItem(Context context) {
            super(context);
            this.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0x993d6f59)));
            this.setPadding(CL.DIP2PX_INT(8),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3));
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);
            this.setLongClickable(true);
            this.setOnClickListener(listener_click_item);
            this.setOnLongClickListener(listener_long_click_item);


            btn_selected=new ImageView(context);
            btn_selected.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),0,0,CL.DIP2PX_INT(8),0));
            btn_selected.setVisibility(View.GONE);
            this.addView(btn_selected);


            iv_icon=new ImageView(context);
            iv_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(8),0));
            this.addView(iv_icon);

            tv_type=CLController.Get_TextView(cc,CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC,0,0,0,0),"",0xffaaaaaa,13,null);
            tv_type.getPaint().setFakeBoldText(true);
            this.addView(tv_type);

            LinearLayout _ll_content=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,0,CL.DIP2PX_INT(4),0,CL.DIP2PX_INT(4)),LinearLayout.VERTICAL,null);
            this.addView(_ll_content);

            tv_name=CLController.Get_TextView(cc, CL.Get_LP_WW(),"",Color.WHITE,15,null);
            tv_name.setSingleLine();
            tv_name.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_name);


            tv_length=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f),"", Color.WHITE,14,null);
            _ll_content.addView(tv_length);

        }
        public void set_basic_data(Data.StructDLItem d){
            this.data=d;

            if(editor){
                btn_selected.setVisibility(View.VISIBLE);
                if(this.data.selected)btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                else btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
            }
            else btn_selected.setVisibility(View.GONE);

            tv_name.setText(this.data.name==null?this.data.title:this.data.name);

            if(data.type_major==Data.Type_Doc){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_doc_s);
                tv_type.setText(d.suffix);
            }
            else if(data.type_major==Data.Type_Other){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_other_s);
                tv_type.setText(d.suffix);
            }

            tv_length.setText(CLTools.Get_Capacity_Format(this.data.length));
        }
    }

}
