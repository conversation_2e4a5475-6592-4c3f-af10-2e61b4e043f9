package amazon.browser.lionpro.screen;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Handler;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.documentfile.provider.DocumentFile;

import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MusicManager;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.primary.LookVideo;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.util.RewardCreator;
import amazon.browser.lionpro.views.CustomGridView;
import amazon.browser.lionpro.views.Deleter;
import amazon.browser.lionpro.views.DialogExport;
import amazon.browser.lionpro.views.DialogExportProgress;
import com.google.android.gms.ads.AdView;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLInputer;
import lion.CLToast;
import lion.CLTools;
import lion.CLVThumLoader;
import lion.widget.CLFlipper;
import lion.widget.GuideNovice;
import lion.widget.GuideNoviceExport;

/**
 * Created by leron on 2016/7/29.
 */
public class ResVideo extends LinearLayout implements CLFlipper.EventListener, CLCallback.CB_TFO<Integer> {

    private CLActivity cc;
    private CLFlipper flipper;
    private CLCallback.CB cber_update;
    private FrameLayout fl_content;
    private Deleter btn_del;
    private ScaleAnimation anim_scale;
    private CustomGridView gv_list;
    private AdapterForVideo adapter;
    private ArrayList<StructVideo> datas=new ArrayList<>();
    private Struct.StoreDir data;

    private CLDialog dialog_menu;
    private LinearLayout dialog_content;

    private boolean discard=false;
    private CLVThumLoader loader;
    private int grid_column=3;
    private int grid_width_height=0;
    private boolean editor=false;
    private int del_number=0;
    private RewardCreator rewardCreator = new RewardCreator();
    public ResVideo(CLActivity context,CLFlipper f,CLCallback.CB cber_update) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_update=cber_update;
        init();
        rewardCreator.LoadReward(cc, this);
    }
    public void update_data(Struct.StoreDir d){
        this.data=d;
        if(this.data==null && this.data.dls==null)return;
        for(int i=0;i<this.data.dls.size();++i){
            Data.StructDLItem _tmp=this.data.dls.get(i);
            String _suffix=null;
            if(_tmp.type_major==Data.Type_M3U8)_suffix="M3U8";
            else _suffix=Data.Get_Type_Suffix(_tmp.type_minor);
            datas.add(new StructVideo(_tmp.name!=null?_tmp.name:_tmp.title,_tmp.path,
                    _suffix, CLTools.Get_Capacity_Format(_tmp.length),_tmp.duration,_tmp));
        }
        adapter.notifyDataSetChanged();
    }


    @Override
    public void on_hide_over() {

    }

    @Override
    public void on_resume_begin() {

    }

    @Override
    public void on_resume_end() {

    }

    @Override
    public void on_back() {
        mHandler.removeCallbacks(runnable);
        flipper.go_previously(this);
    }
    private Handler wait_handler = new Handler();
    private Runnable runnable1 = new Runnable() {
        @Override
        public void run() {
            Global.hideWait();
            openExportSystemUI();
        }
    };

    public void hideWaitTask() {
        if (wait_handler != null) {
            wait_handler.postDelayed(runnable1,10000);
        }
    }

    public void removeWaitTask() {
        if (wait_handler != null) {
            wait_handler.removeCallbacks(runnable1);
        }
    }

    @Override
    public boolean on_callback_success(Integer value, String msg) {
        Global.hideWait();
        if (value == RewardCreator.AD_FULL_SCREEN) {
            removeWaitTask();
            Global.hideWait();
        } else if (value == RewardCreator.AD_LOADED) {

        } else if (value == RewardCreator.AD_EARNED) {
            openExportSystemUI();
        }
        adapter.notifyDataSetChanged();
        return true;
    }

    @Override
    public void on_callback_fail(int code, String msg) {
        Global.hideWait();
        removeWaitTask();
        if (code == RewardCreator.AD_CLOSE_EARLY) {
            hideWaitTask();
            Global.showWait(cc);
            rewardCreator.ReLoadReward(cc, this);
        } else if (code == RewardCreator.AD_LOADED_FAILED) {

        }
    }

    private void showAdsTip() {
        CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.str_reward_tip_title), cc.getResources().getString(R.string.str_reward_tip_content), new CLCallback.CB_TF() {
            @Override
            public void on_callback_success() {
                hideWaitTask();
                Global.showWait(cc);
                if (rewardCreator != null && rewardCreator.rewardedAd != null)
                    rewardCreator.rewardedAd.show(cc, rewardCreator);
            }
            @Override
            public void on_callback_fail(int code, String msg) {

            }
        }).show();
    }

    private void openExportSystemUI() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT_TREE);
        cc.startActivityForResult(intent, 666, new CLActivity.EventResult() {
            @Override
            public void onActivityResult(int result, Intent data) {
                if (result == Activity.RESULT_OK && data != null) {
                    Uri uri = data.getData();
                    DocumentFile df = DocumentFile.fromTreeUri(cc, uri);

                    if (df != null && df.exists() && df.isDirectory() && df.canWrite()) {
                        ArrayList<DialogExport.ExportData> fs = new ArrayList<>();
                        for (int i = 0; i < datas.size(); ++i) {
                            StructVideo _tmp = datas.get(i);
                            if (!_tmp.check) continue;
                            File _o_f = new File(_tmp.path);
                            if (_o_f.exists()) {
                                DialogExport.ExportData _d = new DialogExport.ExportData();
                                _d.o_path = _tmp.path;
                                _d.name = _tmp.name + "." + _tmp.suffix;
                                fs.add(_d);
                            }
                        }
                        if (fs.isEmpty()) {
                            return;
                        }
                        DialogExportProgress dialogExport = new DialogExportProgress(cc);
                        dialogExport.setData(df, fs);
                        dialogExport.show();
                    }
                }
            }
        });
    }


    private ImageView btn_export;
    private View.OnClickListener listener_export=new OnClickListener() {
        @Override
        public void onClick(View v) {
            //权限处理
            if (Global.IsAndroid10()) {
                /*
                if (rewardCreator != null && rewardCreator.rewardedAd != null && !Setting.Share_Setting().get_subscription_flag()) {
                    showAdsTip();
                } else if (Global.ad_interstitial != null) {
                    Global.showInterstitial_donow(cc, new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            openExportSystemUI();
                          //  CLToast.Show(cc, cc.getResources().getString(R.string.str_starting_downloading), true);
                        }
                    });
                } else {
                    openExportSystemUI();
                }

                 */
                openExportSystemUI();
            } else {
                //权限处理
                if (ContextCompat.checkSelfPermission(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        != PackageManager.PERMISSION_GRANTED) {
                    if (ActivityCompat.shouldShowRequestPermissionRationale(cc, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                        cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                            @Override
                            public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                                if (grant_results[0] == PackageManager.PERMISSION_GRANTED) {
                                    show_dialog_export();
                                } else {
                                    CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_open_storage)).show();
                                }
                            }
                        });
                    } else {
                        cc.request_permissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, new CLActivity.EventPermissions() {
                            @Override
                            public void on_request_permissions_result(String[] permissions, int[] grant_results) {
                                if (grant_results[0] == PackageManager.PERMISSION_GRANTED) {
                                    show_dialog_export();
                                } else {
                                    CLDialog.Get_Alert_Dialog(cc, cc.getResources().getString(R.string.tip_open_storage)).show();
                                }
                            }
                        });
                    }
                } else {
                    CL.CLOGI("write external storage is granted");
                    show_dialog_export();
                }
            }
        }
    };
    private void show_dialog_export(){

        ArrayList<DialogExport.ExportData> _ds=new ArrayList<>();
        for(int i=0;i<datas.size();++i){
            StructVideo _tmp=datas.get(i);
            if(!_tmp.check)continue;
            File _o_f=new File(_tmp.path);
            if(_o_f.exists()){
                DialogExport.ExportData _d=new DialogExport.ExportData();
                _d.o_path=_tmp.path;
                _d.name=_tmp.name+"."+_tmp.suffix;
                _ds.add(_d);
            }
        }
        if(_ds.size()==0){
            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists),true);
            return;
        }
        final DialogExport _dialog_export=new DialogExport(cc, _ds, new CLCallback.CB() {
            @Override
            public void on_callback() {
                editor=false;
                del_number=0;
                btn_del.set_number(0);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
                adapter.notifyDataSetChanged();
                for(int i=0;i<datas.size();++i){
                    datas.get(i).check=false;
                }
            }
        });
        _dialog_export.show();
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed,l,t,r,b);
        if(changed){
            if(this.getWidth()>this.getHeight()){
                grid_column=3;
                gv_list.setNumColumns(grid_column);
            }else {
                grid_column=2;
                gv_list.setNumColumns(grid_column);
            }
            grid_width_height=(this.getWidth()-CL.DIP2PX_INT(4)*(grid_column+1))/grid_column;
            grid_width_height=grid_width_height*70/100;
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        loader=new CLVThumLoader(CL.DIP2PX_INT(118),CL.DIP2PX_INT(118));
        discard=false;
        if(datas==null || datas.size()==0)return;

        if(!Setting.Share_Setting().get_tip(Setting.Type_video) && Global.IsAndroid10()) {
            Setting.Share_Setting().set_tip(Setting.Type_video,true);
            //CLDialog.Get_Alert_Scroll_Dialog(cc, cc.getResources().getString(R.string.my_download_tip), 10000, 1000, null).show();
            mHandler.postDelayed(runnable, 300);
        }
//        if (Setting.Share_Setting().get_export_tip() <= 0 && Global.IsAndroid10()) {
//            Setting.Share_Setting().set_export_tip(1);
//            GuideNoviceExport dialog = new GuideNoviceExport();
//            dialog.show(cc.getSupportFragmentManager(), "export_guide");
//        }
    /*
        if(!Setting.Share_Setting().get_tip(Setting.Type_item)){
            flipper.handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                            cc.getResources().getString(R.string.tip_can_check), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_item,true);
                        }
                    }).show();
                }
            },500);
        }
        */
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        loader.clear_task();
        loader.go_exit();
        discard=true;
        clear_resource();
        for(int i = 0; i< gv_list.getChildCount(); ++i){
            View _v= gv_list.getChildAt(i);
            if(_v instanceof VideoItem) {
                VideoItem _vv = (VideoItem) _v;
                _vv.iv_thumb.setImageBitmap(null);
            }
        }
        editor=false;
        del_number=0;
        btn_del.set_number(0);
        btn_del.deformation_direct(false);
        btn_del.setVisibility(View.GONE);
        adapter.notifyDataSetChanged();
    }

    private void clear_resource(){
        if(datas!=null){
            for(int i=0;i<datas.size();++i){
                StructVideo _tmp=datas.get(i);
                recycle_item(_tmp);
            }
            datas.clear();
            CL.CLOGI("P_memory:"+memory);
        }
    }
    private void recycle_item(StructVideo item){
        if(item!=null && item.bitmap_thumb!=null && !item.bitmap_thumb.isRecycled()){
            memory-=item.bitmap_thumb.getByteCount();
            item.bitmap_thumb.recycle();
            item.bitmap_thumb=null;
        }
    }

    private View.OnClickListener listener_ad=new OnClickListener() {
        @Override
        public void onClick(View v) {
//            Global.Show_Interstitial_IM(cc, new CLCallback.CB() {
//                @Override
//                public void on_callback() {
//                }
//            });
        }
    };

    private void init(){
        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                on_back();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.store_video).toString(),0xffd0d0d0,18,null));

     /*   ImageView btn_ad = CLController.Get_ImageView(cc, CL.Get_FLLP(CL.DIP2PX_INT(60), CL.MP, Gravity.RIGHT), null, listener_ad);
        btn_ad.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.tip_ad_normal, R.mipmap.tip_ad_press));
        btn_ad.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        fl_header.addView(btn_ad);
//        if(!Setting.Share_Setting().get_outline_switch_ad() && !Global.Switch_AD) {
//            btn_ad.setVisibility(View.INVISIBLE);
//        }


        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            if (Setting.Share_Setting().get_subscription_flag()) {
                btn_ad.setVisibility(View.INVISIBLE);
            }
        } else {
            btn_ad.setVisibility(View.INVISIBLE);
        }
        */
      //  this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));

        fl_content=new FrameLayout(cc);
        fl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));


        this.addView(fl_content);

        ImageView _empty=new ImageView(cc);
        _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        _empty.setBackgroundResource(R.mipmap.no_data);
        fl_content.addView(_empty);

        gv_list=new CustomGridView(cc);
        gv_list.setMotionEventSplittingEnabled(false);
        gv_list.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP,Gravity.FILL));
        gv_list.setCacheColorHint(Color.TRANSPARENT);
        gv_list.setEmptyView(_empty);
        gv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        int _space=CL.DIP2PX_INT(4);
        gv_list.setHorizontalSpacing(_space);
        gv_list.setVerticalSpacing(_space);
        gv_list.setPadding(_space,_space,_space,_space);
        adapter=new AdapterForVideo();
        gv_list.setAdapter(adapter);
        fl_content.addView(gv_list);

       // addAdView(this);

        dialog_content=CLController.Get_LinearLayout(cc,null,LinearLayout.VERTICAL,0xff1e1e1e,null);
        dialog_content.setClickable(true);
        TextView btn_rename=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(220),CL.DIP2PX_INT(50)),
                cc.getResources().getString(R.string.rename),Color.WHITE,15,listener_dialog_rename);
        btn_rename.setGravity(Gravity.LEFT|Gravity.CENTER_VERTICAL);
        btn_rename.setPadding(CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0);
        dialog_content.addView(btn_rename);
        dialog_content.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff313131));
        TextView btn_check=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(220),CL.DIP2PX_INT(50)),
                cc.getResources().getString(R.string.check),Color.WHITE,15,listener_dialog_check);
        btn_check.setGravity(Gravity.LEFT|Gravity.CENTER_VERTICAL);
        btn_check.setPadding(CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0);
        dialog_content.addView(btn_check);
        dialog_menu=CLDialog.Get_Dialog(cc,dialog_content);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);

        btn_del=new Deleter(cc,listener_del);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        fl_content.addView(btn_del);

        btn_del.measure(0,0);
        int _h=btn_del.getMeasuredHeight();

        if (Global.IsAndroid10()) {
            btn_export = new ImageView(cc);
            btn_export.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.icon_export_normal, R.mipmap.icon_export_click));
            btn_export.setLayoutParams(CL.Get_FLLP(CL.WC, CL.WC, Gravity.BOTTOM | Gravity.RIGHT, 0, 0, CL.DIP2PX_INT(22), CL.DIP2PX_INT(36) + _h));
            btn_export.setClickable(true);
            btn_export.setOnClickListener(listener_export);
            btn_export.setVisibility(View.GONE);
            fl_content.addView(btn_export);
        }

//        View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
//        if(_v!=null)this.addView(_v);

        /*
        int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
        View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);
        this.addView(divid);

        AdSize adsize;
        int banner_type = Setting.Share_Setting().get_banner_type();
        if (banner_type == 0)
            adsize=AdSize.BANNER;
        else if (banner_type == 1)
            adsize=AdSize.FULL_BANNER;
        else if (banner_type == 2)
            adsize=AdSize.LARGE_BANNER;
        else if (banner_type == 3)
            adsize=AdSize.LEADERBOARD;
        else if (banner_type == 4)
            adsize=AdSize.MEDIUM_RECTANGLE;
        else if (banner_type == 5)
            adsize=AdSize.WIDE_SKYSCRAPER;
        else if (banner_type == 6)
            adsize=AdSize.SMART_BANNER;
        else
            adsize=AdSize.BANNER;
        View _v1=Global.Get_Banner(cc, adsize, null);

        //View _v1=Global.Get_Banner(cc, AdSize.LARGE_BANNER, null);
        if(_v1!=null)this.addView(_v1);
        */

        int count = Setting.Share_Setting().get_app_run_count();

        if (!Setting.Share_Setting().get_subscription_flag() && Setting.Share_Setting().get_pos_video_state() != 0 && count >= Setting.Share_Setting().get_pos_video_state() ) {

            int pos = Setting.Share_Setting().get_video_ads_pos();
            int banner_type = Setting.Share_Setting().get_banner2_type();
            Global.Get_banner(cc, this, pos, banner_type, null);
        }


    }


    private Handler mHandler = new Handler();
    Runnable runnable = new Runnable() {

        @Override
        public void run() {
            //显示广告弹窗的dialog
            showAD();
        }
    };


    protected void showAD() {
        //这边为了保险起见,加入了try catch捕获异常,避免崩溃,不过已经remove了子线程,这样做是多余的.有兴趣的童鞋可以尝试哈
        try {
          //  CLDialog.Get_Alert_Scroll_Dialog(cc, cc.getResources().getString(R.string.my_download_tip), 10000, 1000, null).show();
            Setting.Share_Setting().set_export_tip(1);
            GuideNoviceExport dialog = new GuideNoviceExport();
            dialog.show(cc.getSupportFragmentManager(), "export_guide");
        } catch (Exception e) {

            e.printStackTrace();
        }

    }

        public static AdView m_AdView;
    private void addAdView(ViewGroup parent) {
        m_AdView=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), new CLCallback.CB() {
            @Override
            public void on_callback() {
                if (m_AdView != null && m_AdView.getVisibility() != View.GONE)
                    m_AdView.setVisibility(View.GONE);
            }
        });
        if (m_AdView != null)
            parent.addView(m_AdView);
    }
    private final int Max_Memory=(int)(8*1024*1024* CL.Density);
    private int memory=0;
    private CLVThumLoader.LoaderListener listener_loader=new CLVThumLoader.LoaderListener() {
        @Override
        public void on_load_complete(final String opath, String tpath, final Bitmap bm,final Object tag) {
            if(discard)return;
            flipper.handler.post(new Runnable() {
                @Override
                public void run() {
                    boolean _catch=false;
                    for(int i = 0; i< gv_list.getChildCount(); ++i){
                        View _v= gv_list.getChildAt(i);
                        if(_v instanceof VideoItem) {
                            VideoItem _vv = (VideoItem) _v;
                            if (_vv.data == tag && _vv.data.path.equals(opath) && _vv.data.bitmap_thumb==null) {
                                _vv.data.bitmap_thumb=bm;
                                _vv.iv_thumb.setImageBitmap(_vv.data.bitmap_thumb);
                                memory+=bm.getByteCount();
                                CL.CLOGI("P_memory:"+memory);
                                _catch=true;
                                break;
                            }
                        }
                    }
                    if(memory>Max_Memory){
                        int _sp= gv_list.getFirstVisiblePosition();
                        int _lp= gv_list.getLastVisiblePosition();
                        for(int i=datas.size()-1;i>0;--i){
                            if(i<_sp || i>_lp){
                                StructVideo _tmp=datas.get(i);
                                recycle_item(_tmp);
                                if(memory<Max_Memory)break;
                            }
                        }
                    }
                    if(!_catch)bm.recycle();
                }
            });
        }

        @Override
        public void on_load_fail(String opath, Object tag) {

        }
    };

    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand){
                btn_del.deformation(false);
                if (btn_export != null)
                    btn_export.setVisibility(View.VISIBLE);
            }
            else {
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
            }
        }
        @Override
        public void on_cancel_click() {
            editor=false;
            del_number=0;
            btn_del.deformation(false);
            btn_del.setVisibility(View.GONE);
            if (btn_export != null)
                btn_export.setVisibility(View.GONE);
            for(int i=0;i<datas.size();++i){
                datas.get(i).check = false;
            }
            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            final CLDialog _waiter=CLDialog.Get_Force_Wait(cc);
            _waiter.show();
            new Thread(){
                @Override
                public void run() {
                    try {
                        for(int i=0;i<datas.size();++i){
                            StructVideo _item=datas.get(i);
                            if(!_item.check)continue;
                            long _flength=0;
                            if(_item.suffix.equals("M3U8")){
                                File _f = new File(_item.path);
                                if(_f.exists()) {
                                    File[] _fs = _f.listFiles();
                                    for(int n=0;n<_fs.length;++n){
                                        File _ts=_fs[n];
                                        if(_ts.exists()) {
                                            _flength += _ts.length();
                                            _ts.delete();
                                        }
                                    }
                                    _f.delete();
                                }
                            }else {
                                File _f = new File(_item.path);
                                _flength = _f.length();
                                if (_f.exists() && _f.delete()) {
                                    CL.CLOGI("delete file success");
                                    _f = new File(Global.Dir_thum, _item.name);
                                    if (_f.exists() && _f.delete()) {
                                        CL.CLOGI("delete thum success");
                                    }
                                }
                            }
                            --data.files_size;
                            data.length-=_flength;
                            if(data.length<0)data.length=0;
                            recycle_item(_item);
                            datas.remove(_item);
                            data.dls.remove(_item.item);
                            Server.Delete_Download(_item.item);
                            --i;
                        }
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                Server.Force_Update_DL();
                                if(cber_update!=null)cber_update.on_callback();
                                MusicManager _mm=Server.Share_Music();
                                if(_mm!=null)_mm.force_update();
                            }
                        });

                    }catch (Exception ex){
                        CL.CLOGI("delete error:"+ex.toString());
                    }finally {
                        flipper.handler.post(new Runnable() {
                            @Override
                            public void run() {
                                if(cber_update!=null)cber_update.on_callback();
                                _waiter.dismiss();
                                editor=false;
                                del_number=0;
                                btn_del.setVisibility(View.GONE);
                                btn_del.deformation(false);
                                adapter.notifyDataSetChanged();
                            }
                        });
                    }
                }
            }.start();
        }
    };


    private View.OnClickListener listener_dialog_rename=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(dialog_menu!=null && dialog_menu.isShowing())dialog_menu.dismiss();
            final StructVideo _item=((VideoItem)view_last_long_click).data;
            CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                @Override
                public boolean on_callback_success(String obj, String msg) {
                    if(obj.equals(_item.name))return false;
                    Server.Update_Download_Name(_item.item,obj);
                    _item.item.name=obj;
                    _item.name=obj;
                    ((VideoItem) view_last_long_click).tv_name.setText(obj);
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_update_info,Global.Action_rename,
                            _item.item.ident_md5,obj);
                    return false;
                }

                @Override
                public void on_callback_fail(int code, String msg) {

                }
            }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                    .show(cc.getResources().getString(R.string.rename),_item.name,null);
        }
    };
    private View view_last_long_click;
    private View.OnClickListener listener_dialog_check=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(dialog_menu!=null && dialog_menu.isShowing())dialog_menu.dismiss();
            editor=true;
            btn_del.setVisibility(View.VISIBLE);
            btn_del.startAnimation(anim_scale);
            if (btn_export != null) {
                btn_export.setVisibility(View.VISIBLE);
                btn_export.startAnimation(anim_scale);
            }
            listener_click.onClick(view_last_long_click);
            adapter.notifyDataSetChanged();
        }
    };



    private class StructVideo{
        public Data.StructDLItem item;

        public String name;
        public String suffix;
        public String length,duration;
        public String path;

        public Bitmap bitmap_thumb;
        public boolean check=false;

        public StructVideo(String name,String path,String suffix,String length,String duration,Data.StructDLItem item){
            this.name=name;
            this.path=path;
            this.suffix=suffix;
            this.length=length;
            this.duration=duration;
            this.item=item;
        }
    }
    private class AdapterForVideo extends BaseAdapter {

        @Override
        public int getCount() {
            if(data==null || data.dls==null)return 0;
            return data.dls.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new VideoItem(cc);
            VideoItem _v=(VideoItem)cv;
            //_v.set_basic_data(datas.get(position));
            try {
                if (datas != null) {
                    StructVideo value = datas.get(position);
                    _v.set_basic_data(value);
                }

            } catch (IndexOutOfBoundsException e) {
                if (datas.size() > 0)
                    _v.set_basic_data(datas.get(0));
                else
                    return null;
            }
            return cv;
        }
    }

    private View.OnClickListener listener_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(editor){
                if(v instanceof VideoItem){
                    VideoItem _vv=(VideoItem)v;
                    _vv.data.check=!_vv.data.check;
                    if(_vv.data.check) {
                        _vv.iv_check.setBackgroundResource(R.mipmap.comm_select_2);
                        ++del_number;
                    }
                    else {
                        _vv.iv_check.setBackgroundResource(R.mipmap.comm_select_1);
                        --del_number;
                        if(del_number==0){
                            editor=false;
                            btn_del.deformation(false);
                            btn_del.setVisibility(View.GONE);
                            if (btn_export != null)
                                btn_export.setVisibility(View.GONE);
                            for(int i=0;i<data.dls.size();++i){
                                data.dls.get(i).selected=false;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                    btn_del.set_number(del_number);
                }
            }else{
                //open
                if(v instanceof VideoItem){
                    VideoItem _vv=(VideoItem)v;
                    if(CL.Do_Once()){
                        if(_vv.data.suffix.equals("M3U8")){
                            Intent _intent=new Intent();
                            _intent.setClass(cc, LookVideo.class);
                            _intent.putExtra("m3u8",true);
                            _intent.putExtra("name",_vv.data.name);
                            _intent.putExtra("path","http://127.0.0.1:8200/m3u8/"+_vv.data.path.substring(_vv.data.path.lastIndexOf('/')+1)+"/plist");
                            cc.startActivity(_intent);


                  //          CLTools.playFullSrceenMedia(cc, "http://127.0.0.1:8200/m3u8/"+_vv.data.path.substring(_vv.data.path.lastIndexOf('/')+1)+"/plist");
                        }else {
//                            Intent _intent = new Intent();
//                            _intent.setClass(cc, LookVideo.class);
//                            _intent.putExtra("name", _vv.data.name);
//                            _intent.putExtra("path", _vv.data.path);
//                            cc.startActivity(_intent);
                            CLTools.playFullSrceenMedia(cc, _vv.data.path);
                        }
                    }
                }
            }
        }
    };
    private View.OnLongClickListener listener_click_long=new OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            if(editor){
                editor=false;
                del_number=0;
                btn_del.deformation(false);
                btn_del.setVisibility(View.GONE);
                if (btn_export != null)
                    btn_export.setVisibility(View.GONE);
                for(int i=0;i<data.dls.size();++i){
                    data.dls.get(i).selected=false;
                }
                adapter.notifyDataSetChanged();
            }else{
                dialog_menu.show();
                view_last_long_click=v;
            }
            return true;
        }
    };


    private class VideoItem extends FrameLayout {

        private StructVideo data;
        private ImageView iv_check;
        private ImageView iv_thumb;
        private TextView tv_name,tv_type,tv_length,tv_duration;

        public VideoItem(Context context) {
            super(context);
            this.setClickable(true);
            this.setLongClickable(true);
            this.setLayoutParams(new AbsListView.LayoutParams(CL.MP,CL.WC));
            this.setOnClickListener(listener_click);
            this.setOnLongClickListener(listener_click_long);

            iv_thumb=CLController.Get_ImageView(cc,CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL),null,null);
            iv_thumb.setBackgroundColor(Color.GRAY);
            iv_thumb.setScaleType(ImageView.ScaleType.CENTER_CROP);
            this.addView(iv_thumb);


            tv_name=CLController.Get_TextView(cc, CL.Get_FLLP(CL.MP,CL.WC,Gravity.TOP|Gravity.LEFT),"",Color.WHITE,12,null);
            tv_name.setMaxLines(2);
            tv_name.setEllipsize(TextUtils.TruncateAt.END);
            tv_name.setBackgroundColor(0xaa000000);
            tv_name.setPadding(CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3),CL.DIP2PX_INT(3));
            this.addView(tv_name);


            LinearLayout _ll_content=CLController.Get_LinearLayout(cc,
                    CL.Get_FLLP(CL.MP,CL.WC,Gravity.BOTTOM),LinearLayout.HORIZONTAL,null);
            _ll_content.setPadding(CL.DIP2PX_INT(4),CL.DIP2PX_INT(3),CL.DIP2PX_INT(4),CL.DIP2PX_INT(3));
            _ll_content.setGravity(Gravity.CENTER_VERTICAL);
            _ll_content.setBackgroundColor(0xbb000000);
            this.addView(_ll_content);

            tv_type=CLController.Get_TextView(cc,CL.Get_LP(CL.WC,CL.WC),"", Color.WHITE,12,null);
            _ll_content.addView(tv_type);

            _ll_content.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(2,CL.MP,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6),0),0xff000000));

            tv_length=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),"", Color.WHITE,12,null);
            _ll_content.addView(tv_length);

            _ll_content.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(2,CL.MP,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6),0),0xff000000));

            tv_duration=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),"", Color.WHITE,12,null);
            _ll_content.addView(tv_duration);


            iv_check=new ImageView(context);
            iv_check.setLayoutParams(CL.Get_FLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(8),CL.DIP2PX_INT(8)));
            this.addView(iv_check);
        }
        public void set_basic_data(StructVideo d){
            this.data=d;

            AbsListView.LayoutParams _lp=(AbsListView.LayoutParams) this.getLayoutParams();
            _lp.width=CL.MP;
            _lp.height=grid_width_height;
            this.setLayoutParams(_lp);

            if(editor){
                iv_check.setVisibility(View.VISIBLE);
                if(this.data.check)iv_check.setBackgroundResource(R.mipmap.comm_select_2);
                else iv_check.setBackgroundResource(R.mipmap.comm_select_1);
            }
            else iv_check.setVisibility(View.GONE);

            tv_name.setText(this.data.name);
            tv_type.setText(this.data.suffix);
            tv_length.setText(this.data.length);
            tv_duration.setText(this.data.duration);

            iv_thumb.setImageBitmap(null);
            if(this.data.bitmap_thumb!=null){
                iv_thumb.setImageBitmap(this.data.bitmap_thumb);
            }else{
                if(this.data.path!=null && this.data.name!=null && Global.Dir_thum!=null){
                    loader.go_add_item(this.data.path, Global.Dir_thum.getAbsolutePath(),this.data,listener_loader);
                }
            }
        }
    }
}
