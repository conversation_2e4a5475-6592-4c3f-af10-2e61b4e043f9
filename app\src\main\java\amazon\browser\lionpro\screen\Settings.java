package amazon.browser.lionpro.screen;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.net.Uri;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.CommonBackButton;
import amazon.browser.lionpro.util.MenuBus;

import lion.CL;
import lion.CLActivity;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLInputer;
import lion.widget.CLFlipper;
import lion.widget.GuideNovice;
import lion.widget.IosCheckBox;

/**
 * Created by leron on 2016/8/5.
 */
public class Settings extends LinearLayout{

    private CLActivity cc;
    private CLFlipper flipper;
    private CLCallback.CB_Activity cber_activity;
    private View rpMenu;

    private MenuBus menuBus = new MenuBus() {
        @Override
        public void onActionSettingActivity(boolean show) {
//            if (Setting.Share_Setting().get_redpoint() == 1)
//                rpMenu.setVisibility(show ? VISIBLE : GONE);
        }
    };

    public Settings(CLActivity context, CLFlipper f, CLCallback.CB_Activity cber) {
        super(context);
        this.cc=context;
        this.flipper=f;
        this.cber_activity=cber;

        this.setOrientation(LinearLayout.VERTICAL);

        FrameLayout fl_header= CLController.Get_FrameLayout(cc, CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)), Color.TRANSPARENT,null);
        this.addView(fl_header);
        fl_header.addView(new CommonBackButton(cc, new CLCallback.CB() {
            @Override
            public void on_callback() {
                if(cber_activity!=null)cber_activity.on_close();
            }
        }),CL.Get_FLLP(CL.WC,CL.WC, Gravity.LEFT|Gravity.CENTER_VERTICAL));
        fl_header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                cc.getResources().getText(R.string.settings).toString(),0xffd0d0d0,18,null));

     /*   ImageView btn_ad = CLController.Get_ImageView(cc, CL.Get_FLLP(CL.DIP2PX_INT(60), CL.MP, Gravity.RIGHT), null, listener_ad);
        btn_ad.setImageDrawable(CL.Get_StateList_Drawable(cc, R.mipmap.money, R.mipmap.money_press));
        btn_ad.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        fl_header.addView(btn_ad);
        //if(!Setting.Share_Setting().get_outline_switch_ad() && !Global.Switch_AD) {
        if (Setting.Share_Setting().get_outline_switch_ad() || Global.Switch_AD) {
            if (Setting.Share_Setting().get_subscription_flag()) {
                btn_ad.setVisibility(View.INVISIBLE);
            }
        } else {
            btn_ad.setVisibility(View.INVISIBLE);
        }
*/
      //  this.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,2),0xff444444));



        init_UI();


    }

    private View.OnClickListener listener_ad=new OnClickListener() {
        @Override
        public void onClick(View v) {
//            Global.Show_Interstitial_IM(cc, new CLCallback.CB() {
//                @Override
//                public void on_callback() {
//                }
//            });
        }
    };

    private void init_UI(){
//        View _v1=Global.Get_Banner(cc, CL.Get_LLLP(CL.MP,CL.WC), null);
//        if(_v1!=null)this.addView(_v1);
        ScrollView _sv=new ScrollView(cc);
        //_sv.setLayoutParams(CL.Get_LP_MM());
        _sv.setLayoutParams(CL.Get_LLLP(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, 1.0f));

        this.addView(_sv);

        LinearLayout _ll=new LinearLayout(cc);
        _ll.setOrientation(LinearLayout.VERTICAL);
        _ll.setClickable(true);
        _sv.addView(_ll);
       // _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,0,CL.DIP2PX_INT(18),0,0),0xff323232));
        _ll.addView(get_main_page());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_searcher());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_UA());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_full_screen());
     //   _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_night());
    //    _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_fullscreen());
    //    _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));

//        _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
//        View _v=Global.Get_Banner(cc, CL.Get_LLLP(CL.MP,CL.WC), null);
//
//        _ll.addView(_v);

        /*
        int count = Setting.Share_Setting().get_app_run_count();
        long run_time = Setting.Share_Setting().get_app_run_time();
        if(Setting.Share_Setting().get_tip_update()==1 && count > Setting.Share_Setting().get_update_count()
                && run_time > (Setting.Share_Setting().get_server_config_run_time()+300)) {
            _ll.addView(get_update());
            _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        }
*/
        _ll.addView(get_download_settings());
       // _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));

        //_ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,0,CL.DIP2PX_INT(18),0,0),0xff323232));
        _ll.addView(get_about());
      //  _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_use_guide());
     //   _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LLLP(CL.MP,1,CL.DIP2PX_INT(15),0,0,0),0xff323232));
        _ll.addView(get_privacy_policy());
     //   _ll.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff323232));

        if (Setting.Share_Setting().get_complete_first_download()) {
            LinearLayout bottom =new LinearLayout(cc);
            //dl_content.setLayoutParams(CL.Get_LLLP(CL.MP,CL.MP,0,0,CL.DIP2PX_INT(12), CL.DIP2PX_INT(12)));
            bottom.setOrientation(LinearLayout.VERTICAL);
            bottom.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC));
            this.addView(bottom);

            int pos = Setting.Share_Setting().get_ads_pos();
            int banner_type = Setting.Share_Setting().get_banner_type();
            Global.Get_banner(cc, bottom, pos == 1 ? 0 : 1, banner_type, null);
        }
    }

    private LinearLayout get_main_page(){
        String _url= Setting.Share_Setting().get_main_page();
        if(_url.equals(""))_url=cc.getResources().getString(R.string.blank_page);
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setClickable(true);
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                String _url= Setting.Share_Setting().get_main_page();
                final boolean _is_blank_page=_url.equals("")?true:false;
                if(_url.equals(""))_url=cc.getResources().getString(R.string.blank_page);
                final String _url2=_url;
                CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                    @Override
                    public boolean on_callback_success(String obj, String msg) {
                        if(obj!=null)obj=obj.replace("\n","").trim();
                        if(obj==null || obj.isEmpty()){
                            Setting.Share_Setting().set_main_page(null);
                            Global.Process_Main_Page_Url(null);
                            ((TextView)(_ll.getChildAt(1))).setText(cc.getResources().getString(R.string.blank_page));
                        }else{
                            Setting.Share_Setting().set_main_page(obj);
                            Global.Process_Main_Page_Url(obj);
                            ((TextView)(_ll.getChildAt(1))).setText(obj);
                        }
                        return false;
                    }

                    @Override
                    public void on_callback_fail(int code, String msg) {

                    }
                }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                        .show(cc.getString(R.string.main_page),_is_blank_page?"":_url2);
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.main_page),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        TextView _tv=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                _url,Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(_tv);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);
        return _ll;
    }

    private LinearLayout get_searcher(){
        int _s= Setting.Share_Setting().get_searcher();
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setClickable(true);

        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.setting_searcher),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        final TextView _tv=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                (_s==101?cc.getResources().getString(R.string.setting_search_google):cc.getResources().getString(R.string.setting_search_baidu)),
                Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(_tv);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);

        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                show_menu_searcher(new CLCallback.CB(){
                    @Override
                    public void on_callback() {
                        int _ss=Setting.Share_Setting().get_searcher();
                        _tv.setText((_ss==101?cc.getResources().getString(R.string.setting_search_google):cc.getResources().getString(R.string.setting_search_baidu)));
                    }
                });
            }
        });
        return _ll;
    }

    private LinearLayout get_UA(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setClickable(true);

        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.setting_UA),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        int _s= Setting.Share_Setting().get_UA();
        String _ua=cc.getResources().getString(R.string.setting_UA_android);
        if(_s==102)_ua=cc.getResources().getString(R.string.setting_UA_desktop);
        else if(_s==103)_ua=cc.getResources().getString(R.string.setting_UA_iphone);
        final TextView _tv=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                _ua,Color.WHITE,14,null);
        _tv.setGravity(Gravity.RIGHT);
        _ll.addView(_tv);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);

        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                show_menu_UA(new CLCallback.CB(){
                    @Override
                    public void on_callback() {
                        CLBus.Share_Instance().send_msg_immediate(Global.Group_Change_UA,Global.Action_change_UA);
                        int _ss= Setting.Share_Setting().get_UA();
                        String _uaa=cc.getResources().getString(R.string.setting_UA_android);
                        if(_ss==102)_uaa=cc.getResources().getString(R.string.setting_UA_desktop);
                        else if(_ss==103)_uaa=cc.getResources().getString(R.string.setting_UA_iphone);
                        _tv.setText(_uaa);
                    }
                });
            }
        });
        return _ll;
    }

    private LinearLayout get_full_screen(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),cc.getResources().getString(R.string.full_screen_brower),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        final IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        _checker.set_check(Setting.Share_Setting().get_horizontal_full_screen());
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                Setting.Share_Setting().set_horizontal_full_screen(check);
            }
        });
        _ll.addView(_checker);
        return _ll;
    }


    private LinearLayout get_night(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),cc.getResources().getString(R.string.setting_night_mode),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        final IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        _checker.set_check(Setting.Share_Setting().get_only_wifi());
        boolean night_mode = Setting.Share_Setting().get_night_mode();
        _checker.set_check(night_mode);
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                //Setting.Share_Setting().set_only_wifi(check);

//                if (Build.VERSION.SDK_INT >= 23) {
//                    if (android.provider.Settings.canDrawOverlays(cc)) {
//                        CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui,Global.Action_night_mode, Boolean.valueOf(check));
//                    } else {
//                        Intent intent = new Intent(android.provider.Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
//                        cc.startActivity(intent);
//                        _checker.set_check(false);
//                    }
//                } else {
//                    CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui,Global.Action_night_mode, Boolean.valueOf(check));
//                }

                CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui,Global.Action_night_mode, Boolean.valueOf(check));

            }
        });
        _ll.addView(_checker);
        return _ll;
    }


    private LinearLayout get_fullscreen(){
        LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,null);
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),cc.getResources().getString(R.string.full_screen_mode),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);
        final IosCheckBox _checker=new IosCheckBox(cc,null);
        _checker.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(46),CL.DIP2PX_INT(26),0,0,CL.DIP2PX_INT(15),0));
        _checker.set_check(Setting.Share_Setting().get_only_wifi());
        boolean night_mode = Setting.Share_Setting().get_fullscreen_mode();
        _checker.set_check(night_mode);
        _checker.set_check_listener(new IosCheckBox.CBCheckEvent() {
            @Override
            public void on_change(IosCheckBox box, boolean check) {
                CLBus.Share_Instance().send_msg_immediate(Global.Group_main_ui,Global.Action_fullscreen, Boolean.valueOf(check));
            }
        });
        _ll.addView(_checker);
        return _ll;
    }



    private LinearLayout get_download_settings(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setClickable(true);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                flipper.go_next(new DownloadSettings(cc,flipper, null));
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getString(R.string.str_cache_config),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);

        rpMenu = new View(cc);
        rpMenu.setId(R.id.view_red_point);
        int wh = (int)CL.DIP2PX(8);
        rpMenu.setLayoutParams(CL.Get_FLLP(wh, wh, Gravity.CENTER,
                (int)CL.DIP2PX(12), -(int)CL.DIP2PX(12), 0, 0));
        rpMenu.setVisibility(View.GONE);
        rpMenu.setBackgroundResource(R.drawable.red_point);
        _ll.addView(rpMenu);

//        int count = Setting.Share_Setting().get_click_redpoint();
//
//        if (count == 0) {
//            rpMenu.setVisibility(View.VISIBLE);
//
//        } else if (count >= Setting.Share_Setting().get_tip_count()) {
//            rpMenu.setVisibility(View.VISIBLE);
//        }
        return _ll;
    }


    private LinearLayout get_update() {
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setClickable(true);
      //  _ll.setBackgroundColor(0xff505a66);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    cc.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id="+Setting.Share_Setting().get_update_packagename())));
                    MenuBus.ShowMainMenuIconRedPoint(cc, false);
                    MenuBus.ShowMainMenuSettingRedPoint(cc, false);
                    MenuBus.ShowSettingActivityRedPoint(cc, false);

//                    int count = Setting.Share_Setting().get_click_redpoint();
//                    if (count >= 5) {
//                        rpMenu.setVisibility(View.VISIBLE);
//                    } else {
//                        rpMenu.setVisibility(View.GONE);
//                    }

                    Setting.Share_Setting().set_click_redpoint(1);
                    if (rpMenu != null)
                     rpMenu.setVisibility(View.GONE);
                } catch (android.content.ActivityNotFoundException anfe) {
                }
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.str_setting_update),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);

        rpMenu = new View(cc);
        rpMenu.setId(R.id.view_red_point);
        int wh = (int)CL.DIP2PX(8);
        rpMenu.setLayoutParams(CL.Get_FLLP(wh, wh, Gravity.CENTER,
                (int)CL.DIP2PX(12), -(int)CL.DIP2PX(12), 0, 0));
        rpMenu.setVisibility(View.GONE);
        rpMenu.setBackgroundResource(R.drawable.red_point);
        _ll.addView(rpMenu);


        int count = Setting.Share_Setting().get_click_redpoint();

        if (count == 0) {
            rpMenu.setVisibility(View.VISIBLE);

        } else if (count >= Setting.Share_Setting().get_tip_count()) {
            rpMenu.setVisibility(View.VISIBLE);
        }
        return _ll;
    }



    private LinearLayout get_about(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setClickable(true);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                flipper.go_next(new About(cc,flipper));
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.about),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);
        return _ll;
    }


    private LinearLayout get_use_guide(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setClickable(true);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                //flipper.go_next(new About(cc,flipper));
                GuideNovice dialog = new GuideNovice();
                dialog.show(cc.getSupportFragmentManager(), "guide");
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.str_guide_use),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);
        return _ll;
    }

    private LinearLayout get_privacy_policy(){
        final LinearLayout _ll=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)), LinearLayout.HORIZONTAL, null);
        _ll.setClickable(true);
        _ll.setBackgroundColor(this.getResources().getColor(R.color.bg_main));
        _ll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                flipper.go_next(new PrivacyPolicy(cc,flipper));
            }
        });
        _ll.setGravity(Gravity.CENTER_VERTICAL);
        TextView _tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(15),0,0,0),
                cc.getResources().getString(R.string.Privacy_Policy),
                Color.WHITE,14,null);
        _ll.addView(_tv_title);

        ImageView _iv_arrow=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(15),0),
                CL.Get_StateList_Drawable(cc,R.mipmap.toolbar_forward,R.mipmap.toolbar_forward2),null);
        _iv_arrow.setTouchDelegate(_ll.getTouchDelegate());
        _ll.addView(_iv_arrow);
        return _ll;
    }

    private Drawable get_bg(){
        RoundRectShape _shape=new RoundRectShape(new float[]{12,12,12,12,12,12,12,12}, null, null);
        ShapeDrawable _drawable=new ShapeDrawable(_shape);
        _drawable.getPaint().setColor(0xffffffff);
        _drawable.getPaint().setStyle(Paint.Style.FILL);
        int _pad=CL.DIP2PX_INT(6);
        _drawable.setPadding(0, _pad, 0, _pad);
        return _drawable;
    }
    private TextView get_button(Context cc,String name,final View.OnClickListener listener){
        TextView _btn=CLController.Get_Button(cc, CL.Get_LP(CL.MP, CL.WC), name, 0xff007aff,
                16,
                CL.Get_StateList_Drawable(new ColorDrawable(0xffffffff), new ColorDrawable(0xffe0e0e0)),
                listener);
        _btn.setPadding(0,CL.DIP2PX_INT(10),0,CL.DIP2PX_INT(10));
        _btn.setGravity(Gravity.CENTER);
        return _btn;
    }

    private CLDialog dialog_searcher;
    private View dialog_content_searcher;
    private void show_menu_searcher(final CLCallback.CB cber){
        if(dialog_content_searcher ==null){
            LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP_WW(),LinearLayout.VERTICAL,null);
            _ll.setPadding(CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),0);
            _ll.setClickable(true);

            LinearLayout _ll_btns=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.VERTICAL,null);
            _ll_btns.setBackground(get_bg());
            _ll_btns.setGravity(Gravity.CENTER);
            _ll.addView(_ll_btns);

            _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.setting_search_google), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_searcher.dismiss();
                    Setting.Share_Setting().set_searcher(101);
                    if(cber!=null)cber.on_callback();
                }
            }));
            _ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
            _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.setting_search_baidu), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_searcher.dismiss();
                    Setting.Share_Setting().set_searcher(102);
                    if(cber!=null)cber.on_callback();
                }
            }));

            LinearLayout _ll_cancel=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12))
                    ,LinearLayout.VERTICAL,null);
            _ll_cancel.setBackground(get_bg());
            _ll_cancel.setGravity(Gravity.CENTER);
            _ll_cancel.setPadding(CL.DIP2PX_INT(6), 0, CL.DIP2PX_INT(6), 0);
            _ll.addView(_ll_cancel);
            TextView _btn_cancel=get_button(cc, cc.getResources().getString(R.string.cancel), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_searcher.dismiss();
                }
            });
            _btn_cancel.setTextColor(Color.RED);
            _btn_cancel.setPadding(0, 0, 0, 0);
            _ll_cancel.addView(_btn_cancel);
            dialog_content_searcher =_ll;
            dialog_searcher =CLDialog.Get_Dialog_Animation_From_Bottom(cc, dialog_content_searcher);
        }
        dialog_searcher.show();
    }

    private CLDialog dialog_UA;
    private View dialog_content_UA;
    private void show_menu_UA(final CLCallback.CB cber){
        if(dialog_content_UA ==null){
            LinearLayout _ll=CLController.Get_LinearLayout(cc,CL.Get_LP_WW(),LinearLayout.VERTICAL,null);
            _ll.setPadding(CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),0);
            _ll.setClickable(true);

            LinearLayout _ll_btns=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.VERTICAL,null);
            _ll_btns.setBackground(get_bg());
            _ll_btns.setGravity(Gravity.CENTER);
            _ll.addView(_ll_btns);

            _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.setting_UA_android), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_UA.dismiss();
                    Setting.Share_Setting().set_UA(101);
                    if(cber!=null)cber.on_callback();
                }
            }));
            _ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
            _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.setting_UA_desktop), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_UA.dismiss();
                    Setting.Share_Setting().set_UA(102);
                    if(cber!=null)cber.on_callback();
                }
            }));
            _ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
            _ll_btns.addView(get_button(cc, cc.getResources().getString(R.string.setting_UA_iphone), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_UA.dismiss();
                    Setting.Share_Setting().set_UA(103);
                    if(cber!=null)cber.on_callback();
                }
            }));

            LinearLayout _ll_cancel=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12))
                    ,LinearLayout.VERTICAL,null);
            _ll_cancel.setBackground(get_bg());
            _ll_cancel.setGravity(Gravity.CENTER);
            _ll_cancel.setPadding(CL.DIP2PX_INT(6), 0, CL.DIP2PX_INT(6), 0);
            _ll.addView(_ll_cancel);
            TextView _btn_cancel=get_button(cc, cc.getResources().getString(R.string.cancel), new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog_UA.dismiss();
                }
            });
            _btn_cancel.setTextColor(Color.RED);
            _btn_cancel.setPadding(0, 0, 0, 0);
            _ll_cancel.addView(_btn_cancel);
            dialog_content_UA =_ll;
            dialog_UA =CLDialog.Get_Dialog_Animation_From_Bottom(cc, dialog_content_UA);
        }
        dialog_UA.show();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        menuBus.register(cc);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        menuBus.unregister();
    }
}
