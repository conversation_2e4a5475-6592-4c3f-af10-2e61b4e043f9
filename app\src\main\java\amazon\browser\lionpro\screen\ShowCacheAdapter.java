package amazon.browser.lionpro.screen;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.rvlibrary.baseadapter_recyclerview.recyclerview.wrapper.HeaderAndFooterWrapper;

import java.util.concurrent.CopyOnWriteArrayList;


public class ShowCacheAdapter extends RecyclerView.Adapter<ShowCacheAdapter.ViewHolder> {
    private CopyOnWriteArrayList<CopyOnWriteArrayList<Data.StructDLItem>> mData;
    private Context mContext;
    private AdapterInterface.OnHolderItemClickListener holderClickListener;
    private CommonDownloader.Eventer dleventer;
    public ShowCacheAdapter(final Context context,
                            AdapterInterface.OnHolderItemClickListener itemClickListener,
                            CommonDownloader.Eventer eventer,
                            CopyOnWriteArrayList<CopyOnWriteArrayList<Data.StructDLItem>> mData) {
        mContext = context;
        this.mData = mData;
        this.holderClickListener = itemClickListener;
        this.dleventer = eventer;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View inflate = LayoutInflater.from(mContext).inflate(R.layout.vertical_recycler_view, parent, false);
        return new ViewHolder(inflate);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CopyOnWriteArrayList<Data.StructDLItem> subData = mData.get(position);
        holder.page.setLayoutManager(new LinearLayoutManager(mContext));
        DownloadAdapter adapter = new DownloadAdapter(mContext, holderClickListener, dleventer, subData);
        if (subData != null && subData.size() == 0) {
            HeaderAndFooterWrapper headerAndFootWapper = new HeaderAndFooterWrapper(adapter);
            View view =  LayoutInflater.from(mContext).inflate(R.layout.empty_view, holder.page, false);
            headerAndFootWapper.addHeaderView(view);
            TextView txt = (TextView) view.findViewById(R.id.title);
            if (position == 0) {
                txt.setText(mContext.getString(R.string.tip_no_confirm_dl));
            } else {
                txt.setText(mContext.getString(R.string.tip_no_dl));
            }
            ViewGroup.LayoutParams params = holder.page.getLayoutParams();
            holder.page.setAdapter(headerAndFootWapper);
        } else {
            holder.page.setAdapter(adapter);
        }
    }

    @Override
    public int getItemCount() {
        return mData.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private RecyclerView page;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            this.page = (RecyclerView)itemView;
        }
    }
}
