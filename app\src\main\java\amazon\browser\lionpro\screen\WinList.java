package amazon.browser.lionpro.screen;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaMetadataRetriever;
import android.media.ThumbnailUtils;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MusicManager;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.primary.LookVideo;
import amazon.browser.lionpro.views.DialogDLMusic;
import amazon.browser.lionpro.views.DialogDLOther;
import amazon.browser.lionpro.views.DialogDLVideo;
import amazon.browser.lionpro.views.Leida;

import java.io.File;
import java.io.FileOutputStream;
import java.util.concurrent.CopyOnWriteArrayList;

import lion.CL;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLToast;
import lion.CLTools;

/**
 * Created by leron on 2016/7/1.
 */
public class WinList extends LinearLayout{


    private Activity cc;
    private Handler handler;
    public WinList(Activity context, Leida leida) {
        super(context);
        this.cc=context;
        handler=new Handler();
        this.lei_da=leida;
        this.setFocusable(true);
        this.setFocusableInTouchMode(true);
//        RoundRectShape _shape=new RoundRectShape(new float[]{32,32,0,0,0,0,0,0},null,null);
//        ShapeDrawable _dwe_round=new ShapeDrawable(_shape);
//        _dwe_round.getPaint().setStyle(Paint.Style.FILL);
//        _dwe_round.getPaint().setColor(0xff2c2c2c);
//        this.setBackground(_dwe_round);
        this.setBackgroundColor(0xff2c2c2c);
        this.setClickable(true);
        this.setOrientation(LinearLayout.VERTICAL);
        this.setPadding(CL.DIP2PX_INT(2),0,0,0);
        init();
    }


    private Leida lei_da;
    private ImageView btn_clear;
    private ListView lv_list;
    private TextView tv_header,tv_footer;
    private AdapterForItem adapter;
    private CopyOnWriteArrayList<Data.StructDLItem> datas_server;
    private DialogDLVideo dialog_video;
    private DialogDLMusic dialog_music;
    private DialogDLOther dialog_other;
    private boolean discard=false;

    private void init(){
        FrameLayout _fl_header=new FrameLayout(cc);
        _fl_header.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(36)));
        this.addView(_fl_header);
        _fl_header.addView(CLController.Get_TextView(cc, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER),
                cc.getResources().getString(R.string.dl_list),0xffa1a1a1,16,null));
        _fl_header.addView(CLController.Get_TextView_Divider(cc,CL.Get_FLLP(CL.MP,1,Gravity.BOTTOM),0xff2f2f2f));
        btn_clear=CLController.Get_ImageView(cc,CL.Get_FLLP(CL.DIP2PX_INT(60),CL.MP,Gravity.RIGHT),null,listener_clear);
        btn_clear.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_clear_normal,R.mipmap.icon_clear_click));
        btn_clear.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        _fl_header.addView(btn_clear);


        tv_header=CLController.Get_TextView(cc,new AbsListView.LayoutParams(CL.MP,CL.WC),cc.getResources().getString(R.string.tip_no_confirm_dl),0xff505050,15,null);
        tv_header.setGravity(Gravity.CENTER);
        tv_header.setPadding(0,CL.DIP2PX_INT(9),0,CL.DIP2PX_INT(9));
        tv_footer=CLController.Get_TextView(cc,new AbsListView.LayoutParams(CL.MP,CL.WC),cc.getResources().getString(R.string.tip_no_dl),0xff505050,15,null);
        tv_footer.setGravity(Gravity.CENTER);
        tv_footer.setPadding(0,CL.DIP2PX_INT(9),0,CL.DIP2PX_INT(9));

        lv_list=new ListView(cc);
        lv_list.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        lv_list.setBackgroundColor(0xff222222);
        lv_list.setCacheColorHint(Color.TRANSPARENT);
        lv_list.setDivider(new ColorDrawable(0xff2f2f2f));
        lv_list.setDividerHeight(1);
        lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
        lv_list.addHeaderView(tv_header);
        lv_list.addFooterView(tv_footer);
        adapter=new AdapterForItem();
        lv_list.setAdapter(adapter);
        lv_list.removeHeaderView(tv_header);
        lv_list.removeFooterView(tv_footer);
        this.addView(lv_list);

        Server.Set_Listener(listener_server);

        dialog_video=new DialogDLVideo(cc);
        dialog_music=new DialogDLMusic(cc);
        dialog_other=new DialogDLOther(cc);
    }

    private OnClickListener listener_clear=new OnClickListener() {
        @Override
        public void onClick(View v) {
            CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_clear_completed), new CLCallback.CB_TF() {
                @Override
                public void on_callback_success() {
                    Server.clear_completed();
                }
                @Override
                public void on_callback_fail(int code, String msg) {}
            }).show();
        }
    };

    private CLBus.CBEventer listener_update_name=new CLBus.CBEventer() {
        @Override
        public void on_event(int action, Object... msgs) {
            if(action== Global.Action_rename){
                if(datas_server==null)return;
                String _ident=(String)msgs[0];
                String _name=(String)msgs[1];
                CL.CLOGI("rename ident:"+_ident+"  name:"+_name);
                for(int i=0;i<datas_server.size();++i){
                    Data.StructDLItem _item=datas_server.get(i);

                    if(_item.show_type==3 && _item.ident_md5.equals(_ident)){
                        _item.name=_name;
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                adapter.notifyDataSetChanged();
                            }
                        });
                        break;
                    }
                }
            }
        }
    };


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        discard=false;
        CLBus.Share_Instance().register(Global.Group_update_info,listener_update_name,Global.Action_rename);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        discard=true;
        CLBus.Share_Instance().unregister(Global.Group_update_info,listener_update_name);
    }

    private Server.Eventer listener_server=new Server.Eventer() {
        @Override
        public void on_update_data(CopyOnWriteArrayList<Data.StructDLItem> datas) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    datas_server=datas;

                    int _count_wait=0;
                    boolean _has_wait=false;
                    boolean _has_dl=false;
                    for(int i=0;i<datas_server.size();++i){
                        Data.StructDLItem _item=datas_server.get(i);
                        if(_item.show_type==1){
                            ++_count_wait;
                            if(!_has_wait)_has_wait=true;
                        }else if(_item.show_type==3){
                            if(!_has_dl)_has_dl=true;
                        }
                        if(_item.dler!=null && (_item.dler.get_listener()==null || _item.dler.get_listener()!=listener_downloader)){
                            _item.dler.set_listener(listener_downloader);
                        }
                    }
                    if(!_has_wait){
                        if(lv_list.getHeaderViewsCount()==0){
                            lv_list.addHeaderView(tv_header);
                        }
                    }else lv_list.removeHeaderView(tv_header);
                    if(!_has_dl){
                        if(lv_list.getFooterViewsCount()==0){
                            lv_list.addFooterView(tv_footer);
                        }
                        btn_clear.setVisibility(View.GONE);
                    }else {
                        btn_clear.setVisibility(View.VISIBLE);
                        lv_list.removeFooterView(tv_footer);
                    }

                    lei_da.update_count(_count_wait);
                    adapter.notifyDataSetChanged();
                }
            });
        }

        @Override
        public void on_update_downloading_data() {

        }
    };

    private CommonDownloader.Eventer listener_downloader=new CommonDownloader.Eventer() {
        @Override
        public void on_state_change(final Data.StructDLItem item, final int state) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    for(int i=0;!discard && i<lv_list.getChildCount();++i){
                        View _v=lv_list.getChildAt(i);
                        if(_v instanceof CTMItemDL){
                            CTMItemDL _view=(CTMItemDL)_v;
                            if(_view.data!=null && _view.data == item){
                                if(state==CommonDownloader.Eventer.State_Complete){
                                    item.downloaded=true;
                                    if(item.type_major == Data.Type_Video || item.type_major == Data.Type_Music){
                                        if(item.duration==null) {
                                            try {
                                                MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                                                retriever.setDataSource(item.path);
                                                String _ddd = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
                                                item.duration = CLTools.Get_Media_Duration(Integer.parseInt(_ddd));
                                                CL.CLOGI("duration:" + _ddd);
                                                File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                                if (!_thumb.exists()) {
                                                    Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path, MediaStore.Video.Thumbnails.MINI_KIND);
                                                    if (bitmap_thumb != null) {
                                                        bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                                    }
                                                }
                                            } catch (Exception ex) {
                                            }
                                        }
                                    }else if(item.type_major==Data.Type_M3U8){
                                        try {
                                            File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                            if (!_thumb.exists()) {
                                                Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path+"/m_1.ts", MediaStore.Video.Thumbnails.MINI_KIND);
                                                //Bitmap bitmap_thumb = ThumbnailUtils.createVideoThumbnail(item.path+"/1", MediaStore.Video.Thumbnails.MINI_KIND);
                                                if (bitmap_thumb != null) {
                                                    bitmap_thumb.compress(Bitmap.CompressFormat.JPEG, 80, new FileOutputStream(_thumb));
                                                }
                                            }
                                        } catch (Exception ex) {
                                            CL.CLOGE("m3u8 thum error:"+ex.toString(),ex);
                                        }
                                    }
                                    else if(item.type_major == Data.Type_APK){
                                        //生成
                                        //扫描apk
                                        try {
                                            File _thumb = new File(Global.Dir_thum, item.path.replace('/', '_'));
                                            if (!_thumb.exists()) {
                                                PackageManager _pm = cc.getPackageManager();
                                                PackageInfo _file_info = _pm.getPackageArchiveInfo(item.path, 0);
                                                if (_file_info != null) {
                                                    ApplicationInfo _app_info = _file_info.applicationInfo;
                                                    _app_info.sourceDir = item.path;
                                                    _app_info.publicSourceDir = item.path;
                                                    item.name=_app_info.loadLabel(_pm).toString();
                                                    if(item.name!=null)Server.Update_Download_Name(item,item.name);
                                                    Drawable _dwe = _app_info.loadIcon(_pm);
                                                    int w = _dwe.getIntrinsicWidth();
                                                    int h = _dwe.getIntrinsicHeight();
                                                    Bitmap.Config config = _dwe.getOpacity() != PixelFormat.OPAQUE ? Bitmap.Config.ARGB_8888 : Bitmap.Config.RGB_565;
                                                    Bitmap bitmap = Bitmap.createBitmap(w, h, config);
                                                    Canvas canvas = new Canvas(bitmap);
                                                    _dwe.setBounds(0, 0, w, h);
                                                    _dwe.draw(canvas);
                                                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, new FileOutputStream(_thumb));
                                                }
                                            }
                                        }catch (Exception ex){}
                                    }
                                    Server.Update_Download(item);
                                    Server.Share_FileManager().notify_download_complete(item);
                                }
                                _view.set_baisc_data(item);
                            }
                        }
                    }
                }
            });
        }
        @Override
        public void on_load_ratio(final Data.StructDLItem item, long dl_size, float ratio) {
            handler.post(new Runnable() {
                @Override
                public void run() {
                    for(int i=0;!discard && i<lv_list.getChildCount();++i){
                        View _v=lv_list.getChildAt(i);
                        if(_v instanceof CTMItemDL){
                            CTMItemDL _view=(CTMItemDL)_v;
                            if(_view.data!=null && _view.data == item){
                                _view.set_baisc_data(item);
                            }
                        }
                    }
                }
            });
        }

        @Override
        public void on_delete(final Data.StructDLItem item, final boolean success) {
            if(success){
                new Thread() {
                    @Override
                    public void run() {
                        super.run();
                        Server.Delete_Download(item);
                    }
                }.start();

                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        datas_server.remove(item);
                        adapter.notifyDataSetChanged();
                        if(item.type_major==Data.Type_Video || item.type_major==Data.Type_M3U8) {
                            if (dialog_video != null && dialog_video.isShowing() && dialog_video.get_current_data() == item) {
                                dialog_video.dismiss();
                            }
                        }else if(item.type_major==Data.Type_Music){
                            if (dialog_music != null && dialog_music.isShowing() && dialog_music.get_current_data() == item) {
                                dialog_music.dismiss();
                            }
                        }else{
                            if (dialog_other != null && dialog_other.isShowing() && dialog_other.get_current_data() == item) {
                                dialog_other.dismiss();
                            }
                        }
                    }
                });
            }
        }

        @Override
        public void on_error(Data.StructDLItem item, int code) {
            CLToast.Show(cc,"error:"+code+"  "+item.title,false);
        }
    };


    private class AdapterForItem extends BaseAdapter{
        @Override
        public int getCount() {
            if(datas_server==null)return 0;
            return datas_server.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public int getItemViewType(int position) {
            return datas_server.get(position).show_type-1;
        }

        @Override
        public int getViewTypeCount() {
            return 3;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            Data.StructDLItem _data=datas_server.get(position);
            if(_data.show_type==1){
                if(cv==null)cv=new CTMItemWait(cc);
                if(cv instanceof CTMItemWait) {
                    ((CTMItemWait) cv).set_baisc_data(_data);
                }else{
                    cv=new CTMItemWait(cc);
                    ((CTMItemWait) cv).set_baisc_data(_data);
                }
            }else if(_data.show_type==2){
                cv=CLController.Get_TextView_Divider(cc,new AbsListView.LayoutParams(CL.MP,CL.DIP2PX_INT(6)),0xff2c2c2c);
            }else if(_data.show_type==3){
                if(cv==null)cv=new CTMItemDL(cc);
                if(cv instanceof CTMItemDL) {
                    ((CTMItemDL) cv).set_baisc_data(_data);
                }else{
                    cv=new CTMItemDL(cc);
                    ((CTMItemDL) cv).set_baisc_data(_data);
                }
            }
            return cv;
        }
    }
    private class CTMItemWait extends LinearLayout{

        private Data.StructDLItem data;
        private ImageView iv_icon;
        private TextView tv_title,tv_length;
        private ImageView btn_del,btn_download, btn_play;

        private OnClickListener listener_btn_confirm_play=new OnClickListener() {
            @Override
            public void onClick(View v) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        //Server.Remove_Confirm_Downloader(data);
                        try {
                      /*      Intent intent = new Intent();
                            intent.addCategory(Intent.CATEGORY_DEFAULT);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            intent.setAction(Intent.ACTION_VIEW);
                            String _type= MimeTypeMap.getSingleton().getMimeTypeFromExtension(data.suffix);
                            _type = "video/mp4";
//                        CL.CLOGI("mime type:"+_type);
//                        CL.CLOGI("path:"+_vv.data.path);
                            intent.setDataAndType(Uri.fromFile(new File(data.url)), _type);
                            cc.startActivity(Intent.createChooser(intent,cc.getResources().getString(R.string.open)));
*/

                            CLTools.playFullSrceenMedia(cc, data.url);
                        }catch (Exception ex){
                            CL.CLOGE("open error:"+ex.toString(),ex);
                        }
                    }
                });
            }
        };

        private OnClickListener listener_btn_confirm_del=new OnClickListener() {
            @Override
            public void onClick(View v) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        Server.Remove_Confirm_Downloader(data);
                    }
                });
            }
        };
        private OnClickListener listener_btn_confirm_download=new OnClickListener() {
            @Override
            public void onClick(View v) {
             //   Global.Show_Interstitial(cc,null);

                if(Setting.Share_Setting().get_only_wifi()){
                    ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                    NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                    if (networkInfo == null) {
                        CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_network_error)).show();
                        return ;
                    }
                    int nType = networkInfo.getType();
                    if (nType != ConnectivityManager.TYPE_WIFI) {
                        CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                            @Override
                            public void on_callback_success() {
                                handler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        Server.Conversion_To_Downloader(data);
                                        data.dler.set_listener(listener_downloader);
                                    }
                                });
                            }
                            @Override
                            public void on_callback_fail(int code, String msg) {}
                        }).show();
                        return;
                    }
                }
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        Server.Conversion_To_Downloader(data);
                        data.dler.set_listener(listener_downloader);
                    }
                });
            }
        };

        public CTMItemWait(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);

            iv_icon=new ImageView(context);
            iv_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0));
            this.addView(iv_icon);

            tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC,0,0,CL.DIP2PX_INT(2),0),"",0xffaaaaaa,12,null);
            tv_title.getPaint().setFakeBoldText(true);
            this.addView(tv_title);

            tv_length=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC, CL.WC,1.0f),"",0xffaaaaaa,14,null);
            tv_length.setSingleLine();
            this.addView(tv_length);

            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));


            btn_play=new ImageView(context);
            btn_play.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(28),CL.DIP2PX_INT(28)));
            LayoutParams params = (LayoutParams)btn_play.getLayoutParams();
            params.setMargins(18, 0, 18, 0);

            btn_play.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_play_normal,R.mipmap.icon_play_press));
            btn_play.setScaleType(ImageView.ScaleType.FIT_CENTER);
            btn_play.setClickable(true);
            btn_play.setOnClickListener(listener_btn_confirm_play);
            this.addView(btn_play);

            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));

            btn_del=new ImageView(context);
            btn_del.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(44),CL.DIP2PX_INT(44)));
            btn_del.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_del_normal,R.mipmap.icon_del_click));
            btn_del.setScaleType(ImageView.ScaleType.FIT_CENTER);
            btn_del.setClickable(true);
            btn_del.setOnClickListener(listener_btn_confirm_del);
            this.addView(btn_del);

            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f));



            btn_download=new ImageView(context);
            btn_download.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(44),CL.DIP2PX_INT(44)));
            btn_download.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_download_normal,R.mipmap.icon_download_click));
            btn_download.setScaleType(ImageView.ScaleType.FIT_CENTER);
            btn_download.setClickable(true);
            btn_download.setOnClickListener(listener_btn_confirm_download);
            this.addView(btn_download);

//            this.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),CL.DIP2PX_INT(2),CL.DIP2PX_INT(6)),0xff2f2f2f));
        }
        public void set_baisc_data(Data.StructDLItem d){
            this.data=d;
            if(d.type_major==Data.Type_APK){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_apk_s);
                tv_title.setText("APK");
            }
            else if(d.type_major==Data.Type_Music){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_music_s);
                if(d.type_minor==Data.Type_Music_MP3)tv_title.setText("MP3");
                else if(d.type_minor==Data.Type_Music_OGG)tv_title.setText("OGG");
                else if(d.type_minor==Data.Type_Music_FLAC)tv_title.setText("FLAC");
                else if(d.type_minor==Data.Type_Music_WAV)tv_title.setText("WAV");
                else if(d.type_minor==Data.Type_Music_M4A)tv_title.setText("M4A");
                else tv_title.setText("???");
            }
            else if(d.type_major==Data.Type_Video){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                if(d.type_minor==Data.Type_Video_MP4)tv_title.setText("MP4");
                else if(d.type_minor==Data.Type_Video_3GP)tv_title.setText("3GP");
                else if(d.type_minor==Data.Type_Video_MKV)tv_title.setText("MKV");
                else if(d.type_minor==Data.Type_Video_WEBM)tv_title.setText("WEBM");
                else tv_title.setText("???");
            }
            else if(d.type_major==Data.Type_M3U8){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                tv_title.setText("M3U8");
            }
            else if(d.type_major==Data.Type_Doc){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_doc_s);
                CL.CLOGI("set suffix:"+d.suffix);
                tv_title.setText(d.suffix);
            }
            else {
                iv_icon.setBackgroundResource(R.mipmap.res_icon_other_s);
                tv_title.setText(d.suffix);
            }
            if(d.type_major!=Data.Type_M3U8)tv_length.setText(CLTools.Get_Capacity_Format(d.length));
            else tv_length.setText(data.name==null?data.title:data.name);
        }
    }
    private class CTMItemDL extends LinearLayout{

        private Data.StructDLItem data;
        private ImageView iv_icon;
        private TextView tv_title,tv_speed,tv_dl_size,tv_duration;
        private ImageView iv_btn;
        private TextView tv_div;


        private OnClickListener listener_this=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(data.type_major==Data.Type_Video){
                    if(!data.downloaded) {
                        dialog_video.show();
                        dialog_video.update_downloader(data);
                    }else{
                        //open
                        if(CL.Do_Once()){
//                            Intent _intent=new Intent();
//                            _intent.setClass(cc, LookVideo.class);
//                            _intent.putExtra("name",data.name==null?data.title:data.name);
//                            _intent.putExtra("path",data.path);
//                            cc.startActivity(_intent);
                            CLTools.playFullSrceenMedia(cc, data.path);
                        }
                    }
                }else if(data.type_major==Data.Type_M3U8){
                    if(!data.downloaded) {
                        dialog_video.show();
                        dialog_video.update_downloader(data);
                    }else{
                        //open
                        if(CL.Do_Once()){
                            Intent _intent=new Intent();
                            _intent.setClass(cc, LookVideo.class);
                            _intent.putExtra("m3u8",true);
                            _intent.putExtra("name",data.name==null?data.title:data.name);
                            _intent.putExtra("path","http://127.0.0.1:8200/m3u8/"+data.ident_md5+"/plist");
                            cc.startActivity(_intent);
                        }
                    }
                }else if(data.type_major==Data.Type_Music){
                    if(!data.downloaded) {
                        dialog_music.show();
                        dialog_music.update_downloader(data);
                    }else{
                        MusicManager _mm=Server.Share_Music();
                        if(_mm!=null){
                            _mm.play_music(data);
                        }
                    }
                }else if(data.type_major==Data.Type_APK){
                    if(!data.downloaded) {
                        dialog_other.show();
                        dialog_other.update_downloader(data);
                    }else {
                        if(!new File(data.path).exists()){
                            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                            return;
                        }
//                        Intent _intent = new Intent(Intent.ACTION_VIEW);
//                        _intent.setDataAndType(Uri.parse("file://" + data.path), "application/vnd.android.package-archive");
//                        _intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                        cc.startActivity(_intent);


                        CLTools.install(cc, data.path);


                    }
                }else{
                    if(!data.downloaded) {
                        dialog_other.show();
                        dialog_other.update_downloader(data);
                    }else{
                        if(!new File(data.path).exists()){
                            CLToast.Show(cc,cc.getResources().getString(R.string.tip_file_not_exists2),true);
                            return;
                        }
//                        try {
//                            Intent intent = new Intent();
//                            intent.addCategory(Intent.CATEGORY_DEFAULT);
//                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                            intent.setAction(Intent.ACTION_VIEW);
//                            String _type= MimeTypeMap.getSingleton().getMimeTypeFromExtension(data.suffix);
//                            intent.setDataAndType(Uri.fromFile(new File(data.path)), _type);
//                            cc.startActivity(Intent.createChooser(intent,cc.getResources().getString(R.string.open)));
//                        }catch (Exception ex){
//                            CL.CLOGE("open error:"+ex.toString(),ex);
//                        }
                        CLTools.playFullSrceenMedia(cc, data.url);
                    }
                }
            }


//            public static void installApkFile(Context context, String filePath) {
//                Intent intent = new Intent(Intent.ACTION_VIEW);
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//                    intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
//                    Uri contentUri = FileProvider.getUriForFile(context, "com.yuneec.android.saleelfin.fileprovider", new File(filePath));
//                    intent.setDataAndType(contentUri, "application/vnd.android.package-archive");
//                } else {
//                    intent.setDataAndType(Uri.fromFile(new File(filePath)), "application/vnd.android.package-archive");
//                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                }
//                context.startActivity(intent);
//            }

        };
        private OnClickListener listener_btn=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(data.dler!=null){
                    if(data.dler.get_status()==CommonDownloader.Eventer.State_Stop){
                        if(Setting.Share_Setting().get_only_wifi()){
                            ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                            if (networkInfo == null) {
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_network_error)).show();
                                return ;
                            }
                            int nType = networkInfo.getType();
                            if (nType != ConnectivityManager.TYPE_WIFI) {
                                CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                                    @Override
                                    public void on_callback_success() {
                                        handler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                data.dler.go();
                                            }
                                        });
                                    }
                                    @Override
                                    public void on_callback_fail(int code, String msg) {}
                                }).show();
                                return;
                            }
                        }
                        data.dler.go();
                    }
                    else if(data.dler.get_status()==CommonDownloader.Eventer.State_Start)data.dler.stop();
                }
            }
        };

        public CTMItemDL(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);
            this.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0x993d6f59)));
            this.setOnClickListener(listener_this);

            iv_icon=new ImageView(context);
            iv_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(26),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0));
            this.addView(iv_icon);

            tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.DIP2PX_INT(40), CL.WC,0,0,CL.DIP2PX_INT(2),0),"",0xffaaaaaa,12,null);
            tv_title.getPaint().setFakeBoldText(true);
            this.addView(tv_title);

            LinearLayout _ll_info=new LinearLayout(context);
            _ll_info.setOrientation(LinearLayout.VERTICAL);
            _ll_info.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,1.0f,0,CL.DIP2PX_INT(4),0,CL.DIP2PX_INT(4)));
            this.addView(_ll_info);

            tv_speed=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC, CL.WC),"0B/S",0xffaaaaaa,14,null);
            tv_speed.setSingleLine();
            tv_speed.setEllipsize(TextUtils.TruncateAt.END);
            _ll_info.addView(tv_speed);
            LinearLayout _ll_size=new LinearLayout(context);
            _ll_size.setOrientation(LinearLayout.HORIZONTAL);
            _ll_size.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC));
            _ll_info.addView(_ll_size);
            tv_dl_size=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC, CL.WC,1.0f),"",0xffaaaaaa,14,null);
            _ll_size.addView(tv_dl_size);
            tv_duration=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC, CL.WC,0,0,CL.DIP2PX_INT(8),0),"",0xffaaaaaa,14,null);
            _ll_size.addView(tv_duration);

            tv_div=CLController.Get_TextView_Divider(cc, CL.Get_LLLP(1,CL.MP,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),0xff2f2f2f);
            this.addView(tv_div);

            iv_btn=new ImageView(context);
            iv_btn.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(60),CL.DIP2PX_INT(44)));
            iv_btn.setScaleType(ImageView.ScaleType.FIT_CENTER);
            iv_btn.setClickable(true);
            iv_btn.setOnClickListener(listener_btn);
            this.addView(iv_btn);
        }

        public void set_baisc_data(Data.StructDLItem d){
            this.data=d;
            if(data.type_major==Data.Type_APK){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_apk_s);
                tv_title.setText("APK");
            }
            else if(data.type_major==Data.Type_Music){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_music_s);
                if(d.type_minor==Data.Type_Music_MP3)tv_title.setText("MP3");
                else if(d.type_minor==Data.Type_Music_OGG)tv_title.setText("OGG");
                else if(d.type_minor==Data.Type_Music_FLAC)tv_title.setText("FLAC");
                else if(d.type_minor==Data.Type_Music_WAV)tv_title.setText("WAV");
                else if(d.type_minor==Data.Type_Music_M4A)tv_title.setText("M4A");
                else tv_title.setText("???");
            }
            else if(data.type_major==Data.Type_Video){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                if(d.type_minor==Data.Type_Video_MP4)tv_title.setText("MP4");
                else if(d.type_minor==Data.Type_Video_3GP)tv_title.setText("3GP");
                else if(d.type_minor==Data.Type_Video_MKV)tv_title.setText("MKV");
                else if(d.type_minor==Data.Type_Video_WEBM)tv_title.setText("WEBM");
                else tv_title.setText("???");
            }
            else if(d.type_major==Data.Type_M3U8){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_video_s);
                tv_title.setText("M3U8");
            }
            else if(data.type_major==Data.Type_Doc){
                iv_icon.setBackgroundResource(R.mipmap.res_icon_doc_s);
                tv_title.setText(d.suffix);
            }
            else {
                iv_icon.setBackgroundResource(R.mipmap.res_icon_other_s);
                tv_title.setText(d.suffix);
            }

            if(this.data.dler!=null){
                if(this.data.type_major==Data.Type_M3U8){
                    tv_dl_size.setText(CLTools.Get_Capacity_Format(this.data.dler.get_total_size()));
                    if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Start)
                        tv_speed.setText(CLTools.Get_Capacity_Format(this.data.dler.get_speed()) + "/S");
                    else tv_speed.setText(this.data.name!=null?this.data.name:this.data.title);
                }else {
                    tv_dl_size.setText(CLTools.Get_Capacity_Format(this.data.dler.get_total_size()) + "/" + CLTools.Get_Capacity_Format(this.data.length));
                    if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Start)
                        tv_speed.setText(CLTools.Get_Capacity_Format(this.data.dler.get_speed()) + "/S");
                    else tv_speed.setText(this.data.name!=null?this.data.name:this.data.title);
                }
            }else {
                tv_dl_size.setText("");
                tv_speed.setText("");
            }
            tv_duration.setVisibility(View.GONE);
            iv_btn.setVisibility(View.VISIBLE);
            if(this.data.downloaded){
                tv_dl_size.setText(CLTools.Get_Capacity_Format(this.data.length));
                if(this.data.name!=null)tv_speed.setText(this.data.name);
                else tv_speed.setText(this.data.title);
                iv_btn.setVisibility(View.GONE);
                tv_duration.setVisibility(View.VISIBLE);
                if(this.data.duration!=null)tv_duration.setText(this.data.duration);
                else tv_duration.setText(null);
                tv_div.setVisibility(View.GONE);
            }else {
                if (this.data.dler != null) {
                    if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Start) {
                        iv_btn.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_dl2_normal,R.mipmap.icon_dl2_click));
                    } else if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Complete) {
                        iv_btn.setVisibility(View.GONE);
                    } else if (this.data.dler.get_status() == CommonDownloader.Eventer.State_Stop) {
                        iv_btn.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.icon_dl_normal,R.mipmap.icon_dl_click));
                    }
                }
                tv_div.setVisibility(View.VISIBLE);
            }
        }
    }
}
