package amazon.browser.lionpro.toys;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;


import amazon.browser.video.downloader.R;;
import lion.CL;
import lion.CLCallback;

/**
 * Created by leron on 2016/6/24.
 */
public class CommonBackButton extends LinearLayout{

    private ImageView iv_icon;
    private TextView tv_text;
    private CLCallback.CB cber;


    private View.OnTouchListener listener_touch=new OnTouchListener() {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if(event.getAction()==MotionEvent.ACTION_DOWN){
                if (tv_text != null)
                    tv_text.setTextColor(0xff59493f);
                iv_icon.setBackgroundResource(R.mipmap.back_click);
            }else if(event.getAction()==MotionEvent.ACTION_CANCEL||event.getAction()==MotionEvent.ACTION_UP){
                if (tv_text != null)
                    tv_text.setTextColor(Color.WHITE);
                iv_icon.setBackgroundResource(R.mipmap.back_normal);
            }
            return false;
        }
    };
    private View.OnClickListener listener_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(cber!=null)cber.on_callback();
        }
    };

    public void setCB(CLCallback.CB cb) {
        this.cber = cb;
    }

    public CommonBackButton(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.setOrientation(LinearLayout.HORIZONTAL);
        this.setGravity(Gravity.CENTER);
        this.setPadding(CL.DIP2PX_INT(20),CL.DIP2PX_INT(4),CL.DIP2PX_INT(0),CL.DIP2PX_INT(4));
        // this.setPadding(CL.DIP2PX_INT(20),0,0,0);
        this.setClickable(true);
        this.setOnTouchListener(listener_touch);
        this.setOnClickListener(listener_click);

        iv_icon=new ImageView(context);
        // iv_icon.setBackground(getResources().getDrawable(R.drawable.back_press_status));
        iv_icon.setBackgroundResource(R.mipmap.back_normal);
        iv_icon.setAdjustViewBounds(true);

        this.addView(iv_icon);
    }

    public CommonBackButton(Context context, CLCallback.CB cber_back) {
        super(context);
        this.cber=cber_back;
        this.setOrientation(LinearLayout.HORIZONTAL);
        this.setGravity(Gravity.CENTER_VERTICAL);
        this.setPadding(CL.DIP2PX_INT(12),0,0,0);
        this.setClickable(true);
        this.setOnTouchListener(listener_touch);
        this.setOnClickListener(listener_click);

        iv_icon=new ImageView(context);
        iv_icon.setBackgroundResource(R.mipmap.back_normal);
        this.addView(iv_icon);

        tv_text=new TextView(context);
        tv_text.setText(R.string.back);
        tv_text.setTextColor(Color.WHITE);
        tv_text.setTextSize(15);
        this.addView(tv_text);
    }
}
