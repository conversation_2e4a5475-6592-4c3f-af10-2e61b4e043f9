package amazon.browser.lionpro.toys;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lion.UrlUtils;

/**
 * Created by leron on 2016/4/12.
 */
public class Tools {


    public static String Get_Url_Host(String url){
        if (url == null)
            return null;
        try {
            URI _uri=new URI(url);
            String _host=_uri.getHost();
            return _host;
        } catch (URISyntaxException e) {
            return null;
        } catch (NullPointerException e) {
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public static String Fix_Url1(String inUrl) {
        int colon = inUrl.indexOf(':');
        boolean allLower = true;
        for (int index = 0; index < colon; index++) {
            char ch = inUrl.charAt(index);
            if (!Character.isLetter(ch)) {
                break;
            }
            allLower &= Character.isLowerCase(ch);
            if (index == colon - 1 && !allLower) {
                inUrl = inUrl.substring(0, colon).toLowerCase()
                        + inUrl.substring(colon);
            }
        }

        if (inUrl.startsWith("http://") || inUrl.startsWith("https://"))
            return inUrl;
        else if (inUrl.startsWith("http:") || inUrl.startsWith("https:")) {
            if (inUrl.startsWith("http:/") || inUrl.startsWith("https:/")) {
                inUrl = inUrl.replaceFirst("/", "//");
            } else inUrl = inUrl.replaceFirst(":", "://");
        }else{
            return "https://"+inUrl;
        }
        return inUrl;
    }

    public static String Fix_Url(String inUrl) {
        int colon = inUrl.indexOf(':');
        boolean allLower = true;
        for (int index = 0; index < colon; index++) {
            char ch = inUrl.charAt(index);
            if (!Character.isLetter(ch)) {
                break;
            }
            allLower &= Character.isLowerCase(ch);
            if (index == colon - 1 && !allLower) {
                inUrl = inUrl.substring(0, colon).toLowerCase()
                        + inUrl.substring(colon);
            }
        }

        if (inUrl.startsWith("http://") || inUrl.startsWith("https://"))
            return inUrl;
        else if (inUrl.startsWith("http:") || inUrl.startsWith("https:")) {
            if (inUrl.startsWith("http:/") || inUrl.startsWith("https:/")) {
                inUrl = inUrl.replaceFirst("/", "//");
            } else inUrl = inUrl.replaceFirst(":", "://");
        }else{
            return "http://"+inUrl;
        }
        return inUrl;
    }

    public static String Was_Web_Site(String str){
        /*
        if(str==null || str.length()==0)return null;
        str=str.trim().toLowerCase();
        if(str.startsWith(".")||!str.contains("."))return null;
        str=Fix_Url(str);
        String _host=Get_Url_Host(str);
        if(_host==null)return null;
        String[] _domains=new String[]{
                ".com",".cn",".net",".org",".gov",".edu",".int",".mil",".biz",".info",".tv",".pro",".name",
                ".museum",".coop",".aero",".cc",".sh",".me",".asia",".kim",".ad",".ae",".af",
                ".ag",".ai",".al",".am",".an",".ao",".aq",".ar",".as",".at",".au",".aw",".az",
                ".ba",".bb",".bd",".be",".bf",".bg",".bh",".bi",".bj",".bm",".bn",".bo",".br",".bs",".bt",".bv",".bw",
                ".by",".bz",".ca",".cc",".cf",".cg",".ch",".ci",".ck",".cl",".cm",".cn",".co",".cq",".cr",".cu",".cv",".cx",".cy",".cz",".de",".dj",
                ".dk",".dm",".do",".dz",".ec",".ee",".eg",".eh",".es",".et",".ev",".fi",".fj",".fk",".fm",".fo",".fr",".ga",".gb",".gd",
                ".ge",".gf",".gh",".gi",".gl",".gm",".gn",".gp",".gr",".gt",".gu",".gw",".gy",".hk",".hm",".hn",".hr",".ht",".hu",".id",".ie",".il",
                ".in",".io",".iq",".ir",".is",".it",".jm",".jo",".jp",".ke",".kg",".kh",".ki",".km",".kn",".kp",".kr",".kw",".ky",".kz",".la",
                ".lb",".lc",".li",".lk",".lr",".ls",".lt",".lu",".lv",".ly",".ma",".mc",".md",".mg",".mh",".ml",".mm",".mn",".mo",".mp",
                ".mq",".mr",".ms",".mt",".mv",".mw",".mx",".my",".mz",".na",".nc",".ne",".nf",".ng",".ni",".nl",".no",".np",".nr",".nt",".nu",".nz",
                ".om",".pa",".pe",".pf",".pg",".ph",".pk",".pl",".pm",".pn",".pr",".pt",".pw",".py",".qa",
                ".re",".ro",".ru",".rw",".sa",".sb",".sc",".sd",".se",".sg",".sh",".si",".sj",".sk",".sl",".sm",".sn",".so",".sr",".st",
                ".su",".sy",".sz",".tc",".td",".tf",".tg",".th",".tj",".tk",".tm",".tn",".to",".tp",".tr",".tt",".tw",".tz",".ua",".ug",
                ".uk",".us",".uy",".va",".vc",".ve",".vg",".vn",".vu",".wf",".ws",".ye",".yu",".za",".zm",".zr",".zw"
        };
        for(int i=0;i<_domains.length;++i){
            if(_host.endsWith(_domains[i]))return str;
        }
        Pattern _p= Pattern.compile("([0-9]|[0-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}");
        Matcher _m=_p.matcher(_host);
        if(_m.find()){
            return str;
        }
        return null;
        */
        return UrlUtils.smartUrlFilter(str, false, null);
    }


    public static String getVersion(Context context)//获取版本号
    {
        try {
            PackageInfo pi=context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return pi.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return "&&";
        }
    }

    public static String ReplaceBadFilename(String fileName) {
        String str = fileName;
        str = str.replace("\\", "");
        str = str.replace("/", "");
        str = str.replace(":", "");
        str = str.replace("*", "");
        str = str.replace("?", "");
        str = str.replace("\"", "");
        str = str.replace("<", "");
        str = str.replace(">", "");
        str = str.replace("|", "");
        str = str.replace(" ", "");
        return str;
    }

    // 正则检查文件名 return true:合法 false:不合法
    public static boolean CheckFileName(String name) {
        return name.matches("[^\\s\\\\/:\\*\\?\\\"<>\\|](\\x20|[^\\s\\\\/:\\*\\?\\\"<>\\|])*[^\\s\\\\/:\\*\\?\\\"<>\\|\\.]$");
    }

    // 修复文件名
    public static String FixFileName(String name) {
        Pattern pattern = Pattern.compile("[\\s\\\\/:\\*\\?\\\"<>\\|]");
        Matcher matcher = pattern.matcher(name);
        return matcher.replaceAll("");
    }

}
