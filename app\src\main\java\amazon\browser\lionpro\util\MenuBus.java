package amazon.browser.lionpro.util;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class MenuBus {

    private static final String ActionMainMenuICON = "action_main_menu_icon";
    private static final String ActionMainMenuSetting = "action_main_menu_setting";
    private static final String ActionSettingActivity = "action_setting_Activity";




    private static boolean showMainMenuIconRedPoint;
    public static void ShowMainMenuIconRedPoint(Context context, boolean show) {
        showMainMenuIconRedPoint = show;
        if (context != null) {
            Intent intent = new Intent(ActionMainMenuICON);
            intent.putExtra("switch", showMainMenuIconRedPoint);
            LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
        }
    }

    public static boolean IsShowMainMenuIconRedPoint() {
        return showMainMenuIconRedPoint;
    }



    private static boolean showMainMenuSettingRedPoint;
    public static void ShowMainMenuSettingRedPoint(Context context, boolean show) {
        showMainMenuSettingRedPoint = show;
        if (context != null) {
            Intent intent = new Intent(ActionMainMenuSetting);
            intent.putExtra("switch", showMainMenuSettingRedPoint);
            LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
        }
    }

    public static boolean IsShowMainMenuSettingRedPoint() {
        return showMainMenuSettingRedPoint;
    }


    private static boolean showSettingActivityRedPoint;
    public static void ShowSettingActivityRedPoint(Context context, boolean show) {
        showSettingActivityRedPoint = show;
        if (context != null) {
            Intent intent = new Intent(ActionSettingActivity);
            intent.putExtra("switch", showSettingActivityRedPoint);
            LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
        }
    }

    public static boolean IsShowSettingActivityRedPoint() {
        return showSettingActivityRedPoint;
    }

    private Context cc;
    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            switch (action) {
                case ActionMainMenuICON:
                    onActionMainMenuIcon(intent.getBooleanExtra("switch", false));
                    break;
                case ActionMainMenuSetting:
                    onActionMainMenuSetting(intent.getBooleanExtra("switch", false));
                    break;
                case ActionSettingActivity:
                    onActionSettingActivity(intent.getBooleanExtra("switch", false));
                    break;
            }
        }
    };

    public final void register(Context context) {
        if (cc == null) {
            cc = context;
            IntentFilter filter = new IntentFilter();
            filter.addAction(ActionMainMenuICON);
            filter.addAction(ActionMainMenuSetting);
            filter.addAction(ActionSettingActivity);
            LocalBroadcastManager.getInstance(cc).registerReceiver(receiver, filter);
        }
    }

    public final void unregister() {
        if (cc != null) {
            LocalBroadcastManager.getInstance(cc).unregisterReceiver(receiver);
            cc = null;
        }
    }

    public void onActionMainMenuIcon(boolean show) {

    }

    public void onActionMainMenuSetting(boolean show) {

    }

    public void onActionSettingActivity(boolean show) {

    }
}
