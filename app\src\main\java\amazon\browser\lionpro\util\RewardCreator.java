package amazon.browser.lionpro.util;

import android.content.Context;

import androidx.annotation.NonNull;

import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.primary.Global;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.OnUserEarnedRewardListener;
import com.google.android.gms.ads.RequestConfiguration;
import com.google.android.gms.ads.rewarded.RewardItem;
import com.google.android.gms.ads.rewarded.RewardedAd;
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback;

import org.jetbrains.annotations.NotNull;

import java.util.Arrays;
import java.util.List;

import lion.CL;
import lion.CLCallback;

public class RewardCreator  implements OnUserEarnedRewardListener {
    //reward
    public RewardedAd rewardedAd;
    private CLCallback.CB_TFO<Integer> mycb;
    public Boolean rewardedAdLoadFailed;
    private boolean bEarned = false;
    private int index = -1;
    private String ident = "";
    static public int AD_FULL_SCREEN                = 100;
    static public int AD_LOADED                     = 101;
    static public int AD_EARNED                     = 102;
    static public int AD_CLOSE_EARLY                = 103;
    static public int AD_LOADED_FAILED              = 104;
    static public int AD_LOADED_EARNED_SUCCESS      = 105;
    static public int AD_FULL_SCREEN_DISMISSED      = 106;

    static public int AD_REWARD_INITERTITIAL_FULL_SCREEN                     = 210;
    static public int AD_REWARD_INITERTITIAL_FULL_SCREEN_DISMISSED                      = 211;
    public String getIdent() {
        return ident;
    }

    public void setIdent(String ident) {
        this.ident = ident;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public boolean isEarned() {
        return bEarned;
    }
    @Override
    public void onUserEarnedReward(@NonNull @NotNull RewardItem rewardItem) {
        bEarned = true;
        if (mycb != null) {
            mycb.on_callback_success(AD_EARNED, ident);
        }
    }

    public static AdRequest GetAdRequest() {
        AdRequest req = null;
        if (Global.AD_Test) {//3782C414BB19D4729A4161E42F7ED907
            //req = new AdRequest.Builder().addTestDevice("33BE2250B43518CCDA7DE426D04EE231").build();
            List<String> testDeviceIds = Arrays.asList("DABE8D045C3EA23F5133CF880B1EDD56");
            RequestConfiguration configuration =
                    new RequestConfiguration.Builder().setTestDeviceIds(testDeviceIds).build();
            MobileAds.setRequestConfiguration(configuration);
            req=new AdRequest.Builder().build();
        } else {
            req=new AdRequest.Builder().build();
        }
        return req;
    }

    public void ReLoadReward(Context context, CLCallback.CB_TFO<Integer> cb) {
        rewardedAd = null;
        LoadReward(context, cb);
    }

    public void LoadReward(Context context, CLCallback.CB_TFO<Integer> cb) {
        if (!Setting.Share_Setting().get_complete_first_download() || Setting.Share_Setting().get_subscription_flag())
            return;
        if (rewardedAd == null) {
            final AdRequest adRequest = new AdRequest.Builder().build();//GetAdRequest();//new AdRequest.Builder().build();
            final RewardedAdListener rewardedAdListener = new RewardedAdListener();

            if (Global.AD_Test) {
                RewardedAd.load(context, "ca-app-pub-3940256099942544/5354046379", adRequest, rewardedAdListener);
            } else {
                RewardedAd.load(context, "ca-app-pub-3064461767247622/1277576354", adRequest, rewardedAdListener);
            }

            mycb = cb;
        }
    }

    private FullScreenContentCallback fullScreenContentCallback = new FullScreenContentCallback() {
        @Override
        public void onAdShowedFullScreenContent() {
            // Code to be invoked when the ad showed full screen content.
            if (mycb != null) {
                mycb.on_callback_success(AD_FULL_SCREEN, "");
            }
        }

        @Override
        public void onAdDismissedFullScreenContent() {
            // Code to be invoked when the ad dismissed full screen content.
            CL.CLOGI("Eddy onAdDismissedFullScreenContent");
            if (mycb != null) {
                if (!isEarned()) {
                    mycb.on_callback_fail(AD_CLOSE_EARLY, ident);
                } else {

                    mycb.on_callback_fail(AD_LOADED_EARNED_SUCCESS, "");
                }

            }
        }
    };

    private class RewardedAdListener extends RewardedAdLoadCallback {

        @Override
        public void onAdLoaded(@NonNull final RewardedAd rewardedAd) {
            super.onAdLoaded(rewardedAd);
            RewardCreator.this.rewardedAd = rewardedAd;
            rewardedAdLoadFailed = false;
            rewardedAd.setFullScreenContentCallback(fullScreenContentCallback);
            if (mycb != null) {
                mycb.on_callback_success(AD_LOADED, "");
            }
        }

        @Override
        public void onAdFailedToLoad(@NonNull final LoadAdError loadAdError) {
            super.onAdFailedToLoad(loadAdError);
            RewardCreator.this.rewardedAd = null;
            rewardedAdLoadFailed = true;
            if (mycb != null) {
                mycb.on_callback_fail(AD_LOADED_FAILED, null);
            }
        }

    }
}
