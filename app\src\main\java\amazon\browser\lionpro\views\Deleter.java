package amazon.browser.lionpro.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.View;

import amazon.browser.video.downloader.R;

import lion.CL;

/**
 * Created by leron on 2016/4/23.
 */
public class Deleter extends View{

    public interface Eventer{
        void on_icon_click(boolean expand);
        void on_cancel_click();
        void on_delete_click();
    }



    private Drawable dwe_icon,dwe_cancel,dwe_ok;
    private Paint paint;
    private boolean crt_state_expand=false;
    private boolean is_expand=false;
    private boolean is_expanding=false;
    private String del_number="0";

    private Eventer listener;

    public Deleter(Context context,Eventer listen) {
        super(context);

        this.listener=listen;

        paint=new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setTextAlign(Paint.Align.CENTER);
        paint.setTextSize(CL.SP2PX(13));
        dwe_icon=context.getResources().getDrawable(R.mipmap.icon_del);
        dwe_cancel=context.getResources().getDrawable(R.mipmap.icon_del_cancel);
        dwe_ok=context.getResources().getDrawable(R.mipmap.icon_del_ok);
    }

    public void set_number(int number){
        if(number<0)number=0;
        else if(number>999)number=999;
        this.del_number=String.valueOf(number);
        postInvalidate();
    }

    public void deformation(boolean state){
        if(crt_state_expand==state)return;
        is_expanding=true;
        crt_state_expand=state;
        postInvalidate();
    }
    public void deformation_direct(boolean state){
        is_expanding=false;
        crt_state_expand=state;
        if(crt_state_expand) {
            int _right = getRight();
            int _bw = dwe_icon.getIntrinsicWidth();
            int _mw = (int) (_bw * 3.0f);
            int _left = _right - _mw;
            this.setLeft(_left);
        }else{
            int _right = getRight();
            int _bw = dwe_icon.getIntrinsicWidth();
            int _left = _right - _bw;
            this.setLeft(_left);
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int _wd=MeasureSpec.getMode(widthMeasureSpec);
        if(_wd==MeasureSpec.AT_MOST || _wd==MeasureSpec.UNSPECIFIED){
            setMeasuredDimension(dwe_icon.getIntrinsicWidth(),dwe_icon.getIntrinsicHeight());
            return;
        }
        super.onMeasure(widthMeasureSpec,heightMeasureSpec);
    }

    private Path path=new Path();
    @Override
    protected void onDraw(Canvas canvas) {
        int _w=this.getWidth();
        int _h=this.getHeight();
        int _bw=dwe_icon.getIntrinsicWidth();
        int _bh=dwe_icon.getIntrinsicHeight();
        int _mw=(int)(_bw*3.0f);

        if(_w>_bw) {
            float _cy=_bh/2.0f;
            float _c1x=_bw/2.0f;
            float _c2x=_w-_bw/2.0f;
            paint.setStyle(Paint.Style.FILL);
            paint.setColor(0xff003567);
            canvas.drawCircle(_c2x,_cy,_cy,paint);
            canvas.drawRect(_c1x,0,_c2x,_h,paint);

            canvas.save();
            path.reset();
            path.addCircle(_c2x,_cy,_cy, Path.Direction.CCW);
            path.addRect(_c1x,0,_c2x,_h, Path.Direction.CCW);
            canvas.clipPath(path);
            int _cancel_x,_cancel_y;
            _cancel_x=_bw+_bw/2;
            _cancel_y=_h/2;
            dwe_cancel.setBounds(_cancel_x-dwe_cancel.getIntrinsicWidth()/2,_cancel_y-dwe_cancel.getIntrinsicHeight()/2,
                    _cancel_x+dwe_cancel.getIntrinsicWidth()/2,_cancel_y+dwe_cancel.getIntrinsicHeight()/2);
            dwe_cancel.draw(canvas);
            dwe_cancel.draw(canvas);

            int _ok_x,_ok_y;
            _ok_x=_bw*2+_bw/2;
            _ok_y=_h/2;
            dwe_ok.setBounds(_ok_x-dwe_ok.getIntrinsicWidth()/2,_ok_y-dwe_ok.getIntrinsicHeight()/2,
                    _ok_x+dwe_ok.getIntrinsicWidth()/2,_ok_y+dwe_ok.getIntrinsicHeight()/2);
            dwe_ok.draw(canvas);
            canvas.restore();
        }

        dwe_icon.setBounds(0,0,_bw,_bh);
        dwe_icon.draw(canvas);
        paint.setColor(0xffd6d6d6);
        canvas.drawText(del_number,_bw/2,CL.Get_DrawText_YPoint(paint)+(_bh*0.4f)/2,paint);


        //调整大小动画
        if(is_expanding){
            if(crt_state_expand){//展开
                int _left=getLeft();
                int _right=getRight();
                if(_right-_left>_mw){
                    _left=_right-_mw;
                }else if(_right-_left==_mw){
                    is_expanding=false;
                    return;
                }else _left-=Touch_offset_step;
                this.setLeft(_left);
            }else{//收缩
                int _left=getLeft();
                int _right=getRight();
                if(_right-_left<_bw){
                    _left=_right-_bw;
                }else if(_right-_left==_bw){
                    is_expanding=false;
                    return;
                }else _left+=Touch_offset_step;
                this.setLeft(_left);
            }
        }
    }

    private final float Touch_pad=CL.DIP2PX(15);
    private final float Touch_offset_step=CL.DIP2PX(10);
    private PointF point_down=new PointF();
    private boolean touch_in=false;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if(is_expanding)return true;
        int _action=event.getAction();
        float _x=event.getX();
        float _y=event.getY();
        if(_action==MotionEvent.ACTION_DOWN){
            touch_in=true;
            point_down.x=_x;
            point_down.y=_y;
            CL.CLOGI("down:"+_x+" / "+_y);
        }else if(_action==MotionEvent.ACTION_MOVE){
            if(touch_in) {
                if (Math.abs(_x - point_down.x) > Touch_pad || Math.abs(_y - point_down.y) > Touch_pad) {
                    touch_in = false;
                }
            }
        }else if(_action==MotionEvent.ACTION_UP){
            if(touch_in){
                if(!crt_state_expand){
                    crt_state_expand=true;
                    is_expanding=true;
                    if(listener!=null)listener.on_icon_click(false);
                }else{
                    is_expanding=false;
                    int _bw=dwe_icon.getIntrinsicWidth();
                    if(_x<_bw){
                        if(listener!=null)listener.on_icon_click(true);
                    }else if(_x>_bw && _x<_bw*2){
                        if(listener!=null)listener.on_cancel_click();
                    }else if(_x>_bw*2){
                        if(listener!=null)listener.on_delete_click();
                    }
                }
            }
        }else if(_action==MotionEvent.ACTION_CANCEL){

        }
        invalidate();
        return true;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        CL.CLOGI(changed+"_"+!is_expanding+"_"+crt_state_expand);
        if(changed && !is_expanding && crt_state_expand){
            deformation_direct(true);
        }
    }
}
