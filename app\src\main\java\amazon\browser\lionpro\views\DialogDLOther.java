package amazon.browser.lionpro.views;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.StateListDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.CommonDownloader;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.primary.Global;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLTools;

/**
 * Created by leron on 2016/7/12.
 */
public class DialogDLOther extends Dialog {

    private Activity cc;
    private FrameLayout fl_main;
    private LinearLayout ll_main;
    private ImageView iv_major;
    private TextView type_minor;
    private TextView tv_title,tv_size;
    private TextView tv_speed,tv_progress;
    private LinearLayout ll_btns;
    private CLController.DiscolourButton btn_cancel, btn_delete;
    private ImageView btn_dl;
    private Data.StructDLItem data;

    private Handler handler;
    private StateListDrawable dwe_one,dwe_two;

    private boolean waiting=false;


    public DialogDLOther(Activity context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);

        this.cc=context;
//        fl_main=new FrameLayout(this.getContext()){
//            @Override
//            protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
//                super.onLayout(changed, left, top, right, bottom);
//                if(changed){
//                    int _w=this.getWidth();
//                    int _h=this.getHeight();
//                    int _ww=(int)(Math.min(_w,_h)*0.2f);
//                    int _hh=(int)(_ww*0.6f);
//                    iv_major.setLayoutParams(CL.Get_LLLP(_ww,_hh,CL.DIP2PX_INT(8),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8)));
//                    iv_major.setVisibility(View.GONE);
//                    postDelayed(new Runnable() {
//                        @Override
//                        public void run() {
//                            iv_major.setVisibility(View.VISIBLE);
//                        }
//                    },100);
//                }
//            }
//        };
        fl_main=new FrameLayout(this.getContext());
        fl_main.setFocusable(true);
        fl_main.setClickable(true);
        fl_main.setFocusableInTouchMode(true);
        fl_main.requestFocus();
        fl_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(waiting)return;
                dismiss();
            }
        });

        dwe_one=CL.Get_StateList_Drawable(cc,R.mipmap.icon_dl_normal,R.mipmap.icon_dl_click);
        dwe_two=CL.Get_StateList_Drawable(cc,R.mipmap.icon_dl2_normal,R.mipmap.icon_dl2_click);
    }

    private Dialog.OnDismissListener listener_dismiss=new OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialog) {
            handler.removeMessages(1000);
        }
    };

    private View.OnClickListener listener_btn_delete=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(data.dler==null)return;
            CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_del_video), new CLCallback.CB_TF() {
                @Override
                public void on_callback_success() {
                    data.dler.delete();
                }
                @Override
                public void on_callback_fail(int code, String msg) {
                }
            }).show();
        }
    };

    private View.OnClickListener listener_btn_dl=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(data!=null && data.dler!=null){
                if(data.dler.get_status()== CommonDownloader.Eventer.State_Start)data.dler.stop();
                else if(data.dler.get_status()==CommonDownloader.Eventer.State_Stop){
                    if(Setting.Share_Setting().get_only_wifi()){
                        ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                        NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                        if (networkInfo == null) {
                            CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_network_error)).show();
                            return ;
                        }
                        int nType = networkInfo.getType();
                        if (nType != ConnectivityManager.TYPE_WIFI) {
                            CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                                @Override
                                public void on_callback_success() {
                                    handler.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            data.dler.go();
                                        }
                                    });
                                }
                                @Override
                                public void on_callback_fail(int code, String msg) {}
                            }).show();
                            return;
                        }
                    }
                    data.dler.go();
                }
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        CL.Set_Translucent_StatusBar(this.getWindow());
        this.setContentView(fl_main);

        ll_main=new LinearLayout(cc);
        ll_main.setLayoutParams(CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        ll_main.setOrientation(LinearLayout.VERTICAL);
        ll_main.setGravity(Gravity.CENTER_HORIZONTAL);
        ll_main.setClickable(true);
        RoundRectShape _shape=new RoundRectShape(new float[]{8,8,8,8,8,8,8,8}, null, null);
        ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
        _dwe_bg.getPaint().setColor(0xffa0a0a0);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
        ll_main.setBackground(_dwe_bg);
        fl_main.addView(ll_main);

        //thumb
        LinearLayout _ll_header=new LinearLayout(cc);
        _ll_header.setOrientation(LinearLayout.HORIZONTAL);
        _ll_header.setLayoutParams(CL.Get_LP(CL.MP,CL.WC));
        ll_main.addView(_ll_header);

        iv_major= new ImageView(cc);
        iv_major.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),0));
        _ll_header.addView(iv_major);

        LinearLayout _ll_title=new LinearLayout(cc);
        _ll_title.setOrientation(LinearLayout.VERTICAL);
        _ll_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,1.0f));
        _ll_header.addView(_ll_title);


        tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(4),0,CL.DIP2PX_INT(4)),
                null,0xff252525,16,null);
        tv_title.setMaxLines(4);
        tv_title.setEllipsize(TextUtils.TruncateAt.END);
        _ll_title.addView(tv_title);

        type_minor=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,0,CL.DIP2PX_INT(4)),null, 0xff252525,14,null);
        _ll_title.addView(type_minor);

        tv_size=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,0,CL.DIP2PX_INT(4)),null, 0xff252525,14,null);
        _ll_title.addView(tv_size);

        tv_speed=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(6)),"",0xff6935d3,20,null);
        ll_main.addView(tv_speed);

        tv_progress=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(8)),"",0xff252525,16,null);
        ll_main.addView(tv_progress);

        //AD
        View _v= Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC,0,CL.DIP2PX_INT(3),0,CL.DIP2PX_INT(3)), null);
        if(_v!=null)ll_main.addView(_v);

        ll_btns=new LinearLayout(cc);
        ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(50)));
        ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        ll_btns.setGravity(Gravity.RIGHT|Gravity.CENTER_VERTICAL);
        ll_main.addView(ll_btns);

        btn_dl =new ImageView(cc);
        btn_dl.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(80),CL.DIP2PX_INT(44)));
        btn_dl.setScaleType(ImageView.ScaleType.FIT_CENTER);
        btn_dl.setClickable(true);
        btn_dl.setOnClickListener(listener_btn_dl);
        ll_btns.addView(btn_dl);

        btn_delete =CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(44)),
                cc.getString(R.string.delete), 16, 0xffc50b21, 0xff0097dc, listener_btn_delete);
        btn_delete.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        btn_delete.setMinimumWidth(CL.DIP2PX_INT(80));
        ll_btns.addView(btn_delete);

        btn_cancel=CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(44), 0, 0, CL.DIP2PX_INT(6), 0),
                cc.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
        btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        ll_btns.addView(btn_cancel);


        handler=new Handler(){
            @Override
            public void handleMessage(Message msg) {
                if(msg.what==1000){
                    update_info();
                }
            }
        };

        this.setOnDismissListener(listener_dismiss);
    }


    public void update_downloader(Data.StructDLItem d){
        if(d==null)return;
        if(d.type_major!=Data.Type_Doc && d.type_major==Data.Type_APK && d.type_major==Data.Type_Other)return;
        tv_speed.setVisibility(View.VISIBLE);
        this.data=d;
        if(d.type_major==Data.Type_APK){
            iv_major.setBackgroundResource(R.mipmap.res_icon_apk);
        }else if(d.type_major==Data.Type_Doc){
            iv_major.setBackgroundResource(R.mipmap.res_icon_doc);
        }else if(d.type_major==Data.Type_Other){
            iv_major.setBackgroundResource(R.mipmap.res_icon_other);
        }
        tv_title.setText(this.data.title);
        type_minor.setText(cc.getResources().getString(R.string.type)+" : "+this.data.suffix);
        tv_size.setText(cc.getResources().getString(R.string.file_size)+" : "+CLTools.Get_Capacity_Format(this.data.length));
        this.btn_dl.setVisibility(View.VISIBLE);
        this.btn_delete.setVisibility(View.VISIBLE);
        update_info();
    }

    public Data.StructDLItem get_current_data(){
        return this.data;
    }

    private void update_info(){
        if(this.data==null)return;
        if(this.data.dler!=null) {
            tv_speed.setText(CLTools.Get_Capacity_Format(this.data.dler.get_speed()) + "/S");
            tv_progress.setText(CLTools.Get_Capacity_Format(this.data.dler.get_total_size()) + "/" + CLTools.Get_Capacity_Format(this.data.length)+
                    "        "+(int)((double)this.data.dler.get_total_size()/(double) this.data.length*100.0f)+"%");
            if(this.data.dler.get_status()== CommonDownloader.Eventer.State_Start){
                tv_speed.setVisibility(View.VISIBLE);
                btn_dl.setImageDrawable(dwe_two);
            }else if(this.data.dler.get_status()==CommonDownloader.Eventer.State_Stop){
                tv_speed.setVisibility(View.GONE);
                btn_dl.setImageDrawable(dwe_one);
            }else if(this.data.dler.get_status()==CommonDownloader.Eventer.State_Complete){
                tv_speed.setVisibility(View.GONE);
                btn_dl.setVisibility(View.GONE);
            }
            handler.sendEmptyMessageDelayed(1000,1000);
        }else{
            tv_speed.setVisibility(View.GONE);
            this.btn_dl.setVisibility(View.GONE);
            this.btn_delete.setVisibility(View.GONE);
        }
    }

}
