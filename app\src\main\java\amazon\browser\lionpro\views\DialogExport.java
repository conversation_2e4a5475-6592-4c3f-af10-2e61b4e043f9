package amazon.browser.lionpro.views;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLFileChooser;
import lion.CLFileSystem;
import lion.CLToast;
import lion.widget.CLProgresser;

/**
 * Created by leron on 2016/8/2.
 */
public class DialogExport extends Dialog {

    public static class ExportData{
        public String o_path;
        public String name;
    }

    public DialogExport(Context context,ArrayList<ExportData> ds, CLCallback.CB cber) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);
        this.datas=ds;
        this.cber=cber;
        fl_main=new FrameLayout(this.getContext()){
            @Override
            protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
                super.onLayout(changed, left, top, right, bottom);
                if(changed){
                    int _w=this.getWidth();
                    int _h=this.getHeight();
                    int _pad_lr=0;
                    int _pad_tb=0;
                    if(_w<_h){
                        _pad_lr=(int)(_w*0.1f);
                        int _ww=_w-_pad_lr*2;
                        int _hh=(int)(_ww*0.65f);
                        _pad_tb=(_h-_hh)/2;
                    }else{
                        _pad_lr=(int)(_w*0.26f);
                        int _ww=_w-_pad_lr*2;
                        int _hh=(int)(_ww*0.62f);
                        _pad_tb=(_h-_hh)/2;
                        if(_pad_tb<=0)_pad_tb=CL.DIP2PX_INT(16);
                    }
                    setPadding(_pad_lr,_pad_tb,_pad_lr,_pad_tb);
                    setVisibility(View.GONE);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            setVisibility(View.VISIBLE);
                        }
                    },100);
                }
            }
        };
        fl_main.setFocusable(true);
        fl_main.setClickable(true);
        fl_main.setFocusableInTouchMode(true);
        fl_main.requestFocus();
    }


    private FrameLayout fl_main;
    private Handler handler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        CL.Set_Translucent_StatusBar(this.getWindow());
        this.setContentView(fl_main);
        handler=new Handler();
        init();
    }



    private ArrayList<ExportData> datas;
    private CLCallback.CB cber;
    private LinearLayout ll_progress;
    private CLProgresser progresser;
    private TextView parent_tv_msg;
    private void init(){
        fl_main.addView(get_tip(fl_main.getContext()), CL.Get_FLLP(CL.MP, CL.MP, Gravity.CENTER));
        ll_progress=get_export(fl_main.getContext());
    }
    private LinearLayout get_tip(Context cc) {
        LinearLayout _bg = new LinearLayout(cc);
        RoundRectShape _shape = new RoundRectShape(new float[]{8, 8, 8, 8, 8, 8, 8, 8}, null, null);
        ShapeDrawable _dwe_bg = new ShapeDrawable(_shape);
        _dwe_bg.getPaint().setColor(0xffa0a0a0);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
        _bg.setBackground(_dwe_bg);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        TextView _tv_title = new TextView(cc);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12)));
        _tv_title.setText(R.string.export);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);

        TextView _tv_msg = new TextView(cc);
        parent_tv_msg = _tv_msg;
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC, 1.0f,CL.DIP2PX_INT(15), CL.DIP2PX_INT(8), CL.DIP2PX_INT(15), CL.DIP2PX_INT(18)));
        String _tip_msg = cc.getResources().getString(R.string.export_msg);
        _tv_msg.setText(_tip_msg + " "+ Setting.Share_Setting().get_new_export_dir());
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);

        LinearLayout _ll_btns = new LinearLayout(cc);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC, 0, 0, CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);

        CLController.DiscolourButton _btn_open = CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                cc.getString(R.string.export_folder), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //dismiss();
                        show_file_chooser(cc, parent_tv_msg);
                    }
                });
        _btn_open.set_touch_bg_color(0x00ffffff, 0xffd2d2d2);
        _btn_open.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_open);


        CLController.DiscolourButton _btn_cancel = CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                cc.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
        _btn_cancel.set_touch_bg_color(0x00ffffff, 0xffd2d2d2);
        _btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_cancel);
        CLController.DiscolourButton _btn_ok = CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                cc.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        setCancelable(false);
                        fl_main.removeAllViews();
                        fl_main.addView(ll_progress);
                        threader_export.start();
                    }
                });
        _btn_ok.set_touch_bg_color(0x00ffffff, 0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);
        return _bg;
    }

    private LinearLayout get_export(Context cc){
        LinearLayout _ll = new LinearLayout(cc);
        RoundRectShape _shape = new RoundRectShape(new float[]{8, 8, 8, 8, 8, 8, 8, 8}, null, null);
        ShapeDrawable _dwe_bg = new ShapeDrawable(_shape);
        _dwe_bg.getPaint().setColor(0xffa0a0a0);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
        _ll.setBackground(_dwe_bg);
        _ll.setGravity(Gravity.CENTER_HORIZONTAL);
        _ll.setOrientation(LinearLayout.VERTICAL);
        _ll.setClickable(true);
        TextView _tv_title = new TextView(cc);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC, CL.WC, 0, CL.DIP2PX_INT(12), 0, CL.DIP2PX_INT(12)));
        _tv_title.setText(R.string.export);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _ll.addView(_tv_title);

        progresser=new CLProgresser(cc);
        progresser.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(16),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15)));
        progresser.set_show_minor(true);
        _ll.addView(progresser);

        return _ll;
    }

    private Thread threader_export=new Thread(){
        @Override
        public void run() {
            try{
                File _dir=new File(Setting.Share_Setting().get_new_export_dir());
                if(!_dir.exists())_dir.mkdirs();
                if(!_dir.exists()){
                    CLToast.Show(fl_main.getContext(),"Target folder is not exists!",true);
                    return;
                }
                CLCallback.CBO<Float> _cber=new CLCallback.CBO<Float>() {
                    @Override
                    public void on_callback(Float obj) {
                        progresser.set_progress(progresser.get_process_major(),obj);
                    }
                };
                for(int i=0;i<datas.size();++i){
                    progresser.set_progress((float)(i+1)/datas.size(),0);
                    ExportData _tmp=datas.get(i);
                    File _o=new File(_tmp.o_path);
                    if(_o.exists() && _o.isFile()){
                        CLFileSystem.Copy_File_With_Process(_o,new File(_dir,_tmp.name),_cber);
                    }
                }
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        CLDialog.Get_Alert_Dialog(fl_main.getContext(),fl_main.getResources().getString(R.string.tip_operation_success)).show();
                    }
                });
            }catch (Exception ex){
                CLToast.Show(fl_main.getContext(),"Export error!",true);
            }finally {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        dismiss();
                        if(cber!=null)cber.on_callback();
                    }
                });
            }
        }
    };

    private void show_file_chooser(Context cc, TextView parent) {
        String[] _paths=null;
        if(!CLFileSystem.Whether_Availability()){
            CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.not_found_SD)).show();
            return;
        }
        _paths=new String[2];
        _paths[0]=new File(Setting.Share_Setting().get_export_dir()).getAbsolutePath();//new File(Environment.getExternalStorageDirectory(), Global.downloadPathName).getAbsolutePath();
        _paths[1]=Environment.getExternalStorageDirectory().getAbsolutePath();
        if(_paths==null)return;
        CLFileChooser _chooser=new CLFileChooser(cc, true, _paths, new CLCallback.CBO<String>() {
            @Override
            public void on_callback(String obj) {
                File _f=new File(obj);
                if(_f.exists()){
                    String _tip_msg = cc.getResources().getString(R.string.export_msg);
                    parent.setText(_tip_msg + " "+ _f.getAbsolutePath());
                    Setting.Share_Setting().set_new_export_dir(_f.getAbsolutePath());
                    //   Global.reInitStore(Global.Acy_Main.getApplicationContext());
                    //   Server.reInit(Global.Acy_Main.getApplicationContext());
                }
            }
        });
        _chooser.set_resource(R.style.pop_from_bottom, R.mipmap.icon_folder,R.mipmap.icon_folder_add_normal,R.mipmap.icon_folder_add_click);
        _chooser.show();
    }
}
