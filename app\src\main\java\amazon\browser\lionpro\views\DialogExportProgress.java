package amazon.browser.lionpro.views;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.documentfile.provider.DocumentFile;

import amazon.browser.video.downloader.R;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.ArrayList;


import lion.CLToast;

/**
 * Created by leron on 2016/8/2.
 */
public class DialogExportProgress extends Dialog {
    private DocumentFile dir;
    private ArrayList<DialogExport.ExportData> data = null;

    private TextView tvMsg;
    private Button btnCancel;
    private Handler handler;
    private boolean isCancel;
    private Context cc;
    private DialogExportProgress dialog;
    public static class ExportData{
        public String o_path;
        public String name;
    }

    private View.OnClickListener clickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            dialog.dismiss();
            isCancel = true;
        }
    };

    public DialogExportProgress(@NonNull Context context) {
        super(context);
        cc = context;
    }



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        dialog = this;
        handler = new Handler(Looper.getMainLooper());
        setContentView(R.layout.dialog_export);
        //按空白处不能取消动画
        setCanceledOnTouchOutside(false);
        tvMsg = findViewById(R.id.tv_msg);
        btnCancel = findViewById(R.id.dialog_btn_cancel);
        btnCancel.setOnClickListener(clickListener);
        copyFiles();
    }



    public void setData(DocumentFile dir, ArrayList<DialogExport.ExportData> fs) {
        this.dir = dir;
        this.data = fs;
    }

    private void copyFiles() {
        if (data == null || data.isEmpty() || dir == null || !dir.isDirectory() || !dir.canWrite()) {
            this.dismiss();
            return;
        }
        new Thread() {
            @Override
            public void run() {
                final int ds = data.size();
                for (int i = 0; !isCancel && i < data.size(); ++i) {
                    final int ii = i + 1;
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            float r = ((float) ii / (float) ds) * 100;
                            tvMsg.setText(ii + "/" + ds + "       " + (int) r + "%");
                        }
                    });
                    DialogExport.ExportData data1 = data.get(i);
                    File f = new File(data1.o_path);
                    if (f.exists()) {
                        DocumentFile fold = dir.findFile(f.getName());
                        if (fold != null && fold.exists() && fold.length() == f.length()) {
                            continue;
                        }
                        DocumentFile target = dir.createFile("*/*", f.getName());
                        try {
                            FileInputStream fis = new FileInputStream(f);
                            OutputStream os = cc.getContentResolver().openOutputStream(target.getUri());
                            byte[] buff = new byte[1024 * 16];
                            int count;
                            while ((count = fis.read(buff)) != -1) {
                                os.write(buff, 0, count);
                            }
                            fis.close();
                            os.close();
                        } catch (Exception ex) {}
                    }
                }

                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        dialog.dismiss();
                        CLToast.Show(cc,cc.getResources().getString(R.string.tip_operation_success),true);
                    }
                });
            }
        }.start();
    }
}
