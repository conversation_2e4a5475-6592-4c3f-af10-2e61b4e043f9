package amazon.browser.lionpro.views;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Movie;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Bundle;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;

import javax.net.ssl.HttpsURLConnection;

import lion.CL;
import lion.CLBitmapLoader;
import lion.CLCallback;
import lion.CLController;
import lion.CLToast;
import lion.CLTools;

/**
 * Created by leron on 2016/7/5.
 */
public class DialogStoragePicture extends Dialog{

    private Context cc;
    private FrameLayout fl_main;
    private LinearLayout ll_main;
    private CTMView iv_picture;
    private LinearLayout ll_btns;
    private CLController.DiscolourButton btn_cancel,btn_ok;
    private CLController.Waiter ctm_waiter;
    private Handler handler;

    public DialogStoragePicture(Context context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);

        this.cc=context;
        fl_main=new FrameLayout(this.getContext()){
            @Override
            protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
                super.onLayout(changed, left, top, right, bottom);
                if(changed){
                    int _w=this.getWidth();
                    int _h=this.getHeight();
                    int _hh=(int)(Math.min(_w,_h)*0.85f);
                    int _ww=(int)(_hh*0.95f);
                    int _pad_w=(_w-_ww)/2;
                    int _pad_h=(_h-_hh)/2;
                    setPadding(_pad_w,_pad_h,_pad_w,_pad_h);
                    setVisibility(View.GONE);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            setVisibility(View.VISIBLE);
                        }
                    },100);
                }
            }
        };
        fl_main.setFocusable(true);
        fl_main.setClickable(true);
        fl_main.setFocusableInTouchMode(true);
        fl_main.requestFocus();
        fl_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(waiting)return;
                dismiss();
            }
        });
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        CL.Set_Translucent_StatusBar(this.getWindow());
        this.setContentView(fl_main);

        handler=new Handler();

        ll_main=new LinearLayout(cc);
        ll_main.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP, Gravity.FILL));
        ll_main.setOrientation(LinearLayout.VERTICAL);
        ll_main.setGravity(Gravity.CENTER_HORIZONTAL);
        ll_main.setClickable(true);
        RoundRectShape _shape=new RoundRectShape(new float[]{8,8,8,8,8,8,8,8}, null, null);
        ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
        _dwe_bg.getPaint().setColor(0xffa0a0a0);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
        ll_main.setBackground(_dwe_bg);
        fl_main.addView(ll_main);

        TextView _tv_title=new TextView(cc);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(15)));
        _tv_title.setText(R.string.tip_storage_picture_title);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        ll_main.addView(_tv_title);

        iv_picture= new CTMView(cc);
        iv_picture.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f,CL.DIP2PX_INT(2),CL.DIP2PX_INT(2),CL.DIP2PX_INT(2),CL.DIP2PX_INT(2)));
        ll_main.addView(iv_picture);

        FrameLayout _fl_btns=new FrameLayout(cc);
        _fl_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(35),0,CL.DIP2PX_INT(8),CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        ll_main.addView(_fl_btns);

        ll_btns=new LinearLayout(cc);
        ll_btns.setLayoutParams(CL.Get_FLLP(CL.MP,CL.WC,Gravity.FILL));
        ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        ll_btns.setGravity(Gravity.RIGHT);
        _fl_btns.addView(ll_btns);

        btn_cancel=CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                cc.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                    }
                });
        btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        ll_btns.addView(btn_cancel);
        btn_ok=CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                cc.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, listener_btn_storage);
        btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        ll_btns.addView(btn_ok);

        ctm_waiter=new CLController.Waiter(cc,Color.BLACK);
        ctm_waiter.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP,Gravity.FILL));
        ctm_waiter.setVisibility(View.GONE);
        ctm_waiter.setClickable(true);
        _fl_btns.addView(ctm_waiter);

        picture_path=new File(Global.Dir_Download,"pdl_aabbcc_188660_hhh_zzz").getAbsolutePath();

        this.setOnDismissListener(listener_dismiss);
    }

    private Dialog.OnDismissListener listener_dismiss=new OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialog) {
            if(bitmap_picture!=null && !bitmap_picture.isRecycled())bitmap_picture.recycle();
            bitmap_picture=null;
            movie_gif=null;
            iv_picture.movie_start=0;
            is_show=false;
        }
    };
    private View.OnClickListener listener_btn_storage=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            setCancelable(false);
            waiting=true;
            ll_btns.setVisibility(View.GONE);
            ctm_waiter.setVisibility(View.VISIBLE);
            Server.External_Storage_Picture(Data.Get_Type_minor(picture_suffix), picture_md5, new File(picture_path),
                    new CLCallback.CB_TF() {
                        @Override
                        public void on_callback_success() {
                            CLToast.Show(cc,cc.getResources().getString(R.string.tip_operation_success),false);
                            handler.post(new Runnable() {
                                @Override
                                public void run() {
                                    dismiss();
                                }
                            });
//                            if(Data.Get_Type_minor(picture_suffix)==Data.Type_Image_GIF)Global.Youmeng.YM_on_event(cc,"dl_gif");
//                            else Global.Youmeng.YM_on_event(cc,"dl_picture");
                        }
                        @Override
                        public void on_callback_fail(int code, String msg) {
                            if(code==-10){//not support picture format
                                CLToast.Show(cc,cc.getResources().getString(R.string.tip_no_support_picture_format),true);
                                handler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        dismiss();
                                    }
                                });
                            }else if(code==-11){//no space to storage!
                                CLToast.Show(cc,cc.getResources().getString(R.string.tip_sdcard_no_space),false);
                                handler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        dismiss();
                                    }
                                });
                            }else if(code==-12){//system not ready!
                                CLToast.Show(cc,cc.getResources().getString(R.string.tip_hold),false);
                                handler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        setCancelable(true);
                                        waiting=false;
                                        ll_btns.setVisibility(View.VISIBLE);
                                        ctm_waiter.setVisibility(View.GONE);
                                    }
                                });
                            }else if(code==-1){//error
                                CLToast.Show(cc,cc.getResources().getString(R.string.tip_storage_error),false);
                                handler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        dismiss();
                                    }
                                });
                            }
                        }
                    });
        }
    };

    private boolean waiting=false;
    private boolean is_show=false;
    private String last_picture_url =null;
    private boolean picture_is_gif=false;
    private String picture_suffix=null;
    private String picture_path=null;
    private String picture_md5=null;
    private String url_picture=null;
    private Bitmap bitmap_picture=null;
    private Movie movie_gif=null;

    public void update_picture(String url){
        if(url==null)return;
        url_picture=url;
        iv_picture.set_message_code(1);
        waiting=false;
        handler.post(new Runnable() {
            @Override
            public void run() {
                setCancelable(true);
                btn_ok.setVisibility(View.INVISIBLE);
                ll_btns.setVisibility(View.VISIBLE);
                ctm_waiter.setVisibility(View.GONE);
            }
        });
        is_show=true;
        new Thread(thread_loader).start();
    }

    private Thread thread_loader=new Thread(){
        public void run(){
            if(!is_show)return;
            try{
                if(last_picture_url!=null && last_picture_url.equals(url_picture)){
                    //加载上次的图片
                    if(picture_is_gif){
                        movie_gif=Movie.decodeFile(picture_path);
                        CL.CLOGI("gif1 w:"+movie_gif.width()+" h:"+movie_gif.height());
                        CL.CLOGI("gif1 duration:"+movie_gif.duration());
                        last_picture_url=url_picture;
                        iv_picture.postInvalidate();
                    }else {
                        bitmap_picture = CLBitmapLoader.Create_Bitmap_Not_Count(picture_path, CL.DIP2PX_INT(600), CL.DIP2PX_INT(600));
                        CL.CLOGI("create bitmap1 width:" + bitmap_picture.getWidth() + " height:" + bitmap_picture.getHeight());
                        iv_picture.postInvalidate();
                    }
                }
                if(bitmap_picture==null && movie_gif==null){
                    HttpURLConnection _conn=null;
                    if(url_picture.startsWith("http://")){
                        _conn=(HttpURLConnection)new URL(url_picture).openConnection();
                    }else if(url_picture.startsWith("https://")){
                        _conn=(HttpsURLConnection)new URL(url_picture).openConnection();
                    }
                    _conn.setConnectTimeout(5000);
                    _conn.setReadTimeout(5000);
                    _conn.connect();

//                    for (Map.Entry<String,List<String>> item:_conn.getHeaderFields().entrySet()){
//                        CL.CLOGI(item.getKey()+":");
//                        for(int i=0;i<item.getValue().size();++i){
//                            CL.CLOGI("       "+item.getValue().get(i));
//                        }
//                    }

                    FileOutputStream _fos=new FileOutputStream(picture_path);
                    MessageDigest _md5=MessageDigest.getInstance("MD5");
                    byte[] _buff=new byte[8192];
                    int _count=-1;
                    InputStream _is=_conn.getInputStream();
                    while ((_count=_is.read(_buff))!=-1){
                        if(!is_show)return;
                        _fos.write(_buff,0,_count);
                        _md5.update(_buff,0,_count);
                    }
                    _fos.close();
                    _is.close();
                    _conn.disconnect();

                    byte[] data_md5=_md5.digest();
                    StringBuffer _sb_md5=new StringBuffer(data_md5.length*2);
                    for(byte b:data_md5){
                        if ((b & 0xFF) < 0x10) _sb_md5.append("0");
                        _sb_md5.append(Integer.toHexString(b & 0xFF));
                    }
                    picture_md5=_sb_md5.toString();

                    picture_suffix=CLTools.Parse_Image_Format(picture_path);
                    CL.CLOGI("picture type:"+picture_suffix);
                    CL.CLOGI("picture md5:"+picture_md5);
                    if(picture_suffix==null){
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                dismiss();
                                CLToast.Show(cc,cc.getResources().getString(R.string.tip_no_support_picture_format),true);
                            }
                        });
                        return;
                    }
                    if(picture_suffix.equals("gif")){
                        picture_is_gif=true;
                        movie_gif=Movie.decodeFile(picture_path);
                        CL.CLOGI("gif2 w:"+movie_gif.width()+" h:"+movie_gif.height());
                        CL.CLOGI("gif2 duration:"+movie_gif.duration());
                        last_picture_url=url_picture;
                        iv_picture.postInvalidate();
                    }
                    else {
                        picture_is_gif=false;
                        bitmap_picture=CLBitmapLoader.Create_Bitmap_Not_Count(picture_path,CL.DIP2PX_INT(600),CL.DIP2PX_INT(600));
                        CL.CLOGI("create bitmap2 width:"+bitmap_picture.getWidth()+" height:"+bitmap_picture.getHeight());
                        last_picture_url=url_picture;
                        iv_picture.postInvalidate();
                    }
                }
                iv_picture.set_message_code(0);
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        btn_ok.setVisibility(View.VISIBLE);
                    }
                });
            }catch (Exception ex){
//                iv_picture.set_message_code(-100);
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        dismiss();
                        CLToast.Show(cc,cc.getResources().getString(R.string.tip_network_error),true);
                    }
                });
                CL.CLOGE("dialog storage picture error:"+ex.toString(),ex);
            }finally {
                if(!is_show)listener_dismiss.onDismiss(null);
            }
        }
    };
    private class CTMView extends View{
        private int message_code =0;
        private long movie_start =0;

        public CTMView(Context context) {
            super(context);
            this.setLayerType(LAYER_TYPE_SOFTWARE,null);
            p=new Paint(Paint.ANTI_ALIAS_FLAG);
        }
        public void set_message_code(int code){
            this.message_code =code;
            postInvalidate();
        }

        private Paint p;
        private float ball_d=CL.DIP2PX(10);
        private float ball_r=ball_d/2;
        private float ball_zoom=ball_r;
        private float ball_step=0.5f;
        private void draw_waiting(Canvas canvas){
            if(ball_zoom>ball_d)ball_step=-0.5f;
            else if(ball_zoom<=ball_r)ball_step=0.5f;
            ball_zoom+=ball_step;
            this.p.setStyle(Paint.Style.STROKE);
            this.p.setStrokeWidth(2);
            this.p.setColor(Color.RED);
            canvas.drawCircle(canvas.getWidth()/2, canvas.getHeight()/2, ball_r+ball_zoom, this.p);
            invalidate();
        }

        @Override
        public void draw(Canvas canvas) {
            if(message_code==1){//等待
                draw_waiting(canvas);
            }
//            else if(message_code==-100){//异常
//
//            }else if(message_code==-200){//不支持???
//
//            }
            else if(bitmap_picture!=null){
                float _xs=(float)this.getWidth()/(float)bitmap_picture.getWidth();
                float _ys=(float)this.getHeight()/(float)bitmap_picture.getHeight();
                float _ss=(_xs < _ys ? _xs : _ys);
                canvas.save();
                canvas.scale(_ss,_ss,this.getWidth()/2,this.getHeight()/2);
                canvas.drawBitmap(bitmap_picture,(this.getWidth()-bitmap_picture.getWidth())/2,(this.getHeight()-bitmap_picture.getHeight())/2,null);
                canvas.restore();
            }else if(movie_gif!=null){
                float _xs=(float)this.getWidth()/(float)movie_gif.width();
                float _ys=(float)this.getHeight()/(float)movie_gif.height();
                float _ss=(_xs < _ys ? _xs : _ys);
                canvas.save();
                canvas.scale(_ss,_ss,this.getWidth()/2,this.getHeight()/2);
                long curTime=android.os.SystemClock.uptimeMillis();
                if (movie_start == 0) {
                    movie_start = curTime;
                }
                int duraction = movie_gif.duration();
                if(duraction==0)duraction=1000;
                int relTime = (int) ((curTime- movie_start)%duraction);
                movie_gif.setTime(relTime);
                movie_gif.draw(canvas, (this.getWidth()-movie_gif.width())/2,(this.getHeight()-movie_gif.height())/2);
                canvas.restore();
                invalidate();
            }
        }
    }

}
