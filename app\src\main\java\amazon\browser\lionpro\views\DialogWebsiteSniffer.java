package amazon.browser.lionpro.views;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.BaseAdapter;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLToast;
import lion.CLTools;

/**
 * Created by leron on 2016/9/12.
 */
public class DialogWebsiteSniffer extends Dialog {

    private Activity cc;
    private FrameLayout fl_main;
    private LinearLayout ll_main;
    private TextView tv_title;
    private ImageView iv_thumb;
    private ListView lv_list;
    private AdapterForQuality adapter;
    private ResSniffer.SniffDataWebsite data;
    private ResSniffer.SniffWSData crt_select_item;
    private Handler handler;
    private CLCallback.CB_TF cber_oc;

    private boolean discard=false;
    private Bitmap bitmap_thumb;
    private boolean waiting=false;


    public DialogWebsiteSniffer(Activity context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);

        this.cc=context;
        fl_main=new FrameLayout(cc);
        fl_main.setFocusable(true);
        fl_main.setClickable(true);
        fl_main.setFocusableInTouchMode(true);
        fl_main.requestFocus();
        fl_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(waiting)return;
                dismiss();
            }
        });
    }

    private Dialog.OnDismissListener listener_dismiss=new OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialog) {
            discard=true;
            crt_select_item=null;
        }
    };


    private View.OnClickListener listener_cancel=new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if(cber_oc!=null)cber_oc.on_callback_fail(0,data.title);
            iv_thumb.setImageBitmap(null);
            bitmap_thumb=null;
            dismiss();
        }
    };

    private View.OnClickListener listener_ok=new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if(Setting.Share_Setting().get_only_wifi()){
                ConnectivityManager connectivityManager = (ConnectivityManager) cc.getSystemService(Context.CONNECTIVITY_SERVICE);
                NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                if (networkInfo == null) {
                    CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_network_error)).show();
                    return ;
                }
                int nType = networkInfo.getType();
                if (nType != ConnectivityManager.TYPE_WIFI) {
                    CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_only_wifi), new CLCallback.CB_TF() {
                        @Override
                        public void on_callback_success() {
                            goto_download();
                        }
                        @Override
                        public void on_callback_fail(int code, String msg) {}
                    }).show();
                    return;
                }else goto_download();
            }else goto_download();
        }
    };

    public void update_data(ResSniffer.SniffDataWebsite d, CLCallback.CB_TF cber){
        this.cber_oc=cber;
        if(d!=null && data!=null){
            if(d.url_thumb!=null && data.url_thumb!=null && d.url_thumb.equals(data.url_thumb)
                    && d.title!=null && data.title!=null && d.title.equals(data.title)){
                crt_select_item=data.urls.get(0);
                return;
            }
        }

        data=d;
        if(tv_title!=null) {
            tv_title.setText(data.title);
            if(data.url_thumb!=null)load_thumb(data.url_thumb);
            adapter.notifyDataSetChanged();
        }
        crt_select_item=data.urls.get(0);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        CL.Set_Translucent_StatusBar(this.getWindow());
        this.setContentView(fl_main);

        handler=new Handler();

        ll_main=new LinearLayout(cc);
        ll_main.setLayoutParams(CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        ll_main.setOrientation(LinearLayout.VERTICAL);
        ll_main.setClickable(true);
        RoundRectShape _shape=new RoundRectShape(new float[]{8,8,8,8,8,8,8,8}, null, null);
        ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
        _dwe_bg.getPaint().setColor(0xffc0c0c0);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
        ll_main.setBackground(_dwe_bg);
        fl_main.addView(ll_main);

        tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(22),CL.DIP2PX_INT(4),CL.DIP2PX_INT(8),CL.DIP2PX_INT(4)),
                data.title,0xff252525,16,null);
        tv_title.setMaxLines(3);
        ll_main.addView(tv_title);

        iv_thumb= new ImageView(cc);
        iv_thumb.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(150)));
        iv_thumb.setScaleType(ImageView.ScaleType.FIT_CENTER);
        iv_thumb.setBackgroundColor(Color.GRAY);
        iv_thumb.setImageURI(Uri.parse(data.url_thumb));
        ll_main.addView(iv_thumb);

        ll_main.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff888888));
        lv_list=new ListView(cc);
        lv_list.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
        lv_list.setCacheColorHint(Color.TRANSPARENT);
        lv_list.setDivider(new ColorDrawable(0xff888888));
        lv_list.setDividerHeight(1);
        lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
        adapter=new AdapterForQuality();
        lv_list.setAdapter(adapter);
        ll_main.addView(lv_list);
        ll_main.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff888888));

        View v = Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
        if (v != null) {
            ll_main.addView(v);
        }


        LinearLayout _ll_btns=new LinearLayout(cc);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,CL.DIP2PX_INT(8),CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        ll_main.addView(_ll_btns);

        CLController.DiscolourButton _btn_cancel=CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                cc.getString(R.string.remove), 16, 0xff005880, 0xff0097dc, listener_cancel);
        _btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_cancel);
        CLController.DiscolourButton _btn_ok=CLController.Get_Discolour_Button(cc, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                cc.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, listener_ok);
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);

        if(data.url_thumb!=null)load_thumb(data.url_thumb);

        this.setOnDismissListener(listener_dismiss);
    }


    private void load_thumb(final String url){
        new Thread(){
            @Override
            public void run() {
                try{
                    HttpURLConnection _conn=null;
                    if(url.startsWith("http://")){
                        _conn=(HttpURLConnection)new URL(url).openConnection();
                    }else if(url.startsWith("https://")){
                        _conn=(HttpsURLConnection)new URL(url).openConnection();
                    }
                    _conn.setConnectTimeout(5000);
                    _conn.setReadTimeout(5000);
                    _conn.connect();

                    ByteArrayOutputStream _bos=new ByteArrayOutputStream();
                    byte[] _buff=new byte[8192];
                    int _count=-1;
                    InputStream _is=_conn.getInputStream();
                    while ((_count=_is.read(_buff))!=-1){
                        _bos.write(_buff,0,_count);
                    }
                    _is.close();
                    _conn.disconnect();
                    byte[] _data=_bos.toByteArray();
                    bitmap_thumb=BitmapFactory.decodeByteArray(_data,0,_data.length);

                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            if(data.url_thumb.equals(url))iv_thumb.setImageBitmap(bitmap_thumb);
                        }
                    });

                }catch (Exception ex){
                    CL.CLOGE("download error:"+ex.toString(),ex);
                }
            }
        }.start();
    }

    private void goto_download(){
        final CLDialog _dialog_force=CLDialog.Get_Force_Wait(cc);
        _dialog_force.show();
        new Thread(){
            public void run(){
                try{
                    final String _type,_real_url;
                    final int _clength;
                    byte[] _ident;
                    HttpURLConnection _conn=null;
                    if(crt_select_item.url.startsWith("http://")){
                        _conn=(HttpURLConnection) new URL(crt_select_item.url).openConnection();
                    }else if(crt_select_item.url.startsWith("https://")) {
                        _conn = (HttpsURLConnection) new URL(crt_select_item.url).openConnection();
                    }
                    _conn.setConnectTimeout(3000);
                    _conn.setReadTimeout(3000);
                    _conn.setRequestProperty("User-Agent", Global.Crt_UA);
                    _conn.connect();
                    _type=_conn.getContentType();
                    _clength=_conn.getContentLength();
                    _real_url=_conn.getURL().toString();
                    _ident=new byte[32];
                    int _count=_conn.getInputStream().read(_ident);
                    if(_count<32)_ident=null;
                    _conn.disconnect();
                    String _ident_str= CLTools.Parse_Video_Audio_Format(_ident);
                    if(_ident_str==null)throw new RuntimeException("parser error");

                    CL.CLOGI("ident:"+_ident_str);
                    CL.CLOGI("type:"+_type+" length:"+_clength+" real_url:"+_real_url);

                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            _dialog_force.dismiss();
                            if(Server.Add_Sniffer_Download(data.title,bitmap_thumb,_real_url, Data.Type_Video,Data.Type_Video_MP4,data.provenance,_clength, crt_select_item.listHeaderParams)){
                                dismiss();
                                if(cber_oc!=null)cber_oc.on_callback_fail(0,data.title);
                                iv_thumb.setImageBitmap(null);
                                bitmap_thumb=null;
                                CLToast.Show(cc,cc.getResources().getString(R.string.tip_operation_success),true);
                            }else throw new RuntimeException("add download error");
                        }
                    });
                }catch (Exception ex){
                    CLToast.Show(cc,cc.getResources().getString(R.string.tip_operation_fail)+":"+ex.toString(),true);
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            _dialog_force.dismiss();
                        }
                    });
                }
            }
        }.start();
    }


    private class AdapterForQuality extends BaseAdapter{
        @Override
        public int getCount() {
            if(data==null)return 0;
            return data.urls.size();
        }

        @Override
        public Object getItem(int i) {
            return null;
        }

        @Override
        public long getItemId(int i) {
            return 0;
        }

        @Override
        public View getView(int i, View cv, ViewGroup viewGroup) {
            if(cv==null)cv=new QualityView(cc);
            QualityView _v=(QualityView)cv;
            _v.set_data(data.urls.get(i));
            return cv;
        }
    }

    private class QualityView extends LinearLayout{

        private ResSniffer.SniffWSData data;
        private TextView tv_title;
        private ImageView iv_check;

        private View.OnClickListener listener_check=new OnClickListener() {
            @Override
            public void onClick(View view) {
                crt_select_item=data;
                adapter.notifyDataSetChanged();
            }
        };

        public QualityView(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);
            this.setOnClickListener(listener_check);
            iv_check=CLController.Get_ImageView(cc,
                    CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),CL.DIP2PX_INT(22),CL.DIP2PX_INT(8),CL.DIP2PX_INT(32),CL.DIP2PX_INT(8)), null,null);
            this.addView(iv_check);
            tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f),"",0xff000000,16,null);
            this.addView(tv_title);
        }
        public void set_data(ResSniffer.SniffWSData d){
            this.data=d;
            if(d.width!=null && d.height!=null)tv_title.setText(d.quality+" ("+d.width+"x"+d.height+")   "+d.video_size);
            else tv_title.setText(d.quality+" ("+d.wh_string+")   "+d.video_size);
            if(crt_select_item!=null && crt_select_item==data)
                iv_check.setBackgroundResource(R.mipmap.comm_select_2);
            else iv_check.setBackgroundResource(R.mipmap.comm_select_1);
        }
    }

}
