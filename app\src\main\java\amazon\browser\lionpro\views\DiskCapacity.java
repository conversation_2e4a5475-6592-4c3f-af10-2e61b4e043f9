package amazon.browser.lionpro.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;

import amazon.browser.video.downloader.R;

import lion.CL;
import lion.CLTools;

/**
 * Created by leron on 2015/7/29.
 */
public class DiskCapacity extends View {


    private Paint paint;

    private String text;
    private float ratio;
    private long total_space;
    private long used_space;

    private String usedspace,totalspace;

    public DiskCapacity(Context context) {
        super(context);
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setTextAlign(Paint.Align.CENTER);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
        paint.setTextSize(CL.SP2PX(12.0f));

        usedspace=context.getResources().getString(R.string.usedspace);
        totalspace=context.getResources().getString(R.string.totalspace);
    }

    public void set_data(long used, long total) {
        this.total_space = total;
        this.used_space = used;
        this.text = usedspace+":" + CLTools.Get_Capacity_Format(this.used_space) + " / "+totalspace+":" + CLTools.Get_Capacity_Format(this.total_space);
        this.ratio = (float) ((double) used_space / (double) total_space);
        this.postInvalidate();
    }

//    private String get_length_string(long length) {
//        StringBuffer _sb = new StringBuffer();
//        long _g = length / 1073741824l;
//        long _m = (length % 1073741824l) / 1048576;
//        long _k = (length % 1048576) / 1024;
//        if (_g > 0) {
//            _sb.append(_g);
//            if (_m > 0) {
//                float _mm = (float) _m / (float) 1024;
//                int _mmm = (int) (_mm * 100.0f);
//                if (_mmm > 0) {
//                    _sb.append('.');
//                    if (_mmm < 10) _sb.append("0");
//                    _sb.append(_mmm);
//                }
//            }
//            _sb.append('G');
//        } else if (_m > 0) {
//            _sb.append(_m);
//            if (_k > 0) {
//                float _kk = (float) _k / (float) 1024;
//                int _kkk = (int) (_kk * 100.0f);
//                if (_kkk > 0) {
//                    _sb.append('.');
//                    if (_kkk < 10) _sb.append("0");
//                    _sb.append(_kkk);
//                }
//            }
//            _sb.append('M');
//        } else if (_k > 0) {
//            _sb.append(_k).append('K');
//        }
//        else{
//            _sb.append(length+"B");
//        }
//        return _sb.toString();
//    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int _ww=CL.DIP2PX_INT(160);
        int _hh=CL.DIP2PX_INT(26);
        int w_mode= MeasureSpec.getMode(widthMeasureSpec);
        int w_size= MeasureSpec.getSize(widthMeasureSpec);
        if(w_mode== MeasureSpec.AT_MOST || w_mode== MeasureSpec.UNSPECIFIED){
            w_size=_ww;
        }
        int h_mode= MeasureSpec.getMode(heightMeasureSpec);
        int h_size= MeasureSpec.getSize(heightMeasureSpec);
        if(h_mode== MeasureSpec.AT_MOST || h_mode== MeasureSpec.UNSPECIFIED){
            h_size=_hh;
        }
        if(w_size<_ww)w_size=_ww;
        if(h_size<_hh)h_size=_hh;
        this.setMeasuredDimension(w_size, h_size);
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        canvas.drawColor(0xff202529);
        if (ratio > 0.0f) {
            paint.setColor(0xff444444);
            canvas.drawRect(2, 2, this.getWidth() * ratio-2, this.getHeight()-2, paint);
        }

        paint.setColor(0xffa1a1a1);
        if (this.text != null) {
            canvas.drawText(this.text, this.getWidth() / 2.0f, this.getHeight()/2+CL.Get_DrawText_YPoint(paint), paint);
        }
    }


}
