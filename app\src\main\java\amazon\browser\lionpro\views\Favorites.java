package amazon.browser.lionpro.views;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.CycleInterpolator;
import android.view.animation.ScaleAnimation;
import android.view.animation.TranslateAnimation;
import android.widget.BaseAdapter;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Affairs;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.Tools;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLBitmapLoader;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLPictureDownloader;

/**
 * Created by leron on 2016/4/19.
 */
public class Favorites extends FrameLayout{


    private Context cc;
    private ImageView btn_add;
    private Deleter btn_del;
    private ListView lv_list;
    private AdapterForFav adapter;
    private Affairs.TypeFavorite typer;
    private ScaleAnimation anim_scale;


    private View.OnClickListener listener_add=new OnClickListener() {
        @Override
        public void onClick(View v) {
            v.post(new Runnable() {
                @Override
                public void run() {
                    Show_Add_Dialog();
                }
            });

        }
    };
    private Deleter.Eventer listener_del=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand)btn_del.deformation(false);
        }
        @Override
        public void on_cancel_click() {
            del_state=false;
            del_number=0;
            btn_del.deformation_direct(false);
            btn_del.setVisibility(View.GONE);
            btn_add.setVisibility(View.VISIBLE);
            btn_add.startAnimation(anim_scale);
            for(int i=0;i<datas.size();++i){
                datas.get(i).selected=false;
            }
            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            ArrayList<Integer> _ids=new ArrayList<>();
            for(int i=0;i<datas.size();++i){
                if(datas.get(i).selected){
                    _ids.add(datas.get(i).ID);
                    recycle_item(datas.remove(i));
                    --i;
                }
            }
            typer.delete_favorites(cc,_ids);
            del_state=false;
            del_number=0;
            btn_del.deformation_direct(false);
            btn_del.setVisibility(View.GONE);
            btn_add.setVisibility(View.VISIBLE);
            btn_add.startAnimation(anim_scale);
            adapter.notifyDataSetChanged();
            CLBus.Share_Instance().send_msg_immediate(Global.Group_favorite,Global.Action_favorite_del);
            CLBus.Share_Instance().send_msg_immediate(Global.Group_main_menu,Global.Action_main_menu);
        }
    };


    public Favorites(Context context) {
        super(context);
        this.cc=context;
        this.setFocusable(true);
        this.setFocusableInTouchMode(true);
        def_icon=cc.getResources().getDrawable(R.mipmap.address_web);
        lv_list=new ListView(cc){
            @Override
            public boolean onInterceptTouchEvent(MotionEvent ev) {
                if(btn_del.getVisibility()==View.VISIBLE)btn_del.deformation(false);
                return super.onInterceptTouchEvent(ev);
            }
        };

        ImageView _empty=new ImageView(context);
        _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        _empty.setBackgroundResource(R.mipmap.no_data);
        this.addView(_empty);

        lv_list.setLayoutParams(CL.Get_LP_MM());
        lv_list.setCacheColorHint(Color.TRANSPARENT);
        lv_list.setDivider(new ColorDrawable(0xff313131));
        lv_list.setDividerHeight(1);
        lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
        lv_list.setEmptyView(_empty);
        adapter=new AdapterForFav();
        lv_list.setAdapter(adapter);
        this.addView(lv_list);

        btn_add=new ImageView(cc);
        btn_add.setImageDrawable(cc.getResources().getDrawable(R.mipmap.fh_icon_add));
        btn_add.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_add.setClickable(true);
        btn_add.setOnClickListener(listener_add);
        this.addView(btn_add);

        btn_del=new Deleter(cc,listener_del);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        this.addView(btn_del);

        typer=new Affairs.TypeFavorite();
        Dialog_add=new DialogFavorites(this.cc);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        CL.CLOGI("Fav on attached");
        discard=false;
        datas=typer.get_favorites(this.getContext());
        lv_list.setAdapter(adapter);
        adapter.notifyDataSetChanged();
        if(btn_del.getVisibility()==View.VISIBLE){
            btn_del.set_number(0);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        CL.CLOGI("Fav on detached");
        discard=true;
        clear_resource();
        datas.clear();
        datas=null;
        adapter.notifyDataSetChanged();
        if(del_state){
            del_state=false;
            del_number=0;
            btn_del.setVisibility(View.GONE);
            btn_add.setVisibility(View.VISIBLE);
            btn_del.set_number(0);
        }
    }
    private void clear_resource(){
        if(datas==null || datas.size()==0)return;
        for(int i=0;i<datas.size();++i){
            recycle_item(datas.get(i));
        }
    }
    private void recycle_item(Struct.StructWebsite item){
        if(item.bitmap_icon!=null && !item.bitmap_icon.isRecycled()){
            loader.recycle_bitmap(item.bitmap_icon);
        }
        item.bitmap_icon=null;
    }


    private Drawable def_icon;
    private CLBitmapLoader loader=new CLBitmapLoader(CLBitmapLoader.MB*6,"fav");
    private boolean discard=false;
    private CLPictureDownloader.DownloadEventer listener_downloader=new CLPictureDownloader.DownloadEventer() {
        @Override
        public void on_load_ratio(String file_name, int ratio, Object tag) {
        }
        @Override
        public void on_load_complete(String file_name, final Object tag) {
            for(int i=0;i<datas.size();++i){
                Struct.StructWebsite _item=datas.get(i);
                if(_item == tag && _item.bitmap_icon == null){
                    _item.bitmap_icon=loader.create_bitmap(_item.icon_file_name,CL.DIP2PX_INT(50),CL.DIP2PX_INT(50));
                    break;
                }
            }
            lv_list.post(new Runnable() {
                @Override
                public void run() {
                    for(int i=0;!discard && i<lv_list.getChildCount();++i){
                        View _v=lv_list.getChildAt(i);
                        if(_v instanceof ViewForFav){
                            ViewForFav _view=(ViewForFav)_v;
                            if(_view.data!=null && _view.data == tag){
                                if(_view.data.bitmap_icon!=null)_view.iv_icon.setImageBitmap(_view.data.bitmap_icon);
                                else _view.iv_icon.setImageDrawable(def_icon);
                            }
                        }
                    }
                    if(!discard && loader.whether_exceed_memory()){
                        int _start_position=lv_list.getFirstVisiblePosition()-lv_list.getHeaderViewsCount();
                        int _last_position=lv_list.getLastVisiblePosition();
                        for(int i=datas.size()-1;i>0;--i){
                            if(i<_start_position || i>_last_position){
                                Struct.StructWebsite _item=datas.get(i);
                                recycle_item(_item);
                                if(!loader.whether_exceed_memory())break;
                            }
                        }
                    }
                }
            });
        }
        @Override
        public void on_load_fail(String file_name, Object tag) {
        }
    };
    private ArrayList<Struct.StructWebsite> datas;
    private boolean del_state=false;
    private int del_number=0;
    private View.OnClickListener listener_item_click=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(del_state){
                if(v instanceof ViewForFav){
                    ViewForFav _vv=(ViewForFav)v;
                    _vv.data.selected=!_vv.data.selected;
                    if(_vv.data.selected) {
                        _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                        ++del_number;
                    }
                    else {
                        _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
                        --del_number;
                        if(del_number==0){
                            del_state=false;
                            btn_del.setVisibility(View.GONE);
                            btn_add.setVisibility(View.VISIBLE);
                            btn_add.startAnimation(anim_scale);
                            for(int i=0;i<datas.size();++i){
                                datas.get(i).selected=false;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }
                    btn_del.set_number(del_number);
                }
            }else{
                if(v instanceof ViewForFav){
                    ViewForFav _vv=(ViewForFav)v;
                    if(_vv.data!=null && _vv.data.url!=null)
                        CLBus.Share_Instance().send_msg_immediate(Global.Group_open_url,Global.Action_open_url_from_fav_history,_vv.data.url);
                }
            }
        }
    };
    private View.OnLongClickListener listener_item_click_long=new OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            del_state=!del_state;
            adapter.notifyDataSetChanged();
            if(del_state){
                btn_del.setVisibility(View.VISIBLE);
                btn_add.setVisibility(View.GONE);
                btn_del.startAnimation(anim_scale);
                listener_item_click.onClick(v);
            }else{
                del_number=0;
                btn_del.setVisibility(View.GONE);
                btn_add.setVisibility(View.VISIBLE);
                btn_add.startAnimation(anim_scale);
                for(int i=0;i<datas.size();++i){
                    datas.get(i).selected=false;
                }
            }
            return true;
        }
    };
    private class AdapterForFav extends BaseAdapter{
        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }
        @Override
        public Object getItem(int position) {
            return null;
        }
        @Override
        public long getItemId(int position) {
            return 0;
        }
        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new ViewForFav(cc);
            ViewForFav _v=(ViewForFav)cv;
            _v.set_data(datas.get(position));
            return cv;
        }
    }
    private class ViewForFav extends LinearLayout{

        private ImageView btn_selected;
        private ImageView iv_icon;
        private TextView tv_title,tv_context;
        private Struct.StructWebsite data;

        public ViewForFav(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setPadding(CL.DIP2PX_INT(15),CL.DIP2PX_INT(6),CL.DIP2PX_INT(8),CL.DIP2PX_INT(6));
            this.setGravity(Gravity.CENTER_VERTICAL);
            this.setClickable(true);
            this.setLongClickable(true);
            this.setOnClickListener(listener_item_click);
            this.setOnLongClickListener(listener_item_click_long);

            btn_selected=new ImageView(context);
            btn_selected.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),0,0,CL.DIP2PX_INT(15),0));
            btn_selected.setVisibility(View.GONE);
            this.addView(btn_selected);

            iv_icon=new ImageView(context);
            iv_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(24),CL.DIP2PX_INT(24)));
            this.addView(iv_icon);

            LinearLayout _ll_content=new LinearLayout(context);
            _ll_content.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,0,0));
            _ll_content.setOrientation(LinearLayout.VERTICAL);
            this.addView(_ll_content);

            tv_title= CLController.Get_TextView(context, CL.Get_LLLP(CL.WC,CL.WC,0,0,0,0),null,0xffaaaaaa,16,null);
            tv_title.setSingleLine();
            tv_title.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_title);
            tv_context= CLController.Get_TextView(context, CL.Get_LLLP(CL.WC,CL.WC,0,0,0,0),null,0xff888888,13,null);
            tv_context.setSingleLine();
            tv_context.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_context);
        }

        public void set_data(Struct.StructWebsite d){
            this.data=d;
            if(del_state){
                btn_selected.setVisibility(View.VISIBLE);
                if(this.data.selected)btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                else btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
            }
            else btn_selected.setVisibility(View.GONE);
            tv_title.setText(this.data.title);
            tv_context.setText(this.data.url);
            iv_icon.setImageBitmap(null);
            iv_icon.setImageDrawable(def_icon);
            if(this.data!=null){
                if(this.data.bitmap_icon!=null && !this.data.bitmap_icon.isRecycled()){
                    iv_icon.setImageBitmap(data.bitmap_icon);
                }
                else{
                    if(data.icon_file_name!=null){
                        String _name=this.data.icon_file_name.substring(this.data.icon_file_name.lastIndexOf('/')+1);
                        Global.Downloader.add_item("http://"+_name+"/favicon.ico",Global.Dir_thum.getAbsolutePath(),_name,listener_downloader,this.data);
                    }
                }
            }
        }
    }



    private static DialogFavorites Dialog_add;
    private static void Show_Add_Dialog(){
        if(Dialog_add==null)return;
        Dialog_add.show(null,null,null);
    }
    public static void Show_Add_Dialog(String title, String url, CLCallback.CB_TFO<Struct.StructWebsite> cber){
        if(Dialog_add==null)return;
        Dialog_add.show(title,url,cber);
    }
    private class DialogFavorites extends Dialog{

        public DialogFavorites(Context context) {
            super(context, android.R.style.Theme_Translucent_NoTitleBar);
        }


        private LinearLayout ll_main;
        private TextView tv_title;
        private EditText et_title,et_url;
        private CLController.DiscolourButton btn_cancel,btn_ok;
        private TranslateAnimation anim_shake;
        private String v_title,v_url;
        private CLCallback.CB_TFO<Struct.StructWebsite> cber_add;
        private Affairs.TypeFavorite typer=new Affairs.TypeFavorite();

        private View.OnClickListener listener_click_cancel=new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if(cber_add!=null)cber_add.on_callback_fail(0,"cancel");
            }
        };
        private View.OnClickListener listener_click_ok=new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String _title=et_title.getText().toString().trim();
                if(_title==null || _title.equals("")) {
                    et_title.startAnimation(anim_shake);
                    return;
                }
                String _url=et_url.getText().toString().trim();
                if(_url==null || _url.equals("")) {
                    et_url.startAnimation(anim_shake);
                    return;
                }

              //  _url=_url.toLowerCase();
                String _fix_url= Tools.Was_Web_Site(_url);
//                if(_fix_url==null) {
//                    _fix_url = Tools.Fix_Url(_url);
//                    String _fix_url2=Tools.Was_Web_Site(_fix_url);
//                    if(_fix_url2==null)_fix_url=_url;
//                }

                String _host=Tools.Get_Url_Host(_fix_url);
                if(_host==null)_host="default";
                Struct.StructWebsite _data=typer.add_favorite(getContext(),_title,_fix_url,new File(Global.Dir_thum,_host).getAbsolutePath());
                if(_data!=null && datas!=null){
                    for(int i=0;i<datas.size();++i){
                        Struct.StructWebsite _tmp=datas.get(i);
                        if(_tmp.ID==_data.ID){
                            datas.remove(i);
                            break;
                        }
                    }
                    datas.add(0,_data);
                }
                adapter.notifyDataSetChanged();
                dismiss();
                if(cber_add!=null) {
                    cber_add.on_callback_success(_data, "");
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_main_menu,Global.Action_main_menu);

                }
                else{
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_favorite,Global.Action_favorite_add,_data);
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_main_menu,Global.Action_main_menu);
                }
            }
        };

        @Override
        protected void onCreate(Bundle savedInstanceState) {
            super.onCreate(savedInstanceState);

            WindowManager.LayoutParams lp = this.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            this.getWindow().setAttributes(lp);
            this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            int _sh=CL.Set_Translucent_StatusBar(this.getWindow());

            FrameLayout _fl=new FrameLayout(this.getContext()){
                @Override
                protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
                    super.onLayout(changed, left, top, right, bottom);
                    if(changed){
                        int _w=this.getWidth();
                        int _h=this.getHeight();
                        if(_w<_h)setPadding((int)(_w*0.1f),getPaddingTop(),(int)(_w*0.1f),0);
                        else setPadding((int)(_h*0.26f),getPaddingTop(),(int)(_h*0.26f),0);
                        setVisibility(View.GONE);
                        postDelayed(new Runnable() {
                            @Override
                            public void run() {
                               setVisibility(View.VISIBLE);
                            }
                        },100);
                    }
                }
            };
            _fl.setPadding(0,_sh,0,0);
            _fl.setFocusable(true);
            _fl.setClickable(true);
            _fl.setFocusableInTouchMode(true);
            _fl.requestFocus();
            _fl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
            this.setContentView(_fl);

            ll_main=new LinearLayout(this.getContext());
            ll_main.setLayoutParams(CL.Get_FLLP(CL.MP,CL.WC,Gravity.CENTER));
            ll_main.setOrientation(LinearLayout.VERTICAL);
            RoundRectShape _shape=new RoundRectShape(new float[]{8,8,8,8,8,8,8,8}, null, null);
            ShapeDrawable _drawable=new ShapeDrawable(_shape);
            _drawable.getPaint().setColor(0xffa0a0a0);
            _drawable.getPaint().setStyle(Paint.Style.FILL);
            ll_main.setBackground(_drawable);
            ll_main.setClickable(true);
            _fl.addView(ll_main);

            tv_title=new TextView(this.getContext());
            tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15)));
            tv_title.setText(R.string.add_bookmark);
            tv_title.setTextColor(0xff252525);
            tv_title.setTextSize(18);
            ll_main.addView(tv_title);

            et_title=new EditText(this.getContext());
            et_title.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),CL.DIP2PX_INT(20)));
            et_title.setBackgroundResource(R.mipmap.editor_bg);
            et_title.setPadding(CL.DIP2PX_INT(6),et_title.getPaddingTop(),CL.DIP2PX_INT(6),et_title.getPaddingBottom());
            et_title.setHint(R.string.tip_bookmark_title);
            et_title.setHintTextColor(0xff383838);
            et_title.setTextColor(0xff1b1b1b);
            et_title.setTextSize(14);
            et_title.setSingleLine();
            et_title.setText(v_title);
            ll_main.addView(et_title);

            et_url=new EditText(this.getContext());
            et_url.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),0,CL.DIP2PX_INT(15),CL.DIP2PX_INT(15)));
            et_url.setBackgroundResource(R.mipmap.editor_bg);
            et_url.setPadding(CL.DIP2PX_INT(6),et_url.getPaddingTop(),CL.DIP2PX_INT(6),et_url.getPaddingBottom());
            et_url.setHint(R.string.tip_bookmark_website);
            et_url.setHintTextColor(0xff383838);
            et_url.setTextColor(0xff1b1b1b);
            et_url.setTextSize(14);
            et_url.setSingleLine();
            et_url.setText(v_url);
            ll_main.addView(et_url);

            LinearLayout _ll_btns=new LinearLayout(this.getContext());
            _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,0,CL.DIP2PX_INT(15), CL.DIP2PX_INT(12)));
            _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
            _ll_btns.setGravity(Gravity.RIGHT);
            ll_main.addView(_ll_btns);

            btn_cancel=CLController.Get_Discolour_Button(cc,CL.Get_LLLP(CL.WC,CL.DIP2PX_INT(35),0,0,CL.DIP2PX_INT(8),0),
                    this.getContext().getString(R.string.cancel),16,0xff005880,0xff0097dc,listener_click_cancel);
            btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
            btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
            _ll_btns.addView(btn_cancel);
            btn_ok=CLController.Get_Discolour_Button(cc,CL.Get_LLLP(CL.WC,CL.DIP2PX_INT(35),0,0,0,0),
                    this.getContext().getString(R.string.yes),16,0xff005880,0xff0097dc,listener_click_ok);
            btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
            btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
            _ll_btns.addView(btn_ok);

            anim_shake=new TranslateAnimation(0,CL.DIP2PX(8),0,0);
            anim_shake.setDuration(500);
            anim_shake.setInterpolator(new CycleInterpolator(5));
        }

        public void show(String title, String url, CLCallback.CB_TFO<Struct.StructWebsite> cber) {
            super.show();
            if(et_title!=null && et_url!=null) {
                et_title.setText(title);
                et_url.setText(url);
            }else{
                v_title=title;
                v_url=url;
            }
            this.cber_add=cber;
        }
    }
}
