package amazon.browser.lionpro.views;

import android.content.Context;
import android.view.MotionEvent;
import android.view.WindowManager;
import android.widget.ImageView;


import amazon.browser.lionpro.primary.Mainly;

/**
 * Created by EddyHu on 2017/4/8.
 */

public class FloatButton extends ImageView {
    private float x;
    private float y;

    private float mTouchX;
    private float mTouchY;

    private float mStartX;
    private float mStartY;


    // 触屏监听
    float lastX, lastY;
    int oldOffsetX, oldOffsetY;
    int tag = 0;// 悬浮球 所需成员变量
    private OnClickListener mClickListener;
    private WindowManager wm;
//    private WindowManager windowManager = (WindowManager) getContext()
//            .getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
    private WindowManager.LayoutParams windowManagerParams = ((Mainly) getContext()
            .getApplicationContext()).getWindowParams();

    public FloatButton(Context context, WindowManager wm) {
        super(context);
        // TODO Auto-generated constructor stub
        this.wm = wm;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event){



            final int action = event.getAction();
            float x = event.getX();
            float y = event.getY();
            if (tag == 0) {
                oldOffsetX = windowManagerParams.x; // 偏移量
                oldOffsetY = windowManagerParams.y; // 偏移量
            }
            if (action == MotionEvent.ACTION_DOWN) {
                lastX = x;
                lastY = y;
            } else if (action == MotionEvent.ACTION_MOVE) {
                windowManagerParams.x += (int) (x - lastX) / 3; // 减小偏移量,防止过度抖动
                windowManagerParams.y += (int) (y - lastY) / 3; // 减小偏移量,防止过度抖动
                tag = 1;
                wm.updateViewLayout(this, windowManagerParams);
            } else if (action == MotionEvent.ACTION_UP) {
                int newOffsetX = windowManagerParams.x;
                int newOffsetY = windowManagerParams.y;
                // 只要按钮一动位置不是很大,就认为是点击事件
                if (Math.abs(oldOffsetX - newOffsetX) <= 20
                        && Math.abs(oldOffsetY - newOffsetY) <= 20) {
                 //   onFloatViewClick();
                    if(mClickListener!=null) {
                        mClickListener.onClick(this);
                    }
                } else {
                    tag = 0;
                }
            }
            return super.onTouchEvent(event);
    }

    @Override
    public void setOnClickListener(OnClickListener l) {
        this.mClickListener = l;
    }

    private void updateViewPosition() {
        windowManagerParams.x = (int) (x - mTouchX);
        windowManagerParams.y = (int) (y - mTouchY);
        wm.updateViewLayout(this, windowManagerParams);
    }
}


/*
        Rect frame = new Rect();
        getWindowVisibleDisplayFrame(frame);
        int statusBarHeight = 0;//frame.top;
        System.out.println("statusBarHeight:"+statusBarHeight);

        x = event.getRawX();
        y = event.getRawY() - statusBarHeight;
        Log.i("tag", "currX" + x + "====currY" + y);

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mTouchX = event.getX();
                mTouchY = event.getY();
                mStartX = x;
                mStartY = y;
                Log.i("tag", "startX" + mTouchX + "====startY"
                        + mTouchY);
                break;
            case MotionEvent.ACTION_MOVE:
                updateViewPosition();
                break;
            case MotionEvent.ACTION_UP:
                updateViewPosition();
                mTouchX = mTouchY = 0;
                if ((x - mStartX) < 5 && (y - mStartY) < 5) {
                    if(mClickListener!=null) {
                        mClickListener.onClick(this);
                    }
                }
                break;
        }
        return super.onTouchEvent(event);
*/