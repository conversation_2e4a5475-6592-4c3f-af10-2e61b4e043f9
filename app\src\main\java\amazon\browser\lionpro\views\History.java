package amazon.browser.lionpro.views;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;
import android.widget.AdapterView;
import android.widget.BaseExpandableListAdapter;
import android.widget.ExpandableListView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Affairs;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.primary.Global;

import java.util.ArrayList;

import lion.CL;
import lion.CLBitmapLoader;
import lion.CLBus;
import lion.CLCallback;
import lion.CLController;
import lion.CLDialog;
import lion.CLPictureDownloader;

/**
 * Created by leron on 2016/4/19.
 */
public class History extends FrameLayout{


    private Context cc;
    private ExpandableListView elv_list;
    private ImageView btn_clear;
    private Deleter btn_del;
    private AdapterForList adapter;
    private ScaleAnimation anim_scale;


    private View.OnClickListener listener_clear=new OnClickListener() {
        @Override
        public void onClick(View v) {
            CLDialog.Get_Confirm_Dialog(cc, cc.getResources().getString(R.string.tip_clear_history), new CLCallback.CB_TF() {
                @Override
                public void on_callback_success() {
                    clear_resource();
                    datas.clear();
                    adapter.notifyDataSetChanged();
                    btn_clear.setVisibility(View.GONE);
                    typer.clear_history(cc);
                }
                @Override
                public void on_callback_fail(int code, String msg) {}
            }).show();
        }
    };


    public History(Context context) {
        super(context);
        this.cc=context;
        this.setFocusable(true);
        this.setFocusableInTouchMode(true);
        def_icon=cc.getResources().getDrawable(R.mipmap.address_web);
        def_arrow_down=cc.getResources().getDrawable(R.mipmap.down_arrow);
        def_arrow_up=cc.getResources().getDrawable(R.mipmap.up_arrow);

        ImageView _empty=new ImageView(context);
        _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
        _empty.setBackgroundResource(R.mipmap.no_data);
        this.addView(_empty);

        elv_list=new ExpandableListView(cc){
            @Override
            public boolean onInterceptTouchEvent(MotionEvent ev) {
                if(btn_del.getVisibility()==View.VISIBLE)btn_del.deformation(false);
                return super.onInterceptTouchEvent(ev);
            }
        };
        elv_list.setOnItemLongClickListener(listener_list_click_long);
        elv_list.setOnGroupClickListener(listener_list_click_group);
        elv_list.setOnChildClickListener(listener_list_click_item);
        elv_list.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP, Gravity.FILL));
        elv_list.setCacheColorHint(Color.TRANSPARENT);
        elv_list.setDivider(new ColorDrawable(0xff313131));
        elv_list.setChildDivider(new ColorDrawable(0xff313131));
        elv_list.setDividerHeight(1);
        elv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
        elv_list.setGroupIndicator(null);
        elv_list.setChildIndicator(null);
        elv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
        elv_list.setEmptyView(_empty);
        adapter=new AdapterForList();
        elv_list.setAdapter(adapter);
        this.addView(elv_list);


        btn_clear =new ImageView(cc);
        btn_clear.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_clear));
        btn_clear.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_clear.setClickable(true);
        btn_clear.setOnClickListener(listener_clear);
        this.addView(btn_clear);

        btn_del=new Deleter(cc,listener_deleter);
        btn_del.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC, Gravity.BOTTOM|Gravity.RIGHT,0,0,CL.DIP2PX_INT(22),CL.DIP2PX_INT(22)));
        btn_del.setVisibility(View.GONE);
        this.addView(btn_del);

        anim_scale=new ScaleAnimation(0.2f,1.0f,0.2f,1.0f, Animation.RELATIVE_TO_SELF,0.5f,Animation.RELATIVE_TO_SELF,0.5f);
        anim_scale.setDuration(400);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        CL.CLOGI("History on attached");
        discard=false;
        datas=typer.get_history_group(cc);
        //CL.CLOGI("history group size:"+datas.size());
        if(datas==null || datas.size()==0)btn_clear.setVisibility(View.GONE);
        else btn_clear.setVisibility(View.VISIBLE);
        elv_list.setAdapter(adapter);
        adapter.notifyDataSetChanged();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        CL.CLOGI("History on detached");
        discard=true;
        clear_resource();
        datas.clear();
        datas=null;
        del_state_group=false;
        del_state_item=false;
        adapter.notifyDataSetChanged();

        del_number=0;
        btn_del.setVisibility(View.GONE);
        btn_clear.setVisibility(View.VISIBLE);
        btn_del.set_number(0);
        btn_del.deformation_direct(false);
    }
    private void clear_resource(){
        if(datas==null || datas.size()==0)return;
        for(int i=0;i<datas.size();++i){
            Struct.StructHistoryGroup _g=datas.get(i);
            if(_g.datas!=null){
                for(int j=0;j<_g.datas.size();++j) {
                    recycle_item(_g.datas.get(j));
                }
            }
        }
    }
    private void recycle_item(Struct.StructWebsite item){
        if(item.bitmap_icon!=null && !item.bitmap_icon.isRecycled()){
            loader.recycle_bitmap(item.bitmap_icon);
        }
        item.bitmap_icon=null;
    }


    private Drawable def_icon;
    private Drawable def_arrow_down,def_arrow_up;
    private ArrayList<Struct.StructHistoryGroup> datas;
    private Affairs.TypeHistory typer=new Affairs.TypeHistory();
    private CLBitmapLoader loader=new CLBitmapLoader(CLBitmapLoader.MB*6,"history");
    private boolean discard=false;
    private boolean del_state_group,del_state_item;
    private int del_number=0;

    private CLPictureDownloader.DownloadEventer listener_downloader=new CLPictureDownloader.DownloadEventer() {
        @Override
        public void on_load_ratio(String file_name, int ratio, Object tag) {
        }
        @Override
        public void on_load_complete(String file_name, final Object tag) {
            for(int i=0;i<datas.size();++i){
                Struct.StructHistoryGroup _gd=datas.get(i);
                for(int j=0;_gd.datas!=null && j<_gd.datas.size();++j){
                    Struct.StructWebsite _item=_gd.datas.get(j);
                    if(_item == tag && _item.bitmap_icon == null){
                        _item.bitmap_icon=loader.create_bitmap(_item.icon_file_name,CL.DIP2PX_INT(50),CL.DIP2PX_INT(50));
                        break;
                    }
                }
            }
            elv_list.post(new Runnable() {
                @Override
                public void run() {
                    for(int i=0;!discard && i<elv_list.getChildCount();++i){
                        View _v=elv_list.getChildAt(i);
                        if(_v instanceof ViewForItem){
                            ViewForItem _view=(ViewForItem)_v;
                            if(_view.data!=null && _view.data == tag){
                                if(_view.data.bitmap_icon!=null)_view.iv_icon.setImageBitmap(_view.data.bitmap_icon);
                                else _view.iv_icon.setImageDrawable(def_icon);
                            }
                        }
                    }
                    if(!discard && loader.whether_exceed_memory()){
                        int _start_position=elv_list.getFirstVisiblePosition()-elv_list.getHeaderViewsCount();
                        int _last_position=elv_list.getLastVisiblePosition();

                        int _index=0;
                        for(int i=0;i<datas.size();++i){
                            if(elv_list.isGroupExpanded(i)){
                                Struct.StructHistoryGroup _items=datas.get(i);
                                if(_items.datas!=null){
                                    for(int j=0;j<_items.datas.size();++j){
                                        ++_index;
                                        Struct.StructWebsite _gi=_items.datas.get(j);
                                        if(_index<_start_position || _index>_last_position) {
                                            recycle_item(_gi);
                                            if (!loader.whether_exceed_memory()) {
                                                return;
                                            }
                                        }
                                    }
                                }
                            }
                            ++_index;
                        }
                    }
                }
            });
        }
        @Override
        public void on_load_fail(String file_name, Object tag) {
        }
    };


    private void reset_selected_data(){
        if(datas!=null)
            for(int i=0;i<datas.size();++i){
                Struct.StructHistoryGroup _g=datas.get(i);
                _g.selected=false;
                if(_g.datas!=null)
                    for(int j=0;j<_g.datas.size();++j){
                        _g.datas.get(j).selected=false;
                    }
            }
    }
    private Deleter.Eventer listener_deleter=new Deleter.Eventer() {
        @Override
        public void on_icon_click(boolean expand) {
            if(expand)btn_del.deformation(false);
        }
        @Override
        public void on_cancel_click() {
            reset_selected_data();
            del_state_item=false;
            del_state_group=false;
            del_number=0;
            btn_del.deformation_direct(false);
            btn_del.setVisibility(View.GONE);
            btn_clear.setVisibility(View.VISIBLE);
            btn_clear.startAnimation(anim_scale);
            adapter.notifyDataSetChanged();
        }
        @Override
        public void on_delete_click() {
            if(del_state_group){
                CL.CLOGI("delete group");
                clear_resource();
                if(datas!=null) {
                    for (int i = 0; i < datas.size(); ++i) {
                        if (datas.get(i).selected) {
                            typer.delete_history_by_day(cc,datas.get(i).day);
                            datas.remove(i);
                            --i;
                        }
                    }
                }
                del_state_group=false;
                adapter.notifyDataSetChanged();
                del_number=0;
                btn_del.set_number(del_number);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if(datas==null || datas.size()==0)btn_clear.setVisibility(View.GONE);
                else {
                    btn_clear.setVisibility(View.VISIBLE);
                    btn_clear.startAnimation(anim_scale);
                }
            }else if(del_state_item){
                CL.CLOGI("delete item");
                adapter.notifyDataSetInvalidated();
                if(datas!=null) {
                    ArrayList<Integer> _ids=new ArrayList<>();
                    for (int i = 0; i < datas.size(); ++i) {
                        Struct.StructHistoryGroup _g = datas.get(i);
                        if (_g.datas != null) {
                            for (int j = 0; j < _g.datas.size(); ++j) {
                                Struct.StructWebsite _item = _g.datas.get(j);
                                if (_item.selected) {
                                    recycle_item(_item);
                                    _ids.add(_item.ID);
                                    _g.datas.remove(j);
                                    _g.history_size-=1;
                                    --j;
                                }
                            }
                        }
//                        if(_g.datas!=null && _g.datas.size()==0){
//                            datas.remove(i);
//                            --i;
//                        }
                    }
                    typer.delete_history_by_IDs(cc,_ids);
                }
                del_state_item=false;
                adapter.notifyDataSetChanged();
                del_number=0;
                btn_del.set_number(del_number);
                btn_del.deformation_direct(false);
                btn_del.setVisibility(View.GONE);
                if(datas==null || datas.size()==0)btn_clear.setVisibility(View.GONE);
                else {
                    btn_clear.setVisibility(View.VISIBLE);
                    btn_clear.startAnimation(anim_scale);
                }
            }
        }
    };

    private AdapterView.OnItemLongClickListener listener_list_click_long=new AdapterView.OnItemLongClickListener() {
        @Override
        public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
            if(view instanceof ViewForGroup){
                CL.CLOGI("on group long click");
                reset_selected_data();
                if(del_state_item)del_state_item=false;
                if(!del_state_group) {
                    del_state_group=true;
                    ((ViewForGroup) view).data.selected = true;
                    int _gs=adapter.getGroupCount();
                    for(int i=0;i<_gs;++i){
                        if(elv_list.isGroupExpanded(i))elv_list.collapseGroup(i);
                    }
                    del_number=((ViewForGroup) view).data.history_size;
                    btn_clear.setVisibility(View.GONE);
                    btn_del.setVisibility(View.VISIBLE);
                    btn_del.set_number(del_number);
                    btn_del.startAnimation(anim_scale);
                }else{
                    del_state_group=false;
                    del_number=0;
                    btn_del.set_number(del_number);
                    btn_del.setVisibility(View.GONE);
                    btn_clear.setVisibility(View.VISIBLE);
                    btn_clear.startAnimation(anim_scale);
                }
                adapter.notifyDataSetChanged();
            }else if(view instanceof ViewForItem){
                CL.CLOGI("on child long click");
                reset_selected_data();
                if(!del_state_item) {
                    del_state_item=true;
                    ((ViewForItem) view).data.selected = true;
                    del_number=1;
                    btn_clear.setVisibility(View.GONE);
                    btn_del.setVisibility(View.VISIBLE);
                    btn_del.set_number(del_number);
                    btn_del.startAnimation(anim_scale);
                }else{
                    del_state_item=false;
                    del_number=0;
                    btn_del.set_number(del_number);
                    btn_del.setVisibility(View.GONE);
                    btn_clear.setVisibility(View.VISIBLE);
                    btn_clear.startAnimation(anim_scale);
                }
                adapter.notifyDataSetChanged();
            }
            return true;
        }
    };
    private ExpandableListView.OnGroupClickListener listener_list_click_group=new ExpandableListView.OnGroupClickListener() {
        @Override
        public boolean onGroupClick(ExpandableListView parent, View v, int groupPosition, long id) {
            if(del_state_group){
                ViewForGroup _vv=(ViewForGroup)v;
                _vv.data.selected=!_vv.data.selected;
                if(_vv.data.selected){
                    _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                    del_number+=_vv.data.history_size;
                    btn_del.set_number(del_number);
                }else {
                    _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
                    del_number-=_vv.data.history_size;
                    btn_del.set_number(del_number);
                    if(del_number==0){
                        reset_selected_data();
                        del_state_group=false;
                        btn_del.setVisibility(View.GONE);
                        btn_clear.setVisibility(View.VISIBLE);
                        btn_clear.startAnimation(anim_scale);
                        adapter.notifyDataSetChanged();
                    }
                }
            }else {
                if (elv_list.isGroupExpanded(groupPosition)) elv_list.collapseGroup(groupPosition);
                else elv_list.expandGroup(groupPosition, true);
            }
            return true;
        }
    };
    private ExpandableListView.OnChildClickListener listener_list_click_item=new ExpandableListView.OnChildClickListener() {
        @Override
        public boolean onChildClick(ExpandableListView parent, View v, int groupPosition, int childPosition, long id) {
            if(del_state_item){
                ViewForItem _vv=(ViewForItem)v;
                _vv.data.selected=!_vv.data.selected;
                if(_vv.data.selected){
                    _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                    del_number+=1;
                    btn_del.set_number(del_number);
                }else {
                    _vv.btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
                    del_number-=1;
                    btn_del.set_number(del_number);
                    if(del_number==0){
                        reset_selected_data();
                        del_state_item=false;
                        btn_del.setVisibility(View.GONE);
                        btn_clear.setVisibility(View.VISIBLE);
                        btn_clear.startAnimation(anim_scale);
                        adapter.notifyDataSetChanged();
                    }
                }
            }else{
                ViewForItem _vv=(ViewForItem)v;
                if(_vv.data!=null && _vv.data.url!=null)
                    CLBus.Share_Instance().send_msg_immediate(Global.Group_open_url,Global.Action_open_url_from_fav_history,_vv.data.url);
            }
            return true;
        }
    };


    private class AdapterForList extends BaseExpandableListAdapter{
        @Override
        public int getGroupCount() {
            if(datas==null)return 0;
            return datas.size();
        }
        @Override
        public int getChildrenCount(int groupPosition) {
            ArrayList<Struct.StructWebsite> _ds=datas.get(groupPosition).datas;
            if(_ds==null)return 0;
            return _ds.size();
        }
        @Override
        public Object getGroup(int groupPosition) {
            return null;
        }
        @Override
        public Object getChild(int groupPosition, int childPosition) {
            return null;
        }
        @Override
        public long getGroupId(int groupPosition) {
            return 0;
        }
        @Override
        public long getChildId(int groupPosition, int childPosition) {
            return 0;
        }
        @Override
        public boolean hasStableIds() {
            return false;
        }
        @Override
        public View getGroupView(int groupPosition, boolean isExpanded, View cv, ViewGroup parent) {
            if(cv==null)cv=new ViewForGroup(cc);
            ViewForGroup _v=(ViewForGroup)cv;
            _v.set_data(datas.get(groupPosition),isExpanded);
            return cv;
        }
        @Override
        public View getChildView(int groupPosition, int childPosition, boolean isLastChild, View cv, ViewGroup parent) {
            if(cv==null)cv=new ViewForItem(cc);
            ViewForItem _v=(ViewForItem)cv;
            _v.set_data(datas.get(groupPosition).datas.get(childPosition));
            return cv;
        }
        @Override
        public boolean isChildSelectable(int groupPosition, int childPosition) {
            return true;
        }
    }
    private class ViewForGroup extends LinearLayout{

        private Struct.StructHistoryGroup data;
        private ImageView btn_selected;
        private TextView tv_date,tv_count;
        private ImageView iv_arrow;

        public ViewForGroup(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setPadding(CL.DIP2PX_INT(15),CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12));
            this.setGravity(Gravity.CENTER_VERTICAL);

            btn_selected=new ImageView(context);
            btn_selected.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),0,0,CL.DIP2PX_INT(15),0));
            btn_selected.setVisibility(View.GONE);
            this.addView(btn_selected);

            tv_date= CLController.Get_TextView(context, CL.Get_LLLP(CL.WC,CL.WC),null,0xffaaaaaa,18,null);
            tv_date.setSingleLine();
            this.addView(tv_date);
            tv_count= CLController.Get_TextView(context, CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,0,0),null,0xff888888,14,null);
            this.addView(tv_count);

            FrameLayout _fl=new FrameLayout(context);
            _fl.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(60),CL.MP));
            this.addView(_fl);
            iv_arrow=new ImageView(context);
            iv_arrow.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
            _fl.addView(iv_arrow);
        }

        public void set_data(Struct.StructHistoryGroup d,boolean expand){
            this.data=d;
            if(del_state_group){
                btn_selected.setVisibility(View.VISIBLE);
                if(this.data.selected)btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                else btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
                iv_arrow.setVisibility(View.GONE);
            }
            else {
                btn_selected.setVisibility(View.GONE);
                iv_arrow.setVisibility(View.VISIBLE);
            }
            tv_date.setText(this.data.day);
            tv_count.setText("("+this.data.history_size+")");
            if(expand)iv_arrow.setImageDrawable(def_arrow_up);
            else iv_arrow.setImageDrawable(def_arrow_down);
        }
    }
    private class ViewForItem extends LinearLayout{

        private ImageView btn_selected;
        private ImageView iv_icon;
        private TextView tv_title,tv_context;
        private Struct.StructWebsite data;

        public ViewForItem(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setPadding(CL.DIP2PX_INT(15),CL.DIP2PX_INT(6),CL.DIP2PX_INT(8),CL.DIP2PX_INT(6));
            this.setGravity(Gravity.CENTER_VERTICAL);

            btn_selected=new ImageView(context);
            btn_selected.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(30),0,0,CL.DIP2PX_INT(15),0));
            btn_selected.setVisibility(View.GONE);
            this.addView(btn_selected);

            iv_icon=new ImageView(context);
            iv_icon.setLayoutParams(CL.Get_LLLP(CL.DIP2PX_INT(24),CL.DIP2PX_INT(24)));
            this.addView(iv_icon);

            LinearLayout _ll_content=new LinearLayout(context);
            _ll_content.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),0,0,0));
            _ll_content.setOrientation(LinearLayout.VERTICAL);
            this.addView(_ll_content);

            tv_title= CLController.Get_TextView(context, CL.Get_LLLP(CL.WC,CL.WC,0,0,0,0),null,0xffaaaaaa,16,null);
            tv_title.setSingleLine();
            tv_title.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_title);
            tv_context= CLController.Get_TextView(context, CL.Get_LLLP(CL.WC,CL.WC,0,0,0,0),null,0xff888888,13,null);
            tv_context.setSingleLine();
            tv_context.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_context);
        }

        public void set_data(Struct.StructWebsite d){
            tv_title.setText("this is 标题");
            tv_context.setText("http://www.baidu.com");
            iv_icon.setImageDrawable(def_icon);

            this.data=d;
            if(del_state_item){
                btn_selected.setVisibility(View.VISIBLE);
                if(this.data.selected)btn_selected.setBackgroundResource(R.mipmap.comm_select_2);
                else btn_selected.setBackgroundResource(R.mipmap.comm_select_1);
            }
            else btn_selected.setVisibility(View.GONE);
            tv_title.setText(this.data.title);
            tv_context.setText(this.data.url);
            iv_icon.setImageBitmap(null);
            iv_icon.setImageDrawable(def_icon);
            if(this.data!=null){
                if(this.data.bitmap_icon!=null && !this.data.bitmap_icon.isRecycled()){
                    iv_icon.setImageBitmap(data.bitmap_icon);
                }
                else{
                    if(data.icon_file_name!=null){
                        String _name=this.data.icon_file_name.substring(this.data.icon_file_name.lastIndexOf('/')+1);
                        Global.Downloader.add_item("http://"+_name+"/favicon.ico",Global.Dir_thum.getAbsolutePath(),_name,listener_downloader,this.data);
                    }
                }
            }
        }
    }
}
