package amazon.browser.lionpro.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.view.View;

import amazon.browser.video.downloader.R;

import lion.CL;

/**
 * Created by leron on 2016/6/30.
 */
public class Leida extends View{


    private Paint paint;
    private Drawable dwe_bg;//dwe_toy;

    public Leida(Context context) {
        super(context);

        paint=new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(Color.RED);
        paint.setTextSize(CL.DIP2PX(17));
        paint.setTextAlign(Paint.Align.CENTER);
//        paint.setFakeBoldText(true);
        dwe_bg=context.getResources().getDrawable(R.mipmap.leida_bg);
//        dwe_toy=context.getResources().getDrawable(R.mipmap.leida_yellow);

        CL.CLOGI("width:"+dwe_bg.getIntrinsicWidth());
        CL.CLOGI("height:"+dwe_bg.getIntrinsicHeight());
    }

    public void update_count(int size){
        if(size<0)size=0;
        if(ui_last_count==size)return;
        if(this.count==size)return;
        this.count=size;
        number_action=true;
        number_scale=1.0f;
        postInvalidate();
    }

    private boolean number_action=false;
    private float number_scale=0.1f;

    //    private long last_time=0;
//    private float angle=0;
    private int count=0;
    private int ui_last_count=0;
    @Override
    public void draw(Canvas canvas) {

        super.draw(canvas);
        int _x = (this.getWidth() - dwe_bg.getIntrinsicWidth()) / 2;
        int _y = (this.getHeight() - dwe_bg.getIntrinsicHeight()) / 2;
        dwe_bg.setBounds(_x, _y, _x + dwe_bg.getIntrinsicWidth(), _y + dwe_bg.getIntrinsicHeight());
        dwe_bg.draw(canvas);

//        long _crt_time=SystemClock.uptimeMillis();
//        if(last_time==0)last_time=_crt_time;
//        long _cha=_crt_time-last_time;
//        if(_cha>20) {
//            last_time = _crt_time;
//            angle += _cha / 12;
//            if (angle > 360) angle = angle % 360;
//        }


        if (number_action) {
            number_scale -= 0.2f;
            if (number_scale <= 0 && count == 0) {
                number_action = false;
                ui_last_count = count;
            } else if (ui_last_count == 0) {
                float _ss = Math.abs(number_scale - 1.0f);
                canvas.save();
                canvas.scale(_ss, _ss, this.getWidth() / 2, this.getHeight() / 2);
                canvas.drawText(String.valueOf(count), this.getWidth() / 2, this.getHeight() / 2 + CL.Get_DrawText_YPoint(paint), paint);
                canvas.restore();
                if (_ss >= 1.0f) {
                    number_action = false;
                    ui_last_count = count;
                }
            } else {
                float _ss = Math.abs(number_scale);
                canvas.save();
                canvas.scale(_ss, _ss, this.getWidth() / 2, this.getHeight() / 2);
                if (number_scale > 0)
                    canvas.drawText(String.valueOf(ui_last_count), this.getWidth() / 2, this.getHeight() / 2 + CL.Get_DrawText_YPoint(paint), paint);
                else
                    canvas.drawText(String.valueOf(count), this.getWidth() / 2, this.getHeight() / 2 + CL.Get_DrawText_YPoint(paint), paint);
                canvas.restore();
                if (number_scale <= -1.0f) {
                    number_action = false;
                    ui_last_count = count;
                }
            }
            postInvalidateDelayed(60);
        } else {
            if (count > 0)
                canvas.drawText(String.valueOf(count), this.getWidth() / 2, this.getHeight() / 2 + CL.Get_DrawText_YPoint(paint), paint);
        }

//        canvas.save();
//        canvas.rotate(angle,this.getWidth()/2,this.getHeight()/2);
//        int _xx=this.getWidth()/2-dwe_toy.getIntrinsicWidth()/2;
//        int _yy=this.getHeight()/2-dwe_toy.getIntrinsicHeight();
//        dwe_toy.setBounds(_xx,_yy,_xx+dwe_toy.getIntrinsicWidth(),_yy+dwe_toy.getIntrinsicHeight());
//        dwe_toy.draw(canvas);
//        canvas.restore();
    }
}
