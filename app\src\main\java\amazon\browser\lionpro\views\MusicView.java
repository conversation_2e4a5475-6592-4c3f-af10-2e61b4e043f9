package amazon.browser.lionpro.views;

import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MusicManager;
import amazon.browser.lionpro.downloader.Server;

import lion.CL;
import lion.CLController;
import lion.CLTools;

/**
 * Created by leron on 2016/7/17.
 */
public class MusicView extends LinearLayout{

    private Context cc;
    private MusicViewDialog dialog_music;
    private ImageView iv_icon;
    private TextView tv_title;
    private TextView tv_time;
    private ImageView btn_play,btn_next;
    private String music_time_size="00:00";
    private boolean is_stop=false;

    private View.OnClickListener listener_menu=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(dialog_music==null)return;
            dialog_music.show();
        }
    };

    private View.OnClickListener listener_btn_play_pause=new OnClickListener() {
        @Override
        public void onClick(View v) {
            MusicManager _mm=Server.Share_Music();
            if(_mm==null)return;
            _mm.play_pause();
        }
    };
    private View.OnClickListener listener_btn_next=new OnClickListener() {
        @Override
        public void onClick(View v) {
            MusicManager _mm=Server.Share_Music();
            if(_mm==null)return;
            if(is_stop)_mm.stop_music();
            else _mm.play_next();
        }
    };

    private MusicManager.Eventer listener_music=new MusicManager.Eventer() {
        @Override
        public void on_data_update() {

        }
        @Override
        public void on_music_change(Data.StructDLItem item) {
            if(item==null){
                setVisibility(View.GONE);
                return;
            }
            setVisibility(View.VISIBLE);
            String _t=item.name;
            if(_t==null || _t.isEmpty())_t=item.title;
            tv_title.setText(_t);
            music_time_size=CLTools.Get_Time_00_00_String(Server.Share_Music().get_current_duration());
            tv_time.setText("00:00"+" - "+music_time_size);
            handler.removeMessages(1000);
            handler.sendEmptyMessage(1000);
        }
        @Override
        public void on_status_change(int state) {
            if(state==0 || state==1){
                handler.removeMessages(1000);
                btn_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_play));
                btn_next.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_stop));
                is_stop=true;
            }else if(state==2){
                handler.sendEmptyMessage(1000);
                btn_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_pause));
                btn_next.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_next));
                is_stop=false;
            }
        }
    };

    private Handler handler=new Handler(){
        @Override
        public void handleMessage(Message msg) {
            if(msg.what==1000){
                tv_time.setText(CLTools.Get_Time_00_00_String(Server.Share_Music().get_current_position())+" - "+music_time_size);
                handler.sendEmptyMessageDelayed(1000,1000);
            }
        }
    };


    public MusicView(Context context,MusicViewDialog dialog) {
        super(context);
        this.cc=context;
        this.dialog_music=dialog;
        this.setOrientation(LinearLayout.HORIZONTAL);
        this.setBackgroundColor(0xff15181b);
        this.setGravity(Gravity.CENTER_VERTICAL);


        iv_icon= CLController.Get_ImageView(cc, CL.Get_LLLP(CL.DIP2PX_INT(38),CL.DIP2PX_INT(36),CL.DIP2PX_INT(1),CL.DIP2PX_INT(6),CL.DIP2PX_INT(1),CL.DIP2PX_INT(6)),
                CL.Get_StateList_Drawable(cc, R.mipmap.icon_music_click,R.mipmap.icon_music_normal),listener_menu);
        this.addView(iv_icon);

        LinearLayout _ll_content=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,0,0,CL.DIP2PX_INT(8),0),LinearLayout.VERTICAL,null);
        this.addView(_ll_content);
        tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC),"",0xffa1a1a1,14,null);
        tv_title.setSingleLine();
        tv_title.setEllipsize(TextUtils.TruncateAt.END);
        tv_time=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC),"",0xffa1a1a1,12,null);
        _ll_content.addView(tv_title);
        _ll_content.addView(tv_time);

        btn_play=CLController.Get_ImageView(cc, CL.Get_LLLP(CL.WC, CL.WC,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                null, listener_btn_play_pause);
        this.addView(btn_play);

        btn_next=CLController.Get_ImageView(cc, CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(18),0),
                cc.getResources().getDrawable(R.mipmap.icon_music_next),listener_btn_next);
        this.addView(btn_next);

        MusicManager.Add_Listener(listener_music);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        MusicManager _mm=Server.Share_Music();
        if(_mm!=null){
            listener_music.on_status_change(_mm.get_current_state());
            listener_music.on_music_change(_mm.get_current_music());
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        handler.removeMessages(1000);
    }
}
