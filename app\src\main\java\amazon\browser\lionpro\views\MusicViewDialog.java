package amazon.browser.lionpro.views;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.AbsListView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.Data;
import amazon.browser.lionpro.downloader.MusicManager;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.primary.Global;

import java.util.ArrayList;

import lion.CL;
import lion.CLActivity;
import lion.CLCallback;
import lion.CLController;
import lion.CLHelper;
import lion.CLTools;

/**
 * Created by leron on 2016/7/17.
 */
public class MusicViewDialog extends Dialog {

    private CLActivity cc;
    private CTMView ctm_view;


    public MusicViewDialog(CLActivity context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);
        this.cc=context;
        ctm_view=new CTMView(cc);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int _sh= CL.Set_Translucent_StatusBar(this.getWindow());
        ctm_view.setPadding(0,_sh,0,0);
        this.setContentView(ctm_view);
    }

    @Override
    public void onBackPressed() {
        dismiss();
    }

    @Override
    public void dismiss() {
        if(is_dismissing)return;
        is_dismissing=true;
        ctm_view.go_dismiss();
    }

    private boolean is_dismissing=false;

    private Animation.AnimationListener listener_anim_hide=new Animation.AnimationListener() {
        @Override
        public void onAnimationStart(Animation animation) {
        }
        @Override
        public void onAnimationEnd(Animation animation) {
            MusicViewDialog.super.dismiss();
        }
        @Override
        public void onAnimationRepeat(Animation animation) {

        }
    };

    @Override
    public void show() {
        super.show();
        is_dismissing=false;
        ctm_view.go_show();
    }




    private class CTMView extends LinearLayout{

        private ImageView btn_mode;
        private ListView lv_list;
        private AdapterForPL adapter;
        private TranslateAnimation anim_show,anim_hide;

        private LinearLayout menu;
        private PopupWindow poper;

        private LinearLayout ll_player;
        private TextView tv_title;
        private TextView tv_time;
        private ImageView btn_play,btn_next;
        private String music_time_size="00:00";
        private View.OnClickListener listener_btn_play_pause=new OnClickListener() {
            @Override
            public void onClick(View v) {
                MusicManager _mm=Server.Share_Music();
                if(_mm==null)return;
                _mm.play_pause();
            }
        };
        private Handler handler=new Handler(){
            @Override
            public void handleMessage(Message msg) {
                if(msg.what==1000){
                    tv_time.setText(CLTools.Get_Time_00_00_String(Server.Share_Music().get_current_position())+" - "+music_time_size);
                    handler.sendEmptyMessageDelayed(1000,1000);
                }
            }
        };


        private Data.StructDLItem crt_item;
        private boolean is_stop=false;

        private View.OnClickListener listener_mode=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(poper==null){
                    menu=CLController.Get_LinearLayout(cc,null,LinearLayout.VERTICAL,null);
                    LinearLayout _ll_sx=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,listener_mode_sx);
                    _ll_sx.setGravity(Gravity.CENTER_VERTICAL);
                    _ll_sx.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0x993d6f59)));
                    _ll_sx.addView(CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(16),0),
                            cc.getResources().getDrawable(R.mipmap.icon_music_sx),null));
                    _ll_sx.addView(CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,0,0),cc.getResources().getString(R.string.mode_shunxu),0xffffffff,15,null));
                    menu.addView(_ll_sx);
                    menu.addView(CLController.Get_TextView_Divider(cc,CL.Get_LP(CL.MP,1),0xff313131));
                    LinearLayout _ll_sj=CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.DIP2PX_INT(50)),LinearLayout.HORIZONTAL,listener_mode_sj);
                    _ll_sj.setGravity(Gravity.CENTER_VERTICAL);
                    _ll_sj.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0x993d6f59)));
                    _ll_sj.addView(CLController.Get_ImageView(cc,CL.Get_LLLP(CL.WC,CL.WC,CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(16),0),
                            cc.getResources().getDrawable(R.mipmap.icon_music_sj),null));
                    _ll_sj.addView(CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,0,0,0,0),cc.getResources().getString(R.string.mode_suiji),0xffffffff,15,null));
                    menu.addView(_ll_sj);
                    poper=new PopupWindow(menu,CL.DIP2PX_INT(156),CL.DIP2PX_INT(100),true);
                    poper.setBackgroundDrawable(new ColorDrawable(0xff1e1e1e));
                    poper.setOutsideTouchable(true);
                    poper.setTouchable(true);
                }
                poper.showAsDropDown(btn_mode,-CL.DIP2PX_INT(80),0);
            }
        };
        private View.OnClickListener listener_mode_sx=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(poper!=null && poper.isShowing())poper.dismiss();
                Server.Share_Music().set_play_mode(0);
                btn_mode.setBackground(cc.getResources().getDrawable(R.mipmap.icon_music_sx));
            }
        };
        private View.OnClickListener listener_mode_sj=new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(poper!=null && poper.isShowing())poper.dismiss();
                Server.Share_Music().set_play_mode(1);
                btn_mode.setBackground(cc.getResources().getDrawable(R.mipmap.icon_music_sj));
            }
        };
        private View.OnClickListener listener_btn_next=new OnClickListener() {
            @Override
            public void onClick(View v) {
                MusicManager _mm=Server.Share_Music();
                if(_mm==null)return;
                if(is_stop)_mm.stop_music();
                else _mm.play_next();
            }
        };

        public CTMView(Context context) {
            super(context);
            this.setClickable(true);
            this.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
            this.setMotionEventSplittingEnabled(false);
            this.setOrientation(LinearLayout.VERTICAL);
            this.setGravity(Gravity.CENTER_HORIZONTAL);
            this.setBackgroundColor(context.getResources().getColor(R.color.bg_main));




            int pos = Setting.Share_Setting().get_music_ads_pos();

            if (pos == 0) {
//                View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
//                if(_v!=null)this.addView(_v);
//                int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
//                View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);
//                this.addView(divid);
                int count = Setting.Share_Setting().get_app_run_count();
                if (Setting.Share_Setting().get_pos_music_state() != 0 && count >= Setting.Share_Setting().get_pos_music_state()) {
                   // Global.Get_banner(cc, this, null);
                    View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
                    if(_v!=null)this.addView(_v);
                    int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
                    View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(4)), colors);
                    this.addView(divid);
                }
            }

            FrameLayout _header=new FrameLayout(context);
            _header.setClickable(true);
            _header.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC));
            _header.setPadding(0,CL.DIP2PX_INT(4),0,0);
            this.addView(_header);
            _header.addView(CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER,0,CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8)),
                    cc.getResources().getString(R.string.music_player_list),0xffaaaaaa,18,null));
          //  this.addView(CLController.Get_TextView_Divider(context,CL.Get_LLLP(CL.MP,1),0xff313131));
            btn_mode=CLController.Get_ImageView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER_VERTICAL|Gravity.RIGHT,0,0,CL.DIP2PX_INT(16),0),
                    null,listener_mode);
            _header.addView(btn_mode);

            FrameLayout _fl_body=new FrameLayout(context);
            _fl_body.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
            _fl_body.setClickable(true);
            this.addView(_fl_body);
            ImageView _empty=new ImageView(context);
            _empty.setLayoutParams(CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER));
            _empty.setBackgroundResource(R.mipmap.no_data);
            _fl_body.addView(_empty);

            lv_list=new ListView(context);
            lv_list.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f));
            lv_list.setCacheColorHint(Color.TRANSPARENT);
            lv_list.setDivider(new ColorDrawable(0xff313131));
            lv_list.setDividerHeight(1);
            lv_list.setSelector(new ColorDrawable(Color.TRANSPARENT));
            lv_list.setOverScrollMode(View.OVER_SCROLL_NEVER);
            lv_list.setEmptyView(_empty);
            adapter=new AdapterForPL();
            lv_list.setAdapter(adapter);
            _fl_body.addView(lv_list);

            this.addView(CLController.Get_TextView_Divider(context,CL.Get_LLLP(CL.MP,1),0xff313131));

            ll_player=CLController.Get_LinearLayout(cc, CL.Get_LP(CL.MP,CL.WC),LinearLayout.HORIZONTAL,null);
            ll_player.setBackgroundColor(0xff15181b);
            ll_player.setGravity(Gravity.CENTER_VERTICAL);
            this.addView(ll_player);
//            iv_icon= CLController.Get_ImageView(cc, CL.Get_LLLP(CL.DIP2PX_INT(38),CL.DIP2PX_INT(38),CL.DIP2PX_INT(8),CL.DIP2PX_INT(6),CL.DIP2PX_INT(8),CL.DIP2PX_INT(6)),
//                    CL.Get_StateList_Drawable(cc, R.mipmap.icon_music_click,R.mipmap.icon_music_normal),listener_music);
//            ll_player.addView(iv_icon);

            LinearLayout _ll_content=CLController.Get_LinearLayout(cc,
                    CL.Get_LLLP(CL.WC,CL.WC,1.0f,CL.DIP2PX_INT(12),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8)),LinearLayout.VERTICAL,null);
            ll_player.addView(_ll_content);
            tv_title=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC),"",0xffa1a1a1,14,null);
            tv_title.setSingleLine();
            tv_title.setEllipsize(TextUtils.TruncateAt.END);
            tv_time=CLController.Get_TextView(cc,CL.Get_LLLP(CL.MP,CL.WC),"",0xffa1a1a1,12,null);
            _ll_content.addView(tv_title);
            _ll_content.addView(tv_time);

            btn_play=CLController.Get_ImageView(cc, CL.Get_LLLP(CL.WC, CL.WC,CL.DIP2PX_INT(16),0,CL.DIP2PX_INT(16),0),
                    null, listener_btn_play_pause);
            ll_player.addView(btn_play);

            btn_next=CLController.Get_ImageView(cc, CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(22),0),
                    cc.getResources().getDrawable(R.mipmap.icon_music_next),listener_btn_next);
            ll_player.addView(btn_next);


         //   this.addView(CLController.Get_TextView_Divider(context,CL.Get_LLLP(CL.MP,1),0xff313131));
            FrameLayout _fl_arrow=new FrameLayout(context);
            _fl_arrow.setLayoutParams(CL.Get_LP(CL.WC,CL.DIP2PX_INT(40)));
            _fl_arrow.addView(CLController.Get_ImageView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                    context.getResources().getDrawable(R.mipmap.down_arrow),null));
            this.addView(_fl_arrow);


            anim_show=new TranslateAnimation(Animation.RELATIVE_TO_SELF,0,Animation.RELATIVE_TO_SELF,0,
                    Animation.RELATIVE_TO_SELF,1.0f,Animation.RELATIVE_TO_SELF,0);
            anim_show.setDuration(200);

            anim_hide=new TranslateAnimation(Animation.RELATIVE_TO_SELF,0,Animation.RELATIVE_TO_SELF,0,
                    Animation.RELATIVE_TO_SELF,0.0f,Animation.RELATIVE_TO_SELF,1.0f);
            anim_hide.setDuration(200);
            anim_hide.setAnimationListener(listener_anim_hide);






            if (pos == 1) {
                int count = Setting.Share_Setting().get_app_run_count();
                if (Setting.Share_Setting().get_pos_music_state() != 0 && count >= Setting.Share_Setting().get_pos_music_state()) {
                    LinearLayout bottom = new LinearLayout(cc);
                    bottom.setOrientation(LinearLayout.VERTICAL);
                    bottom.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC));
                    this.addView(bottom);

//                int colors[] = { 0xff255779 , 0xff3e7492, 0xffa6c0cd };
//                View divid = CLController.Get_TextView_Gradient_Divider(cc, new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(8)), colors);
//
//                bottom.addView(divid);
//                View _v=Global.Get_Banner(cc,CL.Get_LLLP(CL.MP,CL.WC), null);
//                bottom.addView(_v);

                    //int pos = Setting.Share_Setting().get_ads_pos();
                    int banner_type = Setting.Share_Setting().get_banner_type();
                    Global.Get_banner(cc, bottom, pos , banner_type, null);
                }
            }
        }

        private void go_show(){
            this.startAnimation(anim_show);
        }

        private void go_dismiss(){
            this.startAnimation(anim_hide);
        }


        @Override
        protected void onAttachedToWindow() {
            super.onAttachedToWindow();
         //   Global.Show_Interstitial(cc,null);
            crt_item=Server.Share_Music().get_current_music();
            adapter.datas= Server.Share_Music().get_play_list();
            adapter.notifyDataSetChanged();
            MusicManager.Add_Listener(listener_music);
            if(Server.Share_Music().get_play_mode()==0)btn_mode.setBackground(cc.getResources().getDrawable(R.mipmap.icon_music_sx));
            else btn_mode.setBackground(cc.getResources().getDrawable(R.mipmap.icon_music_sj));

            listener_music.on_status_change(Server.Share_Music().get_current_state());
            listener_music.on_music_change(Server.Share_Music().get_current_music());

            if(adapter.datas==null || adapter.datas.size()==0)return;
            if(!Setting.Share_Setting().get_tip(Setting.Type_play_list)){
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        LinearLayout _ll_main=new LinearLayout(cc);
                        _ll_main.setOrientation(LinearLayout.VERTICAL);
                        _ll_main.setGravity(Gravity.RIGHT);
                        RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                        ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                        _dwe_bg.getPaint().setColor(0xff378d39);
                        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                        _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
                        TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
                                cc.getResources().getString(R.string.tip_check_music), Color.WHITE,14,null);
                        _tip.setBackground(_dwe_bg);
                        _ll_main.addView(_tip);

                        CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
                            @Override
                            public void on_callback() {
                                Setting.Share_Setting().set_tip(Setting.Type_play_list,true);
                            }
                        }).show();
                    }
                },500);
            }
        }

        @Override
        protected void onDetachedFromWindow() {
            super.onDetachedFromWindow();
            MusicManager.Reduce_Listener(listener_music);
            handler.removeMessages(1000);
            editor=false;
            CL.CLOGI("on detached window");
        }

        private MusicManager.Eventer listener_music=new MusicManager.Eventer() {
            @Override
            public void on_data_update() {
                crt_item=Server.Share_Music().get_current_music();
                adapter.datas= Server.Share_Music().get_play_list();
                adapter.notifyDataSetChanged();
            }

            @Override
            public void on_music_change(Data.StructDLItem item) {
                crt_item=Server.Share_Music().get_current_music();
                adapter.notifyDataSetChanged();

                if(item==null){
                    ll_player.setVisibility(View.GONE);
                    return;
                }
                ll_player.setVisibility(View.VISIBLE);
                String _t=item.name;
                if(_t==null || _t.isEmpty())_t=item.title;
                tv_title.setText(_t);
                music_time_size=CLTools.Get_Time_00_00_String(Server.Share_Music().get_current_duration());
                tv_time.setText("00:00"+" - "+music_time_size);
                handler.removeMessages(1000);
                handler.sendEmptyMessage(1000);
            }

            @Override
            public void on_status_change(int state) {
                if(state==0 || state==1){
                    handler.removeMessages(1000);
                    btn_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_play));
                    btn_next.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_stop));
                    is_stop=true;
                }else if(state==2){
                    handler.sendEmptyMessage(1000);
                    btn_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_pause));
                    btn_next.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_next));
                    is_stop=false;
                }
            }
        };
    }

    private class AdapterForPL extends BaseAdapter{

        public ArrayList<Data.StructDLItem> datas;

        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new PLItem(cc);
            PLItem _v=(PLItem)cv;
            _v.set_basic_data(datas.get(position));
            return cv;
        }
    }
    private View.OnClickListener listener_click_item=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(v instanceof PLItem){
                PLItem _vv=(PLItem)v;
                Server.Share_Music().play_music(_vv.data);
            }
        }
    };
    private View.OnClickListener listener_click_remove=new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if(v.getTag() instanceof PLItem){
                PLItem _vv=(PLItem)v.getTag();
                Server.Share_Music().remove_music(_vv.data);
            }
        }
    };
    private boolean editor=false;
    private View.OnLongClickListener listener_long_click_item=new View.OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            editor=!editor;
            ctm_view.adapter.notifyDataSetChanged();
            return true;
        }
    };
    private class PLItem extends FrameLayout{

        private Data.StructDLItem data;
        private ImageView iv_play;
        private TextView tv_name,tv_type,tv_length,tv_duration;
        private Button btn_remove;

        public PLItem(Context context) {
            super(context);
            this.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.TRANSPARENT),new ColorDrawable(0x993d6f59)));
            this.setClickable(true);
            this.setLongClickable(true);
            this.setOnClickListener(listener_click_item);
            this.setOnLongClickListener(listener_long_click_item);

            LinearLayout _ll=new LinearLayout(context);
            _ll.setLayoutParams(CL.Get_FLLP(CL.MP, CL.MP,Gravity.FILL));
            _ll.setOrientation(LinearLayout.HORIZONTAL);
            _ll.setGravity(Gravity.CENTER_VERTICAL);
            this.addView(_ll);

            iv_play=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.DIP2PX_INT(30),CL.DIP2PX_INT(26),CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0),
                    null,null);
            _ll.addView(iv_play);
            LinearLayout _ll_content=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f,0,CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6)),LinearLayout.VERTICAL,null);
            _ll.addView(_ll_content);

            tv_name=CLController.Get_TextView(cc, CL.Get_LP_WW(),"",Color.WHITE,15,null);
            tv_name.setSingleLine();
            tv_name.setEllipsize(TextUtils.TruncateAt.END);
            _ll_content.addView(tv_name);

            LinearLayout _ll_detail=CLController.Get_LinearLayout(cc,CL.Get_LLLP(CL.MP,CL.WC,0,CL.DIP2PX_INT(4),CL.DIP2PX_INT(8),0),LinearLayout.HORIZONTAL,null);
            _ll_content.addView(_ll_detail);

            tv_type=CLController.Get_TextView(cc,CL.Get_LP(CL.DIP2PX_INT(60),CL.WC),"", Color.WHITE,14,null);
            _ll_detail.addView(tv_type);

            tv_length=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC,1.0f),"", Color.WHITE,14,null);
            _ll_detail.addView(tv_length);

            tv_duration=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),"", Color.WHITE,14,null);
            _ll_detail.addView(tv_duration);

            btn_remove=new Button(context);
            btn_remove.setLayoutParams(CL.Get_FLLP(CL.WC,CL.MP,Gravity.RIGHT,0,CL.DIP2PX_INT(2),0,CL.DIP2PX_INT(2)));
            btn_remove.setPadding(CL.DIP2PX_INT(8),0,CL.DIP2PX_INT(8),0);
            btn_remove.setGravity(Gravity.CENTER);
            btn_remove.setText(cc.getResources().getString(R.string.remove));
            btn_remove.setTextColor(Color.WHITE);
            btn_remove.setBackgroundColor(Color.RED);
            btn_remove.setTextSize(13);
            btn_remove.setLongClickable(true);
            btn_remove.setOnLongClickListener(listener_long_click_item);
            btn_remove.setOnClickListener(listener_click_remove);
            this.addView(btn_remove);
        }
        public void set_basic_data(Data.StructDLItem d){
            this.data=d;

            iv_play.setImageDrawable(null);
            if(ctm_view.crt_item!=null){
                if(ctm_view.crt_item.ident_md5.equals(d.ident_md5)){
                    iv_play.setImageDrawable(cc.getResources().getDrawable(R.mipmap.icon_music_playing));
                }
            }

            tv_name.setText(this.data.name==null?this.data.title:this.data.name);

            if(data.type_minor==Data.Type_Music_MP3)tv_type.setText("MP3");
            else if(data.type_minor==Data.Type_Music_OGG)tv_type.setText("OGG");
            else if(data.type_minor==Data.Type_Music_FLAC)tv_type.setText("FLAC");
            else if(data.type_minor==Data.Type_Music_WAV)tv_type.setText("WAV");
            else if(data.type_minor==Data.Type_Music_M4A)tv_type.setText("M4A");
            else tv_type.setText("???");

            tv_length.setText(CLTools.Get_Capacity_Format(this.data.length));
            tv_duration.setText(this.data.duration);

            btn_remove.setVisibility(View.GONE);
            btn_remove.setTag(this);
            if(editor){
                btn_remove.setVisibility(View.VISIBLE);
            }
        }


    }
}
