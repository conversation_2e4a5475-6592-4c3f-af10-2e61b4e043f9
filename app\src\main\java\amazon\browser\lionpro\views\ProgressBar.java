package amazon.browser.lionpro.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import lion.CL;

/**
 * Created by chen<PERSON><PERSON> on 17/2/8.
 */

public class ProgressBar extends View {

    private int fgColor = 0xffffbb33;//0xff00b7ee;
    private int fgbgColor = 0xff27283d;//0xff00b7ee;
    private int animStep;
    private int progress = 0;
    private int animOffset = 0;
    private Context cc;
    private Paint paint;
    private Paint paintbackground;
    public ProgressBar(Context context) {
        super(context);
        this.cc = context;
        init();
    }

    public ProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.cc = context;
        init();
    }

    private void init() {
        this.setWillNotDraw(false);
        float dip = 10.0f;
        animStep = (int) CL.DIP2PX(dip);
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(fgColor);
        paint.setStyle(Paint.Style.FILL_AND_STROKE);

        paintbackground = new Paint(Paint.ANTI_ALIAS_FLAG);
        paintbackground.setColor(fgbgColor);
        paintbackground.setStyle(Paint.Style.FILL_AND_STROKE);
    }

    public void setProgress(int v) {
        this.progress = v;
        if (this.progress < 0) {
            this.progress = 0;
        } else if (this.progress > 100) {
            this.progress = 100;
        }
        postInvalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (progress == 0) {
            return;
        }
        int w = this.getWidth();
        int h = this.getHeight();
       // canvas.drawRect(0, 0, w, h, paintbackground);
        if (progress < 100) {
            float offset = (float) this.progress / 100.0f * w;
            canvas.drawRect(0, 0, offset, h - 1, paint);
            animOffset = 0;
        } else {
            animOffset += animStep;
            if (animOffset >= w) {
                progress = 0;
                return;
            }
            canvas.drawRect(animOffset, 0, w, h - 1, paint);
            invalidate();
        }
    }
}
