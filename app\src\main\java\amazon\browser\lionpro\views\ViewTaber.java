package amazon.browser.lionpro.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.view.MotionEvent;
import android.view.View;

import amazon.browser.video.downloader.R;

import lion.CL;

/**
 * Created by leron on 2016/4/19.
 */
public class ViewTaber extends View{

    public interface Eventer{
        void on_click_left();
        void on_click_right();
    }



    private Paint paint;
    private Drawable dwe_fav,dwe_history;
    private Eventer listener;

    public ViewTaber(Context context,Eventer listen) {
        super(context);
        this.listener=listen;
        paint=new Paint(Paint.ANTI_ALIAS_FLAG);
        dwe_fav=context.getResources().getDrawable(R.mipmap.fh_icon_fav);
        dwe_history=context.getResources().getDrawable(R.mipmap.fh_icon_history);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawColor(0xff202529);
        int _w=this.getWidth();
        int _h=this.getHeight();
        float _half=_w/2;

        paint.setStyle(Paint.Style.FILL);
        paint.setColor(0xff303539);
        float _touch_pad=CL.DIP2PX(10);
        boolean _show_touch_mark=false;
        if(touch_index==1 || touch_index==2)_show_touch_mark=System.currentTimeMillis()-time_down>200?true:false;
        if(touch_index==1 && _show_touch_mark){
            canvas.drawRect(_touch_pad,_touch_pad,_half-_touch_pad,_h-_touch_pad,paint);
        }else if(touch_index==2 && _show_touch_mark){
            canvas.drawRect(_half+_touch_pad,_touch_pad,_w-_touch_pad,_h-_touch_pad,paint);
        }

        int _x1=(int)((_half-dwe_fav.getIntrinsicWidth())/2);
        int _y1=(_h-dwe_fav.getIntrinsicHeight())/2;
        dwe_fav.setBounds(_x1,_y1,_x1+dwe_fav.getIntrinsicWidth(),_y1+dwe_fav.getIntrinsicHeight());
        int _x2=(int)(_w-_half/2-dwe_history.getIntrinsicWidth()/2);
        int _y2=(_h-dwe_history.getIntrinsicHeight())/2;
        dwe_history.setBounds(_x2,_y2,_x2+dwe_history.getIntrinsicWidth(),_y2+dwe_history.getIntrinsicHeight());
        dwe_fav.draw(canvas);
        dwe_history.draw(canvas);

        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(2.0f);
        paint.setColor(0xff313131);
        canvas.drawLine(_half, CL.DIP2PX(10),_half,_h-CL.DIP2PX(10),paint);
        canvas.drawLine(0,_h-1,_w,_h-1,paint);
        paint.setColor(0xff8a8000);//yellow
        paint.setStrokeWidth(6.0f);
        float _pad=CL.DIP2PX(8);
        if(crt_touch_index==1){
            if(offset_index>0){
                offset_index-=Touch_offset_step;
                if(offset_index<0)offset_index=0;
                invalidate();
            }else if(offset_index<0)offset_index=0;
        }else if(crt_touch_index==2){
            if(offset_index<_half){
                offset_index+=Touch_offset_step;
                if(offset_index>_half)offset_index=_half;
                invalidate();
            }else if(offset_index>_half)offset_index=_half;
        }
        canvas.drawLine(offset_index+_pad,_h-6,offset_index+_half-_pad,_h-6,paint);
    }


    private final float Touch_pad=CL.DIP2PX(15);
    private final float Touch_offset_step=CL.DIP2PX(8);
    private int touch_index=0;
    private PointF point_down=new PointF();
    private long time_down=0;
    private int crt_touch_index=1;
    private float offset_index=0;


    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int _action=event.getAction();
        float _x=event.getX();
        float _y=event.getY();
        if(_action==MotionEvent.ACTION_DOWN) {
            point_down.x=_x;
            point_down.y=_y;
            if(point_down.x>this.getWidth()/2)touch_index=2;
            else touch_index=1;
            time_down=System.currentTimeMillis();
        }else if(_action==MotionEvent.ACTION_MOVE){
            if(touch_index==0)return true;
            if(Math.abs(_x-point_down.x)>Touch_pad || Math.abs(_y-point_down.y)>Touch_pad){
                touch_index=0;
            }
        }else if(_action==MotionEvent.ACTION_UP){
            if(touch_index==1){
                if(crt_touch_index!=1) {
                    if (listener != null) listener.on_click_left();
                    crt_touch_index = 1;
                }
            }else if(touch_index==2){
                if(crt_touch_index!=2) {
                    if (listener != null) listener.on_click_right();
                    crt_touch_index = 2;
                }
            }
            touch_index=0;
        }else if(_action==MotionEvent.ACTION_CANCEL){
            touch_index=0;
        }
        invalidate();
        return true;
    }

    public int get_selected_index(){
        return crt_touch_index;
    }
    public void set_selected_index(int index){
        if(crt_touch_index==index)return;
        else{
            if(index<1 && index >2)return;
            else{
                crt_touch_index=index;
                invalidate();
            }
        }
    }
}
