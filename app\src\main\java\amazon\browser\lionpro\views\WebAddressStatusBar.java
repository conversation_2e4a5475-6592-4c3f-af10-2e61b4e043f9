package amazon.browser.lionpro.views;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.RectF;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.AutoCompleteTextView;
import android.widget.BaseAdapter;
import android.widget.EditText;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Affairs;
import amazon.browser.lionpro.datas.Struct;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.toys.Tools;

import java.io.File;
import java.util.ArrayList;

import lion.CL;
import lion.CLBitmapLoader;
import lion.CLController;

/**
 * Created by leron on 2016/4/12.
 */
public class WebAddressStatusBar extends LinearLayout{

    public interface EventListener{
        void on_go_url(String url);
        void on_go_search(String key);
        void on_refresh();
        void on_stop_load();
        void on_change_text();
    }
    public static final int NOTHING = 0;
    public static final int EDIT = 1;
    public static final int LOADING = 2;
    public static final int STOP = 3;

    private Activity cc;
    private EventListener listener;
    private ImageView iv_icon,iv_refresh;
    private AutoCompleteTextView et_addr;
    private Affairs.TypeAutomatically typer=new Affairs.TypeAutomatically();
    private ArrayList<Struct.StructWebsite> datas_auto;
    private AdapterForAuto adapter;
    private int progress;
    private int is_loading=NOTHING;

    private View.OnFocusChangeListener listener_et_focus_change=new OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if(!hasFocus) {
                CL.Hide_Soft_Input(cc,v);
                if(et_addr.getText().toString().isEmpty())
                    set_loading_state(NOTHING);
                else
                    set_loading_state(STOP);
            }
            else {
                if (is_loading!=LOADING)
                    set_loading_state(EDIT);
            }
        }
    };
    private EditText.OnEditorActionListener listener_addr_editor_action=new TextView.OnEditorActionListener() {
        @Override
        public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
            if(actionId == EditorInfo.IME_ACTION_GO){
                String _url=et_addr.getText().toString();
                if(_url==null)return true;
                //_url=_url.trim().toLowerCase();
                if(_url.equals(""))return true;
                String _fix_url= Tools.Was_Web_Site(_url);
                if(_fix_url!=null){
                    String fix_v = _fix_url.replaceFirst("http://", "https://");
                    if(listener!=null)listener.on_go_url(fix_v);
                }else{
                    if(listener!=null)listener.on_go_search(_url);
                }
                return true;
            }
            return false;
        }
    };
    private AbsListView.OnItemClickListener listener_click_item_auto=new AdapterView.OnItemClickListener() {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            CL.Hide_Soft_Input(cc,et_addr);
            Struct.StructWebsite _item=datas_auto.get(position);
            if(listener!=null)listener.on_go_url(_item.url);
        }
    };
    
    private TextWatcher listener_text_change = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            
        }
    
        @Override
        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            if (charSequence.toString().isEmpty()) {
                WebAddressStatusBar.this.set_loading_state(NOTHING);
            }
            else {
                if (WebAddressStatusBar.this.is_loading!=LOADING && WebAddressStatusBar.this.is_loading!=STOP) {
                    WebAddressStatusBar.this.set_loading_state(EDIT);
                }
            }
        }
    
        @Override
        public void afterTextChanged(Editable editable) {
        
        }
    };
//    private TextWatcher listener_addr_text_changer=new TextWatcher() {
//        @Override
//        public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
//        @Override
//        public void onTextChanged(CharSequence s, int start, int before, int count) {
//            if(s.length()>0 && btn_addr_clean.getVisibility()!= View.VISIBLE){
//                btn_addr_clean.setVisibility(View.VISIBLE);
//            }
//            else if(s.length()==0){
//                btn_addr_clean.setVisibility(View.INVISIBLE);
//            }
//        }
//        @Override
//        public void afterTextChanged(Editable s) {}
//    };

    private View.OnClickListener listener_refresh=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(listener==null)return;
            if(is_loading==LOADING){
                listener.on_stop_load();
            }else if (is_loading==STOP){
                listener.on_refresh();
            }else if (is_loading==EDIT) {
                listener.on_change_text();
            }
        }
    };

    private CLBitmapLoader loader=new CLBitmapLoader(1,"auto");



    public WebAddressStatusBar(Activity context,EventListener listener) {
        super(context);
        this.cc=context;
        this.listener=listener;
        this.setMotionEventSplittingEnabled(false);
        this.setGravity(Gravity.CENTER);
        this.setOrientation(LinearLayout.HORIZONTAL);
        this.setBackgroundColor(Color.TRANSPARENT);
        this.setId(11);
        color_progress =cc.getResources().getColor(R.color.dl_title_color);
        paint=new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setStyle(Paint.Style.FILL);
        rect_bg=new RectF();
        pd_mode=new PorterDuffXfermode(PorterDuff.Mode.SRC_IN);

        iv_icon=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.DIP2PX_INT(22),CL.DIP2PX_INT(22),CL.DIP2PX_INT(6),0,CL.DIP2PX_INT(6),0),null,null);
        iv_icon.setImageDrawable(cc.getResources().getDrawable(R.mipmap.address_web));
        this.addView(iv_icon);

        et_addr =new AutoCompleteTextView(cc);
        et_addr.setLayoutParams(CL.Get_LLLP(CL.WC, CL.MP, 1.0f));
        et_addr.setFocusable(true);
        et_addr.setFocusableInTouchMode(true);
        et_addr.setSelectAllOnFocus(true);
        et_addr.setBackgroundColor(Color.TRANSPARENT);
        et_addr.setPadding(0, 0, 0, 0);
        et_addr.setGravity(Gravity.CENTER_VERTICAL);
        et_addr.setTextSize(13);
        et_addr.setSingleLine();
        et_addr.setImeOptions(EditorInfo.IME_ACTION_GO);
        et_addr.setOnEditorActionListener(listener_addr_editor_action);
        et_addr.setOnFocusChangeListener(listener_et_focus_change);
        et_addr.setHintTextColor(Color.GRAY);
        et_addr.setHint(cc.getResources().getString(R.string.address_hint));
        et_addr.setInputType(InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
//        et_addr.setDropDownVerticalOffset(CL.DIP2PX_INT(2));
        et_addr.setThreshold(2);
        et_addr.setDropDownAnchor(11);
        et_addr.setOnItemClickListener(listener_click_item_auto);
        et_addr.addTextChangedListener(listener_text_change);
        adapter=new AdapterForAuto();
        et_addr.setAdapter(adapter);
        this.addView(et_addr);

        iv_refresh=CLController.Get_ImageView(cc,CL.Get_LLLP(CL.DIP2PX_INT(24),CL.DIP2PX_INT(24),CL.DIP2PX_INT(4),0,CL.DIP2PX_INT(4),0),null,listener_refresh);
        //iv_refresh.setImageDrawable(cc.getResources().getDrawable(R.mipmap.address_refresh));
        iv_refresh.setImageDrawable(null);
        this.addView(iv_refresh);
    }

    public void set_address(String addr){
        this.et_addr.setText(addr);
    }

    public String get_address() {
        return this.et_addr.getText().toString();
    }
    public void set_loading_state(int state){
        is_loading=state;
        if(is_loading==LOADING)
            iv_refresh.setImageDrawable(cc.getResources().getDrawable(R.mipmap.address_stop));
        else if(is_loading==STOP)
            iv_refresh.setImageDrawable(cc.getResources().getDrawable(R.mipmap.address_refresh));
        else if(is_loading==EDIT)
            iv_refresh.setImageDrawable(cc.getResources().getDrawable(R.mipmap.address_del));
        else if(is_loading==NOTHING)
            iv_refresh.setImageDrawable(null);
    }
    public void set_progress(int value){
        this.progress=value;
        postInvalidate();
    }
    public void update_website_icon(Bitmap icon){
        if(icon==null){
            iv_icon.setImageBitmap(null);
            iv_icon.setImageDrawable(cc.getResources().getDrawable(R.mipmap.address_web));
        }
        else iv_icon.setImageBitmap(icon);
    }


    private int color_progress = 0;
    private Paint paint;
    private RectF rect_bg;
    private PorterDuffXfermode pd_mode;
    private float offset_progress;
    @Override
    protected void onDraw(Canvas canvas) {
        int _w=this.getWidth();
        int _h=this.getHeight();
        float _rr=CL.DIP2PX_INT(4);
        paint.setColor(0xffc2c7cc);
        //paint.setColor(cc.getResources().getColor(R.color.dl_title_color));
        rect_bg.set(0,0,_w,_h);
        canvas.drawRoundRect(rect_bg,_rr,_rr,paint);

        if(progress>0 && progress<100){
            offset_progress=0;
            int _sl=canvas.saveLayer(0,0,_w,_h,null,Canvas.ALL_SAVE_FLAG);
            canvas.drawRoundRect(rect_bg,_rr,_rr,paint);
            paint.setColor(color_progress);
            paint.setXfermode(pd_mode);
            float _pw=(float)progress/100.0f*_w;
            canvas.drawRect(0,0,_pw,_h,paint);
            paint.setXfermode(null);
            canvas.restoreToCount(_sl);
        }else if(progress == 100){
            if(offset_progress>=_w){
                progress=0;
                offset_progress=0;
                invalidate();
                return;
            }
            paint.setColor(color_progress);
            rect_bg.set(_w-offset_progress<_rr*2?_w-_rr*2:offset_progress,0,_w,_h);
            canvas.drawRoundRect(rect_bg,_rr,_rr,paint);
            offset_progress+=22;
            invalidate();
        }
    }

    private class AdapterForAuto extends BaseAdapter implements Filterable {
        private Drawable icon_def_website=cc.getResources().getDrawable(R.mipmap.address_web);
        private CTMFilter filter;

        @Override
        public int getCount() {
            if(datas_auto==null)return 0;
            return datas_auto.size();
        }

        @Override
        public Object getItem(int position) {
            return datas_auto.get(position);
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new CTMItemView(cc);
            CTMItemView _v=(CTMItemView)cv;
            _v.set_data(datas_auto.get(position));
            return cv;
        }

        @Override
        public Filter getFilter() {
            if(filter==null)filter=new CTMFilter();
            return filter;
        }
    }
    private class CTMFilter extends Filter {
        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            if(constraint==null || constraint.length()==0)return null;
            if(!et_addr.hasFocus())return null;
            FilterResults _datas=new FilterResults();
            ArrayList<Struct.StructWebsite> _vs=typer.get_by_like(cc,constraint.toString());
            _datas.values=_vs;
            _datas.count=_vs.size();
            return _datas;
        }
        @Override
        protected void publishResults(CharSequence constraint, FilterResults results) {
            if(datas_auto!=null){
                for(int i=0;i<datas_auto.size();++i){
                    Struct.StructWebsite _item=datas_auto.get(i);
                    if(_item.bitmap_icon!=null && !_item.bitmap_icon.isRecycled()){
                        loader.recycle_bitmap(_item.bitmap_icon);
                        _item.bitmap_icon=null;
                    }
                }
            }
            if(results==null)datas_auto=null;
            else{
                datas_auto=(ArrayList<Struct.StructWebsite>)results.values;
            }
            adapter.notifyDataSetChanged();
        }
    }
    private class CTMItemView extends RelativeLayout {
        private ImageView iv_icon;
        private TextView tv_title;
        private TextView tv_content;
        private Struct.StructWebsite data;
        public CTMItemView(Context context) {
            super(context);
            this.setLayoutParams(new AbsListView.LayoutParams(CL.MP, CL.DIP2PX_INT(40)));
            this.setBackground(CL.Get_StateList_Drawable(new ColorDrawable(Color.WHITE), new ColorDrawable(color_progress)));

            RelativeLayout.LayoutParams _lp_icon=new RelativeLayout.LayoutParams(CL.DIP2PX_INT(24),CL.DIP2PX_INT(24));
            _lp_icon.setMargins(CL.DIP2PX_INT(8), CL.DIP2PX_INT(7), CL.DIP2PX_INT(6), CL.DIP2PX_INT(6));
            _lp_icon.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
            iv_icon= CLController.Get_ImageView(cc,_lp_icon,null,null);
            iv_icon.setId(101);
            this.addView(iv_icon);


            RelativeLayout.LayoutParams _lp_title=new RelativeLayout.LayoutParams(CL.WC,CL.WC);
            _lp_title.setMargins(0, CL.DIP2PX_INT(3), 0, 0);
            _lp_title.addRule(RelativeLayout.RIGHT_OF, 101);
            tv_title=CLController.Get_TextView(cc, _lp_title, null, Color.BLACK, 12, null);
            tv_title.setSingleLine();
            tv_title.setEllipsize(TextUtils.TruncateAt.END);
            tv_title.setId(102);
            this.addView(tv_title);

            RelativeLayout.LayoutParams _lp_content=new RelativeLayout.LayoutParams(CL.WC,CL.WC);
            _lp_content.setMargins(0, 0, 0, 0);
            _lp_content.addRule(RelativeLayout.RIGHT_OF, 101);
            _lp_content.addRule(RelativeLayout.BELOW, 102);
            tv_content=CLController.Get_TextView(cc,_lp_content,null, 0xff333333,12,null);
            tv_content.setSingleLine();
            tv_content.setEllipsize(TextUtils.TruncateAt.END);
            tv_content.setId(103);
            this.addView(tv_content);
        }
        private void set_data(Struct.StructWebsite data){
            this.data=data;
            iv_icon.setImageBitmap(null);
            if(this.data==null)return;
            tv_title.setText(data.title);
            tv_content.setText(data.url);
            if(data.icon_file_name!=null && data.bitmap_icon==null){
                String _name=data.icon_file_name.substring(data.icon_file_name.lastIndexOf('/')+1);
                if(_name==null)return;
                File _thum=new File(Global.Dir_thum,_name);
                if(_thum.exists()){
                    data.bitmap_icon=loader.create_bitmap(_thum.getAbsolutePath(),CL.DIP2PX_INT(48),CL.DIP2PX_INT(48));
                }
            }
            if(data.bitmap_icon!=null){
                iv_icon.setImageBitmap(data.bitmap_icon);
            }
            else iv_icon.setImageDrawable(adapter.icon_def_website);
        }
    }

}
