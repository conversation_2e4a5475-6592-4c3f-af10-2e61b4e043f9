package amazon.browser.lionpro.views;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Handler;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;

import lion.CL;
import lion.CLCallback;
import lion.CLController;
import lion.CLHelper;
import lion.widget.GuideNovice;

/**
 * Created by leron on 2016/4/14.
 */
public class WebToolBar extends LinearLayout{
    

    
    public interface EventListener{
        void on_click_back();
        void on_click_forward();
        void on_click_fav();
        void on_long_click_fav();
        void on_click_storage();
        void on_click_open_home();
        void on_click_open_new_win();
    }

    private AppCompatActivity cc;
    private EventListener listener;
    private ImageView btn_back,btn_forward;
    private ImageView btn_home, btn_fav,btn_sdcard,btn_multi_win;
    private final TextView win_num;
    private boolean can_back,can_forward;
    private int item_number = 4;

    private View.OnClickListener listener_clicker=new OnClickListener() {
        @Override
        public void onClick(View v) {
            if(listener==null)return;
            if(v==btn_back){
                if(can_back)listener.on_click_back();
            }else if(v==btn_forward){
                if(can_forward)listener.on_click_forward();
            }else if(v==btn_fav){
                listener.on_click_fav();
            }else if(v==btn_sdcard){
                listener.on_click_storage();
            }else if(v==btn_home){
                listener.on_click_open_home();
            }else if(v==btn_multi_win) {
                listener.on_click_open_new_win();
            }
        }
    };
    private View.OnLongClickListener listener_long_fav=new OnLongClickListener() {
        @Override
        public boolean onLongClick(View v) {
            if(listener!=null)listener.on_long_click_fav();
            return true;
        }
    };

    public WebToolBar(Activity context,EventListener listen) {
        super(context);
        this.cc=(AppCompatActivity)context;
        this.listener=listen;
        this.setOrientation(LinearLayout.VERTICAL);
        View divid = CLController.Get_TextView_Divider(cc,new AbsListView.LayoutParams(CL.MP,CL.DIP2PX_INT(1)),0xff2c2c2c);
        this.addView(divid);

        LinearLayout _ll_main=new LinearLayout(cc);
        _ll_main.setOrientation(LinearLayout.HORIZONTAL);
        _ll_main.setGravity(Gravity.CENTER);

        _ll_main.setLayoutParams(CL.Get_LLLP(CL.MP, CL.MP));
        this.addView(_ll_main);
        //this.setOrientation(LinearLayout.HORIZONTAL);
       // this.setGravity(Gravity.CENTER_HORIZONTAL);
        _ll_main.setBackgroundColor(context.getResources().getColor(R.color.bg_main));

        int _width=CL.Get_Screen_Width(cc)/item_number;
        btn_back= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,listener_clicker);
        btn_back.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_back2));
        btn_back.setScaleType(ImageView.ScaleType.CENTER);
        btn_forward= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,listener_clicker);
        btn_forward.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_forward2));
        btn_forward.setScaleType(ImageView.ScaleType.CENTER);

        btn_home = CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,listener_clicker);
        //btn_home.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav2));
        btn_home.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.home_normal,R.mipmap.home_press));
        btn_home.setScaleType(ImageView.ScaleType.CENTER);


        btn_fav= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,listener_clicker);
        btn_fav.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav));
        btn_fav.setScaleType(ImageView.ScaleType.CENTER);
        btn_fav.setLongClickable(true);
        btn_fav.setOnLongClickListener(listener_long_fav);
//        btn_sdcard= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,listener_clicker);
//        btn_sdcard.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav2));
//        btn_sdcard.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.toolbal_store_normal,R.mipmap.toolbal_store_click));
//        btn_sdcard.setScaleType(ImageView.ScaleType.CENTER);

        FrameLayout fl_multi=new FrameLayout(cc);
        fl_multi.setLayoutParams(CL.Get_FLLP(CL.WC,CL.MP,Gravity.FILL));


        btn_multi_win= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,listener_clicker);
        btn_multi_win.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.multi_win_normal,R.mipmap.multi_win_press));
        btn_multi_win.setScaleType(ImageView.ScaleType.CENTER);

        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams)btn_back.getLayoutParams();
        params.gravity = Gravity.CENTER;
        btn_back.setLayoutParams(params);
        _ll_main.addView(btn_back);
        _ll_main.addView(btn_forward);
        _ll_main.addView(btn_home);
        _ll_main.addView(btn_fav);
        //this.addView(btn_sdcard);
        //this.addView(btn_multi_win);
        fl_multi.addView(btn_multi_win);
        win_num = CLController.Get_TextView(cc,CL.Get_FLLP(CL.WC,CL.WC,Gravity.CENTER),
                "1",0xffd0d0d0,9,null);
        fl_multi.addView(win_num);
        _ll_main.addView(fl_multi);
        fl_multi.setVisibility(View.GONE);

    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        int _width=this.getWidth()/item_number;
        ViewGroup.LayoutParams _lp=btn_back.getLayoutParams();
        _lp.width=_width;
        btn_back.setLayoutParams(_lp);
        _lp=btn_forward.getLayoutParams();
        _lp.width=_width;
        btn_forward.setLayoutParams(_lp);

        _lp=btn_home.getLayoutParams();
        _lp.width=_width;
        btn_home.setLayoutParams(_lp);

        _lp=btn_fav.getLayoutParams();
        _lp.width=_width;
        btn_fav.setLayoutParams(_lp);
//        _lp=btn_sdcard.getLayoutParams();
//        _lp.width=_width;
//        btn_sdcard.setLayoutParams(_lp);
        _lp=btn_multi_win.getLayoutParams();
        _lp.width=_width;
        btn_multi_win.setLayoutParams(_lp);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if(!Setting.Share_Setting().get_tip(Setting.Type_fav)){
            final Handler _handler=new Handler();
            _handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    int _width=getWidth();
                    LinearLayout _ll_main=new LinearLayout(cc);
                    _ll_main.setOrientation(LinearLayout.VERTICAL);
                    _ll_main.setGravity(Gravity.RIGHT);
                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
                    _dwe_bg.getPaint().setColor(0xff378d39);
                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
                    _dwe_bg.setPadding(CL.DIP2PX_INT(8),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8),CL.DIP2PX_INT(8));
                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(_width*5/10,CL.WC,0,0,_width/10,0),
                            cc.getResources().getString(R.string.tip_fav), Color.WHITE,14,null);
                    _tip.setBackground(_dwe_bg);
                    _ll_main.addView(_tip);

                    LinearLayout _ll_toolbar=new LinearLayout(cc);
                    _ll_toolbar.setLayoutParams(CL.Get_LP(CL.MP,CL.DIP2PX_INT(45)));
                    _ll_toolbar.setOrientation(LinearLayout.HORIZONTAL);
                    _ll_toolbar.setGravity(Gravity.CENTER_HORIZONTAL);
                    _ll_toolbar.setBackgroundColor(cc.getResources().getColor(R.color.bg_main));
                    _ll_main.addView(_ll_toolbar);

                   // _width=_width/item_number;
                    ImageView _btn_back= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,null);
                    _btn_back.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_back2));
                    _btn_back.setScaleType(ImageView.ScaleType.CENTER);
                    _btn_back.setVisibility(View.INVISIBLE);
                    ImageView _btn_forward= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,null);
                    _btn_forward.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_forward2));
                    _btn_forward.setScaleType(ImageView.ScaleType.CENTER);
                    _btn_forward.setVisibility(View.INVISIBLE);

                    ImageView _btn_home= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,null);
                    //_btn_home.setImageDrawable(cc.getResources().getDrawable(R.mipmap.home_normal));
                    _btn_home.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.home_normal,R.mipmap.home_press));
                    _btn_home.setScaleType(ImageView.ScaleType.CENTER);
                    _btn_home.setVisibility(View.INVISIBLE);

                    ImageView _btn_fav= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,null);
                    _btn_fav.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav));
                    _btn_fav.setScaleType(ImageView.ScaleType.CENTER);

                    ImageView _btn_sdcard= CLController.Get_ImageView(cc, CL.Get_LLLP(_width,CL.MP),null,null);
                    //_btn_sdcard.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav2));
                    _btn_sdcard.setImageDrawable(CL.Get_StateList_Drawable(cc,R.mipmap.toolbal_store_normal,R.mipmap.toolbal_store_click));
                    _btn_sdcard.setScaleType(ImageView.ScaleType.CENTER);
                    _btn_sdcard.setVisibility(View.INVISIBLE);
                    _ll_toolbar.addView(_btn_back);
                    _ll_toolbar.addView(_btn_forward);
                    _ll_toolbar.addView(_btn_home);
                    _ll_toolbar.addView(_btn_fav);
                    _ll_toolbar.addView(_btn_sdcard);

                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.MP, CL.WC, Gravity.BOTTOM), new CLCallback.CB() {
                        @Override
                        public void on_callback() {
                            Setting.Share_Setting().set_tip(Setting.Type_fav,true);
                            _handler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
//                                    LinearLayout _ll_main=new LinearLayout(cc);
//                                    _ll_main.setOrientation(LinearLayout.VERTICAL);
//                                    _ll_main.setGravity(Gravity.RIGHT);
//                                    RoundRectShape _shape=new RoundRectShape(new float[]{32,32,32,32,32,32,32,32}, null, null);
//                                    ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
//                                    _dwe_bg.getPaint().setColor(0xff378d39);
//                                    _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
//                                    _dwe_bg.setPadding(CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12),CL.DIP2PX_INT(12));
//                                    TextView _tip=CLController.Get_TextView(cc,CL.Get_LLLP(CL.WC,CL.WC),
//                                            cc.getResources().getString(R.string.tip_download_picture), Color.WHITE,14,null);
//                                    _tip.setBackground(_dwe_bg);
//                                    _ll_main.addView(_tip);
//
//                                    CLHelper.Get_Helper(_ll_main, CL.Get_FLLP(CL.WC, CL.WC, Gravity.CENTER), new CLCallback.CB() {
//                                        @Override
//                                        public void on_callback() {
//                                                if (Global.country == 0)
//                                                    CLDialog.Get_Alert_Scroll_Dialog(cc,cc.getResources().getString(R.string.download_fun_tip), 10000, 1000, null).show();
//                                        }
//                                    }).show();
                                    GuideNovice dialog = new GuideNovice();
                                    dialog.show(cc.getSupportFragmentManager(), "guide");
                                }
                            },300);
                        }
                    }).show();
                }
            },1000);
        }
    }

    public void set_can_back(boolean can){
        this.can_back=can;
        if(can_back)btn_back.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_back));
        else btn_back.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_back2));
    }

    public void set_can_forward(boolean can){
        this.can_forward=can;
        if(can_forward)btn_forward.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_forward));
        else btn_forward.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_forward2));
    }

    public void set_fav_state(boolean selected){
        if(selected)btn_fav.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav2));
        else btn_fav.setImageDrawable(cc.getResources().getDrawable(R.mipmap.toolbar_fav));
    }

    public void updateWinNum(int num) {
        win_num.setText(String.valueOf(num));
    }
}
