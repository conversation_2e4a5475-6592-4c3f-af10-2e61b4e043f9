package lion;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.FontMetrics;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup.LayoutParams;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import java.util.Map;

public class CL {

    public static final int MP=LayoutParams.MATCH_PARENT;
    public static final int WC=LayoutParams.WRAP_CONTENT;

    public static final int SDK= Build.VERSION.SDK_INT;
    public static boolean Is_Table=false;
    public static float Density;

    private static float dpi;
    private static float font_scale;

    private static boolean isdebug=true;

    public static void Init(Context cc){
        WindowManager wm=(WindowManager)cc.getSystemService(Context.WINDOW_SERVICE);
        Display display=wm.getDefaultDisplay();
        DisplayMetrics metrics=new DisplayMetrics();
        display.getMetrics(metrics);

        CL.dpi=metrics.densityDpi;
        CL.Density=metrics.density;
        CL.font_scale=metrics.scaledDensity;
        int Screen_Width=metrics.widthPixels;
        int Screen_Height=metrics.heightPixels;

        double inches=Math.sqrt(Screen_Width*Screen_Width+Screen_Height*Screen_Height)/CL.dpi;
       // CLOGI("screen inches:"+inches+"  density:"+CL.dpi);
        if(inches>=6.8)Is_Table=true;
        else Is_Table=false;

//		CLOGI("app total memory:"+(Runtime.getRuntime().totalMemory()>>20));
//		CLOGI("app free memory:"+(Runtime.getRuntime().freeMemory()>>20));
//		CLOGI("app max memory:"+(Runtime.getRuntime().maxMemory()>>20));
    }

    public static void CLOGI(String msg){
        if (isdebug)
        Log.i("cllog", msg);
    }
    public static void CLOGE(String msg,Exception ex){
        if (isdebug)
        Log.e("cllog", msg, ex);
    }

    public static int Get_Screen_Width(Context cc){
        WindowManager wm=(WindowManager)cc.getSystemService(Context.WINDOW_SERVICE);
        Display display=wm.getDefaultDisplay();
        DisplayMetrics metrics=new DisplayMetrics();
        display.getMetrics(metrics);
        return metrics.widthPixels;
    }

    public static int Get_Screen_Height(Context cc){
        WindowManager wm=(WindowManager)cc.getSystemService(Context.WINDOW_SERVICE);
        Display display=wm.getDefaultDisplay();
        DisplayMetrics metrics=new DisplayMetrics();
        display.getMetrics(metrics);
        return metrics.heightPixels;
    }

    public static float DIP2PX(float dip){
        //dip/pixel=dpi/160
        return dip*CL.Density;
    }
    public static float SP2PX(float sp){
        return sp*CL.font_scale;
    }
    public static int DIP2PX_INT(float dip){
        //dip/pixel=dpi/160
        return (int)(dip*CL.Density);
    }
    public static int SP2PX_INT(float sp){
        return (int)(sp*CL.font_scale);
    }
    public static float Get_DrawText_YPoint(Paint paint){
        //canvas.drawText("我", this.getWidth()/2, this.getHeight()/2+Get_DrawText_YPoint(_p), _p);
        FontMetrics _fm=paint.getFontMetrics();
        return (-_fm.top-_fm.bottom)/2.0f;
    }
    public static float Get_DrawText_Height(Paint paint){
        FontMetrics _fm=paint.getFontMetrics();
        return -_fm.top-_fm.bottom;
    }

    public static LayoutParams Get_LP_MM(){
        return new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
    }
    public static LayoutParams Get_LP_WW(){
        return new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
    }
    public static LayoutParams Get_LP(int w,int h){
        return new LayoutParams(w, h);
    }
    public static LinearLayout.LayoutParams Get_LLLP(int w,int h){
        LinearLayout.LayoutParams _lp=new LinearLayout.LayoutParams(w, h);
        return _lp;
    }
    public static LinearLayout.LayoutParams Get_LLLP(int w,int h,float weight){
        LinearLayout.LayoutParams _lp=new LinearLayout.LayoutParams(w, h,weight);
        return _lp;
    }
    public static LinearLayout.LayoutParams Get_LLLP(int w,int h,float weight,int left,int top,int right,int bottom){
        LinearLayout.LayoutParams _lp=new LinearLayout.LayoutParams(w, h ,weight);
        _lp.setMargins(left, top, right, bottom);
        return _lp;
    }
    public static LinearLayout.LayoutParams Get_LLLP(int w,int h,int left,int top,int right,int bottom){
        LinearLayout.LayoutParams _lp=new LinearLayout.LayoutParams(w, h);
        _lp.setMargins(left, top, right, bottom);
        return _lp;
    }
    public static FrameLayout.LayoutParams Get_FLLP(int w,int h,int gravity){
        return new FrameLayout.LayoutParams(w, h, gravity);
    }
    public static FrameLayout.LayoutParams Get_FLLP(int w,int h,int gravity,int left,int top,int right,int bottom){
        FrameLayout.LayoutParams _p=Get_FLLP(w,h,gravity);
        _p.setMargins(left, top, right, bottom);
        return _p;
    }
    public static RelativeLayout.LayoutParams Get_RLLP(int w, int h, int left, int top, int right, int bottom) {
        RelativeLayout.LayoutParams _lp=new RelativeLayout.LayoutParams(w, h);
        _lp.setMargins(left, top, right, bottom);
        return _lp;
    }
    public static void Set_Fouces_When_Touch(final View view){
        view.setFocusable(true);
        view.setFocusableInTouchMode(true);
        view.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    view.requestFocus();
                }
                return false;
            }
        });
    }

    public static StateListDrawable Get_StateList_Drawable(Context cc,int id_normal,int id_pressed){
        StateListDrawable _drawable=new StateListDrawable();
        _drawable.addState(new int[]{-android.R.attr.state_pressed}, cc.getResources().getDrawable(id_normal));
        _drawable.addState(new int[]{android.R.attr.state_pressed}, cc.getResources().getDrawable(id_pressed));
        return _drawable;
    }
    public static StateListDrawable Get_StateList_Drawable(Drawable normal,Drawable pressed){
        StateListDrawable _drawable=new StateListDrawable();
        _drawable.addState(new int[]{-android.R.attr.state_pressed},normal);
        _drawable.addState(new int[]{android.R.attr.state_pressed},pressed);
        return _drawable;
    }

    public static void Show_Soft_Input(Context cc,View v){
        InputMethodManager _imm = (InputMethodManager) cc.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        if(_imm.isActive())_imm.showSoftInput(v, InputMethodManager.SHOW_FORCED);
    }
    public static void Hide_Soft_Input(Context cc,View v){
        InputMethodManager _imm = (InputMethodManager) cc.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
        _imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
    }

    public static float Get_P2P_Distance(float x1,float y1,float x2,float y2){
        return (float)Math.sqrt((x2-x1)*(x2-x1)+(y2-y1)*(y2-y1));
    }

    private static long last_time=-1;
    public static boolean Do_Once(){
        if(last_time<0){
            last_time=System.currentTimeMillis();
            return true;
        }
        else{
            long _crt=System.currentTimeMillis();
            if(_crt>last_time+1200){
                last_time=_crt;
                return true;
            }
            else return false;
        }
    }

    // 两次点击按钮之间的点击间隔不能少于1000毫秒
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime;
    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public static int[] Set_Translucent_StatusBar_And_Navigation(Activity activity){
        if(SDK>= Build.VERSION_CODES.KITKAT && SDK<Build.VERSION_CODES.LOLLIPOP){
            Window window = activity.getWindow();
            window.setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            int _h_status = 0;
            int _rid = activity.getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (_rid > 0)_h_status = activity.getResources().getDimensionPixelSize(_rid);
            int _h_nav = 0;
            if(Check_Device_Has_NavigationBar(activity)) {
                _rid = activity.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
                if (_rid > 0) _h_nav = activity.getResources().getDimensionPixelSize(_rid);
            }
            return new int[]{_h_status,_h_nav};
        }else if(SDK>=Build.VERSION_CODES.LOLLIPOP){
            Window window = activity.getWindow();
            window.clearFlags(
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                    |WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
            );
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
            window.setNavigationBarColor(Color.TRANSPARENT);
            int _h_status = 0;
            int _rid = activity.getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (_rid > 0)_h_status = activity.getResources().getDimensionPixelSize(_rid);
            int _h_nav = 0;
            if(Check_Device_Has_NavigationBar(activity)) {
                _rid = activity.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
                if (_rid > 0) _h_nav = activity.getResources().getDimensionPixelSize(_rid);
            }
            return new int[]{_h_status,_h_nav};
        }
        return null;
    }
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public static int Set_Translucent_StatusBar(Window window){
        if(SDK>= Build.VERSION_CODES.KITKAT && SDK<Build.VERSION_CODES.LOLLIPOP){
            window.setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            int _h_status = 0;
            int _rid = window.getContext().getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (_rid > 0)_h_status = window.getContext().getResources().getDimensionPixelSize(_rid);
            return _h_status;
        }else if(SDK>=Build.VERSION_CODES.LOLLIPOP){
            window.clearFlags(
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                            | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION
            );
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                            View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
            int _h_status = 0;
            int _rid = window.getContext().getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (_rid > 0)_h_status = window.getContext().getResources().getDimensionPixelSize(_rid);
            return _h_status;
        }
        return 0;
    }
    public static void Set_Full_Screen(Activity activity,boolean hide){
        if(hide) {
            activity.getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_IMMERSIVE
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
            );
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        }else{
            activity.getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);
            activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        }
    }
    public static boolean Check_Device_Has_NavigationBar(Context activity) {
        //通过判断设备是否有返回键、菜单键(不是虚拟键,是手机屏幕外的按键)来确定是否有navigation bar
        boolean hasMenuKey = ViewConfiguration.get(activity)
                .hasPermanentMenuKey();
        boolean hasBackKey = KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_BACK);
        if (!hasMenuKey && !hasBackKey) {
            //这个设备有导航栏
            return true;
        }
        return false;
    }

    public static void Print_Map(Map<String,String> map){
        for (Map.Entry<String,String> item:map.entrySet()){
            CL.CLOGI(item.getKey()+":"+item.getValue());
        }
    }

    public static int[] GetScreenSize(Context cc) {
        WindowManager wm = (WindowManager) cc.getSystemService(Context.WINDOW_SERVICE);
        Display display = wm.getDefaultDisplay();
        DisplayMetrics metrics = new DisplayMetrics();
        display.getMetrics(metrics);
        return new int[] {metrics.widthPixels, metrics.heightPixels};
    }
}