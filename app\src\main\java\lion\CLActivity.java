package lion;

//import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

/**
 * Created by leron on 2016/8/2.
 */
public class CLActivity extends AppCompatActivity {

    public boolean checkPermission(String permName) {
        PackageManager pm = this.getPackageManager();
        int check = pm.checkPermission(permName, this.getPackageName());
        if (check == PackageManager.PERMISSION_GRANTED) {
            return true;
        } else {
            return false;
        }
    }

    public boolean checkPermissionRationale(String permName) {
        return ActivityCompat.shouldShowRequestPermissionRationale(this, permName);
    }

    public interface EventPermissions{
        void on_request_permissions_result(String[] permissions,int[] grant_results);
    }

    public void request_permissions(String[] reqs,EventPermissions cber){
        if(cber==null)return;
        this.crt_req_permission=cber;
        ActivityCompat.requestPermissions(this,reqs,1001);
    }

    private EventPermissions crt_req_permission;
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if(requestCode==1001){
            if(crt_req_permission!=null)crt_req_permission.on_request_permissions_result(permissions,grantResults);
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == lastRequestCode) {
            if (lastRequestListener != null) {
                lastRequestListener.onActivityResult(resultCode, data);
            }
            lastRequestCode = -11111;
            lastRequestListener = null;
        }
    }


    public interface EventResult {
        void onActivityResult(int result, Intent data);
    }

    private int lastRequestCode = -11111;
    private EventResult lastRequestListener;
    public final void startActivityForResult(Intent intent, int requestCode, EventResult listen) {
        this.lastRequestCode = requestCode;
        this.lastRequestListener = listen;
        super.startActivityForResult(intent, requestCode);
    }
}
