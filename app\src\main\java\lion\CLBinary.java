package lion;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * Created by leron on 2016/7/9.
 */
public class CLBinary {



    public static void Write_To_Stream(OutputStream os,String content) throws IOException {
        if(content==null){
            DataOutputStream _dos=new DataOutputStream(os);
            _dos.writeInt(-1);
        }else if(content.isEmpty()){
            DataOutputStream _dos=new DataOutputStream(os);
            _dos.writeInt(0);
        }else{
            byte[] _data = content.getBytes("utf8");
            DataOutputStream _dos=new DataOutputStream(os);
            _dos.writeInt(_data.length);
            _dos.write(_data);
        }
    }
    public static void Write_To_Stream(OutputStream os,int v) throws IOException {
        //big endian
        DataOutputStream _dos=new DataOutputStream(os);
        _dos.writeInt(v);
    }
    public static void Write_To_Stream(OutputStream os,long v) throws IOException {
        //big endian
        DataOutputStream _dos=new DataOutputStream(os);
        _dos.writeLong(v);
    }
    public static void Write_To_Stream(OutputStream os,float v) throws IOException {
        //big endian
        DataOutputStream _dos=new DataOutputStream(os);
        _dos.writeFloat(v);
    }

    public static String Read_Stream_String(InputStream is) throws IOException {
        DataInputStream _dis=new DataInputStream(is);
        int _v=_dis.readInt();
        if(_v==-1)return null;
        if(_v==0)return "";
        byte[] _data=new byte[_v];
        _dis.readFully(_data);
        return new String(_data,"utf8");
    }
    public static int Read_Stream_Int(InputStream is)throws IOException{
        DataInputStream _dis=new DataInputStream(is);
        int _v=_dis.readInt();
        return _v;
    }
    public static long Read_Stream_Long(InputStream is)throws IOException{
        DataInputStream _dis=new DataInputStream(is);
        long _v=_dis.readLong();
        return _v;
    }
    public static float Read_Stream_Float(InputStream is)throws IOException{
        DataInputStream _dis=new DataInputStream(is);
        float _v=_dis.readFloat();
        return _v;
    }


    public static int Byte_To_Int(byte b){
        return b&0xff;
    }

}
