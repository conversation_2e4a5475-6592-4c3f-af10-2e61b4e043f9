package lion;

import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.BitmapFactory;
import android.graphics.BitmapShader;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.Shader;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class CLBitmapLoader {

	public static final int MB=1024*1024;
	
	private int Max_memory;
	private volatile int memory;
	private String name;
	
	public CLBitmapLoader(int max_memory){
		this.Max_memory=max_memory;
	}
	public CLBitmapLoader(int max_memory,String name){
		this.name=name;
		this.Max_memory=max_memory;
	}
	
	public static Bitmap Create_Bitmap_Not_Count(String path,int width,int height){
		int _w=width,_h=height;
		if(_w<20)_w=20;
		if(_h<20)_h=20;
		BitmapFactory.Options _option=new BitmapFactory.Options();
		_option.inJustDecodeBounds=true;
		BitmapFactory.decodeFile(path, _option);
		if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
			return null;
		}
		int _p_width=_option.outWidth;
		int _p_height=_option.outHeight;
		if(_p_width <= _w || _p_height <= _h){
			_option.inSampleSize=1;
		}
		else{
			int _xs=_p_width/_w;
			int _ys=_p_height/_h;
			int _ss=(_xs > _ys ? _xs : _ys);
			_option.inSampleSize=_ss;
		}
		_option.inJustDecodeBounds=false;
		Bitmap _bitmap=BitmapFactory.decodeFile(path,_option);
		return _bitmap;
	}
	public static Bitmap Create_Bitmap_Not_Count(InputStream path,int width,int height){
		int _w=width,_h=height;
		if(_w<20)_w=20;
		if(_h<20)_h=20;
		BitmapFactory.Options _option=new BitmapFactory.Options();
		_option.inJustDecodeBounds=true;
		BitmapFactory.decodeStream(path, null, _option);
		if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
			return null;
		}
		int _p_width=_option.outWidth;
		int _p_height=_option.outHeight;
		if(_p_width <= _w || _p_height <= _h){
			_option.inSampleSize=1;
		}
		else{
			int _xs=_p_width/_w;
			int _ys=_p_height/_h;
			int _ss=(_xs > _ys ? _xs : _ys);
			_option.inSampleSize=_ss;
		}
		_option.inJustDecodeBounds=false;
		Bitmap _bitmap=BitmapFactory.decodeStream(path, null, _option);
		return _bitmap;
	}

	public static Bitmap Create_Bitmap_With_BG(String path, float radius){
		float _r=radius;
		if(_r<20)_r=20;
		BitmapFactory.Options _option=new BitmapFactory.Options();
		_option.inJustDecodeBounds=true;
		BitmapFactory.decodeFile(path, _option);
		if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
			return null;
		}
		int _p_width=_option.outWidth;
		int _p_height=_option.outHeight;
		if(_p_width <= _r || _p_height <= _r){
			_option.inSampleSize=1;
		}
		else{
			float _xs=_p_width/_r;
			float _ys=_p_height/_r;
			float _ss=(_xs > _ys ? _xs : _ys);
			_option.inSampleSize=(int)_ss;
		}
		_option.inJustDecodeBounds=false;
		Bitmap _bitmap=BitmapFactory.decodeFile(path,_option);
		if(_bitmap!=null){
			float _bx=_bitmap.getWidth()/2;
			float _by=_bitmap.getHeight()/2;
			float _rr=(_bitmap.getWidth()>_bitmap.getHeight()?_bitmap.getHeight()/2:_bitmap.getWidth()/2);
			Bitmap _ttt=Bitmap.createBitmap(_bitmap, (int)(_bx-_rr), (int)(_by-_rr), (int)(_rr*2), (int)(_rr*2));
			if(_ttt==null){
				_bitmap.recycle();
				return null;
			}
			Paint _p=new Paint();
			_p.setAntiAlias(true);
			BitmapShader _sharder=new BitmapShader(_ttt, Shader.TileMode.REPEAT, Shader.TileMode.REPEAT);
			Bitmap _target=Bitmap.createBitmap(_ttt.getWidth(), _ttt.getHeight(), Config.ARGB_8888);
			Canvas _canvas=new Canvas(_target);
			_p.setColor(0x665190d7);
			_p.setStyle(Paint.Style.FILL);
			_canvas.drawRoundRect(new RectF(3, 3, _canvas.getWidth() - 3, _canvas.getHeight() - 3), 36, 36, _p);
			//_canvas.drawCircle(_canvas.getWidth()/2,_canvas.getHeight()/2,_canvas.getWidth()/2,_p);   //画圆外圈
			_p.setAlpha(0xff);
			_p.setShader(_sharder);
			_canvas.drawCircle(_target.getWidth() / 2, _target.getHeight() / 2, _target.getWidth() / 2-CL.DIP2PX_INT(8), _p);
			_ttt.recycle();
			if(_bitmap!=null && !_bitmap.isRecycled())_bitmap.recycle();
			_bitmap=_target;
		}
		return _bitmap;
	}

	public static boolean Compress_And_Store(Bitmap bitmap,int file_length,String path){
        if(bitmap==null || bitmap.isRecycled())return false;
        try {
            int _ratio = 100;
            ByteArrayOutputStream _bos = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, _ratio, _bos);
            while (_bos.toByteArray().length >= file_length && _ratio > 20) {
                _bos.reset();
                _ratio -= 20;
                bitmap.compress(Bitmap.CompressFormat.JPEG,_ratio, _bos);
            }
            FileOutputStream _fos = new FileOutputStream(path);
            _bos.flush();
            byte[] _data = _bos.toByteArray();
            _bos.close();
            _fos.write(_data);
            _fos.close();
            return true;
        } catch (IOException e) {
            CL.CLOGE("compress and store error!",e);
            return false;
        }
    }
	
	public synchronized Bitmap create_bitmap(String path,int width,int height){
		int _w=width,_h=height;
		if(_w<20)_w=20;
		if(_h<20)_h=20;
		BitmapFactory.Options _option=new BitmapFactory.Options();
		_option.inJustDecodeBounds=true;
		BitmapFactory.decodeFile(path, _option);
		if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
			return null;
		}
		int _p_width=_option.outWidth;
		int _p_height=_option.outHeight;
		if(_p_width <= _w || _p_height <= _h){
			_option.inSampleSize=1;
		}
		else{
			int _xs=_p_width/_w;
			int _ys=_p_height/_h;
			int _ss=(_xs > _ys ? _xs : _ys);
			_option.inSampleSize=_ss;
		}
		_option.inJustDecodeBounds=false;
		Bitmap _bitmap=BitmapFactory.decodeFile(path,_option);
		if(_bitmap!=null){
			memory+=_bitmap.getRowBytes()*_bitmap.getHeight();
			logger("create memory:"+memory);
		}
		return _bitmap;
	}
	public synchronized Bitmap create_bitmap(InputStream path,int width,int height){
		int _w=width,_h=height;
		if(_w<20)_w=20;
		if(_h<20)_h=20;
		BitmapFactory.Options _option=new BitmapFactory.Options();
		_option.inJustDecodeBounds=true;
		BitmapFactory.decodeStream(path, null,_option);
		if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
			return null;
		}
		int _p_width=_option.outWidth;
		int _p_height=_option.outHeight;
		if(_p_width <= _w || _p_height <= _h){
			_option.inSampleSize=1;
		}
		else{
			int _xs=_p_width/_w;
			int _ys=_p_height/_h;
			int _ss=(_xs > _ys ? _xs : _ys);
			_option.inSampleSize=_ss;
		}
		_option.inJustDecodeBounds=false;
		Bitmap _bitmap=BitmapFactory.decodeStream(path, null,_option);
		if(_bitmap!=null){
			memory+=_bitmap.getRowBytes()*_bitmap.getHeight();
			logger("create memory:"+memory);
		}
		return _bitmap;
	}
	public synchronized Bitmap create_circle_bitmap(String path,float radius){
		float _r=radius;
		if(_r<20)_r=20;
		BitmapFactory.Options _option=new BitmapFactory.Options();
		_option.inJustDecodeBounds=true;
		BitmapFactory.decodeFile(path, _option);
		if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
			return null;
		}
		int _p_width=_option.outWidth;
		int _p_height=_option.outHeight;
		if(_p_width <= _r || _p_height <= _r){
			_option.inSampleSize=1;
		}
		else{
			float _xs=_p_width/_r;
			float _ys=_p_height/_r;
			float _ss=(_xs > _ys ? _xs : _ys);
			_option.inSampleSize=(int)_ss;
		}
		_option.inJustDecodeBounds=false;
		Bitmap _bitmap=BitmapFactory.decodeFile(path,_option);
		if(_bitmap!=null){
			float _bx=_bitmap.getWidth()/2;
			float _by=_bitmap.getHeight()/2;
			float _rr=(_bitmap.getWidth()>_bitmap.getHeight()?_bitmap.getHeight()/2:_bitmap.getWidth()/2);
			Bitmap _ttt=Bitmap.createBitmap(_bitmap, (int)(_bx-_rr), (int)(_by-_rr), (int)(_rr*2), (int)(_rr*2));
			if(_ttt==null){
				_bitmap.recycle();
				return null;
			}
			Paint _p=new Paint();
			_p.setAntiAlias(true);
			BitmapShader _sharder=new BitmapShader(_ttt, Shader.TileMode.REPEAT, Shader.TileMode.REPEAT);
			_p.setShader(_sharder);
			Bitmap _target=Bitmap.createBitmap(_ttt.getWidth(), _ttt.getHeight(), Config.ARGB_8888);
			Canvas _canvas=new Canvas(_target);
			_canvas.drawCircle(_target.getWidth()/2, _target.getHeight()/2, _target.getWidth()/2, _p);
			_ttt.recycle();
			if(_bitmap!=null && !_bitmap.isRecycled())_bitmap.recycle();
			_bitmap=_target;
			memory+=_bitmap.getRowBytes()*_bitmap.getHeight();
			logger("create memory:"+memory);
		}
		return _bitmap;
	}
	public synchronized Bitmap create_bitmap(String path){
		Bitmap _bitmap=BitmapFactory.decodeFile(path);
		if(_bitmap!=null){
			memory+=_bitmap.getRowBytes()*_bitmap.getHeight();
			logger("create memory:"+memory);
		}
		return _bitmap;
	}
	
	
	public synchronized void recycle_bitmap(Bitmap bitmap){
		if(bitmap!=null && !bitmap.isRecycled()){
			memory-=bitmap.getRowBytes()*bitmap.getHeight();
			bitmap.recycle();
			logger("recycle memory:"+memory);
		}
	}
	
	public boolean whether_exceed_memory(){
		if(this.memory>Max_memory)return true;
		else return false;
	}
	
	private void logger(String msg){
		if(this.name!=null)CL.CLOGI(name + "$"+msg);
		else CL.CLOGI(msg);
	}
}
