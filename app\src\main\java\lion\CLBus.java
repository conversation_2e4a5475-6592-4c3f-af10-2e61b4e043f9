package lion;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by leron on 2015/6/21.
 */
public class CLBus {

    public final static int CLBus_Broadcast =-1;
    public final static int CLBus_Observer =-2;

    public interface CBEventer{
        void on_event(final int action, final Object... msgs);
    }

    private static CLBus Myself;
    public static CLBus Share_Instance(){
        if(Myself==null){
            Myself=new CLBus();
        }
        return Myself;
    }
    private CLBus(){
        worker.start();
    }

    private final String SIGNAL="signal_worker";

    private HashMap<String,ArrayList<Item>> users=new HashMap<String,ArrayList<Item>>();
    private ArrayDeque<Message> messages =new ArrayDeque<Message>();
    private Worker worker=new Worker();

    /**
     * 注册事件和回调
     * @param group 组名
     * @param eventer 回调监听器
     * @param filter 过滤器
     */
    public synchronized void register(String group,CBEventer eventer,int... filter){
        if(group==null || eventer==null || filter==null || filter.length==0)
            throw new RuntimeException("args(group,eventer,filter.length>0) can not be null");
        ArrayList<Item> _tmp=users.get(group);
        if(_tmp==null){
            _tmp=new ArrayList<Item>();
            users.put(group, _tmp);
        }
        _tmp.add(new Item(group,eventer,filter));
    }
    public synchronized void unregister(String group,CBEventer eventer){
        if(group==null || eventer==null)
            throw new RuntimeException("args(group,eventer) can not be null");
        if(users.containsKey(group)){
            ArrayList<Item> _tmp=users.get(group);
            for(int i=0;i<_tmp.size();++i){
                if(_tmp.get(i).listener==eventer){
                    _tmp.remove(i);
                    if(_tmp.size()==0)users.remove(group);
                    break;
                }
            }
        }
    }
    public synchronized void send_msg(String group,int action,Object... msgs){
        ArrayList<Message> _ms=new ArrayList<Message>();
        if(group==null){//广播
            if(action==CLBus_Broadcast){//广播
                for(Map.Entry<String,ArrayList<Item>> tmp:users.entrySet()){
                    ArrayList<Item> _items=tmp.getValue();
                    for(Item _item : _items){
                        _ms.add(new Message(action,_item.listener,msgs));
                    }
                }
            }
            else{
                for(Map.Entry<String,ArrayList<Item>> tmp:users.entrySet()){
                    ArrayList<Item> _items=tmp.getValue();
                    for(Item _item : _items){
                        if(_item.filter[0]==CLBus_Observer){//观察者
                            _ms.add(new Message(action,_item.listener,msgs));
                        }
                        else{//特定接收者
                            for(int n=0;n<_item.filter.length;++n){
                                if(action==_item.filter[n]){
                                    _ms.add(new Message(action,_item.listener,msgs));
                                }
                            }
                        }
                    }
                }
            }
        }
        else{
            if(users.containsKey(group)){
                ArrayList<Item> _tmp=users.get(group);
                for(int i=0;i<_tmp.size();++i){
                    Item _item=_tmp.get(i);
                    if(action== CLBus_Broadcast){//group广播
                        _ms.add(new Message(action,_item.listener,msgs));
                    }
                    else if(_item.filter[0]==CLBus_Observer){//观察者
                        _ms.add(new Message(action,_item.listener,msgs));
                    }
                    else{//特定接收者
                        for(int n=0;n<_item.filter.length;++n){
                            if(action==_item.filter[n]){
                                _ms.add(new Message(action,_item.listener,msgs));
                            }
                        }
                    }
                }
            }
        }
        if(_ms.size()>0){
            synchronized (SIGNAL){
                messages.addAll(_ms);
                SIGNAL.notifyAll();
            }
        }
    }

    public void send_msg_immediate(String group,int action,Object... msgs){
        if(group==null){//广播
            if(action==CLBus_Broadcast){//广播
                for(Map.Entry<String,ArrayList<Item>> tmp:users.entrySet()){
                    ArrayList<Item> _items=tmp.getValue();
                    for(Item _item : _items){
                        _item.listener.on_event(action, msgs);
                    }
                }
            }
            else{
                for(Map.Entry<String,ArrayList<Item>> tmp:users.entrySet()){
                    ArrayList<Item> _items=tmp.getValue();
                    for(Item _item : _items){
                        if(_item.filter[0]==CLBus_Observer){//观察者
                            _item.listener.on_event(action, msgs);
                        }
                        else{//特定接收者
                            for(int n=0;n<_item.filter.length;++n){
                                if(action==_item.filter[n]){
                                    _item.listener.on_event(action, msgs);
                                }
                            }
                        }
                    }
                }
            }
        }
        else{
            if(users.containsKey(group)){
                ArrayList<Item> _tmp=users.get(group);
                for(int i=0;i<_tmp.size();++i){
                    Item _item=_tmp.get(i);
                    if(action== CLBus_Broadcast){//group广播
                        _item.listener.on_event(action, msgs);
                    }
                    else if(_item.filter[0]==CLBus_Observer){//观察者
                        _item.listener.on_event(action, msgs);
                    }
                    else{//特定接收者
                        for(int n=0;n<_item.filter.length;++n){
                            if(action==_item.filter[n]){
                                _item.listener.on_event(action, msgs);
                            }
                        }
                    }
                }
            }
        }
    }

    public synchronized void clear(){
        users.clear();
        synchronized (SIGNAL){
            messages.clear();
            SIGNAL.notifyAll();
        }
    }
    public synchronized void destory(){
        users.clear();
        worker.RUN=false;
        synchronized (SIGNAL){
            messages.clear();
            SIGNAL.notifyAll();
        }
    }

    private class Item{
        public String group;
        public int[] filter;
        public CBEventer listener;

        public Item(String group,CBEventer listener,int... filter){
            this.group=group;
            this.listener=listener;
            this.filter=filter;
        }
    }

    private class Message{
        public int action;
        public Object[] msgs;
        public CBEventer listener;
        public Message(int action,CBEventer listen,Object... msgs){
            this.action=action;
            this.msgs=msgs;
            this.listener=listen;
        }
    }
    private class Worker extends Thread{

        private boolean RUN=true;

        @Override
        public void run() {
            while (RUN) {
                try {
                    Message _msg;
                    synchronized (SIGNAL){
                        _msg= messages.poll();
                        if(_msg==null){
                            SIGNAL.wait();
                            continue;
                        }
                    }
                    _msg.listener.on_event(_msg.action, _msg.msgs);
                } catch (Exception e) {
                    CL.CLOGE("CLBus error:" + e.toString(),e);
                }
            }
        }
    }
}
