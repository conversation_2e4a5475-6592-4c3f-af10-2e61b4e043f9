package lion;

/**
 * Created by leron on 2015/6/13.
 */
public class CLCallback {

    public interface CB{
        void on_callback();
    }
    public interface CBO<T>{
        void on_callback(T obj);
    }
    public interface CB_TF{
        void on_callback_success();
        void on_callback_fail(int code, String msg);
    }
    public interface CB_TFO<T>{
        boolean on_callback_success(T obj, String msg);
        void on_callback_fail(int code, String msg);
    }


    //activity
    public interface CB_Activity{
        void on_close();
    }
}
