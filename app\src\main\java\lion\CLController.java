package lion;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

/**
 * Created by leron on 2015/6/10.
 */
public class CLController {

    public static Button Get_Button(Context cc,LayoutParams lp,String text,int text_color,int text_size,Drawable bg,View.OnClickListener listener){
        Button _btn=new Button(cc);
        if(lp!=null)_btn.setLayoutParams(lp);
        if(text!=null)_btn.setText(text);
        _btn.setTextColor(text_color);
        _btn.setTextSize(text_size);
        _btn.setBackground(bg);
        if(listener!=null)_btn.setOnClickListener(listener);
        return _btn;
    }
    public static CenterIconButton Get_CenterIcon_Button(Context cc,LayoutParams lp,View.OnClickListener listener){
        CenterIconButton _btn=new CenterIconButton(cc);
        if(lp!=null)_btn.setLayoutParams(lp);
        if(listener!=null)_btn.setOnClickListener(listener);
        return _btn;
    }

    public static ImageView Get_ImageView(Context cc, LayoutParams lp, Drawable drawable, View.OnClickListener listener){
        ImageView _iv=new ImageView(cc);
        if(lp!=null)_iv.setLayoutParams(lp);
        _iv.setBackground(drawable);
        if(listener!=null){
            _iv.setClickable(true);
            _iv.setOnClickListener(listener);
        }
        return _iv;
    }
    public static TextView Get_TextView(Context cc,LayoutParams lp,String text,int text_color,float text_size,View.OnClickListener listener){
        TextView _tv=new TextView(cc);
        if(lp!=null)_tv.setLayoutParams(lp);
        if(text!=null)_tv.setText(text);
        if(text_size>0)_tv.setTextSize(text_size);
        _tv.setTextColor(text_color);
        if(listener!=null){
            _tv.setClickable(true);
            _tv.setOnClickListener(listener);
        }
        return _tv;
    }
    public static DiscolourButton Get_Discolour_Button(Context cc,LayoutParams lp,String text,float text_size,int c_normal,int c_click,View.OnClickListener listener){
        DiscolourButton _btn=new DiscolourButton(cc,c_normal,c_click);
        if(lp!=null)_btn.setLayoutParams(lp);
        if(text!=null)_btn.setText(text);
        if(text_size>0)_btn.setTextSize(text_size);
        if(listener!=null){
            _btn.setClickable(true);
            _btn.setOnClickListener(listener);
        }
        return _btn;
    }
    public static TextView Get_TextView_Divider(Context cc,LayoutParams lp,int bg_color){
        TextView _tv=new TextView(cc);
        if(lp!=null)_tv.setLayoutParams(lp);
        _tv.setPadding(0,0,0,0);
        _tv.setBackgroundColor(bg_color);
        return _tv;
    }

    public static TextView Get_TextView_Gradient_Divider(Context cc,LayoutParams lp,int[] bg_colors){
        TextView _tv=new TextView(cc);
        if(lp!=null)_tv.setLayoutParams(lp);
        _tv.setPadding(0,0,0,0);
        //_tv.setBackgroundColor(bg_color);
        GradientDrawable g = new GradientDrawable(GradientDrawable.Orientation.TL_BR, bg_colors);
        _tv.setBackground(g);
        return _tv;
    }

    public static TextView Get_TextView_With_Gravity(Context cc,LayoutParams lp,int gravity,String text,int text_color,float text_size,View.OnClickListener listener){
        TextView _tv=new TextView(cc);
        if(lp!=null)_tv.setLayoutParams(lp);
        _tv.setGravity(gravity);
        if(text!=null)_tv.setText(text);
        if(text_size>0)_tv.setTextSize(text_size);
        _tv.setTextColor(text_color);
        if(listener!=null){
            _tv.setClickable(true);
            _tv.setOnClickListener(listener);
        }
        return _tv;
    }
    public static LinearLayout Get_LinearLayout(Context cc,LayoutParams lp,int orientation,View.OnClickListener listener){
        LinearLayout _ll=new LinearLayout(cc);
        if(lp!=null)_ll.setLayoutParams(lp);
        _ll.setOrientation(orientation);
        if(listener!=null){
            _ll.setClickable(true);
            _ll.setOnClickListener(listener);
        }
        return _ll;
    }
    public static LinearLayout Get_LinearLayout(Context cc,LayoutParams lp,int orientation,int color,View.OnClickListener listener){
        LinearLayout _ll=new LinearLayout(cc);
        if(lp!=null)_ll.setLayoutParams(lp);
        _ll.setOrientation(orientation);
        _ll.setBackgroundColor(color);
        if(listener!=null){
            _ll.setClickable(true);
            _ll.setOnClickListener(listener);
        }
        return _ll;
    }
    public static FrameLayout Get_FrameLayout(Context cc, LayoutParams lp, int color, View.OnClickListener listener){
        FrameLayout _fl=new FrameLayout(cc);
        if(lp!=null)_fl.setLayoutParams(lp);
        _fl.setBackgroundColor(color);
        if(listener!=null){
            _fl.setClickable(true);
            _fl.setOnClickListener(listener);
        }
        return _fl;
    }

    public static RelativeLayout GetRelativeLayout(Context cc, LayoutParams lp, int color, View.OnClickListener listener) {
        RelativeLayout _rl = new RelativeLayout(cc);
        if (_rl == null) {
            return null;
        }
        _rl.setLayoutParams(lp);
        if(listener!=null){
            _rl.setClickable(true);
            _rl.setOnClickListener(listener);
        }
        return _rl;
    }


    @SuppressWarnings("deprecation")
    public static class CenterIconButton extends Button{
        private boolean on_touch=false;
        private Drawable dra_normal,dra_clicked,dra_disable;

        private OnTouchListener listener=new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int _action=event.getAction()&MotionEvent.ACTION_MASK;
                if(_action==MotionEvent.ACTION_DOWN){
                    on_touch=true;
                }
                else if(_action==MotionEvent.ACTION_UP || _action==MotionEvent.ACTION_CANCEL){
                    on_touch=false;
                }
                invalidate();
                return false;
            }
        };

        public CenterIconButton(Context context) {
            super(context);
            this.setOnTouchListener(listener);
        }

        public void set_center_icon(Drawable normal,Drawable clicked){
            this.dra_normal=normal;
            this.dra_clicked=clicked;
            postInvalidate();
        }
        public void set_disable_icon(Drawable disable){
            this.dra_disable=disable;
        }


        @Override
        public void draw(Canvas canvas) {
            if(this.isEnabled()) {
                if (on_touch && dra_clicked != null) {
                    int _w = dra_clicked.getIntrinsicWidth();
                    int _h = dra_clicked.getIntrinsicHeight();
                    int _x = (this.getWidth() - _w) / 2;
                    int _y = (this.getHeight() - _h) / 2;
                    dra_clicked.setBounds(_x, _y, _x + _w, _y + _h);
                    dra_clicked.draw(canvas);
                } else if (!on_touch && dra_normal != null) {
                    int _w = dra_normal.getIntrinsicWidth();
                    int _h = dra_normal.getIntrinsicHeight();
                    int _x = (this.getWidth() - _w) / 2;
                    int _y = (this.getHeight() - _h) / 2;
                    dra_normal.setBounds(_x, _y, _x + _w, _y + _h);
                    dra_normal.draw(canvas);
                }
            }
            else{
                if(dra_disable!=null){
                    int _w = dra_disable.getIntrinsicWidth();
                    int _h = dra_disable.getIntrinsicHeight();
                    int _x = (this.getWidth() - _w) / 2;
                    int _y = (this.getHeight() - _h) / 2;
                    dra_disable.setBounds(_x, _y, _x + _w, _y + _h);
                    dra_disable.draw(canvas);
                }
            }
        }
    }

    public static class CTMScrollView extends ScrollView {

        public CTMScrollView(Context context) {
            super(context);
        }

        private boolean childer_monopoly_event;
        public void childer_monopoly_touch_event(){
            this.childer_monopoly_event=true;
        }
        public void childer_discard_touch_event(){
            this.childer_monopoly_event=false;
        }
        @Override
        public boolean onInterceptTouchEvent(MotionEvent ev) {
            if(childer_monopoly_event){
                if(ev.getAction()==MotionEvent.ACTION_CANCEL || ev.getAction()==MotionEvent.ACTION_UP){
                    childer_monopoly_event=false;
                }
                return false;
            }
            return super.onInterceptTouchEvent(ev);
        }
    }

    //字体变色按钮
    public static class DiscolourButton extends TextView{

        private int color_normal=0xff0079ff;
        private int color_click=0xff7dbafc;
        private int color_bg_normal=0;
        private int color_bg_touch=0xff000000;
        private OnTouchListener listener_touch=new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if(event.getAction()==MotionEvent.ACTION_DOWN){
                    setTextColor(color_click);
                    setBackgroundColor(color_bg_touch);
                }
                else if(event.getAction()==MotionEvent.ACTION_CANCEL ||
                        event.getAction()==MotionEvent.ACTION_UP){
                    setTextColor(color_normal);
                    setBackgroundColor(color_bg_normal);
                }
                return false;
            }
        };

        public DiscolourButton(Context context,int c_normal,int c_click) {
            super(context);
            color_normal=c_normal;
            color_click=c_click;
            this.setTextColor(color_normal);
            this.setOnTouchListener(listener_touch);
            this.setGravity(Gravity.CENTER);
            this.setPadding(CL.DIP2PX_INT(3),this.getPaddingTop(),CL.DIP2PX_INT(3),this.getPaddingBottom());
        }

        public void set_touch_bg_color(int normal,int touch){
            this.color_bg_normal=normal;
            this.color_bg_touch=touch;
        }
    }


    public static class Waiter extends View{

        private Paint paint;
        private int color;
        private float last_width=0,last_height=0;
        private float radius;
        private float bg_radius;
        private PointF[] points;
        private int crt_index;
        private long last_time=0;


        public Waiter(Context context,int color) {
            super(context);
            paint=new Paint(Paint.ANTI_ALIAS_FLAG);
            this.color=color;
        }

        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            int _tw=MeasureSpec.getMode(widthMeasureSpec);
            int _th=MeasureSpec.getMode(heightMeasureSpec);
            if((_tw == MeasureSpec.AT_MOST || _tw == MeasureSpec.UNSPECIFIED)){
                this.setMinimumWidth(CL.DIP2PX_INT(22));
            }
            if(_th == MeasureSpec.AT_MOST || _th == MeasureSpec.UNSPECIFIED){
               this.setMinimumHeight(CL.DIP2PX_INT(22));
            }
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }

        @Override
        public void draw(Canvas canvas) {
            float _width=this.getWidth();
            float _height=this.getHeight();
            if(_width==0 || _height==0)return;
            if(last_width!=_width || last_height!=_height){
                last_width=_width;
                last_height=_height;
                radius=Math.min(_width,_height)/2*0.66f;
                bg_radius=radius/7.0f;
                points=new PointF[16];
                crt_index=points.length/2;
                float _angle=360.0f/points.length;
                for(int i=0;i<points.length;++i){
                    points[i]=new PointF();
                    double _hudu=Math.toRadians(360.0f-i*_angle);
                    points[i].x=(float)(Math.sin(_hudu)*radius);
                    points[i].y=(float)(Math.cos(_hudu)*radius);
//                    CL.CLOGI("x:"+points[i].x+" y:"+points[i].y);
                }
            }

            long _crt_time=SystemClock.uptimeMillis();
            if(last_time==0)last_time= _crt_time;
            long _cha=_crt_time-last_time;
            if(_cha>60){
                last_time=_crt_time;
                int _count=(int)(_cha/60);
                crt_index+=_count;
                if(crt_index>=points.length)crt_index=crt_index%points.length;
            }

            paint.setColor(color);
            paint.setStyle(Paint.Style.FILL);
            float _x=this.getWidth()/2.0f;
            float _y=this.getHeight()/2.0f;
            for(int i=0;i<points.length;++i){
                int _index=(crt_index-i<0?points.length+(crt_index-i):crt_index-i);
                int _alpha=255-_index*20;
                if(_alpha<0)_alpha=0;
                paint.setAlpha(_alpha);
                canvas.drawCircle(points[i].x+_x,points[i].y+_y,bg_radius,paint);
            }
            postInvalidateDelayed(60);
        }
    }


    public static class PopmenuSimple extends LinearLayout{
        private Context cc;
        private LinearLayout ll_btns;
        public PopmenuSimple(Context context,OnClickListener listener_cancel) {
            super(context);
            this.cc=context;
            this.setLayoutParams(CL.Get_LP_WW());
            this.setOrientation(LinearLayout.VERTICAL);
            this.setPadding(CL.DIP2PX_INT(15), 0, CL.DIP2PX_INT(15), 0);
            this.setClickable(true);
            ll_btns= CLController.Get_LinearLayout(cc,CL.Get_LP(CL.MP, CL.WC),LinearLayout.VERTICAL,null);
            ll_btns.setBackground(get_bg());
            ll_btns.setGravity(Gravity.CENTER);
            this.addView(ll_btns);

            RoundRectShape _shape=new RoundRectShape(new float[]{12,12,12,12,12,12,12,12}, null, null);
            ShapeDrawable _drawable1=new ShapeDrawable(_shape);
            _drawable1.getPaint().setColor(0xffffffff);
            _drawable1.getPaint().setStyle(Paint.Style.FILL);
            ShapeDrawable _drawable2=new ShapeDrawable(_shape);
            _drawable2.getPaint().setColor(0xffe0e0e0);
            _drawable2.getPaint().setStyle(Paint.Style.FILL);
            TextView _tv_cancel= CLController.Get_TextView_With_Gravity(cc,CL.Get_LLLP(CL.MP, CL.WC, 0, CL.DIP2PX_INT(12),0, CL.DIP2PX_INT(12)),
                    Gravity.CENTER,"取消",0xff007aff,16,listener_cancel);
            _tv_cancel.setBackground(CL.Get_StateList_Drawable(_drawable1, _drawable2));
            _tv_cancel.setPadding(0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12));
            this.addView(_tv_cancel);
        }
        public void add_button(String title,OnClickListener listener){
            if(ll_btns.getChildCount()>0)
                ll_btns.addView(CLController.Get_TextView_Divider(cc, CL.Get_LP(CL.MP, 1), 0xffdadada));
            ll_btns.addView(get_button(cc, title, listener));
        }
        private Drawable get_bg(){
            RoundRectShape _shape=new RoundRectShape(new float[]{12,12,12,12,12,12,12,12}, null, null);
            ShapeDrawable _drawable=new ShapeDrawable(_shape);
            _drawable.getPaint().setColor(0xffffffff);
            _drawable.getPaint().setStyle(Paint.Style.FILL);
            int _pad=CL.DIP2PX_INT(6);
            _drawable.setPadding(0, _pad, 0, _pad);
            return _drawable;
        }
        private TextView get_button(Context cc,String name,final OnClickListener listener){
            TextView _btn= CLController.Get_Button(cc, CL.Get_LP(CL.MP, CL.WC), name, 0xff007aff,
                    16,CL.Get_StateList_Drawable(new ColorDrawable(0xffffffff), new ColorDrawable(0xffe0e0e0)),
                    listener);
            _btn.setPadding(0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12));
            _btn.setGravity(Gravity.CENTER);
            return _btn;
        }

    }
}
