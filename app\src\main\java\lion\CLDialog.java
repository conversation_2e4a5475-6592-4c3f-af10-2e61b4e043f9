package lion;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;


/**
 * Created by leron on 2016/4/26.
 */
public class CLDialog extends Dialog {

    private CLDialog(Context context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);

        fl_main=new FrameLayout(this.getContext()){
            @Override
            protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
                super.onLayout(changed, left, top, right, bottom);
                if(changed){
                    int _w=this.getWidth();
                    int _h=this.getHeight();
                    if(_w<_h)setPadding((int)(_w*0.1f),getPaddingTop(),(int)(_w*0.1f),0);
                    else setPadding((int)(_h*0.30f),getPaddingTop(),(int)(_h*0.30f),0);
                    setVisibility(View.GONE);
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            setVisibility(View.VISIBLE);
                        }
                    },100);
                }
            }
        };
        fl_main.setFocusable(true);
        fl_main.setClickable(true);
        fl_main.setFocusableInTouchMode(true);
        fl_main.requestFocus();
        fl_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //if(cancel_on_outside)dismiss();
                if (model) return;
                if(cancel_on_outside)dismiss();
            }
        });
    }
    
    private CLDialog(Context context, boolean model) {
        this(context);
        this.model = model;
    }
    private boolean model = false;
    private FrameLayout fl_main;
    private boolean cancel_on_outside=true;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.8f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        CL.Set_Translucent_StatusBar(this.getWindow());
        this.setContentView(fl_main);
    }

    @Override
    public void setCanceledOnTouchOutside(boolean cancel) {
        super.setCanceledOnTouchOutside(cancel);
        cancel_on_outside=cancel;
    }

    private LinearLayout get_common_bg(Context cc){
        LinearLayout _ll=new LinearLayout(cc);
        RoundRectShape _shape=new RoundRectShape(new float[]{8,8,8,8,8,8,8,8}, null, null);
        ShapeDrawable _dwe_bg=new ShapeDrawable(_shape);
        //_dwe_bg.getPaint().setColor(0xffa0a0a0);
        _dwe_bg.getPaint().setColor(0xffffffff);
        _dwe_bg.getPaint().setStyle(Paint.Style.FILL);
//        _dwe_bg.setPadding(16,16,16,16);
        _ll.setBackground(_dwe_bg);
        return _ll;
    }


    public static CLDialog Get_Force_Wait(Context cc){
        final CLDialog _dialog=new CLDialog(cc);
        _dialog.fl_main.setOnClickListener(null);
        _dialog.setCancelable(false);
        CLController.Waiter _v=new CLController.Waiter(cc, Color.GREEN);
        _v.setLayoutParams(CL.Get_FLLP(CL.DIP2PX_INT(52),CL.DIP2PX_INT(52),Gravity.CENTER));
        _dialog.fl_main.addView(_v);
        return _dialog;
    }


    public static DialogInterface.OnKeyListener keylistener = new DialogInterface.OnKeyListener(){
        public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
            if (keyCode==KeyEvent.KEYCODE_BACK)
            {
                //do something...
                return true;
            }
            else
            {
                return true;
            }
        }
    } ;
    public static CLDialog Get_Alert_Scroll_Dialog(Context context, String msg, long millisInFuture, long countDownInterval, View.OnClickListener listener){
        final CLDialog _dialog=new CLDialog(context, true);
        _dialog.setOnKeyListener(keylistener);
        ScrollView _sv=new ScrollView(context);
        _sv.setLayoutParams(CL.Get_LP_MM());

        LinearLayout _bg=_dialog.get_common_bg(context);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        _sv.addView(_bg);
        _dialog.fl_main.setBackgroundColor(0x22000000);
        _dialog.fl_main.addView(_sv,CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        TextView _tv_title=new TextView(context);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12)));
        _tv_title.setText(R.string.dialog_title_tip);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);

        TextView _tv_msg=new TextView(context);
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(2),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15)));
        _tv_msg.setText(msg);
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);

        LinearLayout _ll_btns=new LinearLayout(context);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,0,CL.DIP2PX_INT(12), CL.DIP2PX_INT(12)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);


        if (listener == null) {
            listener = new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    _dialog.dismiss();
                }
            };
        }
        CLController.DiscolourButton _btn_ok=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, 0, 0),
                context.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, listener);
    
        CountDownTime mTime = new CountDownTime(_btn_ok, context.getString(R.string.yes),millisInFuture, countDownInterval);//初始化对象
        mTime.start();
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);

        return _dialog;
    }
    
    static class CountDownTime extends CountDownTimer {
        
        //构造函数  第一个参数代表总的计时时长  第二个参数代表计时间隔  单位都是毫秒
        private View view;
        private String messenge;
        public CountDownTime(View view, String messenge, long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
            this.view = view;
            this.messenge = messenge;
        }
        
        @Override
        public void onTick(long l) { //每计时一次回调一次该方法
            view.setClickable(false);
            if (view instanceof TextView) {
                ((TextView)view).setText(l/1000+"");
            }
        }
        
        @Override
        public void onFinish() { //计时结束回调该方法
            view.setClickable(true);
            if (view instanceof TextView) {
                ((TextView)view).setText(messenge);
            }
        }
    }

    public static CLDialog Get_Alert_Dialog(Context context,String msg){
        final CLDialog _dialog=new CLDialog(context);
        LinearLayout _bg=_dialog.get_common_bg(context);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        _dialog.fl_main.addView(_bg,CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        TextView _tv_title=new TextView(context);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12)));
        _tv_title.setText(R.string.dialog_title_tip);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);

        TextView _tv_msg=new TextView(context);
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(2),CL.DIP2PX_INT(15),CL.DIP2PX_INT(15)));
        _tv_msg.setText(msg);
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);

        LinearLayout _ll_btns=new LinearLayout(context);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,0,CL.DIP2PX_INT(12), CL.DIP2PX_INT(12)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);

        CLController.DiscolourButton _btn_ok=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, 0, 0),
                context.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        _dialog.dismiss();
                    }
                });
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);

        return _dialog;
    }
    
    public static CLDialog Get_Confirm_Model_Dialog(Context context, String title, String msg, final CLCallback.CB_TF cber){
        final CLDialog _dialog=new CLDialog(context, true);
        LinearLayout _bg=_dialog.get_common_bg(context);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        _dialog.fl_main.setBackgroundColor(0x22000000);
        _dialog.fl_main.addView(_bg,CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        TextView _tv_title=new TextView(context);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12)));
        _tv_title.setText(title);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);
        
        TextView _tv_msg=new TextView(context);
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(2),CL.DIP2PX_INT(15),CL.DIP2PX_INT(18)));
        _tv_msg.setText(msg);
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);
        
        LinearLayout _ll_btns=new LinearLayout(context);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,0,CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);
        
        CLController.DiscolourButton _btn_cancel=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                context.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(cber!=null)cber.on_callback_fail(0,"cancel");
                        _dialog.dismiss();
                    }
                });
        _btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_cancel);
        CLController.DiscolourButton _btn_ok=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                context.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(cber!=null)cber.on_callback_success();
                        _dialog.dismiss();
                    }
                });
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);
        
        return _dialog;
    }

    public static CLDialog Get_Confirm_Dialog(Context context, String title, String msg, final CLCallback.CB_TF cber){
        final CLDialog _dialog=new CLDialog(context);
        LinearLayout _bg=_dialog.get_common_bg(context);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        _dialog.fl_main.addView(_bg,CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        TextView _tv_title=new TextView(context);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12)));
        _tv_title.setText(title);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);

        TextView _tv_msg=new TextView(context);
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(2),CL.DIP2PX_INT(15),CL.DIP2PX_INT(18)));
        _tv_msg.setText(msg);
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);

        LinearLayout _ll_btns=new LinearLayout(context);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,0,CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);

        CLController.DiscolourButton _btn_cancel=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                context.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(cber!=null)cber.on_callback_fail(0,"cancel");
                        _dialog.dismiss();
                    }
                });
        _btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_cancel);
        CLController.DiscolourButton _btn_ok=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                context.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(cber!=null)cber.on_callback_success();
                        _dialog.dismiss();
                    }
                });
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);

        return _dialog;
    }


    public static CLDialog Get_Confirm_Dialog(Context context, String msg, final CLCallback.CB_TF cber){
        final CLDialog _dialog=new CLDialog(context);
        LinearLayout _bg=_dialog.get_common_bg(context);
        _bg.setGravity(Gravity.CENTER_HORIZONTAL);
        _bg.setOrientation(LinearLayout.VERTICAL);
        _bg.setClickable(true);
        _dialog.fl_main.addView(_bg,CL.Get_FLLP(CL.MP,CL.WC, Gravity.CENTER));
        TextView _tv_title=new TextView(context);
        _tv_title.setLayoutParams(CL.Get_LLLP(CL.WC,CL.WC,0,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12)));
        _tv_title.setText(R.string.dialog_title_warning);
        _tv_title.setTextColor(0xff252525);
        _tv_title.setTextSize(18);
        _bg.addView(_tv_title);

        TextView _tv_msg=new TextView(context);
        _tv_msg.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(15),CL.DIP2PX_INT(2),CL.DIP2PX_INT(15),CL.DIP2PX_INT(18)));
        _tv_msg.setText(msg);
        _tv_msg.setTextColor(0xff337050);
        _tv_msg.setTextSize(16);
        _tv_msg.setMinLines(2);
        _bg.addView(_tv_msg);

        LinearLayout _ll_btns=new LinearLayout(context);
        _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,0,0,CL.DIP2PX_INT(6), CL.DIP2PX_INT(8)));
        _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
        _ll_btns.setGravity(Gravity.RIGHT);
        _bg.addView(_ll_btns);

        CLController.DiscolourButton _btn_cancel=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35), 0, 0, CL.DIP2PX_INT(6), 0),
                context.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(cber!=null)cber.on_callback_fail(0,"cancel");
                        _dialog.dismiss();
                    }
                });
        _btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_cancel);
        CLController.DiscolourButton _btn_ok=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.DIP2PX_INT(35)),
                context.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if(cber!=null)cber.on_callback_success();
                        _dialog.dismiss();
                    }
                });
        _btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        _btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
        _ll_btns.addView(_btn_ok);

        return _dialog;
    }

    public static CLDialog Get_Dialog(Context context,View v){
        final CLDialog _dialog=new CLDialog(context);
        _dialog.fl_main.addView(v,CL.Get_FLLP(CL.WC,CL.WC, Gravity.CENTER));
        return _dialog;
    }

    public static CLDialog Get_Dialog_Animation_From_Bottom(Context cc,final View view){
        final CLDialog _dialog=new CLDialog(cc);
        _dialog.fl_main=new FrameLayout(cc);
        _dialog.fl_main.setPadding(0, 0, 0, 0);
        _dialog.fl_main.setFocusable(true);
        _dialog.fl_main.setClickable(true);
        _dialog.fl_main.setFocusableInTouchMode(true);
        _dialog.fl_main.requestFocus();
        _dialog.fl_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(_dialog.cancel_on_outside)_dialog.dismiss();
            }
        });
        _dialog.fl_main.addView(view, CL.Get_FLLP(CL.MP, CL.WC, Gravity.BOTTOM));
        _dialog.getWindow().setWindowAnimations(R.style.pop_from_bottom);
        return _dialog;
    }
}
