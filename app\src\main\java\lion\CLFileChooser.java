package lion;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;

import lion.widget.CLFlipper;

/**
 * Created by leron on 2016/8/6.
 */
public class CLFileChooser extends Dialog{

    private Context cc;
    private int rs_anim,rs_folder,rs_folder_add_normal,rs_folder_add_click;
    private boolean show_folder;
    private String[] root_dir;
    private CLCallback.CBO<String> cber;

    public CLFileChooser(Context context, boolean show_folder, String[] dir, CLCallback.CBO<String> cber) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);
        this.cc=context;
        this.show_folder=show_folder;
        this.root_dir=dir;
        this.cber=cber;
    }
    public void set_resource(int rs_windown_anim, int rs_folder,int rs_folder_add_normal,int rs_folder_add_click){
        this.rs_anim=rs_windown_anim;
        this.rs_folder=rs_folder;
        this.rs_folder_add_normal=rs_folder_add_normal;
        this.rs_folder_add_click=rs_folder_add_click;
    }


    private MainBody ctm_view;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int _sh=CL.Set_Translucent_StatusBar(this.getWindow());
        if(rs_anim!=-1)this.getWindow().setWindowAnimations(rs_anim);

        ctm_view=new MainBody(cc);
        ctm_view.setPadding(0,_sh,0,0);
        this.setContentView(ctm_view);
    }

    private CLCallback.CB cber_exit=new CLCallback.CB() {
        @Override
        public void on_callback() {
            dismiss();
        }
    };

    private class MainBody extends LinearLayout{

        private TextView tv_path;
        private ImageView iv_add;
        private CLFlipper flipper;
        private CLController.DiscolourButton btn_ok,btn_cancel;

        private View.OnClickListener listener_click_add=new OnClickListener() {
            @Override
            public void onClick(View v) {
                CLInputer.Get_Single_Line(cc, new CLCallback.CB_TFO<String>() {
                    @Override
                    public boolean on_callback_success(String obj, String msg) {
                        if(obj==null)return false;
                        obj=obj.trim().replace("\n","");
                        File _ddd=new File(tv_path.getText().toString(),obj);
                        if(!_ddd.exists()){
                            if(!_ddd.mkdir()){
                                CLDialog.Get_Alert_Dialog(cc,cc.getResources().getString(R.string.tip_operation_fail)).show();
                                return false;
                            }
                            StructItem _item=new StructItem();
                            _item.file=_ddd;
                            _item.name=_ddd.getName();
                            Chooser _v=(Chooser)flipper.getChildAt(flipper.getChildCount()-1);
                            _v.adapter.datas.add(1,_item);
                            _v.smoothScrollToPosition(0);
                            _v.adapter.notifyDataSetChanged();
                        }else{
                            Chooser _v=(Chooser)flipper.getChildAt(flipper.getChildCount()-1);
                            for(int i=0;i<_v.adapter.datas.size();++i){
                                StructItem _tmp=_v.adapter.datas.get(i);
                                if(_tmp.file==null)continue;
                                if(_tmp.name.equals(obj)){
                                    _v.smoothScrollToPosition(i);
                                    return false;
                                }
                            }
                        }
                        return false;
                    }
                    @Override
                    public void on_callback_fail(int code, String msg) {

                    }
                }).set_btn_text(cc.getResources().getString(R.string.yes),cc.getResources().getString(R.string.cancel))
                    .show(cc.getResources().getString(R.string.new_folder),"",cc.getResources().getString(R.string.tip_input_folder_name));
            }
        };

        public MainBody(Context context) {
            super(context);
            this.setOrientation(LinearLayout.VERTICAL);
            this.setBackgroundColor(0xff333333);

            LinearLayout _ll_header=CLController.Get_LinearLayout(context,CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(45),0,0,0,0),
                    LinearLayout.HORIZONTAL, 0xff333333,null);
            _ll_header.setGravity(Gravity.CENTER_VERTICAL);
            this.addView(_ll_header);

            tv_path=CLController.Get_TextView(context,CL.Get_LLLP(CL.WC, CL.WC,1.0f,CL.DIP2PX_INT(12),0,CL.DIP2PX_INT(12),0),
                    "Please select",Color.WHITE,14,null);
            tv_path.setSingleLine();
            tv_path.setEllipsize(TextUtils.TruncateAt.START);
            _ll_header.addView(tv_path);
            iv_add=CLController.Get_ImageView(context,CL.Get_LP(CL.DIP2PX_INT(60),CL.MP),null,listener_click_add);
            iv_add.setImageDrawable(CL.Get_StateList_Drawable(cc,rs_folder_add_normal,rs_folder_add_click));
            iv_add.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            iv_add.setVisibility(View.GONE);
            _ll_header.addView(iv_add);

            flipper=new CLFlipper(context,cber_exit);
            flipper.setBackgroundColor(0xff2e2e2e);
            flipper.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,1.0f,0,0,0,0));
            this.addView(flipper);

            LinearLayout _ll_btns=new LinearLayout(context);
            _ll_btns.setLayoutParams(CL.Get_LLLP(CL.MP,CL.DIP2PX_INT(44)));
            _ll_btns.setOrientation(LinearLayout.HORIZONTAL);
            _ll_btns.setGravity(Gravity.CENTER_VERTICAL);
            this.addView(_ll_btns);

            btn_cancel=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.MP),
                    context.getString(R.string.cancel), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            cber_exit.on_callback();
                        }
                    });
            btn_cancel.set_touch_bg_color(0x00ffffff,0xff606060);
            btn_cancel.setMinimumWidth(CL.DIP2PX_INT(80));
            _ll_btns.addView(btn_cancel);
            btn_ok=CLController.Get_Discolour_Button(context, CL.Get_LLLP(CL.WC, CL.MP,1.0f),
                    context.getString(R.string.yes), 16, 0xff005880, 0xff0097dc, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            cber_exit.on_callback();
                            if(cber!=null)cber.on_callback(tv_path.getText().toString());
                        }
                    });
            btn_ok.set_touch_bg_color(0x00ffffff,0xff606060);
            btn_ok.setMinimumWidth(CL.DIP2PX_INT(80));
            _ll_btns.addView(btn_ok);


            flipper.addView(new MainChooser(cc,root_dir));
        }

        @Override
        protected void onLayout(boolean changed, int l, int t, int r, int b) {
            super.onLayout(changed, l, t, r, b);
            if(changed){
                int _w=this.getWidth()/2;
                LinearLayout.LayoutParams _lp=(LinearLayout.LayoutParams) btn_cancel.getLayoutParams();
                _lp.width=_w;
                btn_cancel.setLayoutParams(_lp);
            }
        }
    }


    private class StructItem{
        public File file;
        public String name;
    }
    private class AdapterForChooser extends BaseAdapter{

        private String dir;
        private ArrayList<StructItem> datas;
        public AdapterForChooser(String dir){
            this.dir=dir;
            datas=new ArrayList<>();
            datas.add(new StructItem());
            File[] _fs=new File(dir).listFiles();
            if(_fs!=null){
                for(int i=0;i<_fs.length;++i){
                    if(show_folder && _fs[i].isFile())continue;
                    StructItem _item=new StructItem();
                    _item.file=_fs[i];
                    _item.name=_fs[i].getName();
                    datas.add(_item);
                }

                Collections.sort(datas, new Comparator<StructItem>() {
                    @Override
                    public int compare(StructItem obj1, StructItem obj2) {
                        if (obj1.file == null) {
                            return -1;
                        }
                        if (obj2.file == null) {
                            return 1;
                        }

                        if( obj1.file.isDirectory() && obj2.file.isDirectory()) {
                            return obj1.file.getName().compareTo(obj2.file.getName());
                        }
                        return 1;
                    }
                });
            }
        }
        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new ListItem(cc);
            ((ListItem)cv).set_data(datas.get(position));
            return cv;
        }
    }
    private class ListItem extends LinearLayout{

        private StructItem data;
        private ImageView iv_icon;
        private TextView tv_name;

        public ListItem(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);

            iv_icon=CLController.Get_ImageView(context,
                    CL.Get_LLLP(CL.DIP2PX_INT(32),CL.DIP2PX_INT(32),CL.DIP2PX_INT(12),CL.DIP2PX_INT(8),CL.DIP2PX_INT(12),CL.DIP2PX_INT(8)),null,null);
            iv_icon.setImageDrawable(context.getResources().getDrawable(rs_folder));
            iv_icon.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            this.addView(iv_icon);

            tv_name=CLController.Get_TextView(context,CL.Get_LLLP(CL.WC,CL.WC,0,0,CL.DIP2PX_INT(12),0),"",0xffe2e2e2,16,null);
            tv_name.setSingleLine();
            tv_name.setEllipsize(TextUtils.TruncateAt.MIDDLE);
            this.addView(tv_name);
        }

        public void set_data(StructItem f){
            this.data=f;
            if(this.data.file==null)tv_name.setText("..");
            else tv_name.setText(this.data.name);
        }
    }

    private class Chooser extends ListView implements CLFlipper.EventListener{

        private AbsListView.OnItemClickListener listener_click=new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                StructItem _d=adapter.datas.get(position);
                if(_d.file==null)on_back();
                else {
                    ctm_view.tv_path.setText(_d.file.getAbsolutePath());
                    ctm_view.flipper.go_next(new Chooser(cc,_d.file.getAbsolutePath()));
                }
            }
        };

        private AdapterForChooser adapter;

        public Chooser(Context context,String dir) {
            super(context);

            this.setOnItemClickListener(listener_click);
            this.setCacheColorHint(Color.TRANSPARENT);
            this.setDivider(new ColorDrawable(0xff363636));
            this.setDividerHeight(1);
            StateListDrawable _drawable=new StateListDrawable();
            _drawable.addState(new int[]{-android.R.attr.state_pressed,android.R.attr.state_selected,-android.R.attr.state_selected},new ColorDrawable(0x00000000));
            _drawable.addState(new int[]{android.R.attr.state_pressed},new ColorDrawable(0x993d6f59));
            this.setSelector(_drawable);
            this.setOverScrollMode(View.OVER_SCROLL_NEVER);
            adapter=new AdapterForChooser(dir);
            this.setAdapter(adapter);

        }

        @Override
        public void on_hide_over() {

        }

        @Override
        public void on_resume_begin() {

        }

        @Override
        public void on_resume_end() {
            ctm_view.tv_path.setText(adapter.dir);
        }

        @Override
        public void on_back() {
            ctm_view.flipper.go_previously(this);
        }
    }


    private class MainChooser extends ListView implements CLFlipper.EventListener{

        private AbsListView.OnItemClickListener listener_click_main=new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                ctm_view.tv_path.setText(adapter_main.datas[position].getAbsolutePath());
                ctm_view.flipper.go_next(new Chooser(cc,adapter_main.datas[position].getAbsolutePath()));
            }
        };

        private AdapterForMainChooser adapter_main;

        public MainChooser(Context context,String[] dir) {
            super(context);

            this.setOnItemClickListener(listener_click_main);
            this.setCacheColorHint(Color.TRANSPARENT);
            this.setDivider(new ColorDrawable(0xff363636));
            this.setDividerHeight(1);
            StateListDrawable _drawable=new StateListDrawable();
            _drawable.addState(new int[]{-android.R.attr.state_pressed,android.R.attr.state_selected,-android.R.attr.state_selected},new ColorDrawable(0x00000000));
            _drawable.addState(new int[]{android.R.attr.state_pressed},new ColorDrawable(0x993d6f59));
            this.setSelector(_drawable);
            this.setOverScrollMode(View.OVER_SCROLL_NEVER);
            adapter_main=new AdapterForMainChooser(dir);
            this.setAdapter(adapter_main);
        }

        @Override
        public void on_hide_over() {
            ctm_view.iv_add.setVisibility(View.VISIBLE);
        }

        @Override
        public void on_resume_begin() {

        }

        @Override
        public void on_resume_end() {
            ctm_view.iv_add.setVisibility(View.GONE);
            ctm_view.tv_path.setText("Please select");
        }

        @Override
        public void on_back() {

        }
    }

    private class AdapterForMainChooser extends BaseAdapter{

        private File[] datas;
        public AdapterForMainChooser(String[] dir){
            datas=new File[dir.length];
            for(int i=0;i<dir.length;++i){
                datas[i]=new File(dir[i]);
                CL.CLOGI("main dir:"+datas[i].getAbsolutePath());
            }
        }
        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.length;
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View cv, ViewGroup parent) {
            if(cv==null)cv=new ListMainItem(cc);
            ((ListMainItem)cv).set_data(datas[position]);
            return cv;
        }
    }
    private class ListMainItem extends LinearLayout{

        private File data;
        private ImageView iv_icon;
        private TextView tv_name;

        public ListMainItem(Context context) {
            super(context);
            this.setOrientation(LinearLayout.HORIZONTAL);
            this.setGravity(Gravity.CENTER_VERTICAL);

            iv_icon=CLController.Get_ImageView(context,
                    CL.Get_LLLP(CL.DIP2PX_INT(32),CL.DIP2PX_INT(32),CL.DIP2PX_INT(12),CL.DIP2PX_INT(8),CL.DIP2PX_INT(12),CL.DIP2PX_INT(8)),null,null);
            iv_icon.setImageDrawable(context.getResources().getDrawable(rs_folder));
            iv_icon.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            this.addView(iv_icon);

            tv_name=CLController.Get_TextView(context,CL.Get_LLLP(CL.WC,CL.WC,1.0f,0,CL.DIP2PX_INT(8),CL.DIP2PX_INT(12),CL.DIP2PX_INT(8)),"",0xffe2e2e2,16,null);
            this.addView(tv_name);
        }

        public void set_data(File f){
            this.data=f;
            tv_name.setText(this.data.getAbsolutePath());
        }
    }
}
