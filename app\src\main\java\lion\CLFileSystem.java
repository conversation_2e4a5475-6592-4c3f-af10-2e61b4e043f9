package lion;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.os.Environment;
import android.os.storage.StorageManager;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by leron on 2015/7/29.
 */
public class CLFileSystem {

    public static boolean Whether_Availability(){
        if(Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED))return true;
        else return false;
    }
    
    public static String getSDCardBaseDir(){
        if(!Whether_Availability())return null;
        return Environment.getExternalStorageDirectory().getAbsolutePath();
    }
    
    @TargetApi(Build.VERSION_CODES.KITKAT)
    public static File[] Get_App_Store(Context cc){
        if(!Whether_Availability())return null;
        if(CL.SDK<19){
            File[] _ds=null;
            ArrayList<File> _dirs=Get_System_Store(cc);
            if(_dirs!=null && _dirs.size()>0){
                _ds=new File[_dirs.size()];
                _dirs.toArray(_ds);
            }
            return _ds;
        }
        else{
            File[] _dirs=cc.getExternalFilesDirs(null);
            return _dirs;
        }
    }

    public static ArrayList<File> Get_System_Store(Context cc){
        String[] paths=null;
        try {
            StorageManager sm = (StorageManager) cc.getSystemService(Context.STORAGE_SERVICE);
            Class<?>[] paramClasses = {};
            Method getVolumePathsMethod = StorageManager.class.getMethod("getVolumePaths", paramClasses);
            getVolumePathsMethod.setAccessible(true);
            Object[] params = {};
            Object invoke = getVolumePathsMethod.invoke(sm, params);
            paths = (String[])invoke;
        }catch(Exception ex){}
        if(paths==null || paths.length==0){
            paths=new String[1];
            paths[0]=Environment.getExternalStorageDirectory().getAbsolutePath();
        }

        ArrayList<File> _dirs=new ArrayList<>();
        for(int i=0;i<paths.length;++i){
            File _dir=new File(paths[i]);
            if(_dir.exists() && _dir.getTotalSpace()>0)_dirs.add(_dir);
        }
        if(_dirs.size()==0)return null;
        return _dirs;
    }

    public static long[] Get_Used_Totalspace(Context cc){
        long[] _v=new long[2];
        ArrayList<File> _dirs=Get_System_Store(cc);
        for(int i=0;i<_dirs.size();++i){
            File _dir=_dirs.get(i);
            _v[0]+=_dir.getUsableSpace();
            _v[1]+=_dir.getTotalSpace();
        }
        _v[0]=_v[1]-_v[0];
        return _v;
    }

    public static boolean Delete_Dir(File dir){
        if(dir.isDirectory()){
            File[] _subs=dir.listFiles();
            for(int i=0;_subs!=null && i<_subs.length;++i){
                File _f=_subs[i];
                if(_f.isDirectory())Delete_Dir(_f);
                else _f.delete();
            }
            dir.delete();
            return true;
        }
        return false;
    }
    public static void Clear_Dir_Files(File[] files){
        for(int i=0;files!=null && i<files.length;++i){
            File _f=files[i];
            if(_f.isDirectory()){
                Clear_Dir_Files(_f.listFiles());
            }
            _f.delete();
        }
    }
    public static boolean Copy_File(InputStream is,File target){
        try{
            FileOutputStream _fos=new FileOutputStream(target);
            byte[] _buff=new byte[8192];
            int _cc=-1;
            while((_cc=is.read(_buff))!=-1){
                _fos.write(_buff, 0, _cc);
            }
            _fos.flush();
            _fos.close();
            return true;
        }catch (Exception ex){
            return false;
        }
    }
    public static void Copy_File_With_Process(File o_file, File target, CLCallback.CBO<Float> cber){
        try{
            if(target==null)return;
            int _index=1;
            if(target.exists()){
                File _target=new File(target.getAbsolutePath());
                File _dir = target.getParentFile();
                String _name = target.getName();
                while (_target.exists()) {
                    if (_name.contains(".")) {
                        String _one=_name.substring(0,_name.lastIndexOf("."));
                        String _two=_name.substring(_name.lastIndexOf("."));
                        _target=new File(_dir,_one+_index+_two);
                    } else {
                        _target=new File(_dir,_name+_index);
                    }
                    ++_index;
                }
                target=_target;
            }
            FileOutputStream _fos=new FileOutputStream(target);
            FileInputStream _fis=new FileInputStream(o_file);
            long _total=o_file.length();
            long _count=0;
            byte[] _buff=new byte[8192];
            int _cc=-1;
            while((_cc=_fis.read(_buff))!=-1){
                _fos.write(_buff, 0, _cc);
                _count+=_cc;
                if(cber!=null)cber.on_callback((float)_count/_total);
            }
            _fos.flush();
            _fos.close();
        }catch (Exception ex){}
    }
    public static void Print_Dir_Files(String Dir){
        if(Dir!=null){
            File _dir=new File(Dir);
            if(_dir.isFile())return;
            PrintDirFiles(_dir,0);
        }
    }
    private static void PrintDirFiles(File dir,int layer){
        if(dir.exists()){
            StringBuffer _sb=new StringBuffer();
            for(int i=0;i<layer;++i){
                _sb.append("---");
            }
            CL.CLOGI(_sb.toString() + "Dir:" + dir.getAbsolutePath());
            layer+=1;
            File[] _subs=dir.listFiles();
            _sb=new StringBuffer();
            for(int i=0;i<layer;++i){
                _sb.append("---");
            }
            for(File _f:_subs){
                if(_f.isFile()){
                    CL.CLOGI(_sb.toString() + "F:" + _f.getName());
                }
                else if(_f.isDirectory()){
                    PrintDirFiles(_f,layer);
                }
            }
        }
    }
    public static long Get_Dir_Category(File dir){
        long _size=0;
        if(dir.exists()) {
            File[] _subs=dir.listFiles();
            for(int i=0;i<_subs.length;++i){
                File _file=_subs[i];
                if(_file.isDirectory()){
                    _size+=Get_Dir_Category(_file);
                }
                else{
                    _size+=_file.length();
                }
            }
        }
        return _size;
    }

    public static void writeLineFile(List<String> list, String name) throws IOException {
        FileWriter fw=new FileWriter(new File(name));
        //写入中文字符时会出现乱码
        BufferedWriter bw = new BufferedWriter(fw);
        //BufferedWriter  bw=new BufferedWriter(new BufferedWriter(new OutputStreamWriter(new FileOutputStream(new File("E:/phsftp/evdokey/evdokey_201103221556.txt")), "UTF-8")));
        int size = list.size();
        int i = 0;
        for (String arr: list) {
            if (i == size - 1) {
                bw.write("file " + "\'" + arr + "\'");
            } else {
                bw.write("file " + "\'" + arr + "\'" + "\t\n");
            }
            i++;
        }

        bw.close();
        fw.close();
    }

    public static boolean isVideo(String url) {
        return url.endsWith(".mpeg") || url.endsWith(".mpg") || url.endsWith(".avi") || url.endsWith(".mov")
                || url.endsWith(".mpg4") || url.endsWith(".mp4") || url.endsWith(".flv") || url.endsWith(".wmv");
    }

    public static boolean isAudio(String url) {
        return url.endsWith(".mp3") || url.endsWith(".ogg") || url.endsWith(".m3u") || url.endsWith(".wav");
    }

    public static boolean isDoc(String url) {
        return url.endsWith(".pdf") || url.endsWith(".ppt") || url.endsWith(".doc")
                || url.endsWith(".swf") || url.endsWith(".rtf") || url.endsWith(".xls");
    }

    public static boolean isPackage(String url) {
        return url.endsWith(".gz") || url.endsWith(".tgz") || url.endsWith(".zip")
                || url.endsWith(".rar") || url.endsWith(".deb") || url.endsWith(".rpm") || url.endsWith(".7z");
    }

    public static boolean isApp(String url) {
        return url.endsWith(".exe") || url.endsWith(".bin") || url.endsWith(".bat") || url.endsWith(".dmg");
    }

    public static boolean isImage(String url) {
        return url.endsWith(".png") || url.endsWith(".jpeg") || url.endsWith(".gif")
                || url.endsWith(".jpg") || url.endsWith(".bmp") || url.endsWith(".ico") || url.endsWith(".eps");
    }

    public static boolean isContainApp(String url) {
        return url.contains(".apk");
    }
}
