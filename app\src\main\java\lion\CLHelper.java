package lion;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;

/**
 * Created by leron on 2016/8/11.
 */
public class CLHelper extends Dialog{

    private CLHelper(Context context) {
        super(context, android.R.style.Theme_Translucent_NoTitleBar);

        fl_main=new FrameLayout(context);
        fl_main.setFocusable(true);
        fl_main.setClickable(true);
        fl_main.setFocusableInTouchMode(true);
        fl_main.requestFocus();
        fl_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }


    private FrameLayout fl_main;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.3f;
        this.getWindow().setAttributes(lp);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        CL.Set_Translucent_StatusBar(this.getWindow());
        this.setContentView(fl_main);
    }

    public static CLHelper Get_Helper(View v, FrameLayout.LayoutParams lp, final CLCallback.CB cber){
        final CLHelper _dialog=new CLHelper(v.getContext());
        _dialog.fl_main.addView(v,lp);
        _dialog.setCanceledOnTouchOutside(true);
        _dialog.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                cber.on_callback();
            }
        });
        return _dialog;
    }

}
