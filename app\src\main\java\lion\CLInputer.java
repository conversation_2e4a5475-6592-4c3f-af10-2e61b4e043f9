package lion;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputFilter;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import amazon.browser.video.downloader.R;


/**
 * Created by leron on 2015/9/18.
 */
public class CLInputer extends Dialog {

    private Context cc;
    private LinearLayout ll_main;
    private TextView tv_title;
    private CLController.DiscolourButton btn_cancel,btn_ok;
    private EditText et;

    private CLCallback.CB_TFO<String> cber;

    private CLInputer(Context cc){
        super(cc, android.R.style.Theme_Translucent_NoTitleBar);
        this.cc=cc;
        ll_main=new LinearLayout(cc);
        ll_main.setOrientation(LinearLayout.VERTICAL);
        ll_main.setClickable(true);
        ll_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        ScrollView _sv=new ScrollView(cc);
        _sv.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC, 1.0f));
        ll_main.addView(_sv);

        LinearLayout _ll=new LinearLayout(cc);
        _ll.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC));
        _ll.setOrientation(LinearLayout.VERTICAL);
        _ll.setBackgroundColor(0xfff8f8f8);
        ll_main.addView(_ll);
        _ll.addView(CLController.Get_TextView_Divider(cc, CL.Get_LLLP(CL.MP, 1), 0xffe2e2e2));

        LinearLayout _ll_two=new LinearLayout(cc);
        _ll_two.setLayoutParams(CL.Get_LLLP(CL.MP, CL.WC));
        _ll_two.setOrientation(LinearLayout.HORIZONTAL);
        _ll_two.setGravity(Gravity.CENTER_VERTICAL);
        _ll_two.setClickable(true);
        _ll.addView(_ll_two);

        btn_cancel=CLController.Get_Discolour_Button(cc, CL.Get_LP(CL.WC, CL.DIP2PX_INT(40)), "Cancel", 15, 0xff007aff, 0xff1058a7,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                        if(cber !=null) cber.on_callback_fail(-1,null);
                    }
                }
        );
        btn_cancel.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        btn_cancel.setGravity(Gravity.CENTER);
        btn_cancel.setPadding(CL.DIP2PX_INT(18),0,CL.DIP2PX_INT(26),0);
        _ll_two.addView(btn_cancel);

        tv_title=new TextView(cc);
        tv_title.setLayoutParams(CL.Get_LLLP(CL.WC, CL.WC, 1.0f));
        tv_title.setTextSize(16);
        tv_title.setTextColor(0xff555555);
        tv_title.setGravity(Gravity.CENTER);
        _ll_two.addView(tv_title);

        btn_ok=CLController.Get_Discolour_Button(cc, CL.Get_LP(CL.WC, CL.DIP2PX_INT(40)), "OK", 15, 0xff007aff, 0xff1058a7,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dismiss();
                        if(cber !=null){
                            cber.on_callback_success(et.getText().toString(), "");
                        }
                    }
                }
        );
        btn_ok.set_touch_bg_color(0x00ffffff,0xffd2d2d2);
        btn_ok.setGravity(Gravity.CENTER);
        btn_ok.setPadding(CL.DIP2PX_INT(26), 0, CL.DIP2PX_INT(18), 0);
        _ll_two.addView(btn_ok);

        et=new EditText(cc);
        et.setLayoutParams(CL.Get_LLLP(CL.MP,CL.WC,CL.DIP2PX_INT(18),CL.DIP2PX_INT(2),CL.DIP2PX_INT(18),CL.DIP2PX_INT(12)));
        et.setPadding(CL.DIP2PX_INT(3),CL.DIP2PX_INT(5),CL.DIP2PX_INT(3),CL.DIP2PX_INT(5));
        et.setBackgroundColor(Color.WHITE);
        et.setTextSize(14);
        et.setTextColor(0xff000000);
        et.setGravity(Gravity.LEFT | Gravity.TOP);
        _ll.addView(et);

        this.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if(cber !=null) cber.on_callback_fail(0,et.getText().toString());
            }
        });
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WindowManager.LayoutParams lp = this.getWindow().getAttributes();
        lp.dimAmount = 0.6f;
        this.getWindow().setAttributes(lp);
        this.getWindow().setWindowAnimations(R.style.pop_from_bottom);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        this.setContentView(ll_main);
    }

    @Override
    public void show() {
        et.setText("");
        super.show();
    }
    public CLInputer set_btn_text(String ok,String cancel){
        if(ok!=null)btn_ok.setText(ok);
        if(cancel!=null)btn_cancel.setText(cancel);
        return this;
    }
    public void show(String title,String content){
        tv_title.setText(title);
        et.setText(content);
        et.setSelection(et.getText().length());
        super.show();
    }
    public void show(String title,String content,String hint){
        tv_title.setText(title);
        et.setText(content);
        et.setSelection(et.getText().length());
        et.setHint(hint);
        super.show();
    }

    public static CLInputer Get_Single_Line(Context context, CLCallback.CB_TFO<String> cber){
        CLInputer _inputer=new CLInputer(context);
        _inputer.et.setMinLines(2);
        _inputer.cber =cber;
        return _inputer;
    }

    public static CLInputer Get_Digital_Line(Context context, int max,CLCallback.CB_TFO<String> cber){
        CLInputer _inputer=new CLInputer(context);
        _inputer.et.setInputType(EditorInfo.TYPE_CLASS_NUMBER);
        _inputer.et.setSingleLine();
        _inputer.et.setFilters(new InputFilter[]{new InputFilter.LengthFilter(max)});
        _inputer.cber =cber;
        return _inputer;
    }

    public static CLInputer Get_Multi_Line(Context context,int line,CLCallback.CB_TFO<String> cber){
        CLInputer _inputer=new CLInputer(context);
        _inputer.cber =cber;
        if(line>1)_inputer.et.setMinLines(line);
        return _inputer;
    }

}
