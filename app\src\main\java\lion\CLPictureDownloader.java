 package lion;

 import android.graphics.BitmapFactory;

 import java.io.File;
 import java.io.FileOutputStream;
 import java.io.InputStream;
 import java.net.HttpURLConnection;
 import java.net.URL;
 import java.util.ArrayList;

 public class CLPictureDownloader {

     public interface DownloadEventer{
         void on_load_ratio(String file_name, int ratio, Object tag);
         void on_load_complete(String file_name, Object tag);
         void on_load_fail(String file_name, Object tag);
     }

     private class Item{
         public boolean enable_ratio;
         public String url;
         public String store_dir;
         public String store_name;
         public DownloadEventer listener;
         public Object tag;
         public String[] header;

         private int download_file_size;
         private int download_file_point;

         private Item(String url, String store_dir, String store_name,
					  DownloadEventer listener, boolean enable_ratio, Object tag){
             this.enable_ratio=enable_ratio;
             this.url=url;
             this.store_dir=store_dir;
             this.store_name=store_name;
             this.listener=listener;
             this.tag=tag;
         }
         private Item(String url, String store_dir, String store_name,
					  DownloadEventer listener, boolean enable_ratio, String[] header, Object tag){
             this.enable_ratio=enable_ratio;
             this.url=url;
             this.store_dir=store_dir;
             this.store_name=store_name;
             this.listener=listener;
             this.tag=tag;
             this.header=header;
         }
     }
     private final String SIG="signl";
     private boolean RUN=true;
     private Downloader downloader=new Downloader();
     private Loader loader=new Loader();
     private ArrayList<Item> data_load=new ArrayList<Item>();
     private ArrayList<Item> data_download=new ArrayList<Item>();

     public CLPictureDownloader(){
         downloader.start();
         loader.start();
     }

     public void add_item(String url, String store_dir, String store_name,
						  DownloadEventer listener, Object tag){
         if(store_dir==null || store_name==null || tag==null)return;
         add_item(url, store_dir, store_name, listener, false, null, tag);
     }
     public void add_item(String url, String store_dir, String store_name,
						  DownloadEventer listener, String[] header, Object tag){
         if(store_dir==null || store_name==null || tag==null)return;
         add_item(url, store_dir, store_name, listener, false,header,tag);
     }
     public void add_item(String url, String store_dir, String store_name,
						  DownloadEventer listener, boolean enable_ratio, String[] header, Object tag){
         synchronized (SIG) {
             for(int i=0;i<2;++i){
                 Item _tmp=(i==0?loader.item:downloader.item);
                 if(_tmp==null)break;
                 if(store_dir.equals(_tmp.store_dir)
                         && store_name.equals(_tmp.store_name)
                         && listener==_tmp.listener
                         && tag==_tmp.tag){
                     return;
                 }
             }
             for(int i=0;i<data_load.size();++i){
                 Item _tmp=data_load.get(i);
                 if(store_dir.equals(_tmp.store_dir)
                         && store_name.equals(_tmp.store_name)
                         && listener==_tmp.listener
                         && tag==_tmp.tag){
                     return;
                 }
             }
             for(int i=0;i<data_download.size();++i){
                 Item _tmp=data_download.get(i);
                 if(store_dir.equals(_tmp.store_dir)
                         && store_name.equals(_tmp.store_name)
                         && listener==_tmp.listener
                         && tag==_tmp.tag){
                     return;
                 }
             }
             data_load.add(new Item(url,store_dir,store_name,listener,enable_ratio,header,tag));
             SIG.notifyAll();
         }
     }

     public void clear_task(){
         synchronized (SIG) {
             data_load.clear();
             data_download.clear();
         }
     }

     public void go_exit(){
         RUN=false;
         synchronized (SIG) {
             SIG.notifyAll();
         }
     }

     private class Loader extends Thread {
         private Item item;

         public void run(){
             while(RUN){
                 try{
                     synchronized (SIG) {
                         if(data_load.size()==0){
                             SIG.wait();
                             continue;
                         }
                         else{
                             item=data_load.remove(0);
                         }
                     }
                     //下载
                     File _file_target=new File(item.store_dir,item.store_name);
                     if(!_file_target.exists()){
                         if(item.url==null)continue;
                         //不存在,添加到下载列表
                         synchronized (SIG) {
                             data_download.add(item);
                             SIG.notifyAll();
                         }
                         item=null;
                         continue;
                     }
                     //验证
                     BitmapFactory.Options _option=new BitmapFactory.Options();
                     _option.inJustDecodeBounds=true;
                     BitmapFactory.decodeFile(_file_target.getAbsolutePath(), _option);
                     if(_option.mCancel || _option.outWidth==-1 || _option.outHeight==-1){
                         //图片不合法,删除
                         if(_file_target.exists())_file_target.delete();
                         //图片不合法,重新下载,添加到下载列表
                         synchronized (SIG) {
                             data_download.add(item);
                             SIG.notifyAll();
                         }
                         item=null;
                         continue;
                     }
                     //通知
                     synchronized (SIG) {
                         if(item.listener!=null)item.listener.on_load_complete(item.store_name,item.tag);
                     }
                     item=null;
                 }catch(Exception ex){
                     CL.CLOGE("loader worker error:" + ex.toString(),ex);
                 }
             }
             CL.CLOGI("loader thread was over!");
         }
     }

     private class Downloader extends Thread {
         private Item item;

         public void run(){
             while(RUN){
                 try{
                     synchronized (SIG) {
                         if(data_download.size()==0){
                             SIG.wait();
                             continue;
                         }
                         else{
                             item=data_download.remove(0);
                         }
                     }
                     //下载
                     File _file_target=new File(item.store_dir,item.store_name);
                     File _file_tmp=new File(item.store_dir,"###temp"+ System.currentTimeMillis());
                     if(!_file_target.exists()){
                         String _url=item.url;
                         if(_url.contains(" "))_url=_url.replace(" ", "%20");

                         HttpURLConnection _conn = (HttpURLConnection) new URL(_url).openConnection();
                         _conn.setConnectTimeout(5000);
                         _conn.setReadTimeout(5000);
                         _conn.setDoInput(true);
                         _conn.setRequestMethod("GET");
                         if(item.header!=null){
                             for(int i=0;i<item.header.length/2;++i){
                                 _conn.setRequestProperty(item.header[i*2],item.header[i*2+1]);
                             }
                         }
                         _conn.connect();


                         item.download_file_size=_conn.getContentLength();
                         if(item.download_file_size<0)item.download_file_size=0;

                         InputStream _is=_conn.getInputStream();
                         FileOutputStream _fos=new FileOutputStream(_file_tmp);
                         byte[] _buff=new byte[8192];
                         item.download_file_point=0;
                         int _cc=-1;
                         while(RUN && (_cc=_is.read(_buff))!=-1){
                             _fos.write(_buff, 0, _cc);
                             item.download_file_point+=_cc;
                         }
                         _is.close();
                         _fos.close();
                         if(!RUN){
                             if(_file_tmp.exists())_file_tmp.delete();
                             break;
                         }
                         //换名
                         if(!_file_tmp.renameTo(_file_target)){
                             if(_file_tmp.exists())_file_tmp.delete();
                             if(_file_target.exists())_file_target.delete();
                         }
                     }
                     //验证
                     BitmapFactory.Options _option=new BitmapFactory.Options();
                     _option.inJustDecodeBounds=true;
                     BitmapFactory.decodeFile(_file_target.getAbsolutePath(), _option);
                     if(_option.mCancel || _option.outWidth==-1 || _option.outHeight==-1){
                         //图片不合法,删除
                         if(_file_target.exists())_file_target.delete();
                         synchronized (SIG) {
                             if(item.listener!=null)item.listener.on_load_fail(item.store_name,item.tag);
                         }
                     }
                     //通知
                     synchronized (SIG) {
                         if(item.listener!=null)item.listener.on_load_complete(item.store_name,item.tag);
                     }
                     item=null;
                 }catch(Exception ex){
//                     CL.CLOGE("downloader worker error:" + ex.toString(),ex);
                 }
             }
             CL.CLOGI("downloader thread was over!");
         }
     }
 }
