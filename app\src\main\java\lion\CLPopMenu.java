package lion;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.PopupWindow;

/**
 * Created by leron on 2016/7/22.
 */
public class CLPopMenu {


    private PopupWindow poper;
    private BGView bg;
//    private BGDwe dwe_bg;

    public CLPopMenu(View layout, int w, int h){
//        bg=new BGView(layout.getContext());
        poper = new PopupWindow(layout, w, h);
//        dwe_bg=new BGDwe();
//        poper.setBackground(dwe_bg);
//        bg.addView(layout,CL.Get_FLLP(CL.MP, CL.MP,Gravity.FILL));
        poper.setOutsideTouchable(true);
//        poper.setFocusable(true);


    }
    public void set_listener_dismiss(PopupWindow.OnDismissListener listener){
        if(poper!=null && listener!=null){
            poper.setOnDismissListener(listener);
        }
    }
    public void show(View v,int position,int offset_x,int offset_y){
//        poper.update();
//        poper.showAtLocation(v, Gravity.BOTTOM, 0, 0);
        poper.showAsDropDown(v,0,0);
    }


    public void close_menu(){
        poper.dismiss();
    }



    private class BGView extends FrameLayout{

        public BGView(Context context) {
            super(context);
        }

        @Override
        protected void onAttachedToWindow() {
            super.onAttachedToWindow();
            CL.CLOGI("popmenu attach to window");
        }

        @Override
        protected void onDetachedFromWindow() {
            super.onDetachedFromWindow();
            CL.CLOGI("popmenu detach from window");
        }

        @Override
        public void draw(Canvas canvas) {
            canvas.drawColor(Color.BLACK);
        }
    }


//    private class BGDwe extends Drawable{
//
//        private Paint p;
//        private RectF rtf=new RectF();
//        private int position;
//
//        public BGDwe() {
//            p=new Paint(Paint.ANTI_ALIAS_FLAG);
//        }
//
//        @Override
//        public void draw(Canvas canvas) {
//            Rect rect=this.getBounds();
//
//            p.setColor(0xff5190d7);
//            p.setStyle(Paint.Style.FILL);
//            float _v2=CL.DIP2PX(6);
//            float _top=CL.DIP2PX(8);
//            rtf.set(rect.left+4,rect.top+_top+4,rect.right-4,rect.bottom-4);
//            canvas.drawRoundRect(rtf,_v2, _v2, p);
//            float _mid=rect.left+rect.width()- CL.DIP2PX_INT(20);
//            Path _path=new Path();
//            _path.moveTo(_mid, 0);
//            _path.lineTo(_mid-CL.DIP2PX(8), _top+5);
//            _path.lineTo(_mid+CL.DIP2PX(8), _top+5);
//            p.setColor(0xff5190d7);
//            canvas.drawPath(_path, p);
//
//            p.setColor(0xfff8f8f8);
//            p.setStyle(Paint.Style.FILL);
//            canvas.drawRect(view.getPaddingLeft(), view.getPaddingTop(), rect.right-view.getPaddingRight(), rect.bottom-view.getPaddingBottom(), p);
//        }
//
//        @Override
//        public void setAlpha(int alpha) {
//
//        }
//
//        @Override
//        public void setColorFilter(ColorFilter colorFilter) {
//
//        }
//
//        @Override
//        public int getOpacity() {
//            return 0;
//        }
//    }


}
