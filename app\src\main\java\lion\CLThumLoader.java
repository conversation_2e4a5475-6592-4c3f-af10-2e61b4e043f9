package lion;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;

/**
 * Created by leron on 2016/3/18.
 */
public class CLThumLoader {

    public interface LoaderListener{
        void on_load_complete(String opath, String tpath, Bitmap bm, Object tag);
        void on_load_fail(String opath, Object tag);
    }

    private static int grid_width=CL.DIP2PX_INT(120),grid_height=CL.DIP2PX_INT(100);
    public CLThumLoader(){
        worker=new ThumLoader();
        run=true;
        worker.start();
    }
    public CLThumLoader(int w,int h){
        grid_width=w;
        grid_height=h;
        if(grid_width<80)grid_width=80;
        if(grid_height<80)grid_height=80;
        worker=new ThumLoader();
        run=true;
        worker.start();
    }

    public void go_add_item(String original_path,String store_dir,Object tag,LoaderListener cber){
        if(original_path==null || store_dir==null || tag==null || cber==null)return;
        File _store_dir=new File(store_dir);
        if(!_store_dir.exists() || !_store_dir.isDirectory())return;
        synchronized (CLThumLoader.this){
            if(crt_data!=null){
                if(crt_data.original_path.equals(original_path)
                        && crt_data.store_dir_path.equals(store_dir)
                        && crt_data.listener==cber
                        && crt_data.tag==tag){
                    return;
                }
            }
            for(int i=0;i<datas.size();++i){
                StructThum _tmp=datas.get(i);
                if(_tmp.original_path.equals(original_path)
                        && _tmp.store_dir_path.equals(store_dir)
                        && _tmp.listener==cber
                        && _tmp.tag==tag){
                    return;
                }
            }
            datas.add(new StructThum(original_path,_store_dir,tag,cber));
            if(is_sleep)CLThumLoader.this.notifyAll();
        }
    }

    public void clear_task(){
        synchronized (CLThumLoader.this){
            datas.clear();
        }
    }

    public void go_exit(){
        run=false;
        synchronized (CLThumLoader.this){
            CLThumLoader.this.notifyAll();
            if(worker!=null)worker.interrupt();
        }
    }

    private class StructThum{
        private String original_path;
        private String name;
        private String store_dir_path;
        private File store_dir;
        private Object tag;
        private Bitmap bitmap_thum;
        private LoaderListener listener;
        public StructThum(String o_p,File s_d,Object tag,LoaderListener cber){
            original_path=o_p;
            store_dir=s_d;
            store_dir_path=store_dir.getAbsolutePath();
            this.tag=tag;
            this.listener=cber;
            name=original_path.replaceAll(File.separator,"_");
        }

    }

    private ThumLoader worker;
    private boolean is_sleep,run;
    private ArrayList<StructThum> datas=new ArrayList<>();
    private StructThum crt_data=null;
    private class ThumLoader extends Thread{
        @Override
        public void run() {
            while (run){
                try{
                    crt_data=null;
                    synchronized (CLThumLoader.this){
                        if(datas.size()>0)crt_data=datas.remove(0);
                        else {
                            is_sleep=true;
                            CLThumLoader.this.wait();
                            is_sleep=false;
                            continue;
                        }
                    }
                    File _o=new File(crt_data.original_path);
                    if(!_o.exists())continue;//不存在
                    File _t=new File(crt_data.store_dir,crt_data.name);
                    //存在
                    if(_t.exists()){
                        crt_data.bitmap_thum = BitmapFactory.decodeFile(_t.getAbsolutePath());
                        if (crt_data.bitmap_thum == null) {//图片不正常
                            _t.delete();
                            datas.add(crt_data);
                        }else{
                            crt_data.listener.on_load_complete(crt_data.original_path,_t.getAbsolutePath(),crt_data.bitmap_thum,crt_data.tag);
                        }
                        continue;
                    }
                    //生成
                    BitmapFactory.Options _option = new BitmapFactory.Options();
                    _option.inJustDecodeBounds = true;
                    BitmapFactory.decodeFile(crt_data.original_path, _option);
                    if(_option.mCancel || _option.outWidth<=0 || _option.outHeight<=0){
                        //图片不正常则不再加载
                        crt_data.listener.on_load_fail(crt_data.original_path,crt_data.tag);
                    }else {
                        int _p_width = _option.outWidth;
                        int _p_height = _option.outHeight;
                        if (_p_width <= grid_width || _p_height <= grid_height) {
                            _option.inSampleSize = 1;
                        } else {
                            int _xs = _p_width / grid_width;
                            int _ys = _p_height / grid_height;
                            int _ss = (_xs < _ys ? _xs : _ys);
                            _option.inSampleSize = _ss * 2;
                        }
                        _option.inJustDecodeBounds = false;
                        Bitmap _bitmap = BitmapFactory.decodeFile(crt_data.original_path, _option);
                        if (_bitmap != null) {//图片正常
                            _bitmap.compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(_t));
                            //通知
                            crt_data.listener.on_load_complete(crt_data.original_path,_t.getAbsolutePath(),_bitmap,crt_data.tag);
                        }else crt_data.listener.on_load_fail(crt_data.original_path,crt_data.tag);
                    }

                }catch (Exception ex){
                    if(ex instanceof InterruptedException)return;
                    CL.CLOGE("thum loader error:",ex);
                    if(crt_data!=null)crt_data.listener.on_load_fail(crt_data.original_path,crt_data.tag);
                }
            }
            CL.CLOGI("thum loader over");
        }
    }
}
