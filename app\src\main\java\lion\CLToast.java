package lion;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

public class CLToast {
	public static int LONG=Toast.LENGTH_LONG;
	public static int SHORT=Toast.LENGTH_SHORT;
	private static Handler handler=new Handler(Looper.getMainLooper());
	private static Toast toast;
	
	public static void Show(final Context cc,final String msg,final boolean long_time){
		handler.post(new Runnable() {
			@Override
			public void run() {
				if(toast!=null){
					toast.setText(msg);
					toast.setDuration(long_time?Toast.LENGTH_LONG:Toast.LENGTH_SHORT);
					toast.show();
				}
				else {
					toast=Toast.makeText(cc, msg, (long_time?Toast.LENGTH_LONG:Toast.LENGTH_SHORT));
					toast.show();
				}
			}
		});
	}
	public static void Hide(){
		if(toast!=null){
			handler.post(new Runnable() {
				@Override
				public void run() {
					toast.cancel();
				}
			});
		}
	}
}
