package lion;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import androidx.core.content.FileProvider;

import amazon.browser.lionpro.datas.OneMovieBean;
import amazon.browser.lionpro.primary.Global;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by leron on 2016/6/24.
 */
public class CLTools {


    public static String Get_Pass_Time(long time){
        long _now=System.currentTimeMillis();
        if(time>=_now)return "刚刚";
        if(time>_now-3*60*1000){
            return "刚刚";
        }
        long _hour_1=60*60*1000;
        if(time>_now-_hour_1){//1小时内
            long _t=_now-time;
            int _s=(int)(_t/1000/60);
            return _s+"分钟前";
        }
        long _hour_24=_hour_1*24;
        if(time>_now-_hour_24){//1天内
            long _t=_now-time;
            int _s=(int)(_t/1000/60/60);
            return _s+"小时前";
        }
        long _day_30=_hour_24*30;
        if(time>_now-_day_30){//1月内
            long _t=_now-time;
            int _s=(int)(_t/1000/60/60/24);
            return _s+"天前";
        }
        Calendar _calendar=Calendar.getInstance();
        int _now_year=_calendar.get(Calendar.YEAR);
        int _now_month=_calendar.get(Calendar.MONTH)+1;
        _calendar.setTimeInMillis(time);
        int _year=_calendar.get(Calendar.YEAR);
        int _month=_calendar.get(Calendar.MONTH)+1;
        if(_year<_now_year)return(_now_year-_year)+"年前";
        if(_month<_now_month)return(_now_month-_month)+"个月前";
        return "很久以前";
    }

    public static String Get_Time_00_00_String(int time){
        StringBuffer _sb=new StringBuffer();

        int _hh=time/3600000;
        if(_hh>0){
            _sb.append(_hh);
            _sb.append(':');
        }
        int _mm=(time%3600000)/60000;
        if(_mm<10){
            _sb.append(0);
            _sb.append(_mm);
            _sb.append(':');
        }
        else{
            _sb.append(_mm);
            _sb.append(':');
        }
        int _ss=(time%60000)/1000;
        if(_ss<10){
            _sb.append(0);
            _sb.append(_ss);
        }
        else{
            _sb.append(_ss);
        }

        return _sb.toString();
    }

    public static String Get_Capacity_Format(long length){
        StringBuffer _sb=new StringBuffer();
        long _g=length/1073741824l;
        long _m=(length%1073741824l)/1048576;
        long _k=(length%1048576)/1024;
        if(_g>0){
            _sb.append(_g);
            if(_m>0){
                float _mm=(float)_m/(float)1024;
                int _mmm=(int)(_mm*100.0f);
                if(_mmm>0){
                    _sb.append('.');
                    if(_mmm<10)_sb.append("0");
                    _sb.append(_mmm);
                }else{
                    _sb.append(".00");
                }
            }else{
                _sb.append(".00");
            }
            _sb.append('G');
        }
        else if(_m>0){
            _sb.append(_m);
            if(_k>0){
                float _kk=(float)_k/(float)1024;
                int _kkk=(int)(_kk*100.0f);
                if(_kkk>0){
                    _sb.append('.');
                    if(_kkk<10)_sb.append("0");
                    _sb.append(_kkk);
                }else{
                    _sb.append(".00");
                }
            }else{
                _sb.append(".00");
            }
            _sb.append('M');
        }
        else if(_k>0){
            _sb.append(_k).append('K');
        }
        else{
            _sb.append(length+"B");
        }
        return _sb.toString();
    }

    public static String Get_Media_Duration(int millisecond){
        int _second=millisecond/1000;
        if(_second==0)return "0′0″";
        if(_second>=60) {
            int _min = _second / 60;
            int _ss=_second%60;
            return _min+"′"+_ss+"″";
        }else{
            return _second+"″";
        }
    }

    //正则匹配手机号码
    public static  boolean Check_Phone(String str){
        String regExp = "^0?(13[0-9]|15[012356789]|18[0-9]|14[57]|17[0-9])[0-9]{8}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(str);
        return m.find();
    }
    //正则匹配邮箱地址
    public static  boolean Check_Email(String str){
        //"^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$"
        String regExp = "^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(str);
        return m.find();
    }

    //Md5
    public static String Get_MD5(String str){
        String word=null;
        try {
            MessageDigest md5=MessageDigest.getInstance("MD5");
            md5.update(str.getBytes("utf-8"));
            byte[] data=md5.digest();
            StringBuffer sb=new StringBuffer(data.length*2);
            for(byte b:data){
                if ((b & 0xFF) < 0x10) sb.append("0");
                sb.append(Integer.toHexString(b & 0xFF));
            }
            word=sb.toString();
        } catch (Exception e) {
            word=null;
        }
        return word;
    }
    public static String Get_File_MD5(File file){
        String word=null;
        try {
            if(file==null || !file.exists())return null;
            if(file.length()==0)return null;
            MessageDigest md5=MessageDigest.getInstance("MD5");
            FileInputStream _fis=new FileInputStream(file);
            int _c;
            byte[] _buff=new byte[1024*32];
            while((_c=_fis.read(_buff))!=-1){
                md5.update(_buff,0,_c);
            }
            byte[] data=md5.digest();
            StringBuffer sb=new StringBuffer(data.length*2);
            for(byte b:data){
                if ((b & 0xFF) < 0x10) sb.append("0");
                sb.append(Integer.toHexString(b & 0xFF));
            }
            word=sb.toString();
        } catch (Exception e) {
            word=null;
        }
        return word;
    }
    //SHA1 "SHA-1"
    public static String Get_SHA1(String str){
        String word=null;
        try {
            MessageDigest md5=MessageDigest.getInstance("SHA-1");
            md5.update(str.getBytes("utf-8"));
            byte[] data=md5.digest();
            StringBuffer sb=new StringBuffer(data.length*2);
            for(byte b:data){
                if ((b & 0xFF) < 0x10) sb.append("0");
                sb.append(Integer.toHexString(b & 0xFF));
            }
            word=sb.toString();
        } catch (Exception e) {
            word=null;
        }
        return word;
    }

    public static String Replace_Bad_Filename(String fileName){
        String str=fileName;
        str=str.replace("\\", "");
        str=str.replace("/", "");
        str=str.replace(":", "");
        str=str.replace("*", "");
        str=str.replace("?", "");
        str=str.replace("\"", "");
        str=str.replace("<", "");
        str=str.replace(">", "");
        str=str.replace("|", "");
        str=str.replace(" ","");
        return str;
    }


    public static String Get_Url_Host(String url){
        try {
            URI _uri=new URI(url);
            String _host=_uri.getHost();
            return _host;
        } catch (URISyntaxException e) {
            return null;
        }
    }

    public static String GetUrlScheme(String url) {
        try {
            URI uri = new URI(url);
            String scheme = uri.getScheme();
            return scheme;
        } catch (URISyntaxException e) {
            return null;
        }
    }

    public static String Fix_Url(String inUrl) {
        int colon = inUrl.indexOf(':');
        boolean allLower = true;
        for (int index = 0; index < colon; index++) {
            char ch = inUrl.charAt(index);
            if (!Character.isLetter(ch)) {
                break;
            }
            allLower &= Character.isLowerCase(ch);
            if (index == colon - 1 && !allLower) {
                inUrl = inUrl.substring(0, colon).toLowerCase()
                        + inUrl.substring(colon);
            }
        }
        if (inUrl.startsWith("http://") || inUrl.startsWith("https://"))
            return inUrl;
        else if (inUrl.startsWith("http:") || inUrl.startsWith("https:")) {
            if (inUrl.startsWith("http:/") || inUrl.startsWith("https:/")) {
                inUrl = inUrl.replaceFirst("/", "//");
            } else inUrl = inUrl.replaceFirst(":", "://");
        }else{
            return "http://"+inUrl;
        }
        return inUrl;
    }
    public static String Was_Web_Site(String str){
        if(str==null || str.length()==0)return null;
        str=str.trim().toLowerCase();
        if(str.startsWith(".")||!str.contains("."))return null;
        str=Fix_Url(str);
        String _host=Get_Url_Host(str);
        if(_host==null)return null;
        String[] _domains=new String[]{
                ".com",".cn",".net",".org",".gov",".edu",".int",".mil",".biz",".info",".tv",".pro",".name",
                ".museum",".coop",".aero",".cc",".sh",".me",".asia",".kim",".ad",".ae",".af",
                ".ag",".ai",".al",".am",".an",".ao",".aq",".ar",".as",".at",".au",".aw",".az",
                ".ba",".bb",".bd",".be",".bf",".bg",".bh",".bi",".bj",".bm",".bn",".bo",".br",".bs",".bt",".bv",".bw",
                ".by",".bz",".ca",".cc",".cf",".cg",".ch",".ci",".ck",".cl",".cm",".cn",".co",".cq",".cr",".cu",".cv",".cx",".cy",".cz",".de",".dj",
                ".dk",".dm",".do",".dz",".ec",".ee",".eg",".eh",".es",".et",".ev",".fi",".fj",".fk",".fm",".fo",".fr",".ga",".gb",".gd",
                ".ge",".gf",".gh",".gi",".gl",".gm",".gn",".gp",".gr",".gt",".gu",".gw",".gy",".hk",".hm",".hn",".hr",".ht",".hu",".id",".ie",".il",
                ".in",".io",".iq",".ir",".is",".it",".jm",".jo",".jp",".ke",".kg",".kh",".ki",".km",".kn",".kp",".kr",".kw",".ky",".kz",".la",
                ".lb",".lc",".li",".lk",".lr",".ls",".lt",".lu",".lv",".ly",".ma",".mc",".md",".mg",".mh",".ml",".mm",".mn",".mo",".mp",
                ".mq",".mr",".ms",".mt",".mv",".mw",".mx",".my",".mz",".na",".nc",".ne",".nf",".ng",".ni",".nl",".no",".np",".nr",".nt",".nu",".nz",
                ".om",".pa",".pe",".pf",".pg",".ph",".pk",".pl",".pm",".pn",".pr",".pt",".pw",".py",".qa",
                ".re",".ro",".ru",".rw",".sa",".sb",".sc",".sd",".se",".sg",".sh",".si",".sj",".sk",".sl",".sm",".sn",".so",".sr",".st",
                ".su",".sy",".sz",".tc",".td",".tf",".tg",".th",".tj",".tk",".tm",".tn",".to",".tp",".tr",".tt",".tw",".tz",".ua",".ug",
                ".uk",".us",".uy",".va",".vc",".ve",".vg",".vn",".vu",".wf",".ws",".ye",".yu",".za",".zm",".zr",".zw"
        };
        for(int i=0;i<_domains.length;++i){
            if(_host.endsWith(_domains[i]))return str;
        }
        Pattern _p= Pattern.compile("([0-9]|[0-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}");
        Matcher _m=_p.matcher(_host);
        if(_m.find()){
            return str;
        }
        return null;
    }


    public static String Parse_Image_Format(String path){
        try {
            FileInputStream _fis = new FileInputStream(path);
            byte[] _ident=new byte[16];
            int _count=_fis.read(_ident);
            _fis.close();
            if(_count<16)return null;

            if(Byte2Int(_ident[0])==0xff && Byte2Int(_ident[1])==0xd8){
                return "jpg";
            }else if(Byte2Int(_ident[0])==0x89 && Byte2Int(_ident[1])==0x50 && Byte2Int(_ident[2])==0x4e && Byte2Int(_ident[3])==0x47
                    && Byte2Int(_ident[4])==0x0d && Byte2Int(_ident[5])==0x0a && Byte2Int(_ident[6])==0x1a && Byte2Int(_ident[7])==0x0a){
                return "png";
            }else if(Byte2Int(_ident[0])==0x47 && Byte2Int(_ident[1])==0x49 && Byte2Int(_ident[2])==0x46 && Byte2Int(_ident[3])==0x38
                        && (Byte2Int(_ident[4])==0x39 || Byte2Int(_ident[4])==0x37) && Byte2Int(_ident[5])==0x61){
                return "gif";
            }else if(Byte2Int(_ident[0])==0x42 && Byte2Int(_ident[1])==0x4d){
                return "bmp";
            }else if(Byte2Int(_ident[0])==0x52 && Byte2Int(_ident[1])==0x49 && Byte2Int(_ident[2])==0x46 && Byte2Int(_ident[3])==0x46){
                return "webp";
            }
        }catch (Exception ex){CL.CLOGE("parser image format error:"+ex.toString(),ex);}
        return null;
    }
    private static int Byte2Int(byte b){
        return b&0xff;
    }

    public static String Parse_Video_Audio_Format(byte[] ident){
        if(ident==null || ident.length<32){
//            CL.CLOGI("###ident is null or length <32");
            return null;
        }

//        CL.CLOGI("###String:"+new String(ident));

        if(Byte2Int(ident[4])==0x66 && Byte2Int(ident[5])==0x74 && Byte2Int(ident[6])==0x79 && Byte2Int(ident[7])==0x70){
            String _str=new String(ident,4,28).toLowerCase();
            if(_str.contains("m4a"))return "audio/m4a";
            else if(_str.contains("3gp"))return "video/3gpp";
            else if(_str.contains("ftyp"))return "video/mp4";
        }else if(new String(ident).contains("matroska")){
            return "video/mkv";
        }
        else if(Byte2Int(ident[0])==0x49 && Byte2Int(ident[1])==0x44 && Byte2Int(ident[2])==0x33){
            return "audio/mp3";
        }
        else if(Byte2Int(ident[0])==0xff && (Byte2Int(ident[1])&0xe0) == 0xe0){
            return "audio/mp3";
        }
        else if(Byte2Int(ident[0])==0x4f && Byte2Int(ident[1])==0x67 && Byte2Int(ident[2])==0x67 && Byte2Int(ident[3])==0x53){
            return "audio/ogg";
        }
        else if(Byte2Int(ident[0])==0x66 && Byte2Int(ident[1])==0x4c && Byte2Int(ident[2])==0x61 && Byte2Int(ident[3])==0x43){
            return "audio/flac";
        }
        else if(Byte2Int(ident[0])==0x52 && Byte2Int(ident[1])==0x49 && Byte2Int(ident[2])==0x46 && Byte2Int(ident[3])==0x46){
            if(new String(ident).contains("WAVEfmt"))return "audio/wav";
        } else if(Byte2Int(ident[0])==0x23 && Byte2Int(ident[1])==0x45 && Byte2Int(ident[2])==0x58 && Byte2Int(ident[3])==0x54){
            String _str=new String(ident,0,7).toLowerCase();
            if(_str.contains("#extm3u"))return "video/m3u8";
        } else if (Byte2Int(ident[0]) == 0x1A && Byte2Int(ident[1]) == 0x45 && Byte2Int(ident[2]) == 0xDF
                && Byte2Int(ident[3]) == 0xA3) {
            return "video/webm";
        }
        return null;
    }


    public static void install(Context context, String filename) {
        File file = new File(filename);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        // 由于没有在Activity环境下启动Activity,设置下面的标签
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if(Build.VERSION.SDK_INT>=24) { //判读版本是否在7.0以上
            //参数1 上下文, 参数2 Provider主机地址 和配置文件中保持一致   参数3  共享的文件
            Uri apkUri = FileProvider.getUriForFile(context, Global.Install_package_name, file);
            //添加这一句表示对目标应用临时授权该Uri所代表的文件
            //intent.setAction(Intent.ACTION_INSTALL_PACKAGE);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        }else{
            intent.setDataAndType(Uri.fromFile(file),
                    "application/vnd.android.package-archive");
        }
        context.startActivity(intent);
    }




    public static String readTextFromSDcard(InputStream is) throws Exception {
        InputStreamReader reader = new InputStreamReader(is);
        BufferedReader bufferedReader = new BufferedReader(reader);
        StringBuffer buffer = new StringBuffer("");
        String str;
        while ((str = bufferedReader.readLine()) != null) {
            buffer.append(str);
            buffer.append("\n");
        }
        return buffer.toString();
    }


    public static void playFullSrceenMedia(Context cc, String item) {
        Intent intent=new Intent();
        OneMovieBean oBean = new OneMovieBean();
        ArrayList<String> l = new ArrayList<String>(10);
        l.add(item);
        oBean.setUrls(l);
        oBean.setUrl(item);
        intent.putExtra("name", oBean.getTitle());
        intent.putExtra("url", oBean.getUrls());
        intent.setClass(cc, PlayActivity.class);
        cc.startActivity(intent);

    }

    public static boolean  isFolderExists(String strFolder) {
        File file = new File(strFolder);
        if (!file.exists()) {
            if (file.mkdirs()) {
                return true;
            } else {
                return false;

            }
        }
        return true;

    }

    public static byte[] read(InputStream inputStream) throws IOException {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int num = inputStream.read(buffer);
            while (num != -1) {
                baos.write(buffer, 0, num);
                num = inputStream.read(buffer);
            }
            baos.flush();
            return baos.toByteArray();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    public static String[] strSplitToArray(String strSource, String separator, boolean ignoreEmptyValue) {
        if(null == strSource || strSource.trim().length() == 0)
            return new String[0];
        if(null == strSource || strSource.length() == 0 || !strSource.contains(separator))
            return new String[] {strSource};
        StringBuilder sbStr = new StringBuilder(strSource);
        List<String> list = new LinkedList<>();
        int start = 0, end = 0;
        String str = null;
        while((end = sbStr.indexOf(separator)) != -1) {
            str = sbStr.substring(start, end);
            if(!ignoreEmptyValue || false == str.isEmpty()){
                list.add(str);
            }
            sbStr.delete(0, end+separator.length());
        }
        if(sbStr.length() > 0) {
            list.add(sbStr.toString());
        }
        return list.toArray(new String[list.size()]);
    }

}
