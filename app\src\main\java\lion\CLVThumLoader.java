package lion;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.ThumbnailUtils;
import android.provider.MediaStore;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;

/**
 * Created by leron on 2016/5/18.
 */
public class CLVThumLoader {
    public interface LoaderListener{
        void on_load_complete(String opath, String tpath, Bitmap bm, Object tag);
        void on_load_fail(String opath, Object tag);
    }

    private static int grid_width=CL.DIP2PX_INT(220);
    public CLVThumLoader(){
        worker=new ThumLoader();
        run=true;
        worker.start();
    }
    public CLVThumLoader(int w,int h){
        grid_width=w;
        if(grid_width<80)grid_width=80;
        worker=new ThumLoader();
        run=true;
        worker.start();
    }

    public void go_add_item(String original_path,String store_dir,Object tag,LoaderListener cber){
        if(original_path==null || store_dir==null || tag==null || cber==null)return;
        File _store_dir=new File(store_dir);
        if(!_store_dir.exists() || !_store_dir.isDirectory())return;
        synchronized (CLVThumLoader.this){
            if(crt_data!=null && crt_data.original_path != null
                    && crt_data.store_dir_path != null
                    && crt_data.listener != null
                    && crt_data.tag != null) {
                if(crt_data.original_path.equals(original_path)
                        && crt_data.store_dir_path.equals(store_dir)
                        && crt_data.listener==cber
                        && crt_data.tag==tag){
                    return;
                }
            }

            if (datas != null) {
                for (int i = 0; i < datas.size(); ++i) {
                    StructThum _tmp = datas.get(i);
                    if (_tmp != null && _tmp.original_path.equals(original_path)
                            && _tmp.store_dir_path.equals(store_dir)
                            && _tmp.listener == cber
                            && _tmp.tag == tag) {
                        return;
                    }
                }
                datas.add(new StructThum(original_path, _store_dir, tag, cber));
            }
            CLVThumLoader.this.notifyAll();
        }
    }

    public void clear_task(){
        synchronized (CLVThumLoader.this){
            datas.clear();
        }
    }

    public void go_exit(){
        run=false;
        synchronized (CLVThumLoader.this){
            CLVThumLoader.this.notifyAll();
            if(worker!=null)worker.interrupt();
        }
    }

    private class StructThum{
        private String original_path;
        private String name;
        private String store_dir_path;
        private File store_dir;
        private Object tag;
        private Bitmap bitmap_thum;
        private LoaderListener listener;
        @SuppressLint("SuspiciousIndentation")
        public StructThum(String o_p, File s_d, Object tag, LoaderListener cber){
            original_path=o_p;
            store_dir=s_d;
            if (store_dir != null)
            store_dir_path=store_dir.getAbsolutePath();
            this.tag=tag;
            this.listener=cber;
            if (original_path != null)
            name=original_path.replaceAll(File.separator,"_");
        }

    }

    private ThumLoader worker;
    private boolean run;
    private ArrayList<StructThum> datas=new ArrayList<>();
    private StructThum crt_data=null;
    private class ThumLoader extends Thread{
        @Override
        public void run() {
            while (run){
                try{
                    crt_data=null;
                    synchronized (CLVThumLoader.this){
                        if(datas.size()>0)crt_data=datas.remove(0);
                        else {
                            CLVThumLoader.this.wait();
                            continue;
                        }
                    }
                    File _o=new File(crt_data.original_path);
                    if(!_o.exists())continue;//不存在
                    File _t=new File(crt_data.store_dir,crt_data.name);
                    //存在
                    if(_t.exists()){
                        crt_data.bitmap_thum = BitmapFactory.decodeFile(_t.getAbsolutePath());
                        if (crt_data.bitmap_thum == null) {//图片不正常
                            _t.delete();
                            datas.add(crt_data);
                        }else{
                            crt_data.listener.on_load_complete(crt_data.original_path,_t.getAbsolutePath(),crt_data.bitmap_thum,crt_data.tag);
                        }
                        continue;
                    }
                    //生成
                    Bitmap _bvid= ThumbnailUtils.createVideoThumbnail(crt_data.original_path, MediaStore.Video.Thumbnails.MINI_KIND);
                    _bvid=ThumbnailUtils.extractThumbnail(_bvid,grid_width,grid_width);
                    if(_bvid!=null){//缩略图正常
                        _bvid.compress(Bitmap.CompressFormat.JPEG, 100, new FileOutputStream(_t));
                        crt_data.listener.on_load_complete(crt_data.original_path,_t.getAbsolutePath(),_bvid,crt_data.tag);
                    }else crt_data.listener.on_load_fail(crt_data.original_path,crt_data.tag);
                }catch (Exception ex){
                    if(ex instanceof InterruptedException)return;
                    CL.CLOGE("thum loader error:",ex);
                    if(crt_data!=null)crt_data.listener.on_load_fail(crt_data.original_path,crt_data.tag);
                }
            }
            CL.CLOGI("thum loader over");
        }
    }
}
