package lion;

import android.util.Log;

public interface FunctionRunTimeCallBack {
    void doSometing();

    public static void countTime(FunctionRunTimeCallBack callBack) {
        long startTime= System.currentTimeMillis(); //起始时间
        callBack.doSometing(); ///进行回调操作
        long endTime = System.currentTimeMillis(); //结束时间
        Log.i("Eddy countime",String.format("方法使用时间 %d ms", endTime - startTime)); //打印使用时间
    }

}



