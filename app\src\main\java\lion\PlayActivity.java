package lion;

//import io.vov.vitamio.MediaPlayer;
//import io.vov.vitamio.MediaPlayer.OnBufferingUpdateListener;
//import io.vov.vitamio.MediaPlayer.OnCompletionListener;
//import io.vov.vitamio.MediaPlayer.OnErrorListener;
//import io.vov.vitamio.MediaPlayer.OnInfoListener;
//import io.vov.vitamio.MediaPlayer.OnPreparedListener;
//import io.vov.vitamio.MediaPlayer.OnSeekCompleteListener;
//import io.vov.vitamio.MediaPlayer.OnVideoSizeChangedListener;
//import io.vov.vitamio.Vitamio;
//import io.vov.vitamio.utils.StringUtils;
//import io.vov.vitamio.widget.VideoView;

import java.sql.Timestamp;
import java.util.ArrayList;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.graphics.Color;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.Display;
import android.view.GestureDetector;
import android.view.GestureDetector.SimpleOnGestureListener;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.SeekBar;
import android.widget.SeekBar.OnSeekBarChangeListener;
import android.widget.TextView;
import android.media.*;
import android.media.MediaPlayer;
import android.widget.VideoView;

import amazon.browser.video.downloader.R;


public class PlayActivity extends Activity implements OnClickListener,
		MediaPlayer.OnBufferingUpdateListener, MediaPlayer.OnCompletionListener, MediaPlayer.OnPreparedListener,
		MediaPlayer.OnVideoSizeChangedListener, SurfaceHolder.Callback, MediaPlayer.OnErrorListener,
		MediaPlayer.OnInfoListener, MediaPlayer.OnSeekCompleteListener {
	private String path;
	private ImageView imageView, imageback;
	private TextView nametTextView;
	private TextView timeTextView;
	private Button chiceButton;
	private ImageView fontbButton, playbButton, nextButton;
	private VideoView videoView;
	private FrameLayout fLayout;
	private LinearLayout layout;
	private boolean isPlay;
	private SeekBar seekBar;
	private upDateSeekBar update;
	private boolean isLock;
	private String timeString;
	private TextView play_tiem, endTime;
	private String name;
	private ImageView mOperationBg;
	private ImageView mOperationPercent;
	private View mVolumeBrightnessLayout;
	private AudioManager mAudioManager;
	/** 当前声音 */
	private int mMaxVolume;
	/** 当前声音 */
	private int mVolume = -1;
	/** 当前亮度 */
	private float mBrightness = -1f;
	private GestureDetector mGestureDetector;
	private View bar;
	private TextView textViewBF;
	private TextView sudu;
	private TextView sudu2;
	private PopupWindow pWindow;
	private Button qingxiButton, liuchangButton, gaoqingButton;
    
	private boolean isFinish;
	private int pauseSize;
	private int size;
	private ArrayList<String> urls;
    private int flag;
    //private static int mmm=0;
    private boolean bplaycomplete = false;
    private boolean bfirstBuffer = false;
	@Override
	protected void onCreate(Bundle savedInstanceState) {
		requestWindowFeature(Window.FEATURE_NO_TITLE);
		super.onCreate(savedInstanceState);
//		if (!LibsChecker.checkVitamioLibs(this))
//			return;
        //mmm++;
       // Log.i("hck", "onCreate");
		//Vitamio.isInitialized(getApplicationContext());

		getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
				WindowManager.LayoutParams.FLAG_FULLSCREEN);
		setContentView(R.layout.play);
		initView();
		getInitDate();
		setInitDate();
		nextButton.setEnabled(false);
		fontbButton.setEnabled(false);
		play();
		

	}



	private void getInitDate() {
		//path = getIntent().getStringExtra("path");
		name = getIntent().getStringExtra("name");
		urls=getIntent().getStringArrayListExtra("url");


	}
	private void setInitDate() {
		nametTextView.setText(name);
	}
	private void initView() {
		urls=new ArrayList<String>();
		
		seekBar = (SeekBar) findViewById(R.id.seekBar);
		fLayout = (FrameLayout) findViewById(R.id.title_fl);
		fLayout.setVisibility(View.GONE);
		layout = (LinearLayout) findViewById(R.id.buttom_lin);
		layout.setVisibility(View.GONE);
		videoView = (VideoView) findViewById(R.id.surface_view);
		imageView = (ImageView) findViewById(R.id.image_lock);

		imageView.setOnClickListener(this);
		imageView.setVisibility(View.GONE);

		imageback = (ImageView) findViewById(R.id.image_back);
		imageback.setOnClickListener(this);
		imageback.setVisibility(View.GONE);
		sudu = (TextView) findViewById(R.id.sudu);
		sudu2 = (TextView) findViewById(R.id.sudu2);
		bar = findViewById(R.id.pb);
		textViewBF = (TextView) bar.findViewById(R.id.buff);
		play_tiem = (TextView) findViewById(R.id.play_time);
		endTime = (TextView) findViewById(R.id.play_end_time);
		nametTextView = (TextView) findViewById(R.id.movie_name);
		nametTextView.setOnClickListener(this);
		timeTextView = (TextView) findViewById(R.id.movie_time);
		timeString = (new Timestamp(System.currentTimeMillis())).toString()
				.substring(11, 16);
		timeTextView.setText(timeString);
		chiceButton = (Button) findViewById(R.id.zhiliang);
		chiceButton.setOnClickListener(this);
		fontbButton = (ImageView) findViewById(R.id.font);
		fontbButton.setOnClickListener(this);
		playbButton = (ImageView) findViewById(R.id.play);
		playbButton.setOnClickListener(this);
		nextButton = (ImageView) findViewById(R.id.next);
		nextButton.setOnClickListener(this);

		mVolumeBrightnessLayout = findViewById(R.id.operation_volume_brightness);
		mOperationBg = (ImageView) findViewById(R.id.operation_bg);
		mOperationPercent = (ImageView) findViewById(R.id.operation_percent);
		mGestureDetector = new GestureDetector(this, new MyGestureListener());
		mAudioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
		mMaxVolume = mAudioManager
				.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
		setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
		setListener();
		update = new upDateSeekBar();
		new Thread(update).start();
	}

	private class MyGestureListener extends SimpleOnGestureListener {
		@Override
		public boolean onScroll(MotionEvent e1, MotionEvent e2,
				float distanceX, float distanceY) {
			if (e1 == null) {
				return true;
			}
			float mOldX = e1.getX(), mOldY = e1.getY();
			int y = (int) e2.getRawY();
			Display disp = getWindowManager().getDefaultDisplay();
			int windowWidth = disp.getWidth();
			int windowHeight = disp.getHeight();

			if (mOldX > windowWidth * 4.0 / 5)// 右边滑动
				onVolumeSlide((mOldY - y) / windowHeight);
			else if (mOldX < windowWidth / 5.0)// 左边滑动
				onBrightnessSlide((mOldY - y) / windowHeight);

			return super.onScroll(e1, e2, distanceX, distanceY);
		}
	}

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        //return super.onKeyDown(keyCode, event);
        if (keyCode == KeyEvent.KEYCODE_BACK) {

            if (isLock)
                return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
	 * 滑动改变声音大小
	 * 
	 * @param percent
	 */
	private void onVolumeSlide(float percent) {
		if (mVolume == -1) {
			mVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
			if (mVolume < 0)
				mVolume = 0;

			// 显示
			mOperationBg.setImageResource(R.mipmap.video_volumn_bg);
			mVolumeBrightnessLayout.setVisibility(View.VISIBLE);
		}

		int index = (int) (percent * mMaxVolume) + mVolume;
		if (index > mMaxVolume)
			index = mMaxVolume;
		else if (index < 0)
			index = 0;

		// 变更声音
		mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, index, 0);

				ViewGroup.LayoutParams lp = mOperationPercent.getLayoutParams();
		lp.width = findViewById(R.id.operation_full).getLayoutParams().width
				* index / mMaxVolume;
		mOperationPercent.setLayoutParams(lp);
	}

	private void onBrightnessSlide(float percent) {
		if (mBrightness < 0) {
			mBrightness = getWindow().getAttributes().screenBrightness;
			if (mBrightness <= 0.00f)
				mBrightness = 0.50f;
			if (mBrightness < 0.01f)
				mBrightness = 0.01f;

			// 显示
			mOperationBg.setImageResource(R.mipmap.video_brightness_bg);
			mVolumeBrightnessLayout.setVisibility(View.VISIBLE);
		}
		WindowManager.LayoutParams lpa = getWindow().getAttributes();
		lpa.screenBrightness = mBrightness + percent;
		if (lpa.screenBrightness > 1.0f)
			lpa.screenBrightness = 1.0f;
		else if (lpa.screenBrightness < 0.01f)
			lpa.screenBrightness = 0.01f;
		getWindow().setAttributes(lpa);

		ViewGroup.LayoutParams lp = mOperationPercent.getLayoutParams();
		lp.width = (int) (findViewById(R.id.operation_full).getLayoutParams().width * lpa.screenBrightness);
		mOperationPercent.setLayoutParams(lp);
	}

	private void play() {


        String name = "";
		try {
			name = urls.get(flag);
		} catch (Exception e) {
			CLToast.Show(this,"Exception",true);
			return;
		}
		//name = "https://scontent-cdg2-1.cdninstagram.com/t50.2886-16/14131714_623872664453990_1940225070_n.mp4";
        name = name.replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&quot;", "\"")
                .replace("'", "‘")
                .replace("&nbsp;", "©")
                .replace("&copy;", "®")
                .replace("&reg;", "&")
                /*.replace("%2B", "+")
                .replace("%2F", "/")
                .replace("%20", " ")
                .replace("%3F", "?")
                .replace("%25", "%")
                .replace("%23", "#")
                .replace("%26", "&")
                .replace("%3D", "=")*/;
		isPlay = true;
		try {
            videoView.stopPlayback();
			videoView.setVideoURI(Uri.parse(name));
			videoView.setOnCompletionListener(this);
		//	videoView.setOnBufferingUpdateListener(this);
			videoView.setOnErrorListener(this);
			videoView.setOnInfoListener(this);
		    videoView.setOnPreparedListener(this);
			playbButton.setImageResource(R.mipmap.player_pause_highlight);
		} catch (Exception e) {
			Log.i("hck", "PlayActivity " + e.toString());
		}
	}
	class upDateSeekBar implements Runnable {

		@Override
		public void run() {
			if (!isFinish) {
				mHandler.sendMessage(Message.obtain());
				mHandler.postDelayed(update, 1000);
			}

		}
	}
	public String generateTime(long time) {
		int totalSeconds = (int) (time / 1000);
		int seconds = totalSeconds % 60;
		int minutes = (totalSeconds / 60) % 60;
		int hours = totalSeconds / 3600;

		return hours > 0 ? String.format("%02d:%02d:%02d", hours, minutes, seconds) : String.format("%02d:%02d", minutes, seconds);
	}

	@SuppressLint("HandlerLeak")
	Handler mHandler = new Handler() {
		public void handleMessage(Message msg) {
			if (videoView == null) {
				return;
			}
			timeString = (new Timestamp(System.currentTimeMillis())).toString()
					.substring(11, 16);
			timeTextView.setText(timeString);
			play_tiem.setText(generateTime(videoView
					.getCurrentPosition()));
			if (videoView != null) {
				seekBar(videoView.getCurrentPosition());
			}

		};
	};

	private void seekBar(long size) {
		if (videoView.isPlaying()) {
			long mMax = videoView.getDuration();
			int sMax = seekBar.getMax();
			if (mMax <= 0)
				seekBar.setProgress((int) 0);
			else
				seekBar.setProgress((int) (size * sMax / mMax));
		}
	}

	private void setListener() {
		seekBar.setOnSeekBarChangeListener(new OnSeekBarChangeListener() {

			@Override
			public void onStopTrackingTouch(SeekBar seekBar) {
				if (seekBar == null || videoView == null || playbButton == null || seekBar.getMax() <= 0)
					return;

				int value = (int) (seekBar.getProgress()
						* videoView.getDuration() / seekBar.getMax());
				videoView.seekTo(value);
				videoView.start();
				isPlay = true;
				playbButton.setImageResource(0);
				playbButton.setImageResource(R.mipmap.player_pause_highlight);
			}

			@Override
			public void onStartTrackingTouch(SeekBar seekBar) {
				isPlay = false;
				videoView.pause();
				playbButton.setImageResource(R.mipmap.player_play_highlight);
			}

			@Override
			public void onProgressChanged(SeekBar seekBar, int progress,
					boolean fromUser) {

			}
		});

	}

	@Override
	public boolean onTouchEvent(MotionEvent event) {
		if (mGestureDetector.onTouchEvent(event))
			return true;
		switch (event.getAction()) {
		case MotionEvent.ACTION_DOWN:
			if (!isLock) {
				fLayout.setVisibility(View.VISIBLE);
				layout.setVisibility(View.VISIBLE);
				imageback.setVisibility(View.VISIBLE);
			}
			imageView.setVisibility(View.VISIBLE);

			break;
		case MotionEvent.ACTION_UP:
			endGesture();
			break;

		default:
			break;
		}
		return super.onTouchEvent(event);
	}

	private void endGesture() {
		mVolume = -1;
		mBrightness = -1f;

		// 隐藏
		disHandler.removeMessages(0);
		disHandler.sendEmptyMessageDelayed(0, 1000);
		disHandler.removeMessages(1);
		disHandler.sendEmptyMessageDelayed(1, 5000);
	}

	private Handler disHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			if (msg.what == 0) {
				mVolumeBrightnessLayout.setVisibility(View.GONE);
			} else {
				fLayout.setVisibility(View.GONE);
				layout.setVisibility(View.GONE);
				imageView.setVisibility(View.GONE);
				imageback.setVisibility(View.GONE);
				if (pWindow != null && pWindow.isShowing()) {
					pWindow.dismiss();
				}
			}

		}
	};

	@SuppressLint("HandlerLeak")
	@Override
	public void onClick(View v) {
		int id = v.getId();
		if (id == R.id.play) {

			if (bplaycomplete) {
				flag = 0;
				videoView.setVideoURI(Uri.parse(urls.get(0)));
				seekBar.setProgress(0);
				int value = (int) (seekBar.getProgress()
						* videoView.getDuration() / seekBar.getMax());
				videoView.seekTo(value);
				videoView.start();
				isPlay = true;
				bplaycomplete = false;
				playbButton.setImageResource(0);
				playbButton.setImageResource(R.mipmap.player_pause_highlight);
				return;
			}

			if (!bfirstBuffer&& urls.size() > 1) {
				String url = "";
				try {
					url = urls.get(flag);
				} catch (Exception e) {
					CLToast.Show(this,"Exception",true);
					return;
				}


				videoView.setVideoURI(Uri.parse(url));
				seekBar.setProgress(0);
				int value = (int) (seekBar.getProgress()
						* videoView.getDuration() / seekBar.getMax());
				videoView.seekTo(value);
				videoView.start();
				isPlay = true;
				bplaycomplete = false;
				playbButton.setImageResource(0);
				playbButton.setImageResource(R.mipmap.player_pause_highlight);
				return;
			}

			if (isPlay) {
				videoView.pause();
				playbButton.setImageResource(0);
				playbButton
						.setImageResource(R.mipmap.player_play_highlight);
				//playbButton.invalidate();
				isPlay = false;
				seekBar.setEnabled(false);
			} else {
				if (videoView.isPlaying())
					playbButton.setImageResource(0);
				playbButton
						.setImageResource(R.mipmap.player_pause_highlight);
				//		playbButton.invalidate();
				videoView.start();
				isPlay = true;
				seekBar.setEnabled(true);
			}
		} else if (id == R.id.image_lock) {
			if (isLock) {
				isLock = false;
				imageView.setImageResource(R.mipmap.lock_off);
			} else {
				isLock = true;
				imageView.setImageResource(R.mipmap.lock_on);
			}
		} else if (id == R.id.image_back) {
			closePlay();
		} else if (id == R.id.next) {
			videoView.pause();
			if (videoView!=null) {
				size=videoView.getCurrentPosition()+videoView.getDuration()/10;
				videoView.seekTo(size);
				videoView.start();
			}
		} else if (id == R.id.font) {
			videoView.pause();
			if (videoView!=null) {
				size=videoView.getCurrentPosition()-videoView.getDuration()/10;
				videoView.seekTo(size);
				videoView.start();
			}
		} else if (id == R.id.zhiliang) {
			showPop(v);
		}
	}
//	public void onClick(View v) {
//		switch (v.getId()) {
//		case R.id.play:
//
//            if (bplaycomplete) {
//                flag = 0;
//                videoView.setVideoURI(Uri.parse(urls.get(0)));
//                seekBar.setProgress(0);
//                int value = (int) (seekBar.getProgress()
//                        * videoView.getDuration() / seekBar.getMax());
//                videoView.seekTo(value);
//                videoView.start();
//                isPlay = true;
//                bplaycomplete = false;
//				playbButton.setImageResource(0);
//                playbButton.setImageResource(R.mipmap.player_pause_highlight);
//                break;
//            }
//
//            if (!bfirstBuffer&& urls.size() > 1) {
//				String url = "";
//				try {
//					url = urls.get(flag);
//				} catch (Exception e) {
//					CLToast.Show(this,"Exception",true);
//					return;
//				}
//
//
//				videoView.setVideoURI(Uri.parse(url));
//				seekBar.setProgress(0);
//				int value = (int) (seekBar.getProgress()
//						* videoView.getDuration() / seekBar.getMax());
//				videoView.seekTo(value);
//				videoView.start();
//				isPlay = true;
//				bplaycomplete = false;
//				playbButton.setImageResource(0);
//				playbButton.setImageResource(R.mipmap.player_pause_highlight);
//				break;
//			}
//
//			if (isPlay) {
//				videoView.pause();
//				playbButton.setImageResource(0);
//				playbButton
//						.setImageResource(R.mipmap.player_play_highlight);
//				//playbButton.invalidate();
//				isPlay = false;
//				seekBar.setEnabled(false);
//			} else {
//                if (videoView.isPlaying())
//			    playbButton.setImageResource(0);
//				playbButton
//						.setImageResource(R.mipmap.player_pause_highlight);
//				//		playbButton.invalidate();
//				videoView.start();
//				isPlay = true;
//				seekBar.setEnabled(true);
//			}
//			break;
//		case R.id.image_lock:
//			if (isLock) {
//				isLock = false;
//				imageView.setImageResource(R.mipmap.lock_off);
//			} else {
//				isLock = true;
//				imageView.setImageResource(R.mipmap.lock_on);
//			}
//			break;
//		case R.id.image_back:
//			closePlay();
//			break;
//		case R.id.next:
//			videoView.pause();
//			if (videoView!=null) {
//				size=videoView.getCurrentPosition()+videoView.getDuration()/10;
//				videoView.seekTo(size);
//				videoView.start();
//			}
//			break;
//		case R.id.font:
//			videoView.pause();
//			if (videoView!=null) {
//				size=videoView.getCurrentPosition()-videoView.getDuration()/10;
//				videoView.seekTo(size);
//				videoView.start();
//			}
//			break;
//		case R.id.zhiliang:
//			Log.i("hck", "zhiliang");
//			showPop(v);
//			break;
//
//
//		}
//	}

	private void closePlay() {
		videoView.pause();
		finish();
	}

	@Override
	public void surfaceChanged(SurfaceHolder arg0, int arg1, int arg2, int arg3) {

	}

	@Override
	public void surfaceCreated(SurfaceHolder arg0) {
		Log.i("hck", "surfaceCreated");
	}

	@Override
	public void surfaceDestroyed(SurfaceHolder arg0) {

	}

	@Override
	public void onVideoSizeChanged(MediaPlayer arg0, int arg1, int arg2) {
	}
	@Override
	public void onPrepared(MediaPlayer arg0) {
		nextButton.setEnabled(true);
		fontbButton.setEnabled(true);
		bar.setVisibility(View.GONE);

		String url = "";

		try {
			flag = 100;
			url = urls.get(flag);
		} catch (IndexOutOfBoundsException ex) {
			if (urls.size() < 0) {
				url = urls.get(urls.size()-1);
			}
		}

//		if (url.startsWith("http")) {
//			videoView.setBufferSize(1024 * 1000);
//		} else {
//			videoView.setBufferSize(0);
//		}
		endTime.setText(generateTime(videoView.getDuration()));
		bar.setVisibility(View.GONE);
		if (pauseSize > 0) {
			videoView.seekTo(pauseSize);
		}
		pauseSize = 0;
	}
	@Override
	public void onCompletion(MediaPlayer arg0) {
		String url = "";
		try {
			url = urls.get(flag);
		} catch (Exception e) {
			CLToast.Show(this,"Exception",true);
			return;
		}

		videoView.stopPlayback();
		playbButton.setImageResource(0);
        playbButton.setImageResource(R.mipmap.player_play_highlight);
        isPlay = false;
        bplaycomplete = true;
        flag++;
        if (urls.size() - 1>= flag) {
            //videoView.pause();
            videoView.setVideoURI(Uri.parse(url));
            seekBar.setProgress(0);
            seekBar.setEnabled(true);
            int value = (int) (seekBar.getProgress()
                    * videoView.getDuration() / seekBar.getMax());
            videoView.seekTo(value);
            videoView.start();
            isPlay = true;
            bplaycomplete = false;
			bfirstBuffer = false;
			playbButton.setImageResource(0);
            playbButton.setImageResource(R.mipmap.player_pause_highlight);

        }
	}

	@Override
	public void onBufferingUpdate(MediaPlayer arg0, int arg1) {
		textViewBF.setTextColor(Color.WHITE);
		textViewBF.setText(videoView.getBufferPercentage() + "%");

	}

	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
	//	videoView.setVideoLayout(3, 0);
	}

	@Override
	public boolean onError(MediaPlayer arg0, int arg1, int arg2) {
		Log.i("hck", "error :" + arg1 + arg2);
		return false;
	}

	@Override
	public boolean onInfo(MediaPlayer arg0, int arg1, int arg2) {
		switch (arg1) {
		case MediaPlayer.MEDIA_INFO_BUFFERING_START:
			bfirstBuffer = true;
			if (isPlay) {
				bar.setVisibility(View.VISIBLE);
				videoView.pause();
				isPlay = false;
				//playbButton
				//		.setImageResource(R.mipmap.player_play_highlight);
			}
			break;
		case MediaPlayer.MEDIA_INFO_BUFFERING_END:
			if (!isPlay) {
				bar.setVisibility(View.GONE);
				videoView.start();
				isPlay = true;
				//playbButton.setImageResource(R.mipmap.player_pause_highlight);
			}
			break;
		case 901:
			sudu.setTextColor(Color.WHITE);
			sudu.setText(arg2 + "kb/s");
			sudu2.setText(arg2 + "kb/s");
			break;
		}
		return true;
	}

	@Override
	protected void onPause() {
		super.onPause();
		videoView.pause();

	}

	@Override
	protected void onResume() {
		super.onResume();
		if (isPlay) {
			videoView.start();
		}
	}

	@Override
	protected void onDestroy() {
        Log.i("hck", "onDestroy");
		super.onDestroy();
		if (videoView != null) {
			videoView.stopPlayback();
			videoView = null;
		}
		if (pWindow != null && pWindow.isShowing()) {
			pWindow.dismiss();
		}
		isPlay = false;
		isFinish = true;
		System.gc();

	}

	@Override
	public void onSeekComplete(MediaPlayer arg0) {

	}

	private void showPop(View view2) {

//		View view = LayoutInflater.from(this).inflate(R.layout.pop, null);
//		qingxiButton = (Button) view.findViewById(R.id.qingxi);
//		gaoqingButton = (Button) view.findViewById(R.id.gaoqing);
//		liuchangButton = (Button) view.findViewById(R.id.liuchang);
//		pWindow = new PopupWindow(view, LayoutParams.WRAP_CONTENT,
//				LayoutParams.WRAP_CONTENT);
//		pWindow.setOutsideTouchable(true);
//		pWindow.showAtLocation(view2, Gravity.LEFT, Gravity.LEFT, 1);
//		setListener2();

	}

	private void setListener2() {
		qingxiButton.setOnClickListener(this);
		gaoqingButton.setOnClickListener(this);
		liuchangButton.setOnClickListener(this);
	}

}
