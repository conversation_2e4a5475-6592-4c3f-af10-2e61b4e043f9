package lion.web;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.URLDecoder;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

import lion.CL;

/**
 * Created by leron on 2016/8/31.
 */
public class CLHS {


    public interface Eventer{
        void on_server_start();
        void on_server_exit();
        void on_handle(CLRequest req,CLResponse rsp)throws Exception;
    }



    private final int Min_Thread=20;
    private final int Max_Thread=80;


    private HS server;
    private Eventer listener;
    private volatile boolean run=true;
    private Worker[] threads_fixed=new Worker[Min_Thread];
    private AtomicInteger real_work_count=new AtomicInteger(0);
//    private AtomicInteger real_thread_count=new AtomicInteger(0);
    private LinkedBlockingQueue<CLRequest> queue=new LinkedBlockingQueue();

    public CLHS(Eventer listen){
        if(listen==null)throw new IllegalArgumentException("Eventer is null");
        this.listener=listen;
    }

    public void go(){
        if(server==null){
            for(int i=0;i<Min_Thread;++i){
                threads_fixed[i]=new Worker(true);
                threads_fixed[i].start();
            }
            server=new HS();
            server.start();
        }
    }

    public boolean stop(){
        if(server!=null ) {
            run=false;
            server.interrupt();
            for (int i = 0; i < threads_fixed.length; ++i) {
                threads_fixed[i].interrupt();
            }
            return true;
        }else return false;
    }


    private class HS extends Thread{

        private ServerSocket ss;

        @Override
        public void interrupt() {
            super.interrupt();
            try {
                ss.close();
            }catch (Exception ex){}
        }

        @Override
        public void run() {
            try{
                ss=new ServerSocket(8200);
                ss.setReuseAddress(true);
                listener.on_server_start();
                while (run){
                    Socket _s=ss.accept();
                    CLRequest _req=new CLRequest();
                    _req.socket=_s;
                    queue.offer(_req);

                    int _work_count=real_work_count.get();
                    if(_work_count>=Min_Thread && _work_count<Max_Thread){
                        new Worker(false).start();
                    }
                }
            }catch (Exception ex){
                CL.CLOGE("server error:"+ex.toString(),ex);
            }
            listener.on_server_exit();
            CL.CLOGI("server over!!!");
        }
    }



    private class Worker extends Thread{

        private boolean fixed=false;

        public Worker(boolean fixed){
            this.fixed=fixed;
            if(!fixed)CL.CLOGI("开启额外线程...");
        }

        @Override
        public void run() {
//            int _tc=real_thread_count.incrementAndGet();
//            CL.CLOGI("++thread count:" + _tc);
            while (run) {
                boolean _waitting=true;
                try {
                    CLRequest _req = queue.take();
                    _waitting=false;
                    int _count=real_work_count.incrementAndGet();
                    CL.CLOGI(">>>>>> worker count:" + _count);
                    CL.CLOGI("process request:" + _req.socket.getInetAddress().toString());
                    InputStream _is = _req.socket.getInputStream();
                    try {
                        parse_request(_is, _req);
                        listener.on_handle(_req, new CLResponse(_req.socket.getOutputStream()));
                    }catch (InterruptedException ex){
                        throw ex;
                    }catch (Exception ex){}
                    _req.socket.close();
                    if(!fixed)break;
                }catch (InterruptedException ex){
                    break;
                }catch (Exception ex){
                    CL.CLOGE("worker error:"+ex.toString(),ex);
                }finally {
                    if(!_waitting) {
                        int _count = real_work_count.decrementAndGet();
                        CL.CLOGI("<<<<<< worker count:" + _count);
                    }
                }
            }
//            _tc=real_thread_count.decrementAndGet();
//            CL.CLOGI("--thread count:" + _tc);
        }

        private void parse_request(InputStream is,CLRequest req)throws Exception{
            boolean _parse_first=false;
            String _s;
            while ((_s=read_line(is))!=null){
//                CL.CLOGI(_s);
                if(!_parse_first){
                    String[] _strs=_s.split(" ");
                    if(_strs==null || _strs.length!=3)throw new RuntimeException("bad request");
                    _parse_first=true;
                    req.method=_strs[0];
                    req.uri= URLDecoder.decode(_strs[1],"utf8");
                    if(req.uri.contains("?")) {
                        int _at = req.uri.indexOf("?");
                        if (_at >= req.uri.length() - 1) continue;
                        req.original_args = req.uri.substring(_at + 1);
                        req.uri_path = req.uri.substring(0, _at);
                    }else req.uri_path =req.uri;
                    if(req.uri_path!=null && req.uri_path.length()>2 && !req.uri_path.contains(".")){
                        if(req.uri_path.charAt(req.uri_path.length()-1)=='/')req.uri_path=req.uri_path.substring(0,req.uri_path.length()-1);
                    }
                    if(req.uri_path !=null && req.uri_path.length()>3 && req.uri_path.contains(".")){
                        if(req.uri_path.charAt(req.uri_path.length()-1)=='/')req.uri_path=req.uri_path.substring(0,req.uri_path.length()-1);
                        boolean _ok=false;
                        int _index_last_dot=req.uri_path.lastIndexOf('.');
                        if(_index_last_dot==req.uri_path.length()-1){
                            _ok=true;
                        }
                        if(!_ok) {
                            int _index_last_f = req.uri_path.lastIndexOf('/');
                            if(_index_last_dot<_index_last_f){
                                _ok=true;
                            }
                            if(!_ok){
                                if(_index_last_dot-_index_last_f==1){
                                    _ok=true;
                                }
                                if(!_ok){
                                    if(_index_last_f==0){
                                        String _ppp="/";
                                        String _fff=req.uri_path.substring(1);
                                        req.uri_path=_ppp;
                                        req.uri_file_name=_fff;
                                    }else {
                                        String _ppp = req.uri_path.substring(0, _index_last_f);
                                        String _fff = req.uri_path.substring(_index_last_f+1);
                                        req.uri_path = _ppp;
                                        req.uri_file_name = _fff;
                                    }
                                }
                            }
                        }

                    }

                    req.version=_strs[2];
//                    CL.CLOGI("Method:"+req.method+" uri:"+req.uri+" version:"+req.version);
                    if(req.method.toUpperCase().equals("GET")){
                        if(req.original_args!=null){
                            String[] _as=req.original_args.split("&");
                            for(int i=0;i<_as.length;++i) {
                                String _tmp=_as[i];
                                if(_tmp.contains("=")){
                                    if(_tmp.charAt(0)=='=')continue;
                                    else if(_tmp.charAt(_tmp.length()-1)=='=')continue;
                                    String[] _kv=_tmp.split("=");
//                                    CL.CLOGI("arg key:"+_kv[0]+" value:"+_kv[1]);
                                    req.args.put(_kv[0],_kv[1]);
                                }
                            }
                        }
                    }
                }else{//parser header
                    int _at_1=_s.indexOf(':');
                    String _key=URLDecoder.decode(_s.substring(0,_at_1),"utf8");
                    int _at_2=_s.indexOf(' ');
                    String _v=URLDecoder.decode(_s.substring(_at_2+1),"utf8");
//                    CL.CLOGI("key:"+_key+" value:"+_v);
                    req.header.put(_key,_v);
                }
            }
            //parser range
            if(req.header.containsKey("Range")) {
                long _range_start=0,_range_end=0;
                String _range=req.header.get("Range");
                CL.CLOGI("Range:"+_range);
                if(_range.contains("bytes=")){
                    _range=_range.replace("bytes=","");
                    if(_range.contains("-")){
                        String[] _s_e=_range.split("-");
                        if(_s_e.length==1){
                            _range_start=Long.parseLong(_s_e[0]);
                            _range_end=Long.MAX_VALUE;
                        }else if(_s_e.length==2){
                            _range_start=Long.parseLong(_s_e[0]);
                            _range_end=Long.parseLong(_s_e[1]);
                        }
                        CL.CLOGI("start range:" + _range_start + "  end range:" + _range_end);
                        req.request_range=new CLRequest.ReqRange();
                        req.request_range.range_start=_range_start;
                        req.request_range.range_end=_range_end;
                    }
                }
            }

            if(_s==null && req.method!=null && req.method.toUpperCase().equals("POST")
                    && req.header.containsKey("Content-Type")
                    && req.header.get("Content-Type").contains("application/x-www-form-urlencoded")
                    && req.header.containsKey("Content-Length")){
                String _length=req.header.get("Content-Length");
                int _len=Integer.parseInt(_length);
                if(_len>0){
                    byte[] _bb=new byte[_len];
                    new DataInputStream(is).readFully(_bb);
                    String _body=URLDecoder.decode(new String(_bb),"utf8");
                    CL.CLOGI("post body:"+_body);
                    String[] _as=_body.split("&");
                    for(int i=0;i<_as.length;++i) {
                        String _tmp=_as[i];
                        if(_tmp.contains("=")){
                            if(_tmp.charAt(0)=='=')continue;
                            else if(_tmp.charAt(_tmp.length()-1)=='=')continue;
                            String[] _kv=_tmp.split("=");
//                            CL.CLOGI("arg key:"+_kv[0]+" value:"+_kv[1]);
                            req.args.put(_kv[0],_kv[1]);
                        }
                    }
                }
            }
        }
        private String read_line(InputStream is) throws IOException {
            boolean _r=false;
            int _c;
            StringBuffer _sb=new StringBuffer();
            while ((_c=is.read())!=-1){
                if(_c==13){
                    _r=true;
                    continue;
                }
                if(_c==10 && _r){
                    if(_sb.length()==0)return null;
                    return _sb.toString();
                }
                _sb.append((char) _c);
            }
            throw new RuntimeException("request illegal");
        }
    }


}
