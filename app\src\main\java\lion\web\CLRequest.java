package lion.web;

import java.net.Socket;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by leron on 2016/8/19.
 */
public class CLRequest {

    protected Socket socket;

    public String method;
    public String uri;
    public String uri_path,uri_file_name;
    public String version;
    public Map<String,String> header=new HashMap<>();
    public Map<String,String> args=new HashMap<>();
    public ReqRange request_range;

    protected String original_args;

    public static class ReqRange{
        public long range_start;
        public long range_end;
    }
}
