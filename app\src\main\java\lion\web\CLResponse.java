package lion.web;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by leron on 2016/8/23.
 */
public class CLResponse {

    public enum STATUS_CODE {
        Code_200,Code_206,Code_400,Code_500;

        private static String Get_code(STATUS_CODE code){
            if(code==Code_200)return "200";
            else if(code==Code_206)return "206";
            else if(code==Code_400)return "400";
            else if(code==Code_500)return "500";
            return "500";
        }
        private static String Get_code_describe(STATUS_CODE code){
            if(code==Code_200)return "OK";
            else if(code==Code_206)return "Partial Content";
            else if(code==Code_400)return "Bad Request";
            else if(code==Code_500)return "Internal Server Error";
            return "Error";
        }
    }


    private OutputStream os;
    private STATUS_CODE status_code;
    private HashMap<String,String> headers=new HashMap<>();
    private byte[] data_bytes=null;
    private File data_file=null;

    protected CLResponse(OutputStream os){
        this.os=os;
        status_code=STATUS_CODE.Code_200;
    }

    public void set_status_code(STATUS_CODE code){
        status_code=code;
    }
    public void set_content_type(String content_type){
        headers.put("Content-Type",content_type);
    }
    public void set_content_length(String content_length){
        headers.put("Content-Length",content_length);
    }
    public void set_content_range(long start,long end,long total_length){
        headers.put("Content-Range","bytes "+start+"-"+end+"/"+total_length);
    }
    public void set_cookie(String cookie){
        headers.put("Set-Cookie",cookie);
    }
    public void add_header(String key,String value){
        if(key==null || key.trim().isEmpty() || value==null || value.trim().isEmpty())return;
        headers.put(key,value);
    }
    public void set_data(byte[] data){
        this.data_file=null;
        this.data_bytes=data;
    }
    public void set_data(File data){
        this.data_bytes=null;
        this.data_file=data;
    }

    public OutputStream get_output_stream(){
        return os;
    }

    public void do_response()throws Exception{
        StringBuffer _sb=new StringBuffer();
        _sb.append("HTTP/1.1 ").append(STATUS_CODE.Get_code(status_code));
        _sb.append(" ").append(STATUS_CODE.Get_code_describe(status_code));
        _sb.append("\r\n");

        if(!headers.containsKey("Server")){
            headers.put("Server", "lion");
        }
        if(!headers.containsKey("Content-Length")){
            if(data_bytes!=null){
                headers.put("Content-Length",String.valueOf(data_bytes.length));
            }else if(data_file!=null){
                if(data_file.exists())headers.put("Content-Length",String.valueOf(data_file.length()));
            }
        }
        for(Map.Entry<String,String> item:headers.entrySet()){
            _sb.append(item.getKey()).append(": ").append(item.getValue()).append("\r\n");
        }
        _sb.append("\r\n");
        os.write(_sb.toString().getBytes("utf8"));
        if(data_bytes!=null)os.write(data_bytes);
        else if(data_file!=null && data_file.exists()){
            InputStream _is=new FileInputStream(data_file);
            byte[] _buff=new byte[1024*64];
            int _c;
            while ((_c=_is.read(_buff))!=-1){
                os.write(_buff,0,_c);
            }
        }

    }
    public void do_redirect(String url)throws Exception{
        StringBuffer _sb=new StringBuffer();
        _sb.append("HTTP/1.1 ").append("302");
        _sb.append(" ").append("Internal Redirect");
        _sb.append("\r\n");
        _sb.append("Server").append(": ").append("lion").append("\r\n");
        _sb.append("Location").append(": ").append(url).append("\r\n");
        _sb.append("\r\n");
        os.write(_sb.toString().getBytes("utf8"));
    }
}
