package lion.widget;

import android.content.Context;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ViewFlipper;

import lion.CLCallback;

public class C<PERSON>lipper extends ViewFlipper{

	public interface EventListener{
		void on_hide_over();
		void on_resume_begin();
		void on_resume_end();
		void on_back();
	}

	
	public Handler handler=new Handler();
	private TranslateAnimation anim_left_in, anim_out_left, anim_right_in,anim_out_right;
	private boolean ih_in_animation;
	private Animation.AnimationListener listen_anim = new Animation.AnimationListener() {
		public void onAnimationStart(Animation animation) {
			ih_in_animation = true;
		}
		public void onAnimationRepeat(Animation animation) {
		}
		public void onAnimationEnd(Animation animation) {
			ih_in_animation = false;
			setInAnimation(null);
			setOutAnimation(null);
			if(CLFlipper.this.getChildCount()>0){
				if(state_next && getChildCount()>1){
					View _v=getChildAt(getChildCount()-2);
					if(_v instanceof EventListener){
						((EventListener)_v).on_hide_over();
					}
					View _v2=getChildAt(getChildCount()-1);
					if(_v2 instanceof EventListener){
						((EventListener)_v2).on_resume_end();
					}
				}
				else if(!state_next && getChildCount()>0){
					View _v2=getChildAt(getChildCount()-1);
					if(_v2 instanceof EventListener){
						((EventListener)_v2).on_resume_end();
					}
				}
			}
		}
	};
	
	private boolean state_next;
	public CLCallback.CB cber_back;
	
	public CLFlipper(Context context, CLCallback.CB cber_back) {
		super(context);
		this.cber_back=cber_back;

		this.setMotionEventSplittingEnabled(false);
		this.setFocusable(true);
		this.setFocusableInTouchMode(true);
		this.requestFocus();

		anim_left_in = new TranslateAnimation(Animation.RELATIVE_TO_SELF,-1.0f, Animation.RELATIVE_TO_SELF, 0,
				Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0);
		anim_left_in.setDuration(400);
		anim_left_in.setAnimationListener(listen_anim);
		anim_right_in = new TranslateAnimation(Animation.RELATIVE_TO_SELF,1.0f, Animation.RELATIVE_TO_SELF, 0,
				Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0);
		anim_right_in.setDuration(400);
		anim_right_in.setAnimationListener(listen_anim);
		anim_out_left = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0,Animation.RELATIVE_TO_SELF, -1.0f, 
				Animation.RELATIVE_TO_SELF,	0, Animation.RELATIVE_TO_SELF, 0);
		anim_out_left.setDuration(400);
		anim_out_right = new TranslateAnimation(Animation.RELATIVE_TO_SELF, 0,Animation.RELATIVE_TO_SELF, 1.0f,
				Animation.RELATIVE_TO_SELF,	0, Animation.RELATIVE_TO_SELF, 0);
		anim_out_right.setDuration(400);
	}

	@Override
	public boolean onInterceptTouchEvent(MotionEvent ev) {
		if (ih_in_animation)return true;
		return super.onInterceptTouchEvent(ev);
	}

	@Override
	public boolean dispatchKeyEvent(KeyEvent event) {
		boolean _value = super.dispatchKeyEvent(event);
		if(!_value) {
			if(event.getAction()==MotionEvent.ACTION_UP &&
					event.getKeyCode()==KeyEvent.KEYCODE_BACK) {
				if(ih_in_animation)return true;
				if(this.getChildCount()>1){
					View _v=this.getChildAt(this.getChildCount()-1);
					if(_v instanceof EventListener){
						((EventListener)_v).on_back();
						return true;
					}
				}
				if(!on_back()){
					if(cber_back==null)return false;
					else{
						cber_back.on_callback();
						return true;
					}
				}
				return true;
			}
		}
		return _value;
	}

	public boolean go_next(View view){
		if(this.getCurrentView()==view)return false;
		ih_in_animation=true;
		this.addView(view);
		this.setInAnimation(anim_right_in);
		this.setOutAnimation(anim_out_left);
		this.showNext();
		state_next=true;
		return true;
	}
	
	public boolean go_previously(View view){
		if(this.getCurrentView()==view){
			ih_in_animation=true;
			this.setInAnimation(anim_left_in);
			this.setOutAnimation(anim_out_right);
			this.showPrevious();
			this.removeView(view);
			state_next=false;
			if(getChildCount()>0){
				View _v2=getChildAt(getChildCount()-1);
				if(_v2 instanceof EventListener){
					((EventListener)_v2).on_resume_begin();
				}
			}
			return true;
		}
		return false;
	}
	
	public void remove(View view){
		this.removeView(view);
	}
	
	public boolean on_back(){
		return false;
	}
}
