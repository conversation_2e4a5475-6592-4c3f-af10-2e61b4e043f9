package lion.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Movie;
import android.graphics.Paint;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import java.util.ArrayList;

import lion.CL;

/**
 * Created by leron on 2016/7/28.
 */
public class CLGif extends FrameLayout{

    private Context cc;
    private ViewPager pager;
    private AdapterForGif adapter;
    private ArrayList<String> datas;
    private int show_index=-1;



    public CLGif(Context context) {
        super(context);
        this.cc=context;

        pager=new ViewPager(cc);
        pager.setLayoutParams(CL.Get_FLLP(CL.MP,CL.MP, Gravity.FILL));
        pager.addOnPageChangeListener(listener_pager);
        adapter=new AdapterForGif();
        pager.setAdapter(adapter);
        this.addView(pager);
    }

    public void set_data(ArrayList<String> paths,int index){
        this.datas=paths;
        show_index=index;
        if(this.datas==null)show_index=-1;
        else {
            if(show_index<0)show_index=0;
            else if(show_index>datas.size()-1)show_index=datas.size()-1;
        }
        adapter.notifyDataSetChanged();
        if(index!=-1)pager.setCurrentItem(index);

        CL.CLOGI("set gifs size:"+paths.size());
    }

    private ViewPager.OnPageChangeListener listener_pager=new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        }

        @Override
        public void onPageSelected(int position) {
            CL.CLOGI("on page selected:"+position);
        }

        @Override
        public void onPageScrollStateChanged(int state) {
            CL.CLOGI("on page scroll state changed:"+state);
        }
    };


    private class AdapterForGif extends PagerAdapter{

        @Override
        public int getCount() {
            if(datas==null)return 0;
            return datas.size();
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View)object);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            ViewForGif _v=new ViewForGif(cc,datas.get(position));
            container.addView(_v);
            return _v;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }
    }




    private class ViewForGif extends View {

        public ViewForGif(Context context, final String path) {
            super(context);
            this.setLayerType(LAYER_TYPE_SOFTWARE,null);
            p=new Paint(Paint.ANTI_ALIAS_FLAG);

            if(movie_gif==null && !loading){
                new Thread(){
                    @Override
                    public void run() {
                        movie_gif=Movie.decodeFile(path);
                        if(!discard)postInvalidate();
                        else movie_gif=null;
                    }
                }.start();
            }
        }

        private long movie_start =0;
        private Movie movie_gif;
        private boolean loading=false;
        private boolean discard=false;


        private Paint p;
        private float ball_d=CL.DIP2PX(10);
        private float ball_r=ball_d/2;
        private float ball_zoom=ball_r;
        private float ball_step=0.5f;
        private void draw_waiting(Canvas canvas){
            if(ball_zoom>ball_d)ball_step=-0.5f;
            else if(ball_zoom<=ball_r)ball_step=0.5f;
            ball_zoom+=ball_step;
            this.p.setStyle(Paint.Style.STROKE);
            this.p.setStrokeWidth(2);
            this.p.setColor(Color.RED);
            canvas.drawCircle(canvas.getWidth()/2, canvas.getHeight()/2, ball_r+ball_zoom, this.p);
            invalidate();
        }

        @Override
        public void draw(Canvas canvas) {
            if(movie_gif!=null){
                float _xs=(float)this.getWidth()/(float)movie_gif.width();
                float _ys=(float)this.getHeight()/(float)movie_gif.height();
                float _ss=(_xs < _ys ? _xs : _ys);
                canvas.save();
                canvas.scale(_ss,_ss,this.getWidth()/2,this.getHeight()/2);
                long curTime=android.os.SystemClock.uptimeMillis();
                if (movie_start == 0) {
                    movie_start = curTime;
                }
                int duraction = movie_gif.duration();
                if(duraction==0)duraction=1000;
                int relTime = (int) ((curTime- movie_start)%duraction);
                movie_gif.setTime(relTime);
                movie_gif.draw(canvas, (this.getWidth()-movie_gif.width())/2,(this.getHeight()-movie_gif.height())/2);
                canvas.restore();
                invalidate();
            }else draw_waiting(canvas);
        }

        @Override
        protected void onAttachedToWindow() {
            super.onAttachedToWindow();
            discard=false;
            CL.CLOGI("on attache to window");
        }

        @Override
        protected void onDetachedFromWindow() {
            super.onDetachedFromWindow();
            CL.CLOGI("on detached from window");
            discard=true;
            movie_gif=null;
        }
    }
}
