package lion.widget;



import androidx.appcompat.app.AppCompatDialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import java.lang.reflect.Field;

public class CLGuideDialog extends AppCompatDialogFragment {


    @Override
    public void show(FragmentManager manager, String tag) {
        try {
            manager.executePendingTransactions();
            if (isAdded()) {
                return;
            }

            Class clz = this.getClass();

            try {
                Field mDismissed = clz.getDeclaredField("mDismissed");
                mDismissed.setAccessible(true);
                mDismissed.set(this, false);
            } catch (NoSuchFieldException e) {
            } catch (IllegalAccessException e) {
            }
            try {
                Field mShownByMe = clz.getDeclaredField("mShownByMe");
                mShownByMe.setAccessible(true);
                mShownByMe.set(this, true);
            } catch (NoSuchFieldException e) {
            } catch (IllegalAccessException e) {
            }

            FragmentTransaction ft = manager.beginTransaction();
            ft.add(this, tag);
            ft.commitAllowingStateLoss();
        } catch (Exception ex) {}
    }
}
