package lion.widget;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Align;
import android.graphics.Paint.FontMetrics;
import android.graphics.Paint.Style;
import android.graphics.RectF;
import android.os.Handler;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;

import lion.CL;

public class CLImager extends View{
	
	public static final int Type_Center=10001;//图像居中自适应
	public static final int Type_Full=10002;	  //图像最大化居中
	public static final int Type_Page_Number_Point=10003;//用画点表示
	public static final int Type_Page_Number_Digit=10004;//画数字
	public static final int Type_Memory_MIN=10005;
	public static final int Type_Memory_MED=10006;
	public static final int Type_Memory_MAX=10007;
	private static final int Type_Memory_MIN_v=1024*1024*5;//5兆
	private static final int Type_Memory_MED_v=1024*1024*18;//18兆
	private static final int Type_Memory_MAX_v=1024*1024*35;//35兆;
	
	private final float c_r= CL.DIP2PX(6.0f);
	private int Maximum_Velocity;

	private Context cc;
	private Paint main_paint;//宿主view专用
	private int window_width,window_height;//窗口大小
	private Layer layer_one,layer_two;				//交替层
	private VelocityTracker tracker;                //手势记录
	
	private boolean can_loop=false;				//是否可以循环
	private boolean can_auto_loop=false;		//是否可以自动循环
	private boolean can_zoom=false;		    //是否可以缩放
	private boolean can_delete=false;			//是否可以删除
	private int page_number_type=0;		        //画页码类型
	
	private float offset;									//偏移量
	private float monopoly_x,monopoly_y;		//独占touch事件中的xy轴记数
	private int page_count=-1;						//总的页码
	private int in_page=0;                             //窗口页码
	private int crt_page=0;								//当前页码
	private float down_x,down_y;					//触摸点
	private Layer layer_focus;							//获取触摸焦点的层
	private Layer layer_zoom;							//获取缩放的层
	private float last_zoom_distance;				//缩放的最后距离
	private int last_average_x,last_average_y; //缩放的中心点
	private boolean auto_move_right=true;  //自动滑动的方向
	private boolean on_deleting=false;			//是否正在删除层中
	private Layer layer_delete_main,layer_delete_secondary;  //删除时的主次层
	private int image_draw_type=Type_Center;			//画图类型
			
	
	private CLAnimation anim_window=new CLAnimation();               //用于迁移层
	private CLAnimationXY anim_delete=new CLAnimationXY();			 //用于删除层
	
	private ThreadLoader thread_loader=new ThreadLoader();
	private ThreadDownloader thread_downloader=new ThreadDownloader();
	private ArrayList<LayerData> datas;
	
	private OnEventer listener_eventer;
	private OnMonopolyTouchEvent listener_monopoly;
	
	private int last_in_page=0;
	private Handler handler=new Handler(){
		public void handleMessage(android.os.Message msg) {
			if(msg.what==1001){//延迟开始
				handler.sendEmptyMessageDelayed(1002, 5000);
			}
			else if(msg.what==1002){//auto操作
				layer_go_auto_fly();
			}
			else if(msg.what==1003){//pause
				last_in_page=in_page;
				page_count=-1;
				offset=0;
				in_page=0;
				crt_page=0;
				handler.removeMessages(1001);
				handler.removeMessages(1002);
				anim_window.stop();
				anim_delete.stop();
				layer_one.reset();
				layer_two.reset();
				thread_loader.stop_load();
				thread_loader.clear_all_resource(datas);
			}
			else if(msg.what==1004){//resume
				page_count=datas.size();
				in_page=last_in_page;
				crt_page=last_in_page;
				if(can_auto_loop)handler.sendEmptyMessage(1001);
				invalidate();
			}
		};
	};
	
	
	
	public CLImager(Context context, int memory) {
		super(context);
		this.cc=context;
		if(memory==Type_Memory_MIN)thread_loader.Max_Memory=Type_Memory_MIN_v;
		else if(memory==Type_Memory_MAX)thread_loader.Max_Memory=Type_Memory_MAX_v;
		else thread_loader.Max_Memory=Type_Memory_MED_v;
		thread_loader.start();
		thread_downloader.start();
		
		main_paint=new Paint();
		main_paint.setAntiAlias(true);
		main_paint.setTextSize(CL.SP2PX(12));
		main_paint.setTextAlign(Align.CENTER);

		Maximum_Velocity= ViewConfiguration.get(cc).getScaledMaximumFlingVelocity();
		tracker=VelocityTracker.obtain();
		
		layer_one=new Layer();
		layer_two=new Layer();
	}
	
	@Override
	public boolean onTouchEvent(MotionEvent event) {
		//拦截事件,初步筛选
		if(page_count<=0)return false;
		if(on_deleting)return false;

		float _x=event.getX();
		float _y=event.getY();
		invalidate();
		if(event.getAction()==MotionEvent.ACTION_DOWN){
			if(this.listener_monopoly!=null)this.listener_monopoly.on_monopoly_event();
			down_x=_x;
			down_y=_y;
			tracker.clear();
			tracker.addMovement(event);
			layer_zoom=null;
			monopoly_y=0;
			monopoly_x=0;
			//中止动画
			anim_window.stop();
			handler.removeMessages(1001);
			handler.removeMessages(1002);
			
			//交替层的down事件
			if(_x>layer_one.x && _x<layer_one.x+layer_one.width){
				layer_focus=layer_one;
				layer_one.on_down(_x-layer_one.x, _y-layer_one.y);
			}
			else if(_x>layer_two.x && _x<layer_two.x+layer_two.width){
				layer_focus=layer_two;
				layer_two.on_down(_x-layer_two.x, _y-layer_two.y);
			}
			else layer_focus=null;
		}
		else if(event.getAction()==MotionEvent.ACTION_MOVE){
			tracker.addMovement(event);

			if(can_zoom && event.getPointerCount()>1){//zoom
				down_x=_x;
				down_y=_y;
				float _x1=event.getX(0);
				float _y1=event.getY(0);
				float _x2=event.getX(1);
				float _y2=event.getY(1);
				if(layer_zoom==null){
					if(_x1>layer_one.x && _x1<layer_one.x+layer_one.width
							&& _x2>layer_one.x && _x2<layer_one.x+layer_one.width){
						layer_zoom=layer_one;
//						App.CLOG("in layer one");
					}
					else if(_x1>layer_two.x && _x1<layer_two.x+layer_two.width
							&& _x2>layer_two.x && _x2<layer_two.x+layer_two.width){
						layer_zoom=layer_two;
//						App.CLOG("in layer two");
					}
					if(layer_zoom!=null){
						last_average_x=(int)(((_x1-layer_zoom.x)+(_x2-layer_zoom.x))/2.0f);
						last_average_y=(int)(((_y1-layer_zoom.y)+(_y2-layer_zoom.y))/2.0f);
						last_zoom_distance=-10000;
					}
				}
				else{
					float _xx=_x1-_x2;
					float _yy=_y1-_y2;
					float _distance=(float)Math.sqrt(_xx*_xx+_yy*_yy);
					if(last_zoom_distance!=-10000){
						layer_zoom.on_zoom((_distance-last_zoom_distance)/100.0f, last_average_x, last_average_y);
					}
					last_zoom_distance=_distance;
				}
			}
			else{//单点
				if(layer_zoom!=null){
					down_x=_x;
					down_y=_y;
					layer_zoom.on_zoom_over();
					layer_zoom=null;
				}
				//将move事件传给焦点层,如果不处理那就拿回控制权
				float _xx=_x-down_x;
				float _yy=_y-down_y;
				down_x=_x;
				down_y=_y;
				if(layer_focus!=null){
					if(layer_focus.on_move(_xx, _yy)){
						return true;
					}
					else layer_focus=null;
				}
				offset+=_xx;
				if(this.listener_monopoly!=null){
					monopoly_x+=_xx;
					monopoly_y+=_yy;
					float _xxx=Math.abs(monopoly_x);
					float _yyy=Math.abs(monopoly_y);
					if(_yyy > 12.0f && _yyy>_xxx){
						this.listener_monopoly.on_discard_event();
					}
				}
			}
			
		}
		else if(event.getAction()==MotionEvent.ACTION_UP || event.getAction()==MotionEvent.ACTION_CANCEL){
			if(this.listener_monopoly!=null)this.listener_monopoly.on_discard_event();
			//开启动画
			if(can_auto_loop)handler.sendEmptyMessage(1001);
			if(layer_zoom!=null){
				layer_zoom.on_zoom_over();
				layer_zoom=null;
			}
			//计算力道
			tracker.addMovement(event);
			tracker.computeCurrentVelocity(1000, Maximum_Velocity);
			float _xv=tracker.getXVelocity();
			float _yv=tracker.getYVelocity();
			//fly
			if(layer_focus!=null && layer_focus.on_up(_x-layer_focus.x, _y-layer_focus.y,_xv, _yv)){//层里面fly
				return true;
			}
			//控制窗口fly
			if(layer_go_fly(_xv,_yv)){}
			else{
				//归位
				layer_go_back();
			}
			layer_focus=null;
		}
		return true;
	}

	@Override
	public void draw(Canvas canvas) {
		if(page_count<=0)return;
		canvas.drawColor(Color.WHITE);
		//层动画
		if(anim_window.have_next()){
			offset+=anim_window.get_next();
			if(!anim_window.have_next()){
                offset=anim_window.to;
            }
			invalidate();
		}
		
		//删除层动画
		if(can_delete && on_deleting){
			if(anim_delete.have_next()){
				if(layer_delete_secondary!=null){
					layer_delete_secondary.x+=anim_delete.get_next_x();
					canvas.save();
					canvas.translate(layer_delete_secondary.x, layer_delete_secondary.y);
					if(canvas.clipRect(0, 0, layer_delete_secondary.width, layer_delete_secondary.height)){
						layer_delete_secondary.on_draw(canvas);
					}
					canvas.restore();
				}
				if(layer_delete_main!=null){
					layer_delete_main.y+=anim_delete.get_next_y();
					canvas.save();
					canvas.translate(layer_delete_main.x, layer_delete_main.y);
					if(canvas.clipRect(0, 0, layer_delete_main.width, layer_delete_main.height)){
						layer_delete_main.on_draw(canvas);
					}
					canvas.restore();
				}
			}
			else{
				layer_one.y=0;layer_one.x=0;
				layer_two.y=0;layer_two.x=0;
				offset=0;
				on_deleting=false;
				
				int _delete_index=layer_delete_main.page_index;
				on_delete_over(_delete_index);
				if(page_count>0){
					int _crt_page=(_delete_index>=page_count-1?page_count-1:_delete_index);
					layer_one.in_page(_crt_page);
					layer_two.reset();
					in_page=_crt_page;
					canvas.save();
					layer_one.x=offset;
					canvas.translate(layer_one.x, layer_one.y);
					if(canvas.clipRect(0, 0, layer_one.width, layer_one.height)){
						layer_one.on_draw(canvas);
					}
					canvas.restore();
				}
				return;
			}
			invalidate();
			return;
		}
		
		int _in_page=in_page;
		//计算页码
		if(offset>=this.window_width){
			--in_page;
			offset%=this.window_width;
		}
		else if(offset<=-this.window_width){
			++in_page;
			offset%=this.window_width;
		}
		//是否到边界
		if(!can_loop || page_count==1){//不能循环和数据只有1个的时候
			if((in_page==0 && offset>0) || (in_page==page_count-1 && offset<0)){
				offset=0;
			}
		}
		else if(can_loop){
			if(in_page<0)in_page=page_count-1;
			else if(in_page>page_count-1)in_page=0;
		}
		
		canvas.save();
		layer_one.x=offset;
		canvas.translate(layer_one.x, layer_one.y);
		if(canvas.clipRect(0, 0, layer_one.width, layer_one.height)){
			int _next_page=in_page;
			if(_next_page<0)_next_page=page_count-1;
			else if(_next_page>page_count-1)_next_page=0;
			if(layer_one.page_index!=_next_page)layer_one.in_page(_next_page);
			layer_one.on_draw(canvas);
		}
		canvas.restore();
		canvas.save();
		layer_two.x=(offset>=0?offset-this.window_width:offset+this.window_width);
		canvas.translate(layer_two.x, layer_two.y);
		if(canvas.clipRect(0, 0, layer_two.width, layer_two.height)){
			int _next_page=(offset>=0?in_page-1:in_page+1);
			if(_next_page<0)_next_page=page_count-1;
			else if(_next_page>page_count-1)_next_page=0;
			if(layer_two.page_index!=_next_page)layer_two.in_page(_next_page);
			layer_two.on_draw(canvas);
		}
		canvas.restore();
		
		//页码改变回调
		if(this.listener_eventer!=null && _in_page!=in_page)this.listener_eventer.on_page_change(in_page);
		
		//计算显示页码
		int _crt_page=-1;
		if(offset>=this.window_width/2){
			_crt_page=in_page-1;
		}
		else if(offset <this.window_width/2 && offset>-this.window_width/2){
			_crt_page=in_page;
		}
		else if(offset<=-this.window_width/2){
			_crt_page=in_page+1;
		}
		if(_crt_page<0)_crt_page=page_count+_crt_page;
		else if(_crt_page>page_count-1)_crt_page=page_count-_crt_page;
		if(crt_page != _crt_page){
			crt_page=_crt_page;
		}
		//画页码
		if(this.page_number_type==Type_Page_Number_Digit 
				|| this.page_number_type==Type_Page_Number_Point)window_draw_page_number(canvas);
//		App.CLOG("offset:"+offset);
	}
	
	private float text_height=-1;
	private RectF rect_page=new RectF();
	private void window_draw_page_number(Canvas canvas){
		if(this.page_number_type==Type_Page_Number_Point && page_count<=12){
			float _ww=(page_count*2-1)*c_r;
			float _start_x=(this.window_width-_ww)/2;
			main_paint.setColor(0x33000000);
		    canvas.drawRect(0, this.getHeight()-3*c_r, this.window_width, this.window_height, main_paint);
			float _jg=1.2f*c_r;
			float _y=this.window_height-_jg-2;
//			canvas.drawRect(0, _jg, this.window_width, this.window_height-_jg, main_paint);
			for(int i=0,j=0;i<page_count*2-1;i+=2,++j){
				if(j==crt_page)main_paint.setColor(0xff5190d7);
				else main_paint.setColor(0xffaaaaaa);
				canvas.drawCircle(_start_x+i*c_r+c_r/2, _y, c_r/2, main_paint);
			}
		}
		else {
			String _str=(crt_page+1)+"/"+page_count;
			float _str_length=main_paint.measureText(_str);
			main_paint.setColor(0x66000000);//阴影
			float _tx=this.window_width/2;
			if(text_height==-1)text_height=CL.Get_DrawText_Height(main_paint);
			float _ty=this.window_height-text_height-8;
			rect_page.set(_tx-_str_length/2-16, _ty-6, _tx+_str_length/2+16, _ty+text_height+6);
			canvas.drawRoundRect(rect_page, 16, 16, main_paint);
			main_paint.setColor(Color.WHITE);
			canvas.drawText(_str, _tx, _ty+text_height, main_paint);
		}		
	}
	
	//层归位
	private void layer_go_back(){
//		App.CLOG("on go back");
		if(offset>=this.window_width/2){
			float _dis=this.window_width-offset;
			anim_window.reset_data(offset, this.window_width, (int)(600.0f*(_dis/(float)this.window_width)));
		}
		else if(offset > -this.window_width/2 && offset <this.window_width/2){
			float _dis=0;
			if(Math.abs(offset)<3.0f){
				offset=0;
			}
			else if(offset>0){
				_dis=offset;
				anim_window.reset_data(offset, 0, (int)(600.0f*(_dis/(float)this.window_width)));
			}
			else if(offset < 0){
				_dis=-offset;
				anim_window.reset_data(offset, 0, (int)(600.0f*(_dis/(float)this.window_width)));
			}
		}
		else if(offset <= -this.window_width/2){
			float _dis=this.window_width+offset;
			anim_window.reset_data(offset, -this.window_width, (int)(600.0f*(_dis/(float)this.window_width)));
		}
	}
	//层滑动
	private boolean layer_go_fly(float xv,float yv){
		if(!can_loop || page_count==1){
			if((in_page==0 && offset>0) || (in_page==page_count-1 && offset<0)){
				offset=0;
				return false;
			}
		}
		float _dis=0;
		if(xv<-1500.0f){//左滑
//			App.CLOG("左滑 <- 右进");
			if(offset > 0){
				_dis=offset;
				anim_window.reset_data(offset, 0, (int)(600.0f*(_dis/(float)this.window_width)));
			}
			else if(offset < 0){
				_dis=this.window_width+offset;
				anim_window.reset_data(offset, -this.window_width, (int)(600.0f*(_dis/(float)this.window_width)));
			}
		}
		else if(xv>1500.0f){//右滑
//			App.CLOG("右滑 -> 左进");
			if(offset > 0){
				_dis=this.window_width-offset;
				anim_window.reset_data(offset, this.window_width, (int)(600.0f*(_dis/(float)this.window_width)));
			}
			else if(offset < 0){
				_dis=-offset;
				anim_window.reset_data(offset, 0, (int)(600.0f*(_dis/(float)this.window_width)));
			}
		}
		else if(yv<-1500.0f){//上滑
//			App.CLOG("上滑");
			return false;
		}
		else if(yv>1500.0f){//下滑
//			App.CLOG("下滑");
			return false;
		}
		else return false;
		return true;
	}
	//层auto滑动
	private void layer_go_auto_fly(){
		if(can_loop && page_count>1){//循环滚动
			if(offset==0){
				anim_window.reset_data(0, -this.window_width, 400);
				invalidate();
			}
			handler.sendEmptyMessage(1001);
		}
		else if(!can_loop && page_count>1){//来回折返滚动
			if(offset==0){
				if(auto_move_right){
					if(crt_page >= page_count-1){
						auto_move_right=false;
					}
				}
				else{
					if(crt_page <= 0){
						auto_move_right=true;
					}
				}
				if(auto_move_right){
					anim_window.reset_data(offset, -this.window_width, 400);
				}
				else{
					anim_window.reset_data(offset, this.window_width, 400);
				}
				invalidate();
			}
			handler.sendEmptyMessage(1001);
		}
		else {
			//中止
			handler.removeMessages(1001);
			handler.removeMessages(1002);
		}
	}
	
	//层删除
	private void delete_layer(Layer layer){
		on_deleting=true;
		layer_delete_main=layer;
		layer_delete_secondary=(layer_delete_main==layer_one?layer_two:layer_one);
		int _after_delete_in_page=layer_delete_main.page_index+1;
		if(_after_delete_in_page>page_count-1)_after_delete_in_page=layer_delete_main.page_index-1;
		if(_after_delete_in_page>=0){//双层动画
			layer_delete_secondary.in_page(_after_delete_in_page);
			layer_delete_secondary.x=layer_delete_main.x+(_after_delete_in_page>layer_delete_main.page_index?this.window_width:-this.window_width);
			anim_delete.reset_data(layer_delete_secondary.x, 0, 0,	this.window_height, 600);
		}
		else{//单层动画
			layer_delete_secondary=null;
			anim_delete.reset_data(0, 0, 0,	this.window_height, 600);
		}
	}
	private void on_delete_over(int index){
//		App.CLOG("要删除数据:"+index);
		thread_loader.delete_load(datas.remove(index));
		page_count=datas.size();
		if(this.listener_eventer!=null)this.listener_eventer.on_data_delete(index);
		if(page_count==0 && this.listener_eventer!=null)this.listener_eventer.on_data_empty();
	}
	
	private  void update_by_loader(LayerData item){
		layer_one.update_layer_data(item);
		layer_two.update_layer_data(item);
	}
	private void update_by_downloader(LayerData item){
		layer_one.update_layer_data(item);
		layer_two.update_layer_data(item);
	}
	private void update_download_scale(LayerData item){
		if(layer_one.crt_data!=null && layer_one.crt_data==item)postInvalidate();
		else if(layer_two.crt_data!=null && layer_two.crt_data==item)postInvalidate();
	}
	
	private void reset(){
		page_count=-1;
		offset=0;
		in_page=0;
		crt_page=0;
		handler.removeMessages(1001);
		handler.removeMessages(1002);
		anim_window.stop();
		anim_delete.stop();
		layer_one.reset();
		layer_two.reset();
		thread_loader.stop_load();
		thread_loader.clear_all_resource(datas);
		datas=null;
	}
	
	@Override
	protected void onSizeChanged(int w, int h, int oldw, int oldh) {
		this.window_width=w;
		this.window_height=h;
		layer_one.on_window_size_change( w, h);
		layer_two.on_window_size_change( w, h);
		layer_one.reset();
		layer_two.reset();
		//重置所有数据的图像矩形区域
		if(datas!=null){
			for(int i=0;i<datas.size();++i){
				LayerData _item=datas.get(i);
				_item.anim_back.stop();
				_item.anim_back_zoom.stop();
				_item.anim_suit.stop();
				_item.rect_bitmap=null;
				_item.rect_bitmap_thum=null;
			}
		}
	}
	
	
	//对外调用方法
	public void set_data(ArrayList<DataItem> data,int show_index){
		reset();
		if(data!=null){
			this.datas=new ArrayList<LayerData>();
			for(int i=0;i<data.size();++i){
				LayerData _item=new LayerData();
				_item.data=data.get(i);
				this.datas.add(_item);
			}
			page_count=this.datas.size();
			in_page=show_index;
			crt_page=show_index;
			if(can_auto_loop)handler.sendEmptyMessage(1001);
			
//			App.CLOG("on set data:"+this.datas.size());
		}
		postInvalidate();
	}
	public void set_loop(boolean loop){
		this.can_loop=loop;
	}
	public void set_auto_loop(boolean loop){
		this.can_auto_loop=loop;
		if(can_auto_loop)handler.sendEmptyMessage(1001);
		else {
			handler.removeMessages(1001);
			handler.removeMessages(1002);
		}
	}
	public void set_can_zoom(boolean zoom){
		this.can_zoom=zoom;
	}
	public void set_can_delete(boolean delete){
		this.can_delete=delete;
	}
	public void set_show_page_number(int type){
		this.page_number_type=type;
	}
	public void set_image_type(int type){
		this.image_draw_type=type;
	}
	public void set_listen_eventer(OnEventer eventer){
		this.listener_eventer=eventer;
	}
	public void set_pause(){
		if(datas!=null && handler!=null)handler.sendEmptyMessage(1003);
	}
	public void set_resume(){
		if(datas!=null && handler!=null)handler.sendEmptyMessage(1004);
	}
	public void set_listen_monopoly(OnMonopolyTouchEvent eventer){
		this.listener_monopoly=eventer;
	}
	public void exit(){
		this.reset();
		this.thread_loader.exit();
		this.thread_downloader.exit();
	}
	
	
	
	//Layer//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	private class Layer{
		private Paint p;
		private float x,y;
		private int width,height;
		private int page_index=-1;
		
		private boolean touch_in_delete=false;
		private boolean touch_in_click=false;
		private float touch_down_x,touch_down_y;
		private float move_offset_x,move_offset_y;
		
		private CLAnimationDecelerate anim_deceler=new CLAnimationDecelerate();
		
		private LayerData crt_data;
		
		public Layer(){
			p=new Paint();
			p.setAntiAlias(true);
			p.setTextSize(CL.SP2PX(16));
			p.setTextAlign(Align.CENTER);
		}
		
		public void on_down(float x,float y){
			if(this.anim_deceler.hava_next())this.anim_deceler.stop();
			move_offset_x=0;
			move_offset_y=0;
			this.touch_down_x=x;
			this.touch_down_y=y;
			if(can_delete && whether_touch_in_delete(this.touch_down_x, this.touch_down_y))this.touch_in_delete=true;
			else this.touch_in_delete=false;
			touch_in_click=true;
//			App.CLOG(test_name+" on down   x:"+x+" y:"+y);
		}
		public boolean on_move(float x,float y){
			if(crt_data==null)return false;
			if(crt_data.anim_back.have_next() 
					|| crt_data.anim_back_zoom.have_next()
					|| crt_data.anim_suit.have_next())return true;
			move_offset_x+=x;
			move_offset_y+=y;
//			App.CLOG(test_name+" on move   x:"+this.move_offset_x+" y:"+this.move_offset_y);
			boolean _process_one=false;
			if(Math.abs(this.move_offset_x)<5 && Math.abs(this.move_offset_y)<5){
				_process_one=true;
			}
			else {
				_process_one=false;
				this.touch_in_delete=false;
				this.touch_in_click=false;
			}
			boolean _process_two=process_bitmap_move(x, y);
			return _process_one || _process_two;
		}
		public boolean on_up(float x,float y,float xv,float yv){
			if(crt_data==null)return false;
//			App.CLOG(test_name+" on up   x:"+x+" y:"+y);
			if(can_delete && this.touch_in_delete &&
					whether_touch_in_delete(x, y)){
//				App.CLOG("delete:"+this.page_index);
				delete_layer(this);
				return true;
			}
			else if(touch_in_click){
//				App.CLOG("on_layer_click:"+this.page_index);
				if(listener_eventer!=null)listener_eventer.on_item_click(this.page_index, crt_data.data);
				touch_in_click=false;
				return false;
			}
			return process_bitmap_fly(xv, yv);
		}
		public void on_zoom(float scale,int average_x,int average_y){
//			App.CLOG(test_name+" on zoom scale:"+scale+"    x:"+average_x+" y:"+average_y);
			touch_in_click=false;
			if(crt_data!=null && crt_data.rect_bitmap!=null){
				if(crt_data.anim_back.have_next() 
						|| crt_data.anim_back_zoom.have_next()
						|| crt_data.anim_suit.have_next())return;
				
				float _last_zoom=crt_data.zoom;
				crt_data.zoom+=scale;
				if(crt_data.zoom<0.6f)crt_data.zoom=0.6f;
				else if(crt_data.zoom > 8.0f)crt_data.zoom=8.0f;
				float _layer_x=average_x;
				float _layer_y=average_y;
				float _bitmap_x=_layer_x-crt_data.rect_bitmap.left;
				float _bitmap_y=_layer_y-crt_data.rect_bitmap.top;
				_bitmap_x/=_last_zoom;
				_bitmap_y/=_last_zoom;
				float _bitmap_xx=_bitmap_x*crt_data.zoom;
				float _bitmap_yy=_bitmap_y*crt_data.zoom;
				float _bitmap_ww=crt_data.bitmap_width*crt_data.zoom;
				float _bitmap_hh=crt_data.bitmap_height*crt_data.zoom;
				crt_data.rect_bitmap.set(_layer_x-_bitmap_xx,_layer_y-_bitmap_yy,
						_layer_x-_bitmap_xx+_bitmap_ww,_layer_y-_bitmap_yy+_bitmap_hh);
			}
		}
		public void on_zoom_over(){
//			App.CLOG("on zoom over");
			if(crt_data==null || crt_data.rect_bitmap==null)return;
			if(crt_data.anim_back.have_next() 
					|| crt_data.anim_back_zoom.have_next()
					|| crt_data.anim_suit.have_next())return;
			if(crt_data.zoom<1.0f){
				float _to_x=(this.width-crt_data.bitmap_width)/2.0f;
				float _to_y=(this.height-crt_data.bitmap_height)/2.0f;
				crt_data.anim_back.reset_data(crt_data.rect_bitmap.left, _to_x, crt_data.rect_bitmap.top, _to_y, 180);
				crt_data.anim_back_zoom.reset_data(crt_data.zoom, 1.0f, 180);
			}
			else if(crt_data.zoom>1.0f){
				float _x=this.crt_data.rect_bitmap.left;
				float _y=this.crt_data.rect_bitmap.top;
				float _w=this.crt_data.rect_bitmap.width();
				float _h=this.crt_data.rect_bitmap.height();
				
				if(_w>this.width && _h>this.height){
					//边角
					boolean _ok=false;
					if(_x >0){
						_x=0;
						_ok=true;
					}
					else if(_x+_w<this.width){
						_x=this.width-_w;
						_ok=true;
					}
					if(_y >0){
						_y=0;
						_ok=true;
					}
					else if(_y+_h<this.height){
						_y=this.height-_h;
						_ok=true;
					}
					if(_ok)this.crt_data.anim_suit.reset_data(this.crt_data.rect_bitmap.left, _x, this.crt_data.rect_bitmap.top, _y, 180);
				}
				else if(_w>this.width && _h<this.height){
					//高居中,左右贴边
					_y=(this.height-_h)/2;
					if(_x >0)_x=0;
					else if(_x+_w<this.width)_x=this.width-_w;
					this.crt_data.anim_suit.reset_data(this.crt_data.rect_bitmap.left, _x, 
							this.crt_data.rect_bitmap.top, _y, 180);
				}
				else if(_w<this.width && _h>this.height){
					//宽居中,上下贴边
					_x=(this.width-_w)/2;
					if(_y >0)_y=0;
					else if(_y+_h<this.height)_y=this.height-_h;
					this.crt_data.anim_suit.reset_data(this.crt_data.rect_bitmap.left, _x, 
							this.crt_data.rect_bitmap.top, _y, 180);
				}
			}
		}
		
		public void on_draw(Canvas canvas){
			if(this.crt_data!=null){
				if(this.crt_data.state==0 || this.crt_data.state==1 || this.crt_data.state==2){//等待
					draw_bitmap_thum(canvas);
					draw_waiting(canvas);
				}
				else if(this.crt_data.state==3){//画图片
					draw_bitmap(canvas);
				}
				else if(this.crt_data.state==4){
					draw_bitmap_thum(canvas);
					float _scale=(float)this.crt_data.download_scale/(float)crt_data.download_file_size;
					int _ppp=(int)(_scale*100.0f);
					
					FontMetrics _fm=this.p.getFontMetrics();
					float _r=(-_fm.top-_fm.bottom)*1.8f;
					p.setColor(Color.GRAY);
					p.setStyle(Style.FILL);
					canvas.drawCircle(this.width/2, this.height/2, _r, this.p);
					this.p.setColor(Color.WHITE);
					canvas.drawText(_ppp+"%", this.width/2, this.height/2+(-_fm.top-_fm.bottom)/2.0f, this.p);
				}
				
			}
			if(can_delete)draw_delete(canvas);
		}
		public void on_window_size_change(int w,int h){
			this.width=w;
			this.height=h;
		}
		public void in_page(int index){
			this.page_index=index;
			this.crt_data=datas.get(this.page_index);
			thread_loader.add_load(crt_data);
		}
		public void update_layer_data(LayerData item){
			if(crt_data!=null && crt_data==item){
				postInvalidate();
				if(item.state==5){
					thread_loader.add_load(crt_data);
				}
			}
		}
		public void reset(){
			this.page_index=-1;
			if(this.crt_data!=null){
				this.crt_data.anim_back.stop();
				this.crt_data.anim_back_zoom.stop();
				this.crt_data.anim_suit.stop();
				this.crt_data=null;
			}
		}
		
		private boolean whether_touch_in_delete(float x,float y){
			float _xx=this.width-3.0f*ui_delete_radius;
			float _yy=ui_delete_radius;
			float _xx2=_xx+2*ui_delete_radius;
			float _yy2=_yy+2*ui_delete_radius;
			if(x>_xx && x<_xx2 && y>_yy && y<_yy2)return true;
			return false;
		}
		
		private float ball_d=CL.DIP2PX(10);
		private float ball_r=ball_d/2;
		private float ball_zoom=ball_r;
		private float ball_step=0.5f;
		private void draw_waiting(Canvas canvas){
			if(ball_zoom>ball_d)ball_step=-0.5f;
			else if(ball_zoom<=ball_r)ball_step=0.5f;
			ball_zoom+=ball_step;
			this.p.setStyle(Style.STROKE);
			this.p.setStrokeWidth(2);
			this.p.setColor(Color.RED);
			canvas.drawCircle(this.width/2, this.height/2, ball_r+ball_zoom, this.p);
			postInvalidate();
		}
		
		private final float ui_delete_radius=CL.DIP2PX(16);
		private void draw_delete(Canvas canvas){
			canvas.save();
			
			this.p.setStyle(Style.FILL);
			this.p.setColor(Color.GRAY);
			float _x=this.width-3.0f*ui_delete_radius;
			float _y=ui_delete_radius;
			canvas.drawCircle(_x+ui_delete_radius, _y+ui_delete_radius, ui_delete_radius, this.p);
			this.p.setColor(Color.RED);
			this.p.setStyle(Style.STROKE);
			this.p.setStrokeWidth(2.0f);
			float _half=ui_delete_radius/2.0f;
			canvas.drawLine(_x+_half, _y+_half, _x+2.0f*ui_delete_radius-_half, _y+2.0f*ui_delete_radius-_half, this.p);
			canvas.drawLine(_x+2.0f*ui_delete_radius-_half, _y+_half, _x+_half, _y+2.0f*ui_delete_radius-_half, this.p);
			canvas.restore();
		}
		
		private void draw_bitmap_thum(Canvas canvas){
			Bitmap _tmp=crt_data.bitmap_thum;
			if(_tmp!=null){
				if(crt_data.rect_bitmap_thum==null){
					crt_data.rect_bitmap_thum=new RectF();
					
					float _xs=(float)this.width/(float)_tmp.getWidth();
					float _ys=(float)this.height/(float)_tmp.getHeight();
					float _ss=0;
					if(image_draw_type==Type_Center)_ss=(_xs < _ys ? _xs : _ys);
					else _ss=(_xs > _ys ? _xs : _ys);
					float _x=((float)this.width-(float)_tmp.getWidth()*_ss)/2.0f;
					float _y=((float)height-(float)_tmp.getHeight()*_ss)/2.0f;

					crt_data.rect_bitmap_thum.set(_x,_y,_x+(float)_tmp.getWidth()*_ss,_y+(float)_tmp.getHeight()*_ss);
				}
				canvas.drawBitmap(_tmp, null, crt_data.rect_bitmap_thum, this.p);
			}
		}
		private void draw_bitmap(Canvas canvas){
			//初始化图片属性
			Bitmap _tmp=crt_data.bitmap_image;
			if(_tmp!=null){
				if(crt_data.rect_bitmap==null){
					crt_data.rect_bitmap=new RectF();
					
					float _xs=(float)this.width/(float)_tmp.getWidth();
					float _ys=(float)this.height/(float)_tmp.getHeight();
					float _ss=0;
					if(image_draw_type==Type_Center)_ss=(_xs < _ys ? _xs : _ys);
					else _ss=(_xs > _ys ? _xs : _ys);
					float _x=((float)this.width-(float)_tmp.getWidth()*_ss)/2.0f;
					float _y=((float)height-(float)_tmp.getHeight()*_ss)/2.0f;

					crt_data.rect_bitmap.set(_x,_y,_x+(float)_tmp.getWidth()*_ss,_y+(float)_tmp.getHeight()*_ss);
					crt_data.bitmap_width=crt_data.rect_bitmap.width();
					crt_data.bitmap_height=crt_data.rect_bitmap.height();
					crt_data.zoom=1.0f;
				}
				//画回弹
				if(crt_data.anim_back.have_next()){
					float _x=crt_data.anim_back.get_next_x();
					float _y=crt_data.anim_back.get_next_y();
					crt_data.rect_bitmap.left+=_x;
					crt_data.rect_bitmap.top+=_y;
					if(!crt_data.anim_back.have_next()){
						crt_data.rect_bitmap.left=crt_data.anim_back.to_x;
						crt_data.rect_bitmap.top=crt_data.anim_back.to_y;
					}
					invalidate();
				}
				if(crt_data.anim_back_zoom.have_next()){
					crt_data.zoom+=crt_data.anim_back_zoom.get_next();
					float _x=crt_data.rect_bitmap.left;
					float _y=crt_data.rect_bitmap.top;
					crt_data.rect_bitmap.right=_x+crt_data.bitmap_width*crt_data.zoom;
					crt_data.rect_bitmap.bottom=_y+crt_data.bitmap_height*crt_data.zoom;
					if(!crt_data.anim_back_zoom.have_next()){
						crt_data.zoom=crt_data.anim_back_zoom.to;
						_x=crt_data.rect_bitmap.left;
						_y=crt_data.rect_bitmap.top;
						crt_data.rect_bitmap.right=_x+crt_data.bitmap_width*crt_data.zoom;
						crt_data.rect_bitmap.bottom=_y+crt_data.bitmap_height*crt_data.zoom;
					}
					invalidate();
				}
				//画自适应
				if(crt_data.anim_suit.have_next()){
					float _x=crt_data.anim_suit.get_next_x()+crt_data.rect_bitmap.left;
					float _y=crt_data.anim_suit.get_next_y()+crt_data.rect_bitmap.top;
					crt_data.rect_bitmap.set(_x, _y, _x+crt_data.rect_bitmap.width(), _y+crt_data.rect_bitmap.height());
					if(!crt_data.anim_suit.have_next()){
						_x=crt_data.anim_suit.to_x;
						_y=crt_data.anim_suit.to_y;
						crt_data.rect_bitmap.set(_x, _y, _x+crt_data.rect_bitmap.width(), _y+crt_data.rect_bitmap.height());
					}
					invalidate();
				}
				//画惯性
				if(this.anim_deceler.hava_next()){
					float _x=this.anim_deceler.get_next_x()+crt_data.rect_bitmap.left;
					float _y=this.anim_deceler.get_next_y()+crt_data.rect_bitmap.top;
					
					if(crt_data.rect_bitmap.width()>this.width && crt_data.rect_bitmap.height()>this.height){
						if(_x>0)_x=0;
						else if(_x+crt_data.rect_bitmap.width()<this.width)_x=this.width-crt_data.rect_bitmap.width();
						if(_y>0)_y=0;
						else if(_y+crt_data.rect_bitmap.height()<this.height)_y=this.height-crt_data.rect_bitmap.height();
						crt_data.rect_bitmap.set(_x, _y, _x+crt_data.rect_bitmap.width(), _y+crt_data.rect_bitmap.height());
					}
					else if(crt_data.rect_bitmap.width() > this.width && crt_data.rect_bitmap.height()<=this.height){
						if(_x>0)_x=0;
						else if(_x+crt_data.rect_bitmap.width()<this.width)_x=this.width-crt_data.rect_bitmap.width();
						crt_data.rect_bitmap.set(_x, crt_data.rect_bitmap.top, 
								_x+crt_data.rect_bitmap.width(), crt_data.rect_bitmap.top+crt_data.rect_bitmap.height());
					}
					else if(crt_data.rect_bitmap.width() <=this.width && crt_data.rect_bitmap.height()>this.height){
						if(_y>0)_y=0;
						else if(_y+crt_data.rect_bitmap.height()<this.height)_y=this.height-crt_data.rect_bitmap.height();
						crt_data.rect_bitmap.set(crt_data.rect_bitmap.left, _y, 
								crt_data.rect_bitmap.left+crt_data.rect_bitmap.width(), _y+crt_data.rect_bitmap.height());
					}
					else this.anim_deceler.stop();
					invalidate();
				}
				
				canvas.drawBitmap(_tmp, null, crt_data.rect_bitmap, this.p);
			}
		}
		
		private boolean process_bitmap_move(float x,float y){
			if(this.crt_data.rect_bitmap!=null && this.crt_data.zoom>1.0f){
				float _x=this.crt_data.rect_bitmap.left;
				float _y=this.crt_data.rect_bitmap.top;
				float _w=this.crt_data.rect_bitmap.width();
				float _h=this.crt_data.rect_bitmap.height();
				if(_w<=this.width && _h<=this.height){//图片小于窗口
					return false;
				}
				else{//图片大于窗口
					//只要有一边可以动就不要返回控制
					boolean _ok=false;
					if(_w>this.width){//处理左右移动
						_x+=x;
						if(_x>0){
							_x=0;
							_ok=false;
						}
						else if(_x+_w<this.width){
							_x=this.width-_w;
							_ok=false;
						}
						else _ok=true;
					}
					if(_h>this.height){//处理上下移动
						_y+=y;
						if(_y>0){
							_y=0;
							if(!_ok)_ok=false;
						}
						else if(_y+_h<this.height){
							_y=this.height-_h;
							if(!_ok)_ok=false;
						}
					}
					this.crt_data.rect_bitmap.set(_x, _y, _x+_w, _y+_h);
					return _ok;
				}
			}	
			return false;
		}
		private boolean process_bitmap_fly(float xv,float yv){
			if(crt_data==null
					|| crt_data.rect_bitmap==null
					|| crt_data.zoom <=1.0f
					|| crt_data.anim_back.have_next() 
					|| crt_data.anim_back_zoom.have_next()
					|| crt_data.anim_suit.have_next())return false;
			xv/=ctm_velocity;
			yv/=ctm_velocity;
			if(Math.abs(xv) > 5.0f || Math.abs(yv) >5.0f)this.anim_deceler.reset(xv, yv);
			return true;
		}
		private final float ctm_velocity=CL.Density*30.0f;
	}
	
	//单点动画
	private class CLAnimation{
		private float timer;
		private long last_time=-1;
		private float v;
		private float distance;
		private float last_distance;
		private boolean over=true;
		
		public float from,to;
		
		public CLAnimation(){}
		public CLAnimation(float from,float to,int time){
			this.from=from;
			this.to=to;
			this.last_distance=0;
			this.distance=this.to-this.from;
			this.over=false;
			this.timer=time;
		}
		public void reset_data(float from,float to,int time){
			last_time=-1;
			last_distance=0;
			this.from=from;
			this.to=to;
			this.distance=this.to-this.from;
			this.over=false;
			this.timer=time;
		}
		public boolean have_next(){
			if(over)return false;
			return true;
		}
		public float get_next(){
			if(last_time==-1){
				this.last_time=System.currentTimeMillis();
				v=0;
			}
			else {
				long _time=System.currentTimeMillis();
				long crt=_time-last_time;
				if(crt < timer){
					float _crt_x=(float)crt/timer*this.distance;
					v=_crt_x-last_distance;
					last_distance=_crt_x;
				}
				else{
					over=true;
					v=distance-last_distance;
				}
			}
			return v;
		}
		public void stop(){
			over=true;
		}
	}
	//XY点双动画
	private class CLAnimationXY{
		private float timer;
		private long last_time=-1;
		private float distance_x,distance_y;
		private float last_distance_x,last_distance_y;
		private boolean over=true;
		private float xx,yy;
		
		public float from_x,from_y,to_x,to_y;
		
		public CLAnimationXY(){}
		public CLAnimationXY(float from_x,float to_x,float from_y,float to_y,int time){
			this.from_x=from_x;
			this.from_y=from_y;
			this.to_x=to_x;
			this.to_y=to_y;
			this.distance_x=to_x-from_x;
			this.distance_y=to_y-from_y;
			this.over=false;
			this.timer=time;
		}
		public void reset_data(float from_x,float to_x,float from_y,float to_y,int time){
			last_time=-1;
			last_distance_x=0;
			last_distance_y=0;
			this.from_x=from_x;
			this.from_y=from_y;
			this.to_x=to_x;
			this.to_y=to_y;
			this.distance_x=to_x-from_x;
			this.distance_y=to_y-from_y;
			this.over=false;
			this.timer=time;
		}
		public boolean have_next(){
			if(over)return false;
			else{
				if(last_time==-1){
					this.last_time=System.currentTimeMillis();
					xx=0;
					yy=0;
				}
				else {
					long _time=System.currentTimeMillis();
					long crt=_time-last_time;
					if(crt < timer){
						float _crt_x=(float)crt/timer*this.distance_x;
						xx=_crt_x-last_distance_x;
						last_distance_x=_crt_x;
						float _crt_y=(float)crt/timer*this.distance_y;
						yy=_crt_y-last_distance_y;
						last_distance_y=_crt_y;
					}
					else{
						over=true;
						xx=distance_x-last_distance_x;
						yy=distance_y-last_distance_y;
					}
				}
			}
			return true;
		}
		public float get_next_x(){
			return xx;
		}
		public float get_next_y(){
			return yy;
		}
		public void stop(){
			over=true;
		}
	}
	//减速动画
	private class CLAnimationDecelerate{
		private final float a=10f;
		private final float b=-200.0f;
		private final float c=800.0f;
		
		private float y=0.0f;
		private float x=0.0f;
		
		private float xxx=0;
		private float yyy=0;
		
		public CLAnimationDecelerate(){
			
		}
		public void reset(float xv,float yv){
			this.xxx=xv;
			this.yyy=yv;
			this.x=0.1f;
		}
		public void stop(){
			this.x=0.0f;
		}
		public boolean hava_next(){
			if(x==0.0f)return false;
			y=a*(x*x)+b*x+c;
			boolean _ih=(y>0?true:false);
			if(!_ih)x=0.0f;
			else x+=0.1f;
			return true;
		}
		public float get_next_x(){
			float sss=(y/c)*xxx;
			return sss;
		}
		public float get_next_y(){
			float sss=(y/c)*yyy;
			return sss;
		}
	}
	private class ThreadLoader extends Thread{
		private  int Max_Memory=Type_Memory_MED_v;
		private boolean show_original_image=false;
		private int memory=0;
		private boolean run=true;
		private boolean loading=false;
		private boolean sleep=false;
		
		private CTMStream ctm_stream;
		private ArrayList<LayerData> data_loading=new ArrayList<LayerData>();
		private LayerData crt_loading=null;
		
		public void add_load(LayerData item){
			synchronized (this) {
				//0:等待加载 1:正在加载 2:加载完成 4:正在下载
				if(crt_loading==item)return;
				if(datas.size()==1){
					if(item.state==0 || item.state==5){
						item.state=1;
						data_loading.add(item);
					}
				}
				else{
					int _index=datas.indexOf(item);
					LayerData _p=datas.get((_index-1<0?datas.size()-1:_index-1));
					LayerData _n=datas.get((_index+1>=datas.size()?0:_index+1));
					//移除其他
					if(crt_loading!=null){
						if(crt_loading!=item && crt_loading!=_p && crt_loading!=_n){
							stop_load();
						}
					}
					for(int i=0;i<this.data_loading.size();++i){
						this.data_loading.get(i).state=0;
					}
					this.data_loading.clear();
					//添加当前3个
					if(item.state==0 || item.state==5){
						item.state=1;
						data_loading.add(item);
					}
					if(_p.state==0 || _p.state==5){
						_p.state=1;
						data_loading.add(_p);
					}
					if(_n.state==0 || _n.state==5){
						_n.state=1;
						data_loading.add(_n);
					}
//					App.CLOG("add loading item size:"+data_loading.size());
//					for(int i=0;i<this.data_loading.size();++i){
//						App.CLOG("item index:"+datas.indexOf(this.data_loading.get(i)));
//					}
				}
				if(sleep)this.notifyAll();
			}
		}
	
		public void delete_load(LayerData item){
			if(crt_loading==item){
				if(loading)stop_load();
			}
			else{
				synchronized (this) {
//					App.CLOG("thread delete item:"+item.data.file_name);
					data_loading.remove(item);
				}
			}
//			App.CLOG("delete before memory:"+memory);
			if(item.bitmap_thum!=null && !item.bitmap_thum.isRecycled()){
				memory-=item.bitmap_thum.getRowBytes()*item.bitmap_thum.getHeight();
				item.bitmap_thum=null;
			}
			if(item.bitmap_image!=null && !item.bitmap_image.isRecycled()){
				memory-=item.bitmap_image.getRowBytes()*item.bitmap_image.getHeight();
				item.bitmap_image=null;
			}
//			App.CLOG("delete after memory:"+memory);
		}
		
		public void stop_load(){
			if(loading){
				if(ctm_stream!=null)ctm_stream.stop_read();
			}
			synchronized (this) {
				crt_loading=null;
				data_loading.clear();
			}
		}
		
		public void clear_all_resource(ArrayList<LayerData> data){
			if(data!=null){
//				App.CLOG("clear resource before memory:"+memory);
				for(int i=0;i<data.size();++i){
					LayerData item=data.get(i);
					item.state=0;
					if(item.bitmap_thum!=null && !item.bitmap_thum.isRecycled()){
						memory-=item.bitmap_thum.getRowBytes()*item.bitmap_thum.getHeight();
						item.bitmap_thum=null;
					}
					if(item.bitmap_image!=null && !item.bitmap_image.isRecycled()){
						memory-=item.bitmap_image.getRowBytes()*item.bitmap_image.getHeight();
						item.bitmap_image=null;
					}
				}
//				App.CLOG("clear resource after memory:"+memory);
			}
		}

		public void exit(){
			run=false;
			if(sleep){
				synchronized (this) {
					this.notifyAll();
				}
			}
		}
		
		public void run(){
			while(run){
				try{
					crt_loading=null;
					synchronized (this) {
						if(data_loading.size()>0){
							crt_loading=data_loading.remove(0);
						}else{
							sleep=true;
							this.wait();
							if(!run)break;
						}
						sleep=false;
						if(crt_loading==null)continue;
						loading=true;
					}

					int _screen_width=CL.Get_Screen_Width(cc);
					int _screen_height=CL.Get_Screen_Height(cc);

					if(crt_loading.bitmap_image==null || crt_loading.bitmap_image.isRecycled()){
						crt_loading.bitmap_image=null;
						if(new File(crt_loading.data.file_path).exists()){
							BitmapFactory.Options _options=new BitmapFactory.Options();
//							_options.inJustDecodeBounds=true;
//							BitmapFactory.decodeFile(crt_loading.data.file_path,_options);
//							int _out_width=_options.outWidth;
//							int _out_height=_options.outHeight;
//							CL.CLOGI("image w1:"+_out_width+" h1:"+_out_height);
//							if(_out_width > _screen_width
//									|| _out_height > _screen_height){
//								int _xx=_out_width/_screen_width;
//								int _yy=_out_height/_screen_height;
//								_options.inSampleSize=(_xx<_yy?_xx:_yy);
//								if(_options.inSampleSize<1)_options.inSampleSize=1;
//							}
//							else _options.inSampleSize=1;
							_options.inSampleSize=1;
							_options.inJustDecodeBounds=false;
							_options.inPreferredConfig=Bitmap.Config.RGB_565;
							ctm_stream=new CTMStream(crt_loading.data.file_path);
							crt_loading.bitmap_image=BitmapFactory.decodeStream(ctm_stream, null, _options);
//							if(crt_loading.bitmap_image!=null)App.CLOG("image w2:"+crt_loading.bitmap_image.getWidth()+" h2:"+crt_loading.bitmap_image.getHeight());
						}
						
						if(crt_loading.bitmap_image!=null){
							memory+=crt_loading.bitmap_image.getRowBytes()*crt_loading.bitmap_image.getHeight();
							//释放缩略图
							if(crt_loading.bitmap_thum!=null && !crt_loading.bitmap_thum.isRecycled()){
								memory-=crt_loading.bitmap_thum.getRowBytes()*crt_loading.bitmap_thum.getHeight();
								crt_loading.bitmap_thum.recycle();
								crt_loading.bitmap_thum=null;
							}
						}
						else if(crt_loading.data.path_thum!=null){//加载缩略图
							File _target_thum=new File(crt_loading.data.path_thum);
							if(_target_thum.exists() && (crt_loading.bitmap_thum==null || crt_loading.bitmap_thum.isRecycled())){
								crt_loading.bitmap_thum=null;
								BitmapFactory.Options _options2=new BitmapFactory.Options();
								_options2.inJustDecodeBounds=true;
								BitmapFactory.decodeFile(crt_loading.data.path_thum,_options2);
								int _out_width2=_options2.outWidth;
								int _out_height2=_options2.outHeight;
								if(_out_width2 >= _screen_width/8 || _out_height2 >= _screen_height/8){
									int _xx=_out_width2/(_screen_width/8);
									int _yy=_out_height2/(_screen_height/8);
									_options2.inSampleSize=(_xx<_yy?_xx:_yy);
									if(_options2.inSampleSize<1)_options2.inSampleSize=1;
								}
								else _options2.inSampleSize=1;
								_options2.inJustDecodeBounds=false;
								_options2.inPreferredConfig=Bitmap.Config.RGB_565;
								ctm_stream=new CTMStream(crt_loading.data.path_thum);
								crt_loading.bitmap_thum=BitmapFactory.decodeStream(ctm_stream, null, _options2);
							}
							if(crt_loading.bitmap_thum!=null){
								memory+=crt_loading.bitmap_thum.getRowBytes()*crt_loading.bitmap_thum.getHeight();
							}
						}
					}
					if(crt_loading.bitmap_image!=null){
						crt_loading.state=3;
					}
					else if(crt_loading.bitmap_thum!=null){
						crt_loading.state=2;
					}
					if(crt_loading.state==1)crt_loading.state=0;
					
					
					//释放图片
					if(memory>Max_Memory){
//						App.CLOG("memory 超过最大值了:"+memory);
						for(int i=0;i<datas.size();++i){
							int _crt_page=in_page;
							int _p_page=(in_page-1<0?page_count-1:in_page-1);
							int _n_page=(in_page+1>=page_count-1?0:in_page+1);
//							App.CLOG("当前页码:"+_crt_page +"   "+_p_page+"   "+_n_page);
							if(i!=_crt_page && i!=_p_page && i!=_n_page){
//								App.CLOG("清理页码:"+i);
								LayerData _item=datas.get(i);
								if(_item.bitmap_thum!=null && !_item.bitmap_thum.isRecycled()){
									memory-=_item.bitmap_thum.getRowBytes()*_item.bitmap_thum.getHeight();
									_item.bitmap_thum.recycle();
									_item.bitmap_thum=null;
								}
								if(_item.bitmap_image!=null && !_item.bitmap_image.isRecycled()){
									memory-=_item.bitmap_image.getRowBytes()*_item.bitmap_image.getHeight();
									_item.bitmap_image.recycle();
									_item.bitmap_image=null;
								}
								_item.state=0;
							}
							if(memory<Max_Memory)break;
						}
//						App.CLOG("memory 清理后的值:"+memory);
					}
					
					//如果原图为空,则下载图片
					if(crt_loading.bitmap_image==null)thread_downloader.add_item(crt_loading);
					
					ctm_stream=null;
					loading=false;
					if(crt_loading.state!=0)update_by_loader(crt_loading);
					crt_loading=null;
				} catch (OutOfMemoryError e) {
					if(crt_loading!=null)crt_loading.state=0;
					crt_loading=null;
					loading=false;
				} catch(Exception ex){
					if(crt_loading!=null)crt_loading.state=0;
					crt_loading=null;
					loading=false;
//					App.CLOG("therad load bitmap error:"+ex.toString());
				}
			}
			CL.CLOGI("load thread was over");
			memory=0;
			thread_loader=null;
		}
	}
	private class CTMStream extends FileInputStream{
		private boolean stop=false;
		public CTMStream(String path) throws FileNotFoundException {
			super(path);
		}
		public void stop_read(){
			this.stop=true;
		}
		@Override
		public int read() throws IOException {
			if(stop){
				return -1;
			}
			return super.read();
		}
		@Override
		public int read(byte[] buffer) throws IOException {
			if(stop){
				return -1;
			}
			return super.read(buffer);
		}
		@Override
		public int read(byte[] buffer, int byteOffset, int byteCount)
				throws IOException {
			if(stop){
				return -1;
			}
			return super.read(buffer, byteOffset, byteCount);
		}
	}
	private class ThreadDownloader extends Thread{
		
		private ArrayList<LayerData> data_downloading=new ArrayList<LayerData>();
		private LayerData crt_downloading=null;
		
		private boolean run=true;
		private boolean sleep=false;
		private boolean stop=false;
		
		public void add_item(LayerData item){
			if(item==null)return;
			if(item.data.file_name==null || item.data.file_dir==null || item.data.file_url==null)return;
			if(crt_downloading!=null && crt_downloading==item)return;
			synchronized (this) {
				if(!data_downloading.contains(item)){
					data_downloading.add(item);
					this.notifyAll();
				}
			}
		}
		
		public void exit(){
			run=false;
			if(sleep){
				synchronized (this) {
					this.notifyAll();
				}
			}
		}
		
		@Override
		public void run() {
			while(run){
				try{
					crt_downloading=null;
					synchronized (this) {
						if(data_downloading.size()==0){
//							App.CLOG("thread downloader sleep...");
							this.sleep=true;
							this.wait();
							this.sleep=false;
							continue;
						}
						else{
							crt_downloading=data_downloading.remove(0);
							crt_downloading.state=4;
						}
					}
					File _target=new File(crt_downloading.data.file_dir,crt_downloading.data.file_name);
					if(!_target.exists()){
						File _target_temp=new File(crt_downloading.data.file_dir,"##climager#"+crt_downloading.data.file_name+System.currentTimeMillis());
						String _url=crt_downloading.data.file_url;
						if(_url.contains(" "))_url=_url.replace(" ", "%20");

						HttpURLConnection _conn = (HttpURLConnection) new URL(_url).openConnection();
						_conn.setConnectTimeout(5000);
						_conn.setReadTimeout(5000);
						_conn.setDoInput(true);
						_conn.setRequestMethod("GET");
						_conn.connect();
						crt_downloading.download_file_size=_conn.getContentLength();
						InputStream _is=_conn.getInputStream();
						FileOutputStream _fos=new FileOutputStream(_target_temp);
						byte[] _buff=new byte[8192];
						crt_downloading.download_scale=0;
						int _cc=-1;
						while((_cc=_is.read(_buff))!=-1){
							_fos.write(_buff, 0, _cc);
							crt_downloading.download_scale+=_cc;
							update_download_scale(crt_downloading);
							if(stop){
								_is.close();
								_fos.close();
								if(_target_temp.exists())_target_temp.delete();
								break;
							}
						}
						_is.close();
						_fos.close();
						if(_target_temp.renameTo(_target)){
							_target_temp.delete();
						}
						else{
							_target_temp.delete();
							continue;
						}
					}
					//验证合法
					BitmapFactory.Options _option=new BitmapFactory.Options();
					_option.inJustDecodeBounds=true;
					BitmapFactory.decodeFile(_target.getAbsolutePath(), _option);
					if(_option.mCancel || _option.outWidth==-1 || _option.outHeight==-1){
						//图片不合法,删除
						_target.delete();
						crt_downloading.state=0;
					}
					else{
						//图片下载完成,更新界面
						crt_downloading.state=5;
//						App.CLOG("download success:"+crt_downloading.data.file_name);
						update_by_downloader(crt_downloading);
					}
				} catch (OutOfMemoryError e) {
				} catch(Exception ex){
					CL.CLOGI("downloader thread error:"+ex.toString());
				}
			}
		}
	}
	
	private class LayerData{
		public int state=0; //0:等待加载 1:正在加载 2:缩略图加载OK 3:加载完成 4:正在下载 5:下载完成
		public Bitmap bitmap_thum;
		public Bitmap bitmap_image;
		public float zoom=-1;						//缩放倍数
		public RectF rect_bitmap;					//画图片的矩形
		public RectF rect_bitmap_thum;		//缩略图的矩形
		public float bitmap_width;				//图片的宽度
		public float bitmap_height;				//图片的高度
		public int download_scale;				//下载比例
		public int download_file_size=-1;			//下载文件的大小
		
		public DataItem data;
		
		private CLAnimationXY anim_back=new CLAnimationXY();
		private CLAnimation anim_back_zoom=new CLAnimation();
		private CLAnimationXY anim_suit=new CLAnimationXY();
	}
	
	//对外接口//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	public static class DataItem{
		private String path_thum;
		private String file_name;
		private String file_url;
		private String file_dir;
		private String file_path;
		
		public DataItem(String path_thum,String file_name,String file_dir,String file_url){
			this.path_thum=path_thum;
			this.file_name=file_name;
			this.file_url=file_url;
			this.file_dir=file_dir;
			if(this.file_dir!=null)
				this.file_path=new File(file_dir,file_name).getAbsolutePath();
			else this.file_path=new File(file_name).getAbsolutePath();
		}
	}
	public interface OnMonopolyTouchEvent{
		void on_monopoly_event();
		void on_discard_event();
	}
	public interface OnEventer{
		void on_data_delete(int index);
		void on_data_empty();
		void on_page_change(int index);
		void on_item_click(int index, DataItem item);
	}
	
}
