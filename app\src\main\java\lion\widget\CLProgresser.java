package lion.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.view.View;

/**
 * Created by leron on 2016/8/2.
 */
public class CLProgresser extends View{

    public CLProgresser(Context context) {
        super(context);
        paint=new Paint(Paint.ANTI_ALIAS_FLAG);
        rect=new RectF();
    }


    private int color_bg=0xff888888;
    private int color_major=0xff0075a9;
    private int color_minor=0xffc0d62d;
    private Paint paint;
    private RectF rect;
    private boolean show_minor;
    private float v_major,v_minor;


    public void set_show_minor(boolean show){
        this.show_minor=show;
    }

    public void set_progress(float major,float minor){
        v_major=major;
        v_minor=minor;
        if(v_major<0)v_major=0;
        else if(v_major>1.0f)v_major=1.0f;
        if(v_minor<0)v_minor=0;
        else if(v_minor>1.0f)v_minor=1.0f;

        postInvalidate();
    }
    public float get_process_major(){
        return v_major;
    }
    public float get_process_minor(){
        return v_minor;
    }

    @Override
    public void draw(Canvas canvas) {

        int _w=this.getWidth();
        int _h=this.getHeight();
        float _r=_h/2.0f;
        paint.setColor(color_bg);
        rect.set(0,0,_w,_h);
        canvas.drawRoundRect(rect,_r,_r,paint);

        if(v_major>0) {
            canvas.save();
            paint.setColor(color_major);
            canvas.clipRect(0, 0, _w * v_major, _h);
            canvas.drawRoundRect(rect,_r,_r,paint);
            canvas.restore();
        }
        if(show_minor && v_minor>0){
            canvas.save();
            paint.setColor(color_minor);
            canvas.clipRect(0, _h*0.86f, _w * v_minor, _h);
            canvas.drawRoundRect(rect,_r,_r,paint);
            canvas.restore();
        }
    }
}
