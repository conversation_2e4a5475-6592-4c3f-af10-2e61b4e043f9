package lion.widget;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.net.MailTo;
import android.net.http.SslError;
import android.os.Build;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import android.text.InputType;
import android.text.TextUtils;
import android.text.method.PasswordTransformationMethod;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.DownloadListener;
import android.webkit.HttpAuthHandler;
import android.webkit.JavascriptInterface;
import android.webkit.JsResult;
import android.webkit.MimeTypeMap;
import android.webkit.SslErrorHandler;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;

import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.downloader.ResSniffer;
import amazon.browser.lionpro.downloader.Server;
import amazon.browser.lionpro.downloader.extend.WebViewCallBack;
import amazon.browser.lionpro.primary.Global;
import amazon.browser.lionpro.util.IntentUtils;

import java.io.File;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lion.CL;
import lion.CLToast;

/**
 * Created by leron on 2016/4/7.
 */
public class CLWebkit extends WebView implements WebViewCallBack {

    private IntentUtils mIntentUtils;

    public interface EventLinstener{
        void on_page_start(WebView view, String url, Bitmap favicon);
        void on_page_finish(WebView view, String url);
        boolean on_progress(WebView view, int progress);
        void on_received_title(WebView view, String title);
        void on_received_icon(WebView view, Bitmap icon);
        void on_show_customview(CLWebkit webview, View view, WebChromeClient.CustomViewCallback callback);
        void on_hide_customview(CLWebkit webview);
        void on_intercept(String title,String url, String headerParams);
        void on_intercept(WebView view, String url, String headerParams);
        void on_webkit_download(String type,String url,String name,int length, String headerParams);
        void on_long_click_image(String url);
    }

    private String list_Header_Params = "";
    private SslWarningPreferences sslWarningPreferences;
    private Context cc;
  //  private ProgressBar progressbar;
    private String user_agent;
    private EventLinstener listener;
  //  JSVideoTag jsvideotag;
    private int progress;
    private boolean _finish=false;
    boolean _isStar;
    boolean is_hava_website_icon=false;
    String _star_page_url;
    View _view;
    Bitmap _bitmap;
    String title;
    WebChromeClient.CustomViewCallback callback = null;
    boolean have_hide_customview;
    private boolean mInvertPage = false;
 //   @NonNull private final GestureDetector mGestureDetector;
    @NonNull
    private final Paint mPaint = new Paint();
    private static float sMaxFling;
    private static final float[] sNegativeColorArray = {
            -1.0f, 0, 0, 0, 255, // red
            0, -1.0f, 0, 0, 255, // green
            0, 0, -1.0f, 0, 255, // blue
            0, 0, 0, 1.0f, 0 // alpha
    };
    private static final float[] sIncreaseContrastColorArray = {
            2.0f, 0, 0, 0, -160.f, // red
            0, 2.0f, 0, 0, -160.f, // green
            0, 0, 2.0f, 0, -160.f, // blue
            0, 0, 0, 1.0f, 0 // alpha
    };


//    public String get_star_page_url() {
//        return _star_page_url;
//    }
//
//    public void set_star_page_url(String _star_page_url) {
//        this._star_page_url = _star_page_url;
//    }

    public String Title() {
        return title;
    }

    public void set_Title(String title) {
        this.title = title;
    }

    @Override
    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public boolean is_finishloaded() {
        return _finish;
    }

    public void set_finishloaded(boolean is_loading) {
        this._finish = is_loading;
    }

    public boolean is_isStar() {
        return _isStar;
    }

    public void set_Star(boolean _isStar) {
        this._isStar = _isStar;
    }

    public boolean is_hava_website_icon() {
        return is_hava_website_icon;
    }

    public void set_website_icon(boolean is_hava_website_icon) {
        this.is_hava_website_icon = is_hava_website_icon;
    }

    public Bitmap get_bitmap() {
        return _bitmap;
    }

    public View get_view() {
        return _view;
    }

    public void set_view(View _view) {
        this._view = _view;
    }

    public void set_bitmap(Bitmap _bitmap) {
        this._bitmap = _bitmap;
    }

    public WebChromeClient.CustomViewCallback getCallback() {
        return callback;
    }

    public void setCallback(WebChromeClient.CustomViewCallback callback) {
        this.callback = callback;
    }


    public boolean isHave_hide_customview() {
        return have_hide_customview;
    }

    public void setHave_hide_customview(boolean have_hide_customview) {
        this.have_hide_customview = have_hide_customview;
    }

    public CLWebkit(Context context,EventLinstener listener) {
        super(context);
        this.cc=context;

/*
        progressbar = new ProgressBar(context, null,
                android.R.attr.progressBarStyleHorizontal);
        progressbar.setLayoutParams(new LayoutParams(LayoutParams.FILL_PARENT,
                10, 0, 0));

        Drawable drawable = context.getResources().getDrawable(R.drawable.progress_bar_states);
        progressbar.setProgressDrawable(drawable);
        progressbar.setVisibility(View.GONE);

        addView(progressbar);
        */
//        sniffer=new ResourceSniffer();
        this.listener=listener;
        this.sslWarningPreferences = SessionSslWarningPreferences.getInstance();

        this.setDrawingCacheBackgroundColor(Color.WHITE);
        this.setFocusableInTouchMode(true);
        this.setFocusable(true);
        this.setDrawingCacheEnabled(false);
        this.setWillNotCacheDrawing(true);
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.LOLLIPOP_MR1) {
            //noinspection deprecation
            this.setAnimationCacheEnabled(false);
            //noinspection deprecation
            this.setAlwaysDrawnWithCacheEnabled(false);
        }
        this.setBackgroundColor(Color.WHITE);

        this.setScrollbarFadingEnabled(true);
        this.setSaveEnabled(true);
        this.setNetworkAvailable(true);


     //   mGestureDetector = new GestureDetector(activity, new CustomGestureListener());
     //   this.setOnTouchListener(new TouchListener());

        init();
//        user_agent=getSettings().getUserAgentString();
//        Server.Common_User_Agent=user_agent;

//        init_exclude();
    }

//    private boolean is_exclude=false;
//    private Pattern p_youtube;
//    private void init_exclude(){
//        p_youtube=Pattern.compile(".*://.*\\.youtube\\..*/.+");
//    }
//    private void is_exclude_url(String url){
//        is_exclude=false;
//        if(p_youtube.matcher(url).find()){
//            is_exclude=true;
//        }
//    }


    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
//        LayoutParams lp = (LayoutParams) progressbar.getLayoutParams();
//        lp.x = l;
//        lp.y = t;
//        progressbar.setLayoutParams(lp);
        super.onScrollChanged(l, t, oldl, oldt);
    }

    private void setNormalRendering() {

        this.setLayerType(View.LAYER_TYPE_NONE, null);
    }

    private void setHardwareRendering() {

        this.setLayerType(View.LAYER_TYPE_HARDWARE, mPaint);
    }

    private void setColorMode(int mode) {
        mInvertPage = false;
        switch (mode) {
            case 0:
                mPaint.setColorFilter(null);
                // setSoftwareRendering(); // Some devices get segfaults
                // in the WebView with Hardware Acceleration enabled,
                // the only fix is to disable hardware rendering
                setNormalRendering();
                mInvertPage = false;
                break;
            case 1:
                ColorMatrixColorFilter filterInvert = new ColorMatrixColorFilter(
                        sNegativeColorArray);
                mPaint.setColorFilter(filterInvert);
                setHardwareRendering();

                mInvertPage = true;
                break;
            case 2:
                ColorMatrix cm = new ColorMatrix();
                cm.setSaturation(0);
                ColorMatrixColorFilter filterGray = new ColorMatrixColorFilter(cm);
                mPaint.setColorFilter(filterGray);
                setHardwareRendering();
                break;
            case 3:
                ColorMatrix matrix = new ColorMatrix();
                matrix.set(sNegativeColorArray);
                ColorMatrix matrixGray = new ColorMatrix();
                matrixGray.setSaturation(0);
                ColorMatrix concat = new ColorMatrix();
                concat.setConcat(matrix, matrixGray);
                ColorMatrixColorFilter filterInvertGray = new ColorMatrixColorFilter(concat);
                mPaint.setColorFilter(filterInvertGray);
                setHardwareRendering();

                mInvertPage = true;
                break;

            case 4:
                ColorMatrixColorFilter IncreaseHighContrast = new ColorMatrixColorFilter(
                        sIncreaseContrastColorArray);
                mPaint.setColorFilter(IncreaseHighContrast);
                setHardwareRendering();
                break;

        }

    }

    @SuppressWarnings("deprecation")
  //  @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    @SuppressLint({"SetJavaScriptEnabled","NewApi"})
    private void init(){
        final WebSettings _s=this.getSettings();
//        _s.setUserAgentString("Mozilla/5.0 (Linux; Android 4.4.4; en-us; Nexus 5 Build/JOP40D) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2307.2 Mobile Safari/537.36");
//        if (CL.SDK < Build.VERSION_CODES.Q) {
//            _s.setAppCacheEnabled(true);
//        }
        _s.setCacheMode(WebSettings.LOAD_DEFAULT);
        _s.setGeolocationDatabasePath(this.getContext().getFilesDir().toString());
        _s.setAllowFileAccess(true);
        _s.setDatabaseEnabled(true);
        _s.setSupportZoom(true);
        _s.setBuiltInZoomControls(true);
        _s.setDisplayZoomControls(false);
        _s.setAllowContentAccess(true);
        _s.setDefaultTextEncodingName("utf-8");
        _s.setDomStorageEnabled(true);
        _s.setUseWideViewPort(true);
        _s.setLoadWithOverviewMode(true);
        _s.setJavaScriptEnabled(true);
        _s.setJavaScriptCanOpenWindowsAutomatically(true);
        _s.setPluginState(WebSettings.PluginState.ON);
//        _s.setAppCacheMaxSize(Long.MAX_VALUE);
        _s.setBlockNetworkImage(false);
        _s.setSupportMultipleWindows(false);

        if (CL.SDK >= Build.VERSION_CODES.LOLLIPOP) {
            _s.setMixedContentMode(WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE);
            CookieManager.getInstance().setAcceptThirdPartyCookies(this, true);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            this.setImportantForAutofill(View.IMPORTANT_FOR_AUTOFILL_YES);
        }
        int _ua= Setting.Share_Setting().get_UA();
        if(_ua==101) {
//            Global.Crt_UA=WebSettings.getDefaultUserAgent(cc);
//            if (Global.Crt_UA == null) {
//                Global.Crt_UA = Global.UA_Android;
//            }
            Global.Crt_UA = Global.UA_Android;
        }
        else if(_ua==102)Global.Crt_UA=Global.UA_Chrome;
        else if(_ua==103)Global.Crt_UA=Global.UA_Iphone;

        _s.setUserAgentString(Global.Crt_UA);

        if (CL.SDK < 17) {
            _s.setEnableSmoothTransition(true);
        }
        if (Build.VERSION.SDK_INT< 19){
            File _dbs=new File(this.getContext().getFilesDir().getAbsolutePath() + "/databases");
            if(!_dbs.exists())_dbs.mkdirs();
            _s.setDatabasePath(_dbs.getAbsolutePath());
        }

        setColorMode(0);

        _s.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
        _s.setTextZoom(100);
        if (Build.VERSION.SDK_INT > 16) {
            _s.setMediaPlaybackRequiresUserGesture(true);
            _s.setAllowFileAccessFromFileURLs(false);
            _s.setAllowUniversalAccessFromFileURLs(false);
        }
        this.setWebViewClient(new CLWebViewClient());
        this.setWebChromeClient(new CLWebViewChrome());

        this.setDownloadListener(new DownloadListener() {
            @Override
            public void onDownloadStart(String url, String userAgent, String contentDisposition, String mimetype, long contentLength) {
//                CL.CLOGI("on download:"+url+" / "+mimetype);
//
//                CL.CLOGI("下载:"+url+
//                        "\nuserAgent:"+userAgent+
//                        "\ncontentDisposition:"+contentDisposition+
//                        "\nmimetype:"+mimetype+"\nsize:"+contentLength);

                if(mimetype==null || url==null || contentLength <= 0){
//                    CL.CLOGI("download info lose");
                    return;
                }
                String _suffix=MimeTypeMap.getFileExtensionFromUrl(url);
                if(_suffix==null || _suffix.isEmpty())_suffix=MimeTypeMap.getSingleton().getExtensionFromMimeType(mimetype);
                if(_suffix==null || _suffix.isEmpty())return;

                String name=null;//attachment;filename="go1.5.2.windows-amd64.zip"
                try {
                    Matcher _matcher = Pattern.compile("^https?://[^?]+").matcher(url);
                    if(_matcher.find()){
                        String _zzurl=_matcher.group();
                        name = _zzurl.substring(_zzurl.lastIndexOf('/') + 1);
                    }
                    if (name == null) {
                        if (contentDisposition != null && contentDisposition.length() > 0
                                && contentDisposition.contains("filename=")) {
                            Matcher _matcher2 = Pattern.compile("filename=?\".*?\"").matcher(contentDisposition);
                            if (_matcher2.find()) {
                                String _group = _matcher2.group();
                                name = _group.substring(_group.indexOf('\"') + 1, _group.lastIndexOf('\"'));
                            }
                        }
                    }
                }catch(Exception ex){}
                if(name==null || name.isEmpty())name="unnamed"+System.currentTimeMillis()+"."+_suffix;
                else {
                    if(!name.endsWith(_suffix))name=name+"."+_suffix;
                }

              //  CL.CLOGI("suffix:"+_suffix+"   name:"+name);

                if(mimetype.startsWith("image/")){
                    if(listener!=null)listener.on_long_click_image(url);
                }else if(mimetype.startsWith("video/")){
                    if(listener!=null)listener.on_intercept(name,url, list_Header_Params);
                }else if(mimetype.startsWith("audio/")){
                    if(listener!=null)listener.on_intercept(name,url, list_Header_Params);
                }else{
                    if(listener!=null)listener.on_webkit_download(_suffix,url,name,(int)contentLength, list_Header_Params);
                }
            }
        });

        this.setLongClickable(true);
        this.setOnLongClickListener(new OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                if(listener==null)return false;
                final WebView.HitTestResult _result=getHitTestResult();
                if(_result!=null){
                    int _type=_result.getType();
                    if(_type==HitTestResult.IMAGE_TYPE || _type==HitTestResult.SRC_IMAGE_ANCHOR_TYPE){
                        listener.on_long_click_image(_result.getExtra());
                    }
                }
                return true;
            }
        });
      //  jsvideotag = new JSVideoTag();
      //  this.addJavascriptInterface(jsvideotag, "jsvideotag");

        mIntentUtils = new IntentUtils((AppCompatActivity)cc);
        addJavascriptInterface(new JSBridge(), "jsbridge");
    }


    //WebViewClient
    private class CLWebViewClient extends WebViewClient{
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
       //     CL.CLOGI("on override url loading:" + url);
           // CL.CLOGI("Eddy shouldOverrideUrlLoading:" + url);
            try {
                if (url.contains("mailto:")) {
                    MailTo mailTo = MailTo.parse(url);
                    Intent _intent = new Intent(Intent.ACTION_SEND);
                    _intent.putExtra(Intent.EXTRA_EMAIL, new String[]{mailTo.getTo()});
                    _intent.putExtra(Intent.EXTRA_SUBJECT, mailTo.getSubject());
                    _intent.putExtra(Intent.EXTRA_TEXT, mailTo.getBody());
                    _intent.putExtra(Intent.EXTRA_CC, mailTo.getCc());
                    _intent.setType("message/rfc822");
                    cc.startActivity(_intent);
                    view.reload();
                    return true;
                } else if (url.startsWith("intent://")) {
                    Intent intent;
                    try {
                        intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME);
                    } catch (URISyntaxException ignored) {
                        intent = null;
                    }
                    if (intent != null) {
                       // cc.startActivity(intent);
                        if (mIntentUtils.startActivityForUrl(view, url)) {
                            return true;
                        }
                    }
                }
                else if(url.startsWith("ed2k://|file|")){//电驴下载
                    CLToast.Show(cc, "not support ed2k download", true);
                    return true;
                }
                else if(url.startsWith("thunder://")){//迅雷
                    String _url=url.replace("thunder://","");
                    _url=new String(Base64.decode(_url, Base64.DEFAULT));
                    if(_url.startsWith("AA") && _url.endsWith("ZZ")){
                        String _url2=_url.substring("AA".length(), _url.lastIndexOf("ZZ"));
                        if(_url2.length()>0 && _url.contains("http://")){
                            //可以下载
                            view.loadUrl(_url2);
                        }
                    }
                    else CLToast.Show(cc,"not support thunder download",true);
                    return true;
                }
                else if(url.startsWith("Flashget://")){//快车
                    CLToast.Show(cc,"not support Flashget download",true);
                    return true;
                }
                else if(url.startsWith("qqdl://")){//qq旋风
                    CLToast.Show(cc,"not support qqdl download",true);
                    return true;
                }
            }catch (Exception ex){
                //CL.CLOGI("override url load error:"+ex.toString());
                return true;
            }

            //从网页点击跑转到facebook 补丁
//            if (url.contains("facebook.com")) {
//                if (!view.getSettings().getUserAgentString().equals(Global.UaAndroid_Facebook)) {
//                    view.getSettings().setUserAgentString(Global.UaAndroid_Facebook);
//                    view.loadUrl(url);
//                    return true;
//                }
//            }
          //  CL.CLOGI("on override load false");
            //super.shouldOverrideUrlLoading(view, url);
            return false;
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
//            sniffer.is_exclude_url(url);
            if(listener!=null)listener.on_page_start(view,url,favicon);
//            is_exclude_url(url);
        }
        @Override
        public void onPageFinished(WebView view, String url) {
            if(listener!=null)listener.on_page_finish(view,url);
        }
//        @Override
//        public void onLoadResource(WebView view, String url) {
//            if(listener!=null)listener.on_intercept(view.getTitle(),url);
//        }

        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
//            super.shouldInterceptRequest(view, request);
//            if (Setting.Share_Setting().get_update() != 0) {
//                int count = Setting.Share_Setting().get_app_run_count();
//
//                if (!Setting.Share_Setting().get_subscription_flag() && count > Setting.Share_Setting().get_update_count() && !Setting.Share_Setting().get_download_enable()) {
//                    return null;
//                }
//            }
			
			Map<String, String> heads = request.getRequestHeaders();
            if (heads != null && heads.containsKey("Referer")) {
                String referer = heads.get("Referer");
                if (referer != null && referer.contains(".facebook.")) {
                    return super.shouldInterceptRequest(view, request);
                }
            }

            Map<String, String> list = request.getRequestHeaders();
            StringBuffer buffer = new StringBuffer();
            if (list.size() > 0) {
                for (Map.Entry<String, String> entry : list.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    if (buffer.length() > 0) {
                        buffer.append("[this<>map<>list]");
                    }
                    buffer.append(key);
                    buffer.append("[=+v+=]");
                    buffer.append(value);
                }
            }

            if (buffer.toString().length() > 0) {
                list_Header_Params = buffer.toString();
            }
            if (listener != null) listener.on_intercept(view, request.getUrl().toString(), buffer.toString());
            return super.shouldInterceptRequest(view, request);
        }

//        @Override
//        public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
//
//            if (Setting.Share_Setting().get_update() != 0) {
//                int count = Setting.Share_Setting().get_app_run_count();
//
//                if (!Setting.Share_Setting().get_subscription_flag() && count > Setting.Share_Setting().get_update_count() && !Setting.Share_Setting().get_download_enable()) {
//                    return null;
//                }
//            }
//            if (listener != null) listener.on_intercept(view, url);
//            return null;
//        }

        private List<Integer> getAllSslErrorMessageCodes(SslError error) {
            List<Integer> errorCodeMessageCodes = new ArrayList<Integer>(1);

            if (error.hasError(SslError.SSL_DATE_INVALID)) {
                errorCodeMessageCodes.add(R.string.str_message_certificate_date_invalid);
            }
            if (error.hasError(SslError.SSL_EXPIRED)) {
                errorCodeMessageCodes.add(R.string.str_message_certificate_expired);
            }
            if (error.hasError(SslError.SSL_IDMISMATCH)) {
                errorCodeMessageCodes.add(R.string.str_message_certificate_domain_mismatch);
            }
            if (error.hasError(SslError.SSL_NOTYETVALID)) {
                errorCodeMessageCodes.add(R.string.str_message_certificate_not_yet_valid);
            }
            if (error.hasError(SslError.SSL_UNTRUSTED)) {
                errorCodeMessageCodes.add(R.string.str_message_certificate_untrusted);
            }
            if (error.hasError(SslError.SSL_INVALID)) {
                errorCodeMessageCodes.add(R.string.str_message_certificate_invalid);
            }

            return errorCodeMessageCodes;
        }

        @Override
        public void onReceivedSslError(WebView webView, final SslErrorHandler handler, SslError error) {
//            AlertDialog.Builder builder = new AlertDialog.Builder(cc);
//            builder.setTitle("Warning");
//            builder.setMessage("untrusted certificate")
//                    .setCancelable(true)
//                    .setPositiveButton("confirm",
//                            new DialogInterface.OnClickListener() {
//                                @Override
//                                public void onClick(DialogInterface dialog, int id) {
//                                    handler.proceed();
//                                }
//                            })
//                    .setNegativeButton("cancel",
//                            new DialogInterface.OnClickListener() {
//                                @Override
//                                public void onClick(DialogInterface dialog, int id) {
//                                    handler.cancel();
//                                }
//                            });
//            AlertDialog alert = builder.create();
//            alert.show();

            if (sslWarningPreferences.recallBehaviorForDomain(webView.getUrl()) == SslWarningPreferences.Behavior.PROCEED) {
                handler.proceed();
                return;
            }

            if (sslWarningPreferences.recallBehaviorForDomain(webView.getUrl()) == SslWarningPreferences.Behavior.CANCEL) {
                handler.cancel();
                return;
            }

            List<Integer> errorCodeMessageCodes = getAllSslErrorMessageCodes(error);
            StringBuilder stringBuilder = new StringBuilder();
            for (Integer messageCode : errorCodeMessageCodes) {
                stringBuilder.append(" - ").append(cc.getString(messageCode)).append('\n');
            }
            String alertMessage = cc.getString(R.string.str_message_insecure_connection, stringBuilder.toString());
            View view = LayoutInflater.from(cc).inflate(R.layout.dialog_ssl_warning, null);
            CheckBox dontAskAgain = view.findViewById(R.id.checkBoxDontAskAgain);


            AlertDialog.Builder builder = new AlertDialog.Builder(cc);
            builder.setTitle("Warning");
            builder.setView(view);
            builder.setMessage(alertMessage)
                    .setCancelable(true)
                    .setPositiveButton(cc.getString(R.string.yes),
                            new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int id) {
                                    if (dontAskAgain.isChecked()) {
                                        sslWarningPreferences.rememberBehaviorForDomain(webView.getUrl(), SslWarningPreferences.Behavior.PROCEED);
                                    }
                                    handler.proceed();
                                }
                            })
                    .setNegativeButton(cc.getString(R.string.cancel),
                            new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int id) {
                                    if (dontAskAgain.isChecked()) {
                                        sslWarningPreferences.rememberBehaviorForDomain(webView.getUrl(), SslWarningPreferences.Behavior.CANCEL);
                                    }
                                    handler.cancel();
                                }
                            }).setOnCancelListener(new DialogInterface.OnCancelListener() {
                @Override
                public void onCancel(DialogInterface dialog) {
                    handler.cancel();
                }
            });
            AlertDialog alert = builder.create();
            alert.show();

        }

        @Override
        public void onFormResubmission(WebView view, final Message dontResend, final Message resend) {
            AlertDialog.Builder builder = new AlertDialog.Builder(cc);
            builder.setTitle("Form resubmission");
            builder.setMessage("Would you like to resend the data?")
                    .setCancelable(true)
                    .setPositiveButton("confirm",
                            new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int id) {
                                    resend.sendToTarget();
                                }
                            })
                    .setNegativeButton("cancel",
                            new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int id) {
                                    dontResend.sendToTarget();
                                }
                            });
            AlertDialog alert = builder.create();
            alert.show();
        }

        @Override
        public void onReceivedHttpAuthRequest(WebView view, final HttpAuthHandler handler, String host, String realm) {
            AlertDialog.Builder builder = new AlertDialog.Builder(cc);
            final EditText name = new EditText(cc);
            final EditText password = new EditText(cc);
            LinearLayout passLayout = new LinearLayout(cc);
            passLayout.setOrientation(LinearLayout.VERTICAL);

            passLayout.addView(name);
            passLayout.addView(password);

            name.setHint("Username");
            password.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD);
            password.setTransformationMethod(new PasswordTransformationMethod());
            password.setHint("Password");
            builder.setTitle("Login");
            builder.setView(passLayout);
            builder.setCancelable(true).setPositiveButton("login", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int id) {
                    String user = name.getText().toString();
                    String pass = password.getText().toString();
                    handler.proceed(user.trim(), pass.trim());
                }
            }).setNegativeButton("cancel", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int id) {
                    handler.cancel();
                }
            });
            AlertDialog alert = builder.create();
            alert.show();
        }

//        @Override
//        public boolean shouldOverrideKeyEvent(WebView view, KeyEvent event) {
//            return super.shouldOverrideKeyEvent(view, event);
//        }
//        @Override
//        public void onReceivedLoginRequest(WebView view, String realm, String account, String args) {
//            super.onReceivedLoginRequest(view, realm, account, args);
//        }
    }
    //WebChromeClient
    private class CLWebViewChrome extends WebChromeClient{
        private int progress;
        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            if(listener!=null) {
                boolean resutl = listener.on_progress(view, newProgress);
//                if (!resutl) {
//                    if (newProgress == 100) {
//                        progressbar.setVisibility(GONE);
//                    } else {
//                        if (progressbar.getVisibility() == GONE)
//                            progressbar.setVisibility(VISIBLE);
//                        progressbar.setProgress(newProgress);
//                    }
//                } else {
//                    progressbar.setVisibility(GONE);
//                }
            }
            if (progress != newProgress) {
                progress = newProgress;
                if (progress == 100) {
                    view.evaluateJavascript(webPerLoadJS, new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            if (!TextUtils.isEmpty(value) && value.startsWith("[\"") && value.endsWith("\"]")) {
                                String vs = value.substring(2, value.length() - 2);
                                String[] vss = vs.split("\",\"");
                                if (vss == null) {
                                    return;
                                }
                                StringBuffer buffer = new StringBuffer();
                                for (int i = 0; i < vss.length; ++i) {
                                    String k = "Referer";
                                    String v = view.getUrl();
                                    buffer.append(k);
                                    buffer.append("[=+v+=]");
                                    buffer.append(v);
                                    buffer.append("[this<>map<>list]");
                                    k = "User-Agent";
                                    v = getSettings().getUserAgentString();
                                    buffer.append(k);
                                    buffer.append("[=+v+=]");
                                    buffer.append(v);

                                    if (listener != null) listener.on_intercept(view, vss[i], buffer.toString());
                                }

                            }
                        }
                    });
                    // 页面重刷时清空
                    fbVid.clear();
                    view.evaluateJavascript(webFinishFacebookJS, null);
                }
            }
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
            if(listener!=null)listener.on_received_title(view,title);
        }

        @Override
        public void onReceivedIcon(WebView view, Bitmap icon) {
            if(listener!=null)listener.on_received_icon(view,icon);
        }

        @Override
        public void onShowCustomView(View view, CustomViewCallback callback) {
            if(listener!=null)listener.on_show_customview(CLWebkit.this, view,callback);
        }

        @Override
        public void onShowCustomView(View view, int requestedOrientation, CustomViewCallback callback) {
            super.onShowCustomView(view, requestedOrientation, callback);
        }

        @Override
        public void onHideCustomView() {
            if(listener!=null)listener.on_hide_customview(CLWebkit.this);
        }

        @Override
        public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
            CLToast.Show(cc,message,false);
            result.cancel();
            return true;
        }

        @Override
        public boolean onCreateWindow(WebView view, boolean isDialog, boolean isUserGesture, Message resultMsg) {
            return super.onCreateWindow(view, isDialog, isUserGesture, resultMsg);
        }

        @Override
        public void onCloseWindow(WebView window) {
            super.onCloseWindow(window);
        }

        //        @Override
//        public boolean onJsConfirm(WebView view, String url, String message, JsResult result) {
//            return super.onJsConfirm(view, url, message, result);
//        }
//
//        @Override
//        public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
//            return super.onJsPrompt(view, url, message, defaultValue, result);
//        }
//
//        @Override
//        public boolean onJsBeforeUnload(WebView view, String url, String message, JsResult result) {
//            return super.onJsBeforeUnload(view, url, message, result);
//        }
//
//        @Override
//        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
//            return super.onShowFileChooser(webView, filePathCallback, fileChooserParams);
//        }
    }

    public void Events(String url) {
        this.post(new Runnable() {
            @Override
            public void run() {
                loadUrl("javascript:" + Global.js_inject);
            }
        });

        ((Activity)cc).runOnUiThread(new Runnable() {
            @Override
            public void run() {

            }
        });

    }

    public class JSVideoTag{
        @JavascriptInterface
        public void finded(final String url, final String msg, final String type) {
            if (url == null && msg == null && type == null)
                return;
            String tmpStr = "";
            if (msg != null && type.matches("mp4")) {
                if (msg.indexOf("http://") <= -1 && msg.indexOf("https://") <= -1) {
                    tmpStr = "http:"+msg;
                } else {
                    tmpStr = msg;
                }
                Server.Sniff_Url(new ResSniffer.SniffData(tmpStr, "",""));
            }

        }
    }


    // facebook视频简单去重复，避免过多嗅探导致封号
    private LinkedHashSet<String> fbVid = new LinkedHashSet<>();


    private class JSBridge {
        @JavascriptInterface
        public void log(String info) {
            //   Utils.Log("js_bridge:" + info);
        }

        @JavascriptInterface
        public void onInfo(String id, String referer, String src) {
            if (fbVid.contains(id)) {
                return;
            } else {
                fbVid.add(id);
            }
            post(new Runnable() {
                @Override
                public void run() {
                    StringBuffer buffer = new StringBuffer();
                    String k = "Referer";
                    String v = referer;
                    buffer.append(k);
                    buffer.append("[=+v+=]");
                    buffer.append(v);
                    buffer.append("[this<>map<>list]");
                    k = "User-Agent";
                    v = getSettings().getUserAgentString();
                    buffer.append(k);
                    buffer.append("[=+v+=]");
                    buffer.append(v);

                    if (listener != null) listener.on_intercept(id, src, buffer.toString());
                }
            });
        }
    }

    private final String webPerLoadJS = "javascript:function grab() {" +
            "var result = [];" +
            "var links = document.getElementsByTagName('link');" +
            "if (links != null) {" +
            "for (var i = 0; i < links.length; i++) {" +
            "var link = links[i];" +
            "if (link.rel == \"preload\" && (link.as == \"fetch\" || link.as == \"video\" || link.as == \"audio\")) {" +
            "result.push(link.href);" +
            "}" +
            "}" +
            "}" +
            "return result" +
            "}grab();";

    private final String webFinishFacebookJS = "javascript:function grab_facebook() {" +
            "Array.from(document.getElementsByTagName('div')).forEach(" +
            "function(d){" +
            "if(d.hasAttribute('data-video-url')){" +
            "var txt = d.getAttribute('data-video-url');" +
            "window.jsbridge.onInfo(txt, document.location.origin, txt);" +
            "}});" +
            "}" +
            "if (document.location.host.includes('.facebook.')){" +
            "setInterval(function(){grab_facebook()}, 5000);" +
            "}";
//    private final String webFinishFacebookJS = "javascript:function grab_facebook() {" +
//            "Array.from(document.getElementsByTagName('div')).forEach(" +
//            "function(d){" +
//            "if(d.hasAttribute('data-store')){" +
//            "var txt = JSON.parse(d.getAttribute('data-store'));" +
//            "if (txt.hasOwnProperty('videoID') && txt.hasOwnProperty('src')){" +
//            "window.jsbridge.onInfo(txt.videoID, document.location.origin, txt.src);" +
//            "}}});" +
//            "}" +
//            "if (document.location.host.includes('.facebook.')){" +
//            "setInterval(function(){grab_facebook()}, 2000);" +
//            "}";


}
