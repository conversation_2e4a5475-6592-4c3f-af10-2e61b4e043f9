package lion.widget;

import android.app.Activity;
import android.app.Dialog;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import amazon.browser.video.downloader.R;

import lion.CL;


public class GuideNovice extends CLGuideDialog {


    private Activity cc;
    private FrameLayout root;
    private ViewPager viewPager;

    private View pointOne;
    private View pointTwo;
    private View pointThree;
    private View pointFour;
    private Bitmap bitmapOne;
    private Bitmap bitmapTwo;
    private Bitmap bitmapThree;
    private Bitmap bitmapFour;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        cc = this.getActivity();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        root = (FrameLayout) inflater.inflate(R.layout.layout_guide_use, null);
        viewPager = root.findViewById(R.id.viewpager);
        pointOne = root.findViewById(R.id.guide_point_one);
        pointTwo = root.findViewById(R.id.guide_point_two);
        pointThree = root.findViewById(R.id.guide_point_three);
        pointFour = root.findViewById(R.id.guide_point_Four);
        return root;
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        viewPager.setOffscreenPageLimit(4);
        viewPager.addOnPageChangeListener(listenerViewpager);
        viewPager.setAdapter(new AdapterForGuide());
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (bitmapOne != null && !bitmapOne.isRecycled()) {
            bitmapOne.recycle();
            bitmapOne = null;
        }
        if (bitmapTwo != null && !bitmapTwo.isRecycled()) {
            bitmapTwo.recycle();
            bitmapTwo = null;
        }
        if (bitmapThree != null && !bitmapThree.isRecycled()) {
            bitmapThree.recycle();
            bitmapThree = null;
        }

        if (bitmapFour != null && !bitmapFour.isRecycled()) {
            bitmapFour.recycle();
            bitmapFour = null;
        }
    }

    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        dialog.getWindow().setBackgroundDrawable(null);
        dialog.getWindow().getDecorView().setBackground(null);
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().getAttributes().width = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().getAttributes().height = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().getAttributes().gravity = Gravity.FILL;
        return dialog;
    }

    private ViewPager.OnPageChangeListener listenerViewpager = new ViewPager.SimpleOnPageChangeListener() {
        @Override
        public void onPageSelected(int position) {
            pointOne.setBackgroundResource(R.drawable.gray_point);
            pointTwo.setBackgroundResource(R.drawable.gray_point);
            pointThree.setBackgroundResource(R.drawable.gray_point);
            pointFour.setBackgroundResource(R.drawable.gray_point);
            if (position == 0) {
                pointOne.setBackgroundResource(R.drawable.blue_point);
            } else if (position == 1) {
                pointTwo.setBackgroundResource(R.drawable.blue_point);
            } else if (position == 2) {
                pointThree.setBackgroundResource(R.drawable.blue_point);
            } else {
                pointFour.setBackgroundResource(R.drawable.blue_point);
            }
        }
    };

    private class AdapterForGuide extends PagerAdapter {
        @Override
        public int getCount() {
            return 4;
        }

        @Override
        public boolean isViewFromObject(@NonNull View view, @NonNull Object o) {
            return view == o;
        }

        @NonNull
        @Override
        public Object instantiateItem(@NonNull ViewGroup container, int position) {
            View v = null;
            if (position == 0) {
                v = createOne();
            } else if (position == 1) {
                v = createTwo();
            } else if (position == 2) {
                v = createThree();
            } else if (position == 3) {
                v = createFour();
            }
            container.addView(v);
            return v;
        }
    }

    private View createOne() {
        FrameLayout container = (FrameLayout) LayoutInflater.from(cc).inflate(R.layout.layout_guide_use_item, null);
        ImageView iv = container.findViewById(R.id.guide_image);
        TextView tv = container.findViewById(R.id.guide_text);
        ImageView back = container.findViewById(R.id.guide_arrow_left);
        ImageView forward = container.findViewById(R.id.guide_arrow_right);
        back.setVisibility(View.GONE);
        View.OnClickListener listenerClick = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                viewPager.setCurrentItem(1, true);
            }
        };
        container.setOnClickListener(listenerClick);
        forward.setOnClickListener(listenerClick);
        tv.setText(R.string.str_guide_use_one);

        if (bitmapOne == null) {
            try {
                AssetManager am = cc.getAssets();
               // bitmapOne = BitmapFactory.decodeStream(am.open("picture/guide_novice_1.png"));
                bitmapOne = BitmapFactory.decodeStream(am.open("picture/guide1.png"));
            } catch (Exception ex) {}

            if (bitmapOne != null && !bitmapOne.isRecycled()) {
                int[] wh = CL.GetScreenSize(cc);
                float width = wh[0] < wh[1] ? wh[0] : wh[1];
                float height = width / ((float) bitmapOne.getWidth() / (float) bitmapOne.getHeight());
                ViewGroup.LayoutParams lp = iv.getLayoutParams();
                lp.width = (int) width;
                lp.height = (int) height;
                iv.setImageBitmap(bitmapOne);
            }
        }
        return container;
    }

    private View createTwo() {
        FrameLayout container = (FrameLayout) LayoutInflater.from(cc).inflate(R.layout.layout_guide_use_item, null);
        ImageView iv = container.findViewById(R.id.guide_image);
        TextView tv = container.findViewById(R.id.guide_text);
        final ImageView back = container.findViewById(R.id.guide_arrow_left);
        ImageView forward = container.findViewById(R.id.guide_arrow_right);
        View.OnClickListener listenerClick = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (v == back) {
                    viewPager.setCurrentItem(0, true);
                } else {
                    viewPager.setCurrentItem(2, true);
                }
            }
        };
        back.setOnClickListener(listenerClick);
        container.setOnClickListener(listenerClick);
        forward.setOnClickListener(listenerClick);
        tv.setText(R.string.str_guide_use_two);

        if (bitmapTwo == null) {
            try {
                AssetManager am = cc.getAssets();
               // bitmapTwo = BitmapFactory.decodeStream(am.open("picture/guide_novice_2.png"));
                bitmapTwo = BitmapFactory.decodeStream(am.open("picture/guide2.png"));
            } catch (Exception ex) {}

            if (bitmapTwo != null && !bitmapTwo.isRecycled()) {
                int[] wh = CL.GetScreenSize(cc);
                float width = wh[0] < wh[1] ? wh[0] : wh[1];
                float height = width / ((float) bitmapTwo.getWidth() / (float) bitmapTwo.getHeight());
                ViewGroup.LayoutParams lp = iv.getLayoutParams();
                lp.width = (int) width;
                lp.height = (int) height;
                iv.setImageBitmap(bitmapTwo);
            }
        }
        return container;
    }

    private View createThree() {
        FrameLayout container = (FrameLayout) LayoutInflater.from(cc).inflate(R.layout.layout_guide_use_item, null);
        ImageView iv = container.findViewById(R.id.guide_image);
        TextView tv = container.findViewById(R.id.guide_text);
        final ImageView back = container.findViewById(R.id.guide_arrow_left);
        ImageView forward = container.findViewById(R.id.guide_arrow_right);
        View.OnClickListener listenerClick = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (v == back) {
                    viewPager.setCurrentItem(1, true);
                } else {
                    viewPager.setCurrentItem(3, true);
                }
            }
        };
        container.setOnClickListener(listenerClick);
        back.setOnClickListener(listenerClick);
        forward.setOnClickListener(listenerClick);
        tv.setText(R.string.str_guide_use_three);

        if (bitmapThree == null) {
            try {
                AssetManager am = cc.getAssets();
                //bitmapThree = BitmapFactory.decodeStream(am.open("picture/guide_novice_3.png"));
                bitmapThree = BitmapFactory.decodeStream(am.open("picture/guide3.png"));
            } catch (Exception ex) {}

            if (bitmapThree != null && !bitmapThree.isRecycled()) {
                int[] wh = CL.GetScreenSize(cc);
                float width = wh[0] < wh[1] ? wh[0] : wh[1];
                float height = width / ((float) bitmapThree.getWidth() / (float) bitmapThree.getHeight());
                ViewGroup.LayoutParams lp = iv.getLayoutParams();
                lp.width = (int) width;
                lp.height = (int) height;
                iv.setImageBitmap(bitmapThree);
            }
        }
        return container;
    }

    private View createFour() {
        FrameLayout container = (FrameLayout) LayoutInflater.from(cc).inflate(R.layout.layout_guide_use_item, null);
        ImageView iv = container.findViewById(R.id.guide_image);
        TextView tv = container.findViewById(R.id.guide_text);
        ImageView back = container.findViewById(R.id.guide_arrow_left);
        ImageView forward = container.findViewById(R.id.guide_arrow_right);
        final TextView ok = container.findViewById(R.id.guide_over);
        ok.setVisibility(View.VISIBLE);
        forward.setVisibility(View.GONE);
        View.OnClickListener listenerClick = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (v == ok) {
                    dismissAllowingStateLoss();
                } else {
                    viewPager.setCurrentItem(1, true);
                }
            }
        };
        ok.setOnClickListener(listenerClick);
        back.setOnClickListener(listenerClick);
        tv.setText(R.string.str_guide_use_not_youtube);
        tv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));

        if (bitmapFour == null) {
            try {
                AssetManager am = cc.getAssets();
                bitmapFour = BitmapFactory.decodeStream(am.open("picture/guide_novice_4.png"));
            } catch (Exception ex) {}

            if (bitmapFour != null && !bitmapFour.isRecycled()) {
                int[] wh = CL.GetScreenSize(cc);
                LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams )iv.getLayoutParams();
                lp.width = (int) CL.DIP2PX(49);
                lp.height = (int) CL.DIP2PX(49);
                lp.setMargins(0, (int) CL.DIP2PX(120), 0, 0);
                iv.setLayoutParams(lp);
                iv.setImageBitmap(bitmapFour);


                FrameLayout.LayoutParams lp_txt = (FrameLayout.LayoutParams )tv.getLayoutParams();
                lp_txt.setMargins((int) CL.DIP2PX(60), (int) CL.DIP2PX(180), (int) CL.DIP2PX(60), 0);
                tv.setLayoutParams(lp_txt);
            }
        }
        return container;
    }
}
