package lion.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.PointF;
import android.view.MotionEvent;
import android.view.View;

import lion.CL;

public class Ios<PERSON>heckBox extends View {

	private Paint paint;
	private CBMonopolyTouchEvent listener_monopoly;
	private CBCheckEvent eventer;
	private boolean first_draw =true;
	private PointF down_point=new PointF();
	private boolean selected=false;
	private boolean ih_move_good=false;
	private boolean ih_click=true;
	private float move_distance=0;
	private float p_x;
	
	public IosCheckBox(Context context,CBMonopolyTouchEvent listener) {
		super(context);
		paint=new Paint();
		paint.setAntiAlias(true);
		this.listener_monopoly =listener;
	}

	public void set_check_listener(CBCheckEvent eventer){
		this.eventer=eventer;
	}

	public void set_check(boolean check){
		selected=check;
		if(this.getWidth()==0)return;
		int _w=this.getWidth();
		int _h=this.getHeight()-4;
		float _r=_h/2.0f;

		move_distance=0;
		if(selected)p_x=_w-_r;
		else p_x=_r;
		postInvalidate();
	}

	public boolean get_check(){
		return selected;
	}

	@Override
	protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
		int _ww= CL.DIP2PX_INT(38);
		int _hh= CL.DIP2PX_INT(22);
		int w_mode=MeasureSpec.getMode(widthMeasureSpec);
		int w_size=MeasureSpec.getSize(widthMeasureSpec);
		if(w_mode==MeasureSpec.AT_MOST || w_mode==MeasureSpec.UNSPECIFIED){
			w_size=_ww;
		}
		int h_mode=MeasureSpec.getMode(heightMeasureSpec);
		int h_size=MeasureSpec.getSize(heightMeasureSpec);
		if(h_mode==MeasureSpec.AT_MOST || h_mode==MeasureSpec.UNSPECIFIED){
			h_size=_hh;
		}
		if(w_size<_ww)w_size=_ww;
		if(h_size<_hh)h_size=_hh;
		this.setMeasuredDimension(w_size, h_size);
	}
	
	@Override
	public void draw(Canvas canvas) {
		super.draw(canvas);
		int _w = this.getWidth();
		int _h = this.getHeight() - 4;
		float _r = _h / 2.0f;
		float _ry = _r + 2;

		paint.setStyle(Style.FILL_AND_STROKE);
		if (selected) {
			paint.setColor(0xff12c583);
			canvas.drawCircle(_r, _ry, _r, paint);
			canvas.drawCircle(_w - _r, _ry, _r, paint);
			canvas.drawRect(_r, 2, _w - _r, _h + 2, paint);
		} else {
			paint.setColor(0xffa3a3a3);
			canvas.drawCircle(_r, _ry, _r, paint);
			canvas.drawCircle(_w - _r, _ry, _r, paint);
			canvas.drawRect(_r, 2, _w - _r, _h + 2, paint);
		}

		if (first_draw) {
			first_draw = false;
			paint.setStyle(Style.FILL);
			if (selected) {
				p_x = _w - _r;
				paint.setColor(Color.WHITE);
				canvas.drawCircle(p_x, _ry, _r - 2, paint);
			} else {
				p_x = _r;
				paint.setColor(0xff888888);
				canvas.drawCircle(p_x, _ry, _r - 2, paint);
			}
			return;
		}

		p_x += move_distance;
		if (p_x > _w - _r) p_x = _w - _r;
		else if (p_x < _r) p_x = _r;

		paint.setStyle(Style.FILL);
		if (selected) {
			paint.setColor(Color.WHITE);
			canvas.drawCircle(p_x, _ry, _r - 2, paint);
		} else {
			paint.setColor(0xff888888);
			canvas.drawCircle(p_x, _ry, _r - 2, paint);
		}
	}
	
	@Override
	public boolean onTouchEvent(MotionEvent event) {
		if(event.getAction()==MotionEvent.ACTION_DOWN){
			if(listener_monopoly !=null) listener_monopoly.on_monopoly_event();
			down_point.x=event.getX();
			down_point.y=event.getY();
			move_distance=0;
			ih_click=true;
		}
		else if(event.getAction()==MotionEvent.ACTION_MOVE){
			if(!ih_move_good){//先判断是否在移动范围中
				float _xv=event.getX();
				float _yv=event.getY();
				float _v=CL.DIP2PX(5);
				float _xx=Math.abs(_xv-down_point.x);
				float _yy=Math.abs(_yv-down_point.y);
				if(_xx>_v || _yy>_v){
					ih_click=false;
					if(_yy>_v)if(listener_monopoly !=null) listener_monopoly.on_discard_event();
					else if(_xx>_v)ih_move_good=true;
				}
			}
			else{//移动
				float _x=event.getX();
				move_distance=_x-down_point.x;
				down_point.x=_x;
			}
			this.invalidate();
		}
		else if(event.getAction()==MotionEvent.ACTION_UP || event.getAction()==MotionEvent.ACTION_CANCEL){
			boolean _last_selected=selected;
			if(listener_monopoly !=null) listener_monopoly.on_discard_event();
			ih_move_good=false;
			move_distance=0;
			
			int _w=this.getWidth();
			int _h=this.getHeight()-4;
			float _r=_h/2.0f;

			if(ih_click){
				selected=!selected;
				if(selected)p_x=_w-_r;
				else p_x=_r;
			}else{
				if(p_x>_w/2){
					selected=true;
					p_x=_w-_r;
				}
				else {
					p_x=_r;
					selected=false;
				}
			}
			this.invalidate();
			if(selected!=_last_selected){
				if(this.eventer!=null)this.eventer.on_change(this, selected);
			}
		}
		return true;
	}
	
	//回调接口
	public interface CBMonopolyTouchEvent{
		void on_monopoly_event();
		void on_discard_event();
	}
	
	public interface CBCheckEvent{
		void on_change(IosCheckBox box, boolean check);
	}
}
