package lion.widget;

import android.net.Uri;

import java.util.HashMap;

public class SessionSslWarningPreferences implements  SslWarningPreferences {
    private static SessionSslWarningPreferences ferences;
    private HashMap<String, Behavior> ignoredSslWarnings = new HashMap<String, Behavior>();

    public static synchronized SslWarningPreferences getInstance() {
        if (null == ferences) {
            ferences = new SessionSslWarningPreferences();
        }
        return ferences;
    }

    public void rememberBehaviorForDomain(String url, Behavior behavior) {
        if (url != null) {
            Uri newUrl = Uri.parse(url);
            if (newUrl != null && newUrl.getHost() != null) {
                ignoredSslWarnings.put(url, behavior);
            }
        }
    }


    public Behavior recallBehaviorForDomain(String url) {
        if (url != null) {
            Uri newUrl = Uri.parse(url);
            if (newUrl != null && newUrl.getHost() != null) {
                return ignoredSslWarnings.get(url);
            }
        }
        return Behavior.CANCEL;
    }
}
