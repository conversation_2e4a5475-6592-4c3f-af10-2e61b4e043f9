package lion.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.MotionEvent;
import android.view.View;

import lion.CL;


public class VideoProgressController extends View {
	
	public interface CLVideoProgressListener{
		void on_progress_move(float progress);
		void on_progress_click(float progress);
	}

	private int left_pad= CL.DIP2PX_INT(6);
	private int up_pad=CL.DIP2PX_INT(2);
	private int circle_radius= CL.DIP2PX_INT(5);
	
	
	private Paint paint;
	private float progress;
	private float progress_buff;
	private CLVideoProgressListener listen;
	
	private boolean on_touch=false;

	public VideoProgressController(Context context, CLVideoProgressListener listener) {
		super(context);
		this.setClickable(true);
		paint=new Paint();
		paint.setAntiAlias(true);
		paint.setStyle(Paint.Style.FILL_AND_STROKE);
		this.listen=listener;
	}
	
	public void set_data(float v){
		if(v<0.0f)progress=0.0f;
		else if(v>1.0f)progress=1.0f;
		else progress=v;
		this.postInvalidate();
	}
	public void set_data(int v,int MAX){
		if(MAX==0)return;
		float _p=(float)v/(float)MAX;
		set_data(_p);
	}
	public void set_buff_data(int v){
		this.progress_buff=v/100.0f;
		this.postInvalidate();
	}
	
	public float get_data(){
		return progress;
	}
	public int get_data(int MAX){
		return (int)((float)MAX*progress);
	}
	
	@Override
	public boolean onTouchEvent(MotionEvent event) {
		if(!this.isClickable())return false;
		
		if(!on_touch){
			on_touch=true;
		}
		
		int _x=(int)event.getX();
		if(_x<left_pad)_x=left_pad;
		if(_x>this.getWidth()-left_pad)_x=this.getWidth()-left_pad;
		_x-=left_pad;
		int _xx=this.getWidth()-left_pad*2;
		
		if(event.getAction()==MotionEvent.ACTION_DOWN ||event.getAction()==MotionEvent.ACTION_MOVE){
			progress=(float)_x/(float)_xx;
			listen.on_progress_move(progress);
		}
		else if(event.getAction()==MotionEvent.ACTION_UP ||event.getAction()==MotionEvent.ACTION_CANCEL){
			progress=(float)_x/(float)_xx;
			listen.on_progress_click(progress);
			on_touch=false;
		}
		this.invalidate();
		return true;
	}

	@Override
	protected void onDraw(Canvas canvas) {
		super.onDraw(canvas);
		
		paint.setColor(0xff393939);
		canvas.drawRect(left_pad, this.getHeight()/2-up_pad, this.getWidth()-left_pad, this.getHeight()/2+up_pad, paint);
		
		paint.setColor(0x88888888);
		canvas.drawRect(left_pad, this.getHeight()/2-up_pad, progress_buff*(this.getWidth()-left_pad*2)+left_pad, this.getHeight()/2+up_pad, paint);
			
		paint.setColor(0xff39bbff);
		canvas.drawRect(left_pad, this.getHeight()/2-up_pad, progress*(this.getWidth()-left_pad*2)+left_pad, this.getHeight()/2+up_pad, paint);
		canvas.drawCircle(progress*(this.getWidth()-left_pad*2)+left_pad, this.getHeight()/2, circle_radius, paint);
	}
}
