/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package lion.widget;

import android.content.Context;
import android.graphics.PointF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.Scroller;

//import com.browser.lionpro.R;
import amazon.browser.video.downloader.R;
import amazon.browser.lionpro.datas.Setting;
import amazon.browser.lionpro.screen.Home;
import amazon.browser.lionpro.views.WebToolBar;


/**
 * Created by chenlu<PERSON> on 17/1/2.
 */

public class WebPageScrollView extends LinearLayout {

    private PointF mDownPoint = new PointF();
    // 方向 0:无方向 1:上 2:下
    private int ori = 0;
    private int oriCopy = 0;
    private int mTouchSlopUp = 0;
    private int mTouchSlopDown = 0;
    private int mBarHeight;
    private Scroller scroller;
    private boolean isExhibition = false;
    private boolean isMoving = false;
    private WebToolBar mBottomBar;
    public WebPageScrollView(Context context, Home home) {
        super(context);
        mBottomBar = home.getToolbar();
        init();
    }

    public WebPageScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        int touchSlop = ViewConfiguration.get(this.getContext()).getScaledTouchSlop();
        mTouchSlopUp = -touchSlop;
        mTouchSlopDown = touchSlop;
        mBarHeight = getContext().getResources().getDimensionPixelSize(R.dimen.top_bar_height);
        scroller = new Scroller(getContext());
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        int action = ev.getAction();
        float x = ev.getX();
        float y = ev.getY();
        if (action == MotionEvent.ACTION_DOWN) {
            mDownPoint.x = x;
            mDownPoint.y = y;
            ori = 0;
        } else if (action == MotionEvent.ACTION_MOVE) {

        } else if (action == MotionEvent.ACTION_UP) {
            float yoffset = mDownPoint.y - y;
            if (yoffset > mTouchSlopDown) {
                ori = 1;
                processSearchBarAnim();
            } else if (yoffset < mTouchSlopUp) {
                ori = 2;
                processSearchBarAnim();
            }
            postInvalidate();
        }

        return super.onInterceptTouchEvent(ev);
    }

    private View mSearchBar;
    private FrameLayout mWebview;
    private ProgressBar mProgressBar;

    private void processSearchBarAnim() {
        if (!Setting.Share_Setting().get_fullscreen_mode()) {
            return;
        }

        if (this.getChildCount() > 1) {
            mSearchBar = this.getChildAt(0);
            mWebview = (FrameLayout)this.getChildAt(1);

            mProgressBar = (ProgressBar)mWebview.getChildAt(mWebview.getChildCount()-1);

//            if (this.getChildCount() > 4) {
//                mBottomBar = this.getChildAt(4);
//            } else {
//                mBottomBar = this.getChildAt(3);
//            }

            if (ori == 1 && oriCopy != 1) {
                oriCopy = 1;
                if (mSearchBar.getY() > -mBarHeight) {
                    scroller.startScroll(0, 0, 0, -mBarHeight, 800);
                    isExhibition = true;
                    onExhibition(isExhibition);
                    isMoving = true;
                    mBottomBar.setVisibility(View.GONE);
//                    if (mProgressBar.getProgress() > 0)
//                    mProgressBar.setVisibility(View.VISIBLE);

                }
            } else if (ori == 2 && oriCopy != 2) {
                oriCopy = 2;
                if (mSearchBar.getY() < 0) {
                    scroller.startScroll(0, -mBarHeight, 0, mBarHeight, 800);
                    isExhibition = false;
                    onExhibition(isExhibition);
                    isMoving = true;
                    mBottomBar.setVisibility(View.VISIBLE);

//                    mProgressBar.setVisibility(View.GONE);

                }
            }
        }
    }

    @Override
    public void computeScroll() {
        if (scroller.computeScrollOffset()) {
            int y = scroller.getCurrY();
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) mSearchBar.getLayoutParams();
            lp.setMargins(0, y, 0, 0);
            mSearchBar.setLayoutParams(lp);

//            lp = (LinearLayout.LayoutParams) mBottomBar.getLayoutParams();
//
//            if (Math.abs(y) <= mBarHeight) {
//                lp.setMargins(0, lp.topMargin-y, 0, 0);
//            }
//
//            mBottomBar.setLayoutParams(lp);
        } else {
            isMoving = false;
        }
    }

    public boolean isExhibition() {
        return isExhibition;
    }

    public void onExhibition(boolean exhibition) {

    }

    public boolean isMoving() {
        return isMoving;
    }

    public void autoSmoothShow(boolean show) {
        if (show) {
            if (isExhibition) {
                ori = 2;
                processSearchBarAnim();
            }
        } else {
            if (!isExhibition) {
                ori = 1;
                processSearchBarAnim();
            }
        }
    }

    public void immediateShow(boolean show) {
        if (show) {
            if (isExhibition) {
                isExhibition = false;
                ori = 2;
                oriCopy = 2;
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mSearchBar.getLayoutParams();
                lp.setMargins(0, 0, 0, 0);
                mSearchBar.setLayoutParams(lp);
            }
        } else {
            if (!isExhibition) {
                isExhibition = true;
                ori = 1;
                oriCopy = 1;
                RelativeLayout.LayoutParams lp = (RelativeLayout.LayoutParams) mSearchBar.getLayoutParams();
                lp.setMargins(0, -mBarHeight, 0, 0);
                mSearchBar.setLayoutParams(lp);
            }
        }
    }
}
