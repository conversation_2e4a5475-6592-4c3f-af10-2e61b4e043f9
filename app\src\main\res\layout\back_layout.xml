<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        >
        <ImageView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/back_normal"
            android:layout_marginStart="8dp"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"
            />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_more_apps"
            android:textColor="#ffd0d0d0"
            android:textSize="18sp"
            android:layout_centerInParent="true"
            />
    </RelativeLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="2px"
        android:background="#ff444444"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/apps_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</LinearLayout>
