<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/bg_main_title"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent">
        <amazon.browser.lionpro.toys.CommonBackButton
            android:id="@+id/backArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/left_title"
            app:layout_constraintBottom_toTopOf="@id/div"

            tools:ignore="MissingConstraints" />
        <TextView
            android:id="@+id/left_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_found"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@+id/backArrow"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/right_title"
            app:layout_constraintBottom_toTopOf="@id/div"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/right_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_downloading"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@+id/left_title"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/delete"
            app:layout_constraintBottom_toTopOf="@id/div"
            tools:ignore="MissingConstraints" />
        <ImageView
            android:id="@+id/delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="20dp"
            android:clickable="false"
            android:visibility="invisible"
            android:background="@mipmap/icon_clear_click"
            app:layout_constraintLeft_toRightOf="@+id/right_title"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@id/div"
            tools:ignore="MissingConstraints" />
        <View
            android:id="@+id/div"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/left_title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@id/viewpager2"
            tools:ignore="MissingConstraints" />
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpager2"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:descendantFocusability="blocksDescendants"
            app:layout_constraintVertical_weight="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/div"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="MissingConstraints"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>