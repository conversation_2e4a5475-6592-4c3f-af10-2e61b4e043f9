<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2009 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->



    <!--<com.aladdin.video.views.RoundCornerProgressBar-->
        <!--xmlns:android="http://schemas.android.com/apk/res/android"-->
        <!--xmlns:app="http://schemas.android.com/apk/res-auto"-->
        <!--android:id="@+id/progress_bar"-->
        <!--android:layout_width="wrap_content"-->
        <!--android:layout_height="20dp"-->
        <!--android:layout_marginBottom="10dp"-->
        <!--android:layout_marginTop="10dp"-->
        <!--app:rcBackgroundColor="@color/custom_progress_background"-->
        <!--app:rcBackgroundPadding="2dp"-->
        <!--app:rcMax="100"-->
        <!--app:rcProgress="90"-->
        <!--app:rcProgressColor="@color/custom_progress_red_progress"-->
        <!--app:rcRadius="20dp" />-->

<amazon.browser.lionpro.views.HorizontalProgressBarWithNumber
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/id_progressbar01"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:progress_text_visibility="invisible"
    />