<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_marginLeft="8dp"
    android:layout_marginRight="15dp"
    android:layout_marginTop="2dp"
    >

    <ImageView
        android:id="@+id/v_icon"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="15dp"
        android:layout_marginTop="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_name"
        />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:ellipsize="middle"
        android:lines="2"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/font_txt_color"
        app:layout_constraintVertical_weight="1"
        app:layout_constraintLeft_toRightOf="@+id/v_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_more"
        app:layout_constraintBottom_toTopOf="@+id/v_progress"
        tools:text="Test视频aaaaaaa" />

    <ImageView
        android:id="@+id/btn_more"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:padding="10dp"
        android:src="@mipmap/more3"
        app:layout_constraintVertical_chainStyle="spread_inside"
        app:layout_constraintBottom_toTopOf="@+id/btn_download"
        app:layout_constraintLeft_toRightOf="@+id/tv_name"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseAppTint" />

    <amazon.browser.lionpro.views.ProgressBar
        android:id="@+id/v_progress"
        android:layout_width="0dp"
        android:layout_height="4dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintLeft_toLeftOf="@id/tv_name"
        app:layout_constraintRight_toRightOf="@id/tv_name"
        app:layout_constraintTop_toBottomOf="@id/tv_name"
        app:layout_constraintBottom_toTopOf="@+id/tv_length"
        />

    <TextView
        android:id="@+id/tv_format"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:textColor="@color/font_txt_color"
        app:layout_constraintLeft_toLeftOf="@+id/v_progress"
        app:layout_constraintRight_toLeftOf="@id/tv_length"
        app:layout_constraintTop_toBottomOf="@id/v_progress"
        app:layout_constraintBaseline_toBaselineOf="@+id/tv_length"
        android:textSize="14sp"
        tools:text="M3U8"
        />

    <TextView
        android:id="@+id/tv_length"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="2dp"
        android:textColor="@color/font_txt_color"
        app:layout_constraintLeft_toRightOf="@id/tv_format"
        app:layout_constraintRight_toLeftOf="@id/tv_speed"
        app:layout_constraintTop_toBottomOf="@id/v_progress"
        app:layout_constraintBottom_toBottomOf="parent"
        android:textSize="14sp"
        android:gravity="center"
        android:singleLine="true"
        tools:text="999.99M/130.99M"
        />

    <TextView
        android:id="@+id/tv_speed"
        android:layout_width="52dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toRightOf="@id/tv_length"
        app:layout_constraintRight_toLeftOf="@+id/btn_download"
        app:layout_constraintTop_toBottomOf="@id/v_progress"
        app:layout_constraintBaseline_toBaselineOf="@+id/tv_length"
        android:textColor="@color/font_txt_color"
        android:textSize="12sp"
        android:gravity="center"
        tools:text="999k/s"
        />

    <ImageView
        android:id="@+id/btn_download"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:layout_constraintLeft_toLeftOf="@id/btn_more"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_more"
        app:layout_constraintBottom_toBottomOf="parent"
        android:padding="8dp"
        android:src="@mipmap/dl_download_normal"
        tools:ignore="UseAppTint" />


    <View
        android:layout_width="0px"
        android:layout_height="1px"
        android:layout_marginTop="0dp"
        app:layout_constraintTop_toBottomOf="@id/tv_length"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/tv_format"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/background_dark"
        />


</androidx.constraintlayout.widget.ConstraintLayout>