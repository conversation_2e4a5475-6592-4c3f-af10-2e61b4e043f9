<?xml version="1.0" encoding="utf-8"?>
<amazon.browser.lionpro.views.KProgressHUD.BackgroundLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/background"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="16dp"
    android:background="@android:color/transparent"
    android:gravity="center"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/label"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="4dp"/>

    <TextView
        android:id="@+id/details_label"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="13sp"/>
</amazon.browser.lionpro.views.KProgressHUD.BackgroundLayout>