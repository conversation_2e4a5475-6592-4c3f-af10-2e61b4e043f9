<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:orientation="horizontal">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/AppTheme.AdAttribution"
        />

    <ImageView
        android:id="@+id/left_img"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:padding="8dp"
        android:src="@drawable/gmts_ad_sources_icon"/>

    <TextView
        android:id="@+id/right_txt"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:textColor="@color/divider"
        android:text="xxdddddxxxx"/>
</LinearLayout>
