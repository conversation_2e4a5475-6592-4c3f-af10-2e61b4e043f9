<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:supportsRtl="false"
    android:background="@color/bg_main_title"
    >

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>


    <FrameLayout
        android:layout_width="60dp"
        android:layout_height="18dp"
        android:layout_marginBottom="8dp"
        android:supportsRtl="false"
        android:layout_gravity="center_horizontal|bottom">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:supportsRtl="false">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1">
                <View
                    android:id="@+id/guide_point_one"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_gravity="center"
                    android:background="@drawable/blue_point"/>
            </FrameLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1">
                <View
                    android:id="@+id/guide_point_two"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_gravity="center"
                    android:background="@drawable/gray_point"/>
            </FrameLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1">
                <View
                    android:id="@+id/guide_point_three"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_gravity="center"
                    android:background="@drawable/gray_point"/>
            </FrameLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1">
                <View
                    android:id="@+id/guide_point_Four"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_gravity="center"
                    android:background="@drawable/gray_point"/>
            </FrameLayout>



        </LinearLayout>
    </FrameLayout>
</FrameLayout>