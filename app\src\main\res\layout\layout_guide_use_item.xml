<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:supportsRtl="false"
    android:background="@color/bg_main_title"
    >
<!--    android:background="#fff"-->


    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        >



        <ImageView
            android:id="@+id/guide_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            />


    </LinearLayout>

    <TextView
        android:id="@+id/guide_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:textColor="#ff4411"
        android:textSize="20dp"
        android:layout_gravity="center_horizontal"
        />


    <ImageView
        android:id="@+id/guide_arrow_left"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginStart="15dp"
        android:layout_marginBottom="100dp"
        android:layout_gravity="bottom|left"
        android:background="@drawable/play_circle_bg"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingEnd="5dp"
        android:src="@mipmap/icon_toolbar_back"
        />

    <ImageView
        android:id="@+id/guide_arrow_right"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="100dp"
        android:layout_gravity="bottom|right"
        android:background="@drawable/play_circle_bg"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingStart="5dp"
        android:src="@mipmap/icon_toolbar_forward"
        />

    <TextView
        android:id="@+id/guide_over"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_gravity="bottom|right"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="100dp"
        android:paddingStart="40dp"
        android:paddingEnd="40dp"
        android:background="@drawable/blue_bound"
        android:gravity="center"
        android:textColor="#fff"
        android:textSize="16dp"
        android:text="@string/yes"
        android:visibility="gone"
        />

</FrameLayout>