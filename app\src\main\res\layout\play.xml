<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#000000" >

    <VideoView
        android:id="@+id/surface_view"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true" />

    <FrameLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent" >

        <FrameLayout
            android:id="@+id/title_fl"
            android:layout_width="fill_parent"
            android:layout_height="50dp"
            android:background="@mipmap/test_widget_bg"
            android:visibility="gone" >

            <TextView
                android:id="@+id/movie_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="#ffffff"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/movie_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right|center_vertical"
                android:layout_marginRight="10dp"
                android:text="11:34"
                android:textColor="#ffffff" />
        </FrameLayout>

        <ImageView
            android:id="@+id/image_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:scaleType="centerInside"
            android:src="@drawable/goback_drawable"
            android:visibility="gone" />

        <LinearLayout
            android:id="@+id/buttom_lin"
            android:layout_width="fill_parent"
            android:layout_height="70dp"
            android:layout_gravity="bottom"
            android:background="@mipmap/test_widget_bg"
            android:gravity="center_vertical"
            android:orientation="vertical"
            >

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" >

                <TextView
                    android:id="@+id/play_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="3dp"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />

                <SeekBar
                    android:id="@+id/seekBar"
                    style="@style/player_progressBarStyleHorizontal"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_toLeftOf="@+id/play_end_time"
                    android:layout_toRightOf="@+id/play_time" />

                <TextView
                    android:id="@+id/play_end_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="3dp"
                    android:text="00:00:00"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >

                <Button
                    android:onClick="showP"
                    android:id="@+id/zhiliang"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:background="@mipmap/player_quality_current_background"
                    android:text="--"
                    android:visibility="gone"
                    android:textColor="#ffffff"
                    android:textSize="12sp" />

                <FrameLayout
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true" >

                    <ImageView
                        android:id="@+id/font"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="left"
                        android:scaleType="centerInside"
                        android:src="@mipmap/player_prev_highlight" />

                    <ImageView
                        android:id="@+id/play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:scaleType="centerInside"

                        />
                    <!--android:src="@mipmap/player_pause_highlight"-->
                    <ImageView
                        android:id="@+id/next"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="right"
                        android:scaleType="centerInside"
                        android:src="@mipmap/test_player_next_highlight" />
                </FrameLayout>
                <TextView 
                    android:layout_alignParentRight="true"
                    android:gravity="center_vertical"
                    android:layout_marginRight="2dp"
                    android:layout_width="wrap_content"
                    android:layout_height="fill_parent"
                    android:id="@+id/sudu2"
                    android:textColor="#ffffff"
                    android:textSize="14sp"
                    />
            </RelativeLayout>
        </LinearLayout>
    </FrameLayout>



    <FrameLayout
        android:id="@+id/left_lock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:background="#00000000"
        android:orientation="horizontal"
        android:padding="0dip"
        android:visibility="visible" >

        <ImageView
            android:id="@+id/image_lock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="15dp"
            android:scaleType="centerInside"
            android:src="@mipmap/lock_off"
            android:visibility="gone" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/operation_volume_brightness"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="#00000000"
        android:orientation="horizontal"
        android:padding="0dip"
        android:visibility="invisible" >

        <ImageView
            android:id="@+id/operation_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@mipmap/video_volumn_bg" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:paddingBottom="25dip" >

            <ImageView
                android:id="@+id/operation_full"
                android:layout_width="94dip"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:src="@mipmap/video_num_bg" />

            <ImageView
                android:id="@+id/operation_percent"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:scaleType="matrix"
                android:src="@mipmap/video_num_front" />
        </FrameLayout>
      
    </FrameLayout>



    <LinearLayout
        android:id="@+id/pb"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"

        android:layout_centerInParent="true"
        android:orientation="vertical"
        >

        <ProgressBar
            android:id="@+id/progress"
            style="@style/my_progress_circle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            />

        <LinearLayout
            android:id="@+id/pb1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal"
            >
            <TextView
                android:id="@+id/sudu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginRight="5dp"
                android:textColor="#ffffff"
                android:text="  "
                android:textSize="15sp" />

            <TextView
                android:id="@+id/buff"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="0%"
                android:textSize="15sp" />
        </LinearLayout>
        
    </LinearLayout>
  
</RelativeLayout>