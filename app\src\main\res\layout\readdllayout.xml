<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="16dp"
    android:layout_marginRight="16dp"
    android:layout_marginTop="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/img"
            android:layout_width="30dp"
            android:layout_height="30dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_format"
            app:layout_constraintHorizontal_weight="1"

            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_name"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tv_format"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="left"
            android:lines="1"
            android:text="123"
            android:textColor="@color/font_txt_color"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintVertical_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/video_size"
            app:layout_constraintTop_toBottomOf="@+id/img"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textSize="16sp"
            android:lines="2"
            android:text=""
            android:gravity="left"
            android:layout_marginStart="10dp"
            android:textColor="@color/font_txt_color"
            app:layout_constraintLeft_toRightOf="@+id/img"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints" />

        <TextView
            android:id="@+id/video_size"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textSize="14sp"
            android:lines="1"
            android:text=""
            android:gravity="left"
            android:layout_marginStart="10dp"
            android:textColor="@color/font_txt_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintLeft_toRightOf="@+id/img"
            app:layout_constraintRight_toLeftOf="@+id/play"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            tools:ignore="MissingConstraints" />

        <ImageView
            android:id="@+id/play"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/play_video_button_status"
            app:layout_constraintBottom_toBottomOf="parent"

            app:layout_constraintLeft_toRightOf="@+id/video_size"
            app:layout_constraintRight_toLeftOf="@+id/delete"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            tools:ignore="MissingConstraints" />

        <ImageView
            android:id="@+id/delete"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/delete_video_button_status"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/play"
            app:layout_constraintRight_toLeftOf="@+id/download"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            tools:ignore="MissingConstraints" />

        <ImageView
            android:id="@+id/download"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="0dp"
            android:background="@drawable/pause_video_button_status"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/delete"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            tools:ignore="MissingConstraints" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/download"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_name"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/background_dark"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>