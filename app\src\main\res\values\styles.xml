<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppThemeNotActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    
    <style name="pop_from_bottom">
        <item name="android:windowEnterAnimation">@anim/pop_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/pop_dismiss_bottom</item>
    </style>

    <style name="ActionSheetDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:background">@null</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="My.Translucent" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:windowFrame">@null</item>
        <item name="android:background">@null</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="player_progressBarStyleHorizontal">
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:maxHeight">8.0dip</item>
        <item name="android:indeterminate">false</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/player_seekbar_horizontal</item>
        <item name="android:minHeight">8.0dip</item>
        <item name="android:thumb">@drawable/player_seek_thumb</item>
        <item name="android:thumbOffset">0.0dip</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="my_progress_circle">
        <item name="android:indeterminateDrawable">@drawable/play_cycle_process</item>
        <item name="android:minWidth">96dp</item>
        <item name="android:minHeight">96dp</item>
        <item name="android:maxWidth">96dp</item>
        <item name="android:maxHeight">96dp</item>
    </style>

    <style name="AppTheme.AdAttribution">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">left</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">12sp</item>
        <item name="android:text">Ad</item>
        <item name="android:background">#FFCC66</item>
        <item name="android:width">15dp</item>
        <item name="android:height">15dp</item>
    </style>


    <style name="AppTheme.recyleviewdivider" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:listDivider">@drawable/divider</item>
    </style>
</resources>
