// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '2.2.0'
    repositories {
        jcenter()
        mavenCentral()
        google()
//        maven {
//            url "https://cdn01.static.adfalcon.com/sdk/android/maven"
//        }
       // maven { url "http://dl.bintray.com/populov/maven" }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.11.1'
        //classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.4'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.jakewharton:butterknife-gradle-plugin:10.2.3'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.3'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
     //   jcenter()
        mavenCentral()
        google()
        maven {
            url 'https://jitpack.io'
        }
        maven {
            url "https://sdk.tapjoy.com/"
        }
//        maven {
//            url  "https://adcolony.bintray.com/AdColony"
//        }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
